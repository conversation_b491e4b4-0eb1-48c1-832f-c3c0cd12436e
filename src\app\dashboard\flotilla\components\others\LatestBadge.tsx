import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import React from 'react';
import { BadgeTranslations, BadgeTranslationsMX } from '../translations/statusTranslations';

const LatestBadge = () => {
  const { isCountryUSA } = useCountry();
  const latest = isCountryUSA ? BadgeTranslations.latest : BadgeTranslationsMX.latest;
  return (
    <span className="inline-flex items-center text-[14px] bg-[#C6E7FF] border-[1.5px] border-[#28A3FD] px-2 py-1 rounded-sm">
      <span className="w-2 h-2 bg-[#28A3FD] rounded-full mr-2"></span>
      <span className="font-bold text-[#28A3FD]">{latest}</span>
    </span>
  );
};

export default LatestBadge;
