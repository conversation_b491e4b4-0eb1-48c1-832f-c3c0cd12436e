import axios from 'axios';
import { URL_API } from '@/constants';

export interface ResetPasswordForm {
  email: string;
}

export default async function sendResetPassword(body: ResetPasswordForm) {
  try {
    const data = {
      ...body,
    };

    const response = await axios.post(`${URL_API}/auth/recoverPassword`, data);

    return response.data;
  } catch (error: any) {
    return new Error(error.response.data.message);
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
}
