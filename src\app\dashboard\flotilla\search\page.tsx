import getCurrentUser from '@/actions/getCurrentUser';
// import SearchFlotilla from '../../components/SearchComponent';
import { getSearchQuery } from '@/actions/getSearchQuery';
import SearchBar from './components/SearchBar';
import Card from '@/components/stockCard/Card';
import Link from 'next/link';
import { pagesSearchRedirect } from '@/constants';
import { VehicleCard } from '@/actions/getAllVehicles';
import { redirect } from 'next/navigation';

interface SearchComponentProps {
  searchParams: {
    q: string;
    country?: string | null;
  };
}

export default async function SearchResult({ searchParams: { q, country } }: SearchComponentProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const vehicles = await getSearchQuery(q, 'search');

  if (!vehicles || vehicles.filter((vehicle: VehicleCard) => vehicle?._id)?.length == 0) {
    return (
      <>
        <SearchBar />
        <div>
          <h1 className="">No se encontraron vehiculos con el criterio de busqueda</h1>
        </div>
      </>
    );
  }

  return (
    <>
      <SearchBar />
      <div className="flex justify-between items-center mb-4">
        <h1>Resultados encontrados: {vehicles.length}</h1>
      </div>
      <div className="flex flex-row flex-wrap gap-5 mt-[20px]">
        {vehicles.map((vehicle, i) => {
          return (
            <Link
              href={`/dashboard/flotilla/${pagesSearchRedirect[vehicle.status || 'stock']}/${vehicle._id}${
                country ? `?country=${encodeURI(country)}` : ''
              }`}
              prefetch={false}
              key={i}
            >
              <Card
                {...vehicle}
                contract={vehicle.carNumber}
                extensionCarNumber={vehicle.extensionCarNumber}
                status={vehicle.status}
                dischargedReason={vehicle.dischargedData?.reason}
                isBlocked={vehicle.isBlocked}
              />
            </Link>
          );
        })}
      </div>
    </>
  );
}
