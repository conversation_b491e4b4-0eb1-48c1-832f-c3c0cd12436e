import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { listStatus } from '../activos/[id]/lib';
import { cookies } from 'next/headers';
import { getCookie } from 'cookies-next';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { combineSearchParamsAndFilters } from '@/constants';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Flotilla  | Bajas',
  description: 'Esto es la flotilla',
};

interface BajasPageProps {
  searchParams: Record<string, string>;
}

export default async function Bajas({ searchParams }: BajasPageProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });

  const result = await getStockVehicles({
    limit: 50,
    // listStatus: ['stock', 'overhauling'],
    status: 'discharged',
    searchParams: definitiveFilters,
    excludeStatus: listStatus,
  });

  if (!result) return null;

  return (
    <VehicleInfiniteList
      route="reingresos"
      page="Reingresos"
      data={result.stock}
      totalCount={result.totalCount}
    />
  );
}
