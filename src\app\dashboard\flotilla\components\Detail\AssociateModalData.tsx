/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable consistent-return */
'use client';
import DocumentDisplay from '@/components/DocumentDisplay';
import { MdDelete, MdEmail, MdOutlineContentCopy } from 'react-icons/md';
import { BsFillTelephoneFill } from 'react-icons/bs';
import { RiCloseLine, RiWhatsappFill } from 'react-icons/ri';
import { FaStar } from 'react-icons/fa';
import { AiOutlineClose } from 'react-icons/ai';
import 'animate.css';
// import { AssociateModalDataProps } from '../page';
import { IconButton, Tab, TabList, TabPanel, TabPanels, Tabs, useToast } from '@chakra-ui/react';
import ZoomImage from '../others/ZoomImage';
import { useDetail } from '../detailContext';
import { Contact, Participant, VehicleResponse } from '@/actions/getVehicleData';
import CustomModal from '@/components/Modals/CustomModal';
import { format, parseISO } from 'date-fns';
import { memo, useEffect, useMemo, useState } from 'react';
import InputFile from '@/components/Inputs/InputFile';
import axios from 'axios';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Capabilities, Countries, Sections, Subsections, URL_API } from '@/constants';
import {
  addressVerificationValidatorSchema,
  curpValidatorSchema,
  deliveredImagesValidatorSchema,
  driverLicenseBackValidatorSchema,
  driverLicenseFrontValidatorSchema,
  garageImgValidatorSchema,
  ineBackValidatorSchema,
  ineFrontValidatorSchema,
  taxStatusValidatorSchema,
} from '@/validatorSchemas/updateAssociateModals';
import * as Yup from 'yup';
import CustomInput from '@/components/Inputs/CustomInput';
import InputPhone from '@/components/Inputs/InputPhone';
import InputDate from '@/components/Inputs/InputDate';
import { useFormikContext } from 'formik';
import Image from 'next/image';
import AddMoreFiles from '@/components/Inputs/AddMoreFiles';
import { contactsSchema, curpRegExp } from '@/validatorSchemas/assignAssociateSchema';
import EditPicture from '@/components/Inputs/EditPicture';
import { CiEdit } from 'react-icons/ci';
import { IoCloseSharp } from 'react-icons/io5';
import AddFiles from '@/components/Inputs/AddFiles';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import ModalContainer from '../Modals/ModalContainer';
import FormikContainer from '@/components/Formik/FormikContainer';
import InputArray from '@/components/Inputs/InputArray';
import { useEditAssociateModal } from '@/app/dashboard/(dashboard)/stores/useRecordsModal';
import { SectionsAssociateModalUS } from './AssociateModalUS';
import { PopoverForSignatures } from '../Modals/FinishInService';
import Link from 'next/link';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import PrimaryButton from '@/components/PrimaryButton';
import { LoaderCircle } from 'lucide-react';

function getLengthName(name: string) {
  return name?.length > 25 ? name.slice(0, 30) + '...' : name.slice(0, 30);
}

const citiesObj: { [key: string]: string } = {
  cdmx: 'Ciudad de México',
  gdl: 'Guadalajara',
  mty: 'Monterrey',
  tij: 'Tijuana',
  qro: 'Querétaro',
};
export default memo(AssociateModalData);

export interface ExtendedProps {
  carNumber: VehicleResponse['carNumber'];
  vehicle?: VehicleResponse;
  vehicleId?: string;
}

function AssociateModalData(props: VehicleResponse['drivers'][number] & ExtendedProps) {
  const { isAssocOpen, handleIsAssocOpen } = useDetail();
  const vehicle = props.vehicle as VehicleResponse;
  const props2 = {
    ...props,
    onClose: handleIsAssocOpen,
    showSignDocs: vehicle.step.stepNumber >= 4,
    signDocs: props.signDocs || {
      contract: false,
      promissoryNote: false,
      deliveryReceipt: false,
      warranty: false,
      invoice: false,
      privacy: false,
      contactInfo: false,
    },
  };

  const isCountryUSA = props?.country === Countries['United States'];

  return (
    <>
      {isAssocOpen ? (
        <div
          className={`
            absolute top-0 left-0 z-[100] w-full h-full
            ${isAssocOpen ? 'visible' : 'invisible'}
            bg-black
            bg-opacity-50
            flex
            pt-[70px]
            justify-center
            transition-opacity
            ease-in-out
          `}
        >
          {
            isCountryUSA ? <SectionsAssociateModalUS {...props2} /> : <SectionsAssociateModal {...props2} />
          }
        </div>
      ) : null}
    </>
  );
}

// interface OnClose {
//   onClose: () => void;
// }

interface SectionsAssociateModalProps {
  onClose: () => void;
  showSignDocs: boolean;
}

type Props = VehicleResponse['drivers'][number] & ExtendedProps & SectionsAssociateModalProps & {
  isDriverBox?: boolean;
}

export function SectionsAssociateModal(
  props: Props
) {
  const address = props.address;
  const displayCompleteAddr = `${address.addressStreet.toUpperCase()}
  ${address.exterior} ${address.colony.toUpperCase()}
${citiesObj[address.city]?.toUpperCase() || ''} C.P. ${address.postalCode} ${address.state?.toUpperCase()} `;
  const toast = useToast();
  const router = useRouter();
  const [CLABE, setCLABE] = useState('');

  const { user } = useCurrentUser();

  const admissionRequestId = props.admissionRequestId;

  const redirectAdmissionRequest = useMemo(() => {

    const selfUrl = window.location.href.split('/').slice(0, 3).join('/');
    const fullUrl = `${selfUrl}/dashboard/clientes/solicitudes/${admissionRequestId}`; // to use as link tag
    const url = `/dashboard/clientes/solicitudes/${admissionRequestId}`; // to use as router.push

    return { fullUrl, url };

  }, [admissionRequestId]);

  useEffect(() => {
    (async () => {
      try {
        const result = await axios.get(`${URL_API}/wire4/get-bank-accounts/${props.email}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        setCLABE(result.data.clabe);
      } catch (error) {
        console.log(error);
      }
    })();
  }, [props.email, user.accessToken]);

  const handleCopy = (text: string | number) => {
    navigator.clipboard.writeText(text.toString());
    toast({
      title: 'Copiado',
      status: 'success',
      duration: 3000,
      position: 'top',
    });
  };
  const [nameFiles, setNameFiles] = useState({
    curp: '',
    taxStatus: '',
    addressVerification: '',
    ineFront: '',
    ineBack: '',
    driverLicenseFront: '',
    driverLicenseBack: '',
    bankStatementsOne: '',
    bankStatementsTwo: '',
    bankStatementsThree: '',
    bankStatementsFour: '',
    bankStatementsFive: '',
    bankStatementsSix: '',
    garage: '',
    unSignedContract: '',
    avalINE: '',
  });

  const handleSetNames = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const carNumber = useMemo(() => props.carNumber, [props.carNumber]);

  const headersConfig = useMemo(
    () => ({
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'multipart/form-data',
      },
    }),
    [user.accessToken]
  );

  const [isEdit, setIsEdit] = useState(false);

  const [deliveredImages, setDeliveredImages] = useState(props.deliveredImages);
  const [deleteableImgs, setDeleteableImgs] = useState<string[]>([]);
  const [addImgs, setAddImgs] = useState({} as FileList);

  const [nameFile, setNameFile] = useState(props.avalData?.ine?.originalName || '');

  const associateModal = useEditAssociateModal();

    const ability = usePermissions();
    const canEditDriverProfile = canPerform(
      ability,
      Capabilities.EditDriverProfile,
      Sections.Fleet,
      Subsections.General
    );

  return (
    <div
      id="modal-container"
      className={`
              w-full
              md:w-[90%]
              min-h-[650px] 
              h-[max-content] 
              max-h-[90%]
              relative bg-white
              rounded-[10px] p-[30px]
              text-[#262D33] flex flex-col
              animate__animated animate__fadeInDown animate__faster duration-100
            `}
    >
      <div className="flex justify-between w-full ">
        <p className="text-[18px] font-[600] ">Perfil del cliente</p>
        <AiOutlineClose onClick={props.onClose} size={22} strokeWidth="2px" cursor="pointer" />
        {/* <p onClick={handleIsAssocOpen}>X</p> */}
      </div>
      <section className="flex mt-[30px] gap-[20px] min-h-[120px] h-full flex-wrap">
        <div className="min-w-[120px] min-h-[120px] w-[120px] h-[120px] rounded relative">
          {/* <EditImage /> */}
          <EditPicture
            picture={props.picture}
            onSubmit={async (file) => {
              await axios.patch(
                `${URL_API}/associate/update/picture/${props._id}`,
                {
                  picture: file,
                  vehicleId: props.vehicle?._id || props.vehicleId,
                },
                headersConfig
              );
            }}
            canEdit={canEditDriverProfile}
          />
          {/* {props.picture ? (
            <Image
              src={props.picture.url}
              layout="fill"
              objectFit="cover"
              alt="profile"
              className="rounded-[10px]"
            />
          ) : (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src="https://img.freepik.com/vector-premium/imagen-dibujos-animados-hongo-palabra-hongo_587001-200.jpg?w=2000"
              alt="something"
            />
          )} */}
        </div>
        <div id="info" className="flex flex-col h-full flex-wrap ">
          <div className="flex justify-between ">
            <div className="flex items-center gap-3">
              <p className="text-[#5800F7] text-[20px] font-[600] ">
                {props.firstName} {props.lastName}
              </p>
              <MdOutlineContentCopy
                onClick={() =>
                  handleCopy(
                    // backticks
                    `${props.firstName} ${props.lastName}`
                  )
                }
                cursor="pointer"
              />
              <div className="flex gap-2 text-[#FFAB00] items-center">
                <FaStar /> <p>4.8</p>
              </div>

              {canEditDriverProfile && <IconButton
                icon={<CiEdit size={30} strokeWidth="1px" color={'#5800F7'} />}
                p={0}
                // data-cy={testId}
                bgColor="transparent"
                aria-label="ciedit"
                onClick={associateModal.onOpen}
              />}

              {associateModal.isOpen && (
                <ModalContainer
                  title="Editar conductor"
                  onClose={associateModal.onClose}
                  width="w-[75%]"
                  removeScrollBar={false}
                >
                  <Tabs>
                    <TabList>
                      <Tab>Pagina 1</Tab>
                      <Tab>Pagina 2</Tab>
                    </TabList>

                    <TabPanels>
                      <TabPanel>
                        <FormikContainer
                          initialValues={{
                            firstName: props.firstName,
                            lastName: props.lastName,
                            email: props.email,
                            phone: props.phone.replace('+52', ''),
                            addressStreet: props.address.addressStreet,
                            exterior: props.address.exterior,
                            interior: props.address.interior,
                            postalCode: props.address.postalCode,
                            birthDay: props.birthDay,
                            colony: props.address.colony,
                            curp: props.curp,
                            avalName: props.avalData?.name || '',
                            avalPhone: props.avalData?.phone?.replace('+52', '') || '',
                            avalEmail: props.avalData?.email || '',
                            avalAddress: props.avalData?.address || '',
                          }}
                          validatorSchema={Yup.object().shape({
                            firstName: Yup.string(),
                            lastName: Yup.string(),
                            email: Yup.string().required(),
                            phone: Yup.string().required('El teléfono es un campo obligatorio').length(10, 'El número de teléfono debe tener 10 dígitos'),
                            curp: Yup.string().matches(curpRegExp, 'CURP inválida'),
                            avalName: Yup.string().notRequired(), // not required for this week
                            avalPhone: Yup.string().length(10, 'El número de teléfono debe tener 10 dígitos').notRequired(), // not required for this week
                            avalEmail: Yup.string().notRequired(), // not required for this week
                            avalINE: Yup.mixed().notRequired(), // not required for this week
                            avalAddress: Yup.string().notRequired(), // not required for this week
                          })}
                          onSubmit={async (values, helpers) => {
                            try {
                              const isSameEmail = props.email === values.email;
                              if (!isSameEmail) {
                                const result = await axios.get(`${URL_API}/associate/email/${values.email}`, {
                                  headers: {
                                    Authorization: `Bearer ${user?.accessToken}`,
                                  },
                                });
                                if (result.data.message.toLowerCase().includes('email encontrado')) {
                                  // agregar error al campo
                                  helpers.setFieldError('email', 'Email ya esta registrado con un asociado');
                                  return;
                                }
                              }
                            } catch (error) {
                              console.log(error);
                            }
                            try {
                              if (!values.phone.startsWith('+52')) {
                                values.phone = "+52" + values.phone;
                              }
                              if (values.avalPhone.length == 10 && !values.avalPhone.startsWith('+52')) {
                                values.avalPhone = "+52" + values.avalPhone;
                              }
                              const dataToSend = {
                                ...values,
                                avalData: {
                                  name: values.avalName,
                                  phone: values.avalPhone,
                                  email: values.avalEmail,
                                  address: values.avalAddress,
                                },
                              };
                              const result = await axios.patch(
                                `${URL_API}/associate/update/associateData/${props._id}`,
                                dataToSend,
                                {
                                  headers: {
                                    Authorization: headersConfig.headers.Authorization,
                                    'Content-Type': 'multipart/form-data',
                                  },
                                }
                              );
                              toast({
                                title: result.data.message,
                                description: 'Actualizando pagina...',
                                duration: 3000,
                                status: 'success',
                                position: 'top',
                              });
                              setTimeout(() => {
                                window.location.reload();
                              }, 3050);
                            } catch (error: any) {
                              toast({
                                title: error.response.data.message,
                                duration: 6000,
                                status: 'error',
                                position: 'top',
                              });
                            } finally {
                              // onClose();
                              props.onClose();
                            }
                          }}
                        >
                          <div className="flex">
                            <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
                              <CustomInput name="firstName" label="Nombres" type="text" />
                              <CustomInput name="lastName" label="Apellidos" type="text" />
                              <InputPhone name="phone" label="Telefono" placeholder="" />
                              <CustomInput name="colony" label="Colonia" type="text" />
                              <CustomInput name="postalCode" label="Codigo Postal" type="text" />
                              <CustomInput name="addressStreet" label="Calle" type="text" />
                              <CustomInput name="exterior" label="Numero exterior" type="text" />
                              <CustomInput name="interior" label="Numero interior (opcional)" type="text" />
                              <CustomInput name="curp" label="Curp" type="text" />
                              <InputDate
                                name="birthDay"
                                label="Fecha de nacimiento"
                                defaultValue={props.birthDay}
                              />

                              <CustomInput name="avalName" label="Nombre aval" type="text" />
                              <InputPhone name="avalPhone" label="Telefono aval" placeholder="" />
                              <CustomInput name="avalEmail" label="Email aval" type="text" />
                              <CustomInput name="avalAddress" label="Dirección de aval" type="text" />

                              <InputFile
                                name="avalINE"
                                accept="all-images"
                                placeholder="No mayor a 5mb"
                                nameFile={nameFile}
                                handleSingleSetName={setNameFile}
                                label="INE de aval"
                                buttonText="Añadir"
                              />
                            </div>
                          </div>
                        </FormikContainer>
                      </TabPanel>
                      <TabPanel>
                        <FormikContainer
                          initialValues={{
                            contacts: (Array.isArray(props.contacts)
                              ? props.contacts as Contact[]
                              : []
                            ).length > 1
                              ? (props.contacts as Contact[]).map((contact) => {
                                return {
                                  contactName: contact.name,
                                  contactKinship: contact.kinship,
                                  contactPhone: contact.phone,
                                  contactAddress: contact.address,
                                };
                              })
                              : [
                                {
                                  contactName: '',
                                  contactKinship: '',
                                  contactPhone: '',
                                  contactAddress: '',
                                },
                              ],
                          }}
                          validatorSchema={contactsSchema}
                          validateChanges
                          onSubmit={async (values) => {
                            // console.log('values', values);

                            const obj = {
                              contacts: values.contacts.map((contact) => {
                                return {
                                  name: contact.contactName,
                                  kinship: contact.contactKinship,
                                  phone: contact.contactPhone,
                                  address: contact.contactAddress,
                                };
                              }),
                            };

                            try {
                              const result = await axios.patch(
                                `${URL_API}/associate/update/associateData/${props._id}`,
                                obj,
                                {
                                  headers: {
                                    Authorization: headersConfig.headers.Authorization,
                                    'Content-Type': 'multipart/form-data',
                                  },
                                }
                              );
                              toast({
                                title: result.data.message,
                                description: 'Actualizando pagina...',
                                duration: 3000,
                                status: 'success',
                                position: 'top',
                              });
                              setTimeout(() => {
                                window.location.reload();
                              }, 3050);
                            } catch (error: any) {
                              toast({
                                title: error.response.data.message || 'Error al enviar los datos',
                                duration: 6000,
                                status: 'error',
                                position: 'top',
                              });
                            }
                          }}
                        >
                          <div>
                            <InputArray
                              name="contacts"
                              nameItem='Referencia'
                              label="Referencias"
                              fields={[
                                { name: 'contactName', type: 'text', label: 'Nombre' },
                                { name: 'contactKinship', type: 'text', label: 'Parentesco' },
                                { name: 'contactPhone', type: 'text', label: 'Celular' },
                                { name: 'contactAddress', type: 'textarea', label: 'Dirección' },
                              ]}
                              initialItem={{
                                contactName: '',
                                contactKinship: '',
                                contactPhone: '',
                                contactAddress: '',
                              }}
                            />
                          </div>
                        </FormikContainer>
                      </TabPanel>
                    </TabPanels>
                  </Tabs>
                </ModalContainer>
              )}
            </div>
          </div>
          <div className="flex flex-wrap gap-x-[35px]">
            <div className="flex items-center gap-2">
              <p>
                <span className="font-bold">ID</span> {props._id?.substring(0, 5)}
              </p>
              <MdOutlineContentCopy
                className="size-[16px]"
                onClick={() => handleCopy(props._id)}
                cursor="pointer"
              />
            </div>
            <div className="flex items-center gap-2">
              <p>
                <span className="font-bold">Fecha de nacimiento </span>
                {format(parseISO(props.birthDay), 'dd/MM/yyyy')}
              </p>
            </div>
            <div className="flex items-center gap-2">
              <p>
                <span className="font-bold">CURP</span> {props.curp}
              </p>
              <MdOutlineContentCopy
                className="size-[16px]"
                onClick={() => handleCopy(props.curp)}
                cursor="pointer"
              />
            </div>
            <div className="flex items-center gap-2">
              <p>
                <span className="font-bold">RFC</span> {props.rfc}
              </p>
              <MdOutlineContentCopy onClick={() => handleCopy(props.rfc)} cursor="pointer" />
            </div>
            {CLABE && (
              <div className="flex items-center gap-2">
                <p>
                  <span className="font-bold">CLABE</span> {CLABE}
                </p>
                <MdOutlineContentCopy onClick={() => handleCopy(CLABE)} cursor="pointer" />
              </div>
            )}
          </div>
          <p>
            {displayCompleteAddr || 'Av. 16 de Septiembre 4 s/n Santa María Acolman C.P. 55878 Edo. México'}
          </p>
          <div className="flex gap-3 mt-[2px] text-white ">
            {props.platforms?.uber && (
              <div className="w-[80px] h-[27px] rounded bg-[#000] flex justify-center items-center ">
                <p>Uber</p>
              </div>
            )}
            {props.platforms?.didi && (
              <div className="w-[80px] h-[27px] rounded bg-[#F38031] flex justify-center items-center ">
                <p>Didi</p>
              </div>
            )}
          </div>
        </div>
      </section>
      <section id="contact" className="mt-[20px] flex flex-col flex-wrap w-full">
        <p className="font-[600]">Métodos de contacto</p>
        <div className="flex flex-wrap gap-x-[30px] gap-y-[15px] ">
          <div className="flex gap-2 items-center mt-[15px]">
            <div className="w-[40px] h-[40px] bg-[#CC6FF833] rounded-[9px] flex justify-center items-center ">
              <BsFillTelephoneFill color="#CC6FF8" size={22} />
            </div>
            <p className="text-[#CC6FF8]">{props.phone}</p>
            <MdOutlineContentCopy onClick={() => handleCopy(props.phone)} cursor="pointer" />
          </div>
          <div className="flex gap-2 items-center mt-[15px]">
            <div className="w-[40px] h-[40px] bg-[#25D36633] rounded-[9px] flex justify-center items-center ">
              <RiWhatsappFill color="#25D366" size={22} />
            </div>
            <p className="text-[#25D366]">{props.whatsApp ? props.whatsApp : props.phone}</p>
            <MdOutlineContentCopy
              onClick={() => handleCopy(props.whatsApp ? props.whatsApp : props.phone)}
              cursor="pointer"
            />
          </div>
          <div className="flex gap-2 items-center mt-[15px]">
            <div className="w-[40px] h-[40px] bg-[#5CAFFC33] rounded-[9px] flex justify-center items-center ">
              <MdEmail color="#5CAFFC" size={22} />
            </div>
            <p className="text-[#5CAFFC]">{props.email}</p>
            <MdOutlineContentCopy onClick={() => handleCopy(props.email)} cursor="pointer" />
          </div>
          {
            admissionRequestId && (
              <div className="flex gap-2 items-center mt-[15px]">
                <Link
                  className='text-blue-500 underline'
                  href={redirectAdmissionRequest.fullUrl}
                >
                  Ver solicitud de admisión
                </Link>
              </div>
            )
          }


        </div>
      </section>
      <section id="documents" className="mt-[30px] flex flex-col  text-[#464E5F] w-full">
        <p className="font-[600] mb-[15px]">Documentación</p>
        <div className="grid w-full lg:grid-cols-2 gap-4 ">
          <FinishedDocAndModal
            property="curp"
            driver={props}
            nameFile={nameFiles.curp}
            handleSetNames={handleSetNames}
            validatorSchema={curpValidatorSchema}
            carNumber={carNumber}
            label="CURP"
            pText="CURP"
            accept="pdf"
            onClose={() => {
              setNameFiles({
                ...nameFiles,
                curp: '',
              });
            }}
            canEdit={canEditDriverProfile}
          />
          <FinishedDocAndModal
            property="taxStatus"
            driver={props}
            nameFile={nameFiles.taxStatus}
            handleSetNames={handleSetNames}
            validatorSchema={taxStatusValidatorSchema}
            carNumber={carNumber}
            label="Situación fiscal"
            pText="Constancia de situación fiscal"
            accept="pdf"
            onClose={() => {
              setNameFiles({
                ...nameFiles,
                taxStatus: '',
              });
            }}
            canEdit={canEditDriverProfile}
          />
          <FinishedDocAndModal
            property="addressVerification"
            driver={props}
            nameFile={nameFiles.addressVerification}
            handleSetNames={handleSetNames}
            validatorSchema={addressVerificationValidatorSchema}
            carNumber={carNumber}
            label="Comprobante"
            pText="Comprobante de domicilio"
            accept="pdf"
            onClose={() => {
              setNameFiles({
                ...nameFiles,
                addressVerification: '',
              });
            }}
            canEdit={canEditDriverProfile}
          />
          <div className="flex items-end justify-between w-full md:gap-3 2xl:gap-0">
            <p>Contrato no firmado (GENERADO)</p>
            {props.unSignedContractDoc && (
              <div className="w-[max-content] overflow-hidden">
                <DocumentDisplay
                  url={props.unSignedContractDoc.url}
                  docName={getLengthName(props.unSignedContractDoc.originalName)}
                  backgroundColor="rgba(88, 0, 247, 0.2)"
                  textColor="#5800F7"
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex gap-[30px] flex-wrap mt-[20px]">
          <div className="flex flex-col gap-[15px]">
            <div className='flex items-center gap-4'>
              <p>INE</p>
              {
                (props.documents.ine?.ineFront || props.documents.ine?.ineBack) && (
                  <CustomModal
                    header="Editar INE"
                    isPrimaryButton
                    updateIconColor="#5800F7"
                    canEdit={canEditDriverProfile}
                    isUpdate
                    initialValues={{
                      ineFront: '',
                      ineBack: '',
                    }}
                    openButtonText="Editar INE"
                    confirmButtonText="Enviar"

                    onSubmit={async (values) => {
                      // console.log('values', values);

                      const obj = { ...values, carNumber };

                      try {

                        await axios.patch(
                          `${URL_API}/associate/update/ine/${props._id}`,
                          obj,
                          headersConfig
                        );

                        toast({
                          title: 'INE actualizado',
                          status: 'success',
                          duration: 3000,
                          position: 'top',
                        })
                        return window.location.reload();

                      } catch (error: any) {
                        if (error.response) {
                          toast({
                            title: error.response.data.message || 'Error al actualizar el INE',
                            status: 'error',
                            duration: 3000,
                            position: 'top',
                          })
                        }
                      }


                    }}

                    body={
                      <EditINEBodyModal
                        ineFront={props.documents.ine?.ineFront}
                        ineBack={props.documents.ine?.ineBack}
                      />
                    }


                    onCloseModal={() => {
                      setNameFiles({
                        ...nameFiles,
                        ineFront: '',
                        ineBack: '',
                      });
                    }}
                  />
                )
              }

            </div>
            <div className="flex gap-3 flex-wrap">
              {/* INE FRONT */}

              {props.documents.ine?.ineFront ? (
                <ZoomImage
                  imageUrl={
                    props.documents.ine?.ineFront?.url ||
                    'https://tolucalabellacd.com/wp-content/uploads/2021/04/como-tramitar-licencia-conducir-edomex-requisitos-costos-citas.jpg'
                  }
                  name={props.documents.ine?.ineFront?.originalName}
                />
              ) : (
                <div className="flex items-center">
                  {/* INE FRONT MODAL */}
                  <CustomModal
                    header="Añadir INE fremte"
                    initialValues={{ ineFront: '' }}
                    openButtonText="Subir INE"
                    confirmButtonText="Enviar"
                    validatorSchema={ineFrontValidatorSchema}
                    onSubmit={async (values) => {
                      const obj = { ...values, carNumber };

                      const result = await axios.patch(
                        `${URL_API}/associate/update/ine/${props._id}`,
                        obj,
                        headersConfig
                      );
                      return result;
                    }}
                    body={
                      <div className="flex flex-col">
                        <InputFile
                          name="ineFront"
                          label="INE Frente"
                          nameFile={nameFiles.ineFront}
                          handleSetName={handleSetNames}
                          accept="all-images"
                          placeholder="Archivo no mayor a 3mb"
                          buttonText="Subir archivo"
                        />
                      </div>
                    }
                    isPrimaryButton
                    onCloseModal={() => {
                      setNameFiles({
                        ...nameFiles,
                        ineFront: '',
                      });
                    }}
                    canEdit = {canEditDriverProfile}
                  />
                </div>
              )}

              {/* INE BACK */}
              {props.documents.ine?.ineBack ? (
                <ZoomImage
                  imageUrl={
                    props.documents.ine?.ineBack?.url ||
                    'https://tolucalabellacd.com/wp-content/uploads/2021/04/como-tramitar-licencia-conducir-edomex-requisitos-costos-citas.jpg'
                  }
                  name={props.documents.ine?.ineBack?.originalName}
                />
              ) : (
                <div className="flex items-center">
                  <CustomModal
                    header="Añadir INE Reverso"
                    initialValues={{ ineBack: '' }}
                    openButtonText="Subir INE Reverso"
                    confirmButtonText="Enviar"
                    validatorSchema={ineBackValidatorSchema}
                    onSubmit={async (values) => {
                      const obj = { ...values, carNumber };

                      const result = await axios.patch(
                        `${URL_API}/associate/update/ine/${props._id}`,
                        obj,
                        headersConfig
                      );
                      return result;
                    }}
                    body={
                      <div className="flex flex-col">
                        <InputFile
                          name="ineBack"
                          label="INE Reverso"
                          nameFile={nameFiles.ineBack}
                          handleSetName={handleSetNames}
                          accept="all-images"
                          placeholder="Archivo no mayor a 3mb"
                          buttonText="Subir archivo"
                        />
                      </div>
                    }
                    isPrimaryButton
                    onCloseModal={() => {
                      setNameFiles({
                        ...nameFiles,
                        ineBack: '',
                      });
                    }}
                    canEdit = {canEditDriverProfile}
                  />
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-[15px] flex-wrap">
            <div className='flex items-center gap-3'>
              <p>Lic. Conducir</p>

              {
                (props.documents.driverLicense?.driverLicenseFront ||
                  props.documents.driverLicense?.driverLicenseBack) && (
                  <CustomModal
                    header="Editar Licencia de conducir"
                    isPrimaryButton
                    updateIconColor="#5800F7"
                    isUpdate
                    initialValues={{
                      driverLicenseFront: '',
                      driverLicenseBack: '',
                    }}
                    openButtonText="Editar Licencia"
                    confirmButtonText="Enviar"
                    onSubmit={async (values) => {
                      // console.log('values', values);

                      const obj = { ...values, carNumber };

                      try {

                        await axios.patch(
                          `${URL_API}/associate/update/driverLicense/${props._id}`,
                          obj,
                          headersConfig
                        );

                        toast({
                          title: 'Licencia actualizada',
                          status: 'success',
                          duration: 3000,
                          position: 'top',
                        })
                        return window.location.reload();

                      } catch (error: any) {
                        if (error.response) {
                          toast({
                            title: error.response.data.message || 'Error al actualizar la Licencia',
                            status: 'error',
                            duration: 3000,
                            position: 'top',
                          })
                        }
                      }
                    }}
                    body={
                      <EditDriverLicenseBodyModal
                        driverLicenseFront={props.documents.driverLicense?.driverLicenseFront}
                        driverLicenseBack={props.documents.driverLicense?.driverLicenseBack}
                      />
                    }
                    onCloseModal={() => {
                      setNameFiles({
                        ...nameFiles,
                        driverLicenseFront: '',
                        driverLicenseBack: '',
                      });
                    }}
                    canEdit = {canEditDriverProfile}
                  />
                )
              }


            </div>
            <div className="flex gap-3 flex-wrap items-center">
              {props.documents.driverLicense?.driverLicenseFront ? (
                <ZoomImage
                  imageUrl={
                    props.documents.driverLicense?.driverLicenseFront?.url ||
                    'https://tolucalabellacd.com/wp-content/uploads/2021/04/como-tramitar-licencia-conducir-edomex-requisitos-costos-citas.jpg'
                  }
                  name={props.documents.driverLicense?.driverLicenseFront?.originalName}
                />
              ) : (
                <CustomModal
                  header="Añadir Licencia Frente"
                  initialValues={{ driverLicenseFront: '' }}
                  openButtonText="Subir Licencia Frente"
                  confirmButtonText="Enviar"
                  validatorSchema={driverLicenseFrontValidatorSchema}
                  onSubmit={async (values) => {
                    const obj = { ...values, carNumber };

                    const result = await axios.patch(
                      `${URL_API}/associate/update/driverLicense/${props._id}`,
                      obj,
                      headersConfig
                    );
                    return result;
                  }}
                  body={
                    <div className="flex flex-col items-center">
                      <InputFile
                        name="driverLicenseFront"
                        label="Licencia Frente"
                        nameFile={nameFiles.driverLicenseFront}
                        handleSetName={handleSetNames}
                        accept="all-images"
                        placeholder="Archivo no mayor a 3mb"
                        buttonText="Subir archivo"
                      />
                    </div>
                  }
                  isPrimaryButton
                  onCloseModal={() => {
                    setNameFiles({
                      ...nameFiles,
                      driverLicenseFront: '',
                    });
                  }}
                  canEdit={canEditDriverProfile}
                />
              )}

              {props.documents.driverLicense?.driverLicenseBack ? (
                <ZoomImage
                  imageUrl={
                    props.documents.driverLicense?.driverLicenseBack?.url ||
                    'https://www.informador.mx/__export/1536274569489/sites/elinformador/img/2018/09/06/licencia_crop1536274322622.jpg_788543494.jpg'
                  }
                  name={props.documents.driverLicense?.driverLicenseBack?.originalName}
                />
              ) : (
                <CustomModal
                  header="Añadir Licencia Reverso"
                  initialValues={{ driverLicenseBack: '' }}
                  openButtonText="Subir Licencia Reverso"
                  confirmButtonText="Enviar"
                  validatorSchema={driverLicenseBackValidatorSchema}
                  onSubmit={async (values) => {
                    const obj = { ...values, carNumber };

                    const result = await axios.patch(
                      `${URL_API}/associate/update/driverLicense/${props._id}`,
                      obj,
                      headersConfig
                    );
                    return result;
                  }}
                  body={
                    <div className="flex flex-col">
                      <InputFile
                        name="driverLicenseBack"
                        label="Licencia Reverso"
                        nameFile={nameFiles.driverLicenseBack}
                        handleSetName={handleSetNames}
                        accept="all-images"
                        placeholder="Archivo no mayor a 3mb"
                        buttonText="Subir archivo"
                      />
                    </div>
                  }
                  isPrimaryButton
                  onCloseModal={() => {
                    setNameFiles({
                      ...nameFiles,
                      driverLicenseBack: '',
                    });
                  }}
                  canEdit={canEditDriverProfile}
                />
              )}
            </div>
          </div>

          {/* {
            props.documents.driverLicense?.driverLicenseFront ? (
              <FinishedDocAndModal
                property="curp"
                driver={props}
                nameFile={nameFiles.curp}
                handleSetNames={handleSetNames}
                validatorSchema={curpValidatorSchema}
                carNumber={carNumber}
                label="CURP"
                pText="CURP"
                accept="pdf"
                onClose={() => {
                  setNameFiles({
                    ...nameFiles,
                    curp: '',
                  });
                }}
              />) :

              <UnsfinishedDocAndModal
                property="driverLicenseFront"
                driver={props}
                nameFile={nameFiles.driverLicenseFront}
                handleSetNames={handleSetNames}
                validatorSchema={driverLicenseFrontValidatorSchema}
                carNumber={carNumber}
                label="Licencia Frente"
                pText="Licencia Frente"
                accept="all-images"
                onClose={() => {
                  setNameFiles({
                    ...nameFiles,
                    driverLicenseFront: '',
                  });
                }}
              />
          } */}

          <div className="flex flex-col gap-[15px]">
            <p>Cochera de resguardo</p>
            <div className="flex gap-3">
              {props.documents.garage ? (
                <ZoomImage
                  imageUrl={
                    props.documents.garage?.url ||
                    'https://arquitecturamas.mx/fotos/arquitectura_cochera_queretaro_juriquilla_cumbres_del_lago_1.jpg'
                  }
                  name={props.documents.garage?.originalName}
                />
              ) : (
                <CustomModal
                  header="Agregar foto de garage"
                  openButtonText="Agregar fotos de entrega"
                  confirmButtonText="Enviar"
                  onCloseModal={() => {
                    setNameFiles({
                      ...nameFiles,
                      garage: '',
                    });
                  }}
                  validatorSchema={garageImgValidatorSchema}
                  initialValues={{ garage: '' }}
                  isPrimaryButton
                  onSubmit={async (values) => {
                    const response = await axios.patch(
                      `${URL_API}/associate/update/garage/${props._id}`,
                      values,
                      headersConfig
                    );
                    return response;
                  }}
                  body={
                    <>
                      <InputFile
                        name="garage"
                        label="Foto de cochera"
                        placeholder="Imagen no mayor a 3mb"
                        buttonText="Subir"
                        handleSetName={handleSetNames}
                        nameFile={nameFiles.garage}
                        accept="all-images"
                      />
                    </>
                  }
                  canEdit={canEditDriverProfile}
                />
              )}
            </div>
          </div>
          <div className="flex flex-col gap-[15px]">
            <p>INE AVAL</p>
            <div className="flex gap-3">
              {props.avalData?.ine && (
                <ZoomImage
                  imageUrl={
                    props.avalData?.ine?.url ||
                    'https://arquitecturamas.mx/fotos/arquitectura_cochera_queretaro_juriquilla_cumbres_del_lago_1.jpg'
                  }
                  name={props.avalData?.ine?.originalName}
                />
              )}
            </div>
          </div>
        </div>
      </section>
      {(props.signDocs && props.showSignDocs) && (
        <section id="signedDocs" className="mt-[30px] flex flex-col text-[#464E5F] w-full">
          <p className="font-[600] mb-[15px]">Documentos firmados</p>
          <div className="grid w-full sm:grid-cols-2 gap-4 ">
            <UnsfinishedDocAndModal
              property="contract"
              carNumber={carNumber}
              label="Contrato"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="promissoryNote"
              carNumber={carNumber}
              label="Pagaré"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="deliveryReceipt"
              carNumber={carNumber}
              label="Acta de entrega"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="warranty"
              carNumber={carNumber}
              label="Garantia"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="invoice"
              carNumber={carNumber}
              label="Uso de Factura"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="privacy"
              carNumber={carNumber}
              label="Privacidad"
              driver={props}
              canEdit={canEditDriverProfile}
            />
            <UnsfinishedDocAndModal
              property="contactInfo"
              carNumber={carNumber}
              label="Información de contacto"
              driver={props}
              canEdit={canEditDriverProfile}
            />
          </div>
        </section>
      )}
      {props.deliveredImages?.length > 0 && (
        <section id="deliveredImages" className=" mt-[30px]">
          <div className="flex flex-col gap-3 text-primaryPurple">
            <div className="flex items-center gap-3">
              <p className=" text-textGray2">Fotos de entrega</p>
              {isEdit ? (
                <div className="flex items-center gap-3">
                  <IoCloseSharp
                    className="cursor-pointer"
                    size={30}
                    strokeWidth="1px"
                    onClick={() => {
                      setIsEdit(!isEdit);
                      setDeliveredImages(props.deliveredImages);
                      setDeleteableImgs([]);
                    }}
                  />
                  <p
                    className="cursor-pointer"
                    onClick={() => {
                      setIsEdit(!isEdit);
                      setDeliveredImages(props.deliveredImages);
                      setDeleteableImgs([]);
                    }}
                  >
                    Cancelar
                  </p>
                  <button
                    onClick={() => {
                      if (deliveredImages.length + Array.from(addImgs).length < 3)
                        return toast({
                          title: 'Imagenes insuficientes',
                          description: 'No puede haber menos de 3 imagenes',
                          status: 'error',
                          duration: 3000,
                          position: 'top',
                        });
                      return Swal.fire({
                        title: '¿Estas seguro?',
                        // text: 'No podrás revertir esto',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#5800F7',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Si, eliminar!',
                      }).then(async (result) => {
                        if (result.isConfirmed) {
                          try {
                            const r = await axios.patch(
                              `${URL_API}/associate/update/deliveredImages/${props._id}`,
                              {
                                deleteableImgs,
                                addImgs,
                                isEditable: true,
                                vehicleId: props.vehicle?._id || props.vehicleId,
                              },
                              headersConfig
                            );
                            router.refresh();
                            setIsEdit(false);
                            setDeleteableImgs([]);
                            setAddImgs({} as FileList);
                            toast({
                              title: r.data.message || 'Imagenes de entrega actualizadas',
                              status: 'success',
                              duration: 3000,
                              position: 'top',
                            });
                            setDeliveredImages(
                              r.data.deliveredImages as { url: string; originalName: string; docId: string }[]
                            );
                          } catch (error) {
                            console.log(error);
                            toast({
                              title: 'Error al eliminar/agregar imagenes',
                              status: 'error',
                              duration: 3000,
                              position: 'top',
                            });
                          }
                        }
                      });

                      // const result = axios.patch(
                      //   `${URL_API}/associate/update/deliveredImages/${props._id}`,
                      //   {
                      //     deleteableImgs,
                      //   },
                      //   headersConfig
                      // );
                      // return result;
                    }}
                  >
                    Confirmar
                  </button>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <CiEdit
                    className="cursor-pointer"
                    size={30}
                    strokeWidth="1px"
                    onClick={() => {
                      setIsEdit(!isEdit);
                    }}
                  />
                  <p>Editar</p>
                </div>
              )}
            </div>
            {props.deliveredImages.length > 0 && (
              <div className="flex flex-wrap gap-3">
                {deliveredImages.map((image, i) => (
                  <div key={i} className="relative">
                    {isEdit && (
                      <>
                        <div
                          className="
                          absolute top-[-4px] right-[-10px] z-[3]
                          cursor-pointer bg-textGray2
                          rounded text-red-500
                        "
                          onClick={() => {
                            if (deliveredImages.length + Array.from(addImgs).length > 3) {
                              const find = deliveredImages.find((img) => img.docId === image.docId);
                              const filter = deliveredImages.filter((img) => img.docId !== image.docId);
                              setDeliveredImages(filter);
                              if (find) {
                                setDeleteableImgs([...deleteableImgs, find.docId]);
                              }
                            } else {
                              toast({
                                title: 'Imagenes insuficientes',
                                description: 'No puede haber menos de 3 imagenes',
                                status: 'error',
                                duration: 3000,
                                position: 'top',
                              });
                            }
                          }}
                        >
                          <MdDelete size={22} />
                        </div>
                      </>
                    )}

                    <ZoomImage imageUrl={image.url} name={image.originalName} key={i} />
                  </div>
                ))}
                {Array.from(addImgs).map((file, index) => (
                  <div className="relative " key={index}>
                    <div
                      className="
                          absolute top-[-4px] right-[-10px] z-[3]
                          cursor-pointer bg-textGray2
                          rounded text-red-500
                        "
                      onClick={() => {
                        const find = addImgs[index];
                        const filter = Array.from(addImgs).filter((img) => img !== find);
                        const combinedFileList = new DataTransfer();
                        filter.forEach((f) => {
                          combinedFileList.items.add(f);
                        });
                        setAddImgs(combinedFileList.files);
                        // setAddImgs();
                      }}
                    >
                      <MdDelete size={22} />
                    </div>
                    {/* <img src={URL.createObjectURL(file)} alt={`Imagen ${index + 1}`} /> */}
                    <ZoomImage imageUrl={URL.createObjectURL(file)} name={file.name} key={index} />
                  </div>
                ))}
                {isEdit && (
                  <div className="ml-2">
                    <AddFiles
                      accept="all-images"
                      multiple
                      currentImages={addImgs}
                      onChange={(fls) => {
                        setAddImgs(fls);
                      }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </section>
      )}
      {(!props.deliveredImages || props.deliveredImages.length < 1) && (
        <section id="deliveredImages" className=" mt-[30px]">
          <div className="flex flex-col gap-3">
            <p>Fotos de entrega</p>
            <CustomModal
              header="Agregar fotos de entrega"
              openButtonText="Agregar fotos de entrega"
              confirmButtonText="Enviar"
              onCloseModal={() => { }}
              isPrimaryButton
              // reloadWindow={false}
              validatorSchema={deliveredImagesValidatorSchema}
              initialValues={{ deliveredImages: '' }}
              onSubmit={async (values) => {
                const result = await axios.patch(
                  `${URL_API}/associate/update/deliveredImages/${props._id}`,
                  values,
                  headersConfig
                );
                return result;
              }}
              body={<Body />}
              canEdit={canEditDriverProfile}
            />
          </div>
        </section>
      )}

      {
        (props.vehicle && !props.isDriverBox) && <AdendumWaiting vehicle={props.vehicle} />
      }

      {props.adendumDocs && props.adendumDocs.length > 0 && (
        <section id="adendumDocs" className=" mt-[30px]">
          <div className="flex flex-col gap-3">
            <p>Adendums firmados: </p>
            {props.adendumDocs.map((doc, i) => {
              return (
                <DocumentDisplay
                  key={i}
                  url={doc.url}
                  docName={getLengthName(doc.originalName)}
                  backgroundColor="rgba(88, 0, 247, 0.2)"
                  textColor="#5800F7"
                />
              );
            })}
          </div>
        </section>
      )}
    </div>
  );
}

interface ImagesType {
  url: string;
  name: string;
}

const Body = () => {
  const [files, setFiles] = useState<FileList>({} as FileList);
  const [imgs, setImgs] = useState<ImagesType[]>([]);
  const { setFieldValue } = useFormikContext();

  const onChange = (fileList: FileList | null) => {
    if (fileList) {
      setFiles((prevFileList) => {
        if (prevFileList) {
          // Convierte los FileList a arrays
          // const prevFilesArray = Array.from(prevFileList);
          const newFilesArray = Array.from(fileList);

          // Combina los dos arrays
          const combinedFilesArray = [...newFilesArray];

          // Crea un nuevo FileList a partir del array combinado
          const combinedFileList = new DataTransfer();
          combinedFilesArray.forEach((file) => {
            combinedFileList.items.add(file);
          });
          return combinedFileList.files;
        } else {
          // Si prevFileList es null, simplemente devuelve newFileList
          return fileList;
        }
      });

      // Convierte las imagenes a url blob para preview
      const fls2 = Object.values(fileList);
      // const newFiles =
      const filterNews = fls2.filter((item1) => !imgs.some((item2) => item1.name === item2.name));
      // console.log('FILTER NEWS', filterNews);
      const imgs2 = filterNews?.map((file) => {
        return {
          url: URL.createObjectURL(file),
          name: file.name,
        };
      });
      setImgs([]);
      setImgs([...imgs, ...imgs2]);
    }
  };

  const removeImg = (url: string) => {
    const imgIndex = imgs.findIndex((img) => img.url === url);

    if (imgIndex !== -1) {
      const newImgs = [...imgs];
      newImgs.splice(imgIndex, 1);
      setImgs(newImgs);

      // Elimina el archivo correspondiente del FileList
      setFiles((prevFileList) => {
        if (prevFileList) {
          const newFileList = new DataTransfer();
          newImgs.forEach((img) => {
            // Agrega los archivos al nuevo FileList
            newFileList.items.add(new File([new Blob([img.name])], img.name));
          });
          setFieldValue('deliveredImages', newFileList.files);
          return newFileList.files;
        } else {
          setFieldValue('deliveredImages', prevFileList);
          return prevFileList;
        }
      });
    }
  };

  return (
    <div className="flex flex-col ">
      {imgs.length < 1 ? (
        <InputFile
          name="deliveredImages"
          label="Fotos de entrega (5 imagenes minimo)"
          nameFile=""
          multiple
          accept="all-images"
          buttonText="Subir"
          placeholder="Archivos no mayores a 3mb"
          onChange={(fls) => {
            onChange(fls);
          }}
        />
      ) : (
        <div className={` flex flex-col`}>
          <label htmlFor="deliveredImages">Fotos de entrega (5 imagenes minimo)</label>
          <div
            className={`
              flex gap-3 items-center pt-[10px]
              ${imgs.length >= 5 && 'pb-[10px] overflow-y-hidden overflow-x-scroll custom-scroll'}
            `}
            style={{ width: '100%' }}
          >
            {imgs.map((img, index) => {
              return (
                <div className="flex min-w-[80px] relative  " key={index}>
                  <div
                    className="absolute z-[5 bg-gray-300 rounded-full top-[-10px] right-[-10px] cursor-pointer "
                    onClick={() => removeImg(img.url)}
                  >
                    <RiCloseLine size={22} width="2px" className="text-red-600" />
                  </div>
                  <Image
                    width="1000"
                    height="1000"
                    src={img.url}
                    alt={img.name}
                    // key={index}
                    className="w-[80px] h-[80px] object-cover "
                  // style={{ width: '80px !important', height: '80px !important', objectFit: 'cover' }}
                  />
                </div>
              );
            })}
            <AddMoreFiles
              name="deliveredImages"
              accept="all-images"
              multiple
              currentImages={files}
              onChange={(fls) => onChange(fls)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

type SignDocsKeys = keyof VehicleResponse['drivers'][number]['signDocs']
// type xd = keyof VehicleResponse['drivers'][number]['documents']['driverLicense']['driverLicenseFront']
interface DocsAndModalProps {
  driver: VehicleResponse['drivers'][number];
  carNumber: string;
  label: string;
  property: SignDocsKeys;
  accept?: 'pdf' | 'all-images';
  canEdit: boolean;
}

function UnsfinishedDocAndModal({ driver, carNumber, label, property, accept = 'pdf', canEdit }: DocsAndModalProps) {
  const [nameFile, setNameFile] = useState(driver.signDocs[property]?.originalName || '');

  const { user } = useCurrentUser();

  const headers = useMemo(
    () => ({
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'multipart/form-data',
      },
    }),
    [user.accessToken]
  );
  const onSubmit = async (values: any) => {
    const obj = {
      ...values,
      carNumber,
    };
    const result = await axios.patch(`${URL_API}/associate/update/signedDocs/${driver._id}`, obj, headers);
    return result;
  };
  return (
    <div className="flex flex-col md:flex-row md:items-end justify-between w-full md:gap-3 2xl:gap-0 flex-wrap">
      <p>{label}</p>
      {driver.signDocs[property] ? (
        <div className="flex gap-2 ">
          <div className="w-[max-content] overflow-hidden">
            <DocumentDisplay
              url={driver.signDocs[property].url}
              docName={getLengthName(driver.signDocs[property].originalName)}
              backgroundColor="rgba(88, 0, 247, 0.2)"
              textColor="#5800F7"
            />
          </div>
          <CustomModal
            header={`Añadir ${label}`}
            initialValues={{ [property]: '' }}
            openButtonText={`Subir ${label}`}
            confirmButtonText="Enviar"
            isUpdate
            updateIconColor="#5800F7"
            // validatorSchema={addressVerificationValidatorSchema}
            onSubmit={onSubmit}
            body={
              <>
                <div className="flex flex-col">
                  <InputFile
                    name={property}
                    label={label}
                    nameFile={nameFile}
                    handleSingleSetName={setNameFile}
                    // handleSetName={handleSetNames}
                    accept={accept}
                    placeholder="Archivo no mayor a 3mb"
                    buttonText="Subir archivo"
                  />
                </div>
              </>
            }
            isPrimaryButton
            onCloseModal={() => { }}
            canEdit={canEdit}
          />
        </div>
      ) : (
        <CustomModal
          header={`Añadir ${label}`}
          initialValues={{ [property]: '' }}
          openButtonText={`Subir ${label}`}
          confirmButtonText="Enviar"
          // validatorSchema={addressVerificationValidatorSchema}
          onSubmit={onSubmit}
          body={
            <>
              <div className="flex flex-col">
                <InputFile
                  name={property}
                  label={label}
                  nameFile={nameFile}
                  handleSingleSetName={setNameFile}
                  // handleSetName={handleSetNames}
                  accept={accept}
                  placeholder="Archivo no mayor a 3mb"
                  buttonText="Subir archivo"
                />
              </div>
            </>
          }
          isPrimaryButton
          onCloseModal={() => { }}
          canEdit={canEdit}
        />
      )}
    </div>
  );
}

type DocsKeys = keyof VehicleResponse['drivers'][number]['documents'];

type OmittedProps = 'ine' | 'driverLicense'; // Reemplaza con las propiedades que deseas omitir

type DocsKeysWithoutOmitted = Exclude<DocsKeys, OmittedProps>;

interface FinishedDocAndModalProps {
  nameFile: string;
  // property: Property2;
  handleSetNames: (name: string, value: string) => void;
  validatorSchema: Yup.ObjectSchema<any>;
  onClose: () => void;
  pText: string;
  driver: VehicleResponse['drivers'][number];
  carNumber: string;
  label: string;
  property: DocsKeysWithoutOmitted;
  // property2?: DocsKeysWithIncluded;
  // subProp?: SubPropType;
  accept?: 'pdf' | 'all-images';
  section?: 'associate-docs' | 'contract-docs';
  canEdit: boolean;
}

function FinishedDocAndModal({
  driver,
  carNumber,
  label,
  pText,
  property,
  accept = 'pdf',
  onClose,
  handleSetNames,
  nameFile,
  canEdit,
  // section = 'contract-docs',
}: FinishedDocAndModalProps) {
  const { user } = useCurrentUser();

  const headers = useMemo(
    () => ({
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'multipart/form-data',
      },
    }),
    [user.accessToken]
  );

  const onSubmit = async (values: any) => {
    const obj = { ...values, carNumber, isEdit: true };

    const result = await axios.patch(`${URL_API}/associate/update/${property}/${driver._id}`, obj, headers);
    return result;

    // if()

  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-end justify-between w-full gap-2 sm:gap-0 md:gap-3 2xl:gap-0 ">
      <p>{pText}</p>
      {driver.documents[property] ? (
        <div className="flex gap-2">
          <div className="w-[max-content] overflow-hidden">
            <DocumentDisplay
              url={driver.documents[property].url}
              docName={getLengthName(driver.documents[property].originalName)}
              backgroundColor="rgba(88, 0, 247, 0.2)"
              textColor="#5800F7"
            />
          </div>
          <CustomModal
            header={`Añadir ${label}`}
            initialValues={{ [property]: '' }}
            openButtonText={`Subir ${label}`}
            confirmButtonText="Enviar"
            isUpdate
            updateIconColor="#5800F7"
            onSubmit={onSubmit}
            body={
              <>
                <div className="flex flex-col">
                  <InputFile
                    name={property}
                    label={label}
                    nameFile={driver.documents[property].originalName || nameFile}
                    handleSetName={handleSetNames}
                    accept={accept}
                    placeholder="Archivo no mayor a 3mb"
                    buttonText="Subir archivo"
                  />
                </div>
              </>
            }
            isPrimaryButton
            onCloseModal={() => { }}
            canEdit={canEdit}
          />
        </div>
      ) : (
        <CustomModal
          header={`Añadir ${label}`}
          initialValues={{ [property]: '' }}
          openButtonText={`Subir ${label}`}
          confirmButtonText="Enviar"
          // validatorSchema={addressVerificationValidatorSchema}
          onSubmit={async (values) => {
            const obj = { ...values, carNumber };

            const result = await axios.patch(
              `${URL_API}/associate/update/${property}/${driver._id}`,
              obj,
              headers
            );
            return result;
          }}
          body={
            <>
              <div className="flex flex-col">
                <InputFile
                  name={property}
                  label={label}
                  nameFile={nameFile}
                  handleSetName={handleSetNames}
                  accept={accept}
                  placeholder="Archivo no mayor a 3mb"
                  buttonText="Subir archivo"
                />
              </div>
            </>
          }
          isPrimaryButton
          onCloseModal={onClose}
          canEdit={canEdit}
        />
      )}
    </div>
  );
}

function EditINEBodyModal({ ineFront, ineBack }: any) {
  const [nameFiles, setNameFiles] = useState({
    ineFront: ineFront?.originalName || '',
    ineBack: ineBack?.originalName || '',
  })

  function handleSetNames(name: string, value: string) {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  }

  return (
    <div className="flex flex-col gap-3">
      <InputFile
        name="ineFront"
        label="INE Frente"
        // nameFile={nameFiles.ineFront}
        // nameFile={props.documents.ine?.ineFront?.originalName || nameFiles.ineFront}
        // handleSetName={handleSetNames}
        nameFile={nameFiles.ineFront}
        handleSetName={handleSetNames}
        accept="all-images"
        placeholder="Archivo no mayor a 3mb"
        buttonText="Subir archivo"
      />
      <InputFile
        name="ineBack"
        label="INE Reverso"
        // nameFile={nameFiles.ineBack}
        // nameFile={props.documents.ine?.ineBack?.originalName || nameFiles.ineBack}
        nameFile={nameFiles.ineBack}
        handleSetName={handleSetNames}
        accept="all-images"
        placeholder="Archivo no mayor a 3mb"
        buttonText="Subir archivo"
      />
    </div>
  )
}

function EditDriverLicenseBodyModal({ driverLicenseFront, driverLicenseBack }: any) {
  const [nameFiles, setNameFiles] = useState({
    driverLicenseFront: driverLicenseFront?.originalName || '',
    driverLicenseBack: driverLicenseBack?.originalName || '',
  })

  function handleSetNames(name: string, value: string) {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  }

  return (
    <div className="flex flex-col gap-3">
      <InputFile
        name="driverLicenseFront"
        label="Licencia Frente"
        nameFile={nameFiles.driverLicenseFront}
        handleSetName={handleSetNames}
        accept="all-images"
        placeholder="Archivo no mayor a 3mb"
        buttonText="Subir archivo"
      />
      <InputFile
        name="driverLicenseBack"
        label="Licencia Reverso"
        nameFile={nameFiles.driverLicenseBack}
        handleSetName={handleSetNames}
        accept="all-images"
        placeholder="Archivo no mayor a 3mb"
        buttonText="Subir archivo"
      />
    </div>
  )
}


type AdendumDigitalSignature = {
  participants: Participant[];
  documentID: string;
  url: string;
  signed: boolean;
  isSent: boolean;
};

function AdendumWaiting({ vehicle }: { vehicle: VehicleResponse }) {
  const [data, setData] = useState<AdendumDigitalSignature[]>([]);
  // const [loading, setLoading] = useState(true);
  const { user } = useCurrentUser();
  const toast = useToast();
  console.log('vehicle', vehicle);
  const lastDriver = vehicle.drivers[vehicle.drivers.length - 1];

  useEffect(() => {
    const getAdendumData = async () => {
      try {
        const response = await axios.get(`${URL_API}/associate/adendumDigitalSignature/${lastDriver._id}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        console.log('adendumDigitalSignature response ', response.data);
        setData(response.data.data);
      } catch (error: any) {
        toast({
          title: error.response.data.message,
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      } finally {
        // setLoading(false); //
      }
    };

    getAdendumData();
  }, [user, lastDriver._id, toast]);

  return (
    <>
      {
        data.length > 0 && (
          <>
            {
              data[data.length - 1].isSent && !data[data.length - 1].signed &&
              <section className='flex flex-col gap-3 mt-5'>
                  <p>Adendum en espera de firma</p>
                  <div className="flex gap-5 flex-wrap items-center">

                    <PopoverForSignatures
                      participants={data[data.length - 1].participants}
                      signed={data[data.length - 1].signed}
                  />
                    <ShowRetryButton
                      adendumDigitalSignatures={data[data.length - 1]}
                      associateId={vehicle.drivers[vehicle.drivers.length - 1]?._id}
                    />

                  </div>
              </section>
            }

          </>
        )
      }
    </>
  )
}

function ShowRetryButton({
  adendumDigitalSignatures,
  associateId,
}
  :
  { adendumDigitalSignatures: AdendumDigitalSignature, associateId: string }) {

  // check if all participants have signed the adendum to retry process the completed adendum
  const [loading, setLoading] = useState(false);
  const allParticipantsSigned = adendumDigitalSignatures.
    participants.every((participant) => participant.signed);
  const toast = useToast();
  const { user } = useCurrentUser();
  console.log('associateId', associateId);
  return (
    <>
      {
        allParticipantsSigned && (
          <div className=''>
            <PrimaryButton
              disabled={loading}
              onClick={async () => {
                console.log('retry');
                try {
                  setLoading(true);
                  const { data } = await axios.post(`${URL_API}/webhooks/weetrust/manual-retry/adendum/completedDocument`, {
                    documentID: adendumDigitalSignatures.documentID,
                    associateId,
                  }, {
                    headers: {
                      Authorization: `Bearer ${user.accessToken}`,
                    },
                  });
                  console.log('data', data);

                  toast({
                    title: 'Proceso de completar adendum iniciado',
                    status: 'success',
                    position: 'top',
                    duration: 3000,
                    isClosable: true,
                  })
                  setTimeout(() => {
                    window.location.reload();
                  }, 3000);
                  setLoading(false);
                } catch (error) {
                  console.log('error', error);
                  toast({
                    title: 'Error al reintentar completar adendum',
                    status: 'error',
                    position: 'top',
                    duration: 3000,
                    isClosable: true,
                  })
                }
              }}
            >
              {loading ? <>Cargando... (puede tardar un poco) <LoaderCircle className='ml-2 animate-spin' /></> : 'Reintentar completar'}
            </PrimaryButton>
            <span className='text-xs mt-1'>
              (Esto simplemente realizará el proceso de completar el adendum, no enviará el adendum de nuevo)
            </span>
          </div>
        )
      }
    </>
  )
}
