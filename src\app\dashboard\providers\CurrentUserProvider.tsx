'use client';
import React, { createContext, useContext, useEffect, useState } from 'react';
import { UserResponse } from '@/actions/getUserById';
import { apiVendorPlatform } from '@/services/appointmentService';

interface ContextProps {
  setUser: React.Dispatch<React.SetStateAction<UserResponse>>;
  user: UserResponse;
  isSuperAdminOrAdmin: boolean;
}

const fakeUser = {} as UserResponse;
const setFakeUser = () => {};

const CurrentUserContext = createContext<ContextProps>({
  user: fakeUser,
  setUser: setFakeUser,
  isSuperAdminOrAdmin: true,
});

interface ProviderProps {
  children: React.ReactNode;
  currentUser: UserResponse;
}

export const useCurrentUser = () => {
  const context = useContext(CurrentUserContext);

  if (!context) throw new Error('There is not current user context');

  return context;
};

export const CurrentUserProvider = ({ children, currentUser }: ProviderProps) => {
  const [user, setUser] = useState(currentUser);
  const isSuperAdminOrAdmin = currentUser.role === 'administrador' || currentUser.role === 'superadmin';

  // const user = useCurrentUser().user; // Obtiene el usuario actual y su token
  useEffect(() => {
    apiVendorPlatform.interceptors.request.use(
      (config) => {
        if (user.accessToken) {
          config.headers.Authorization = `Bearer ${user.accessToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }, [user.accessToken]);

  return (
    <CurrentUserContext.Provider value={{ user, setUser, isSuperAdminOrAdmin }}>
      {children}
    </CurrentUserContext.Provider>
  );
};

export default CurrentUserProvider;
