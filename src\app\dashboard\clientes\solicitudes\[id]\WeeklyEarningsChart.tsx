'use client';

import { formatDate } from '@/utils/dates';
import { Stack, Text } from '@chakra-ui/react';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';

interface WeeklyEarning {
  totalAmount: number;
  week: number;
  fromDate: string;
  toDate: string;
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(value);
};

const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div
        className="custom-tooltip"
        style={{
          backgroundColor: '#000',
          padding: '10px',
          border: '1px solid #000',
          borderRadius: '10px',
          color: '#fff',
          fontSize: '13px',
        }}
      >
        <p>Semana: {data.Semana}</p>
        <p>Del: {data.De}</p>
        <p>Al: {data.Al}</p>
        <p>Ganancias: {formatCurrency(data.Ganancias)}</p>
      </div>
    );
  }

  return null;
};

export default function WeeklyEarningsChart({ weeklyEarnings }: { weeklyEarnings: WeeklyEarning[] }) {
  const transformedData = weeklyEarnings
    .map((earning, index) => ({
      Ganancias: earning.totalAmount,
      Semana: earning.week,
      De: formatDate(earning.fromDate),
      Al: formatDate(earning.toDate),
      Index: index + 1,
    }))
    .sort((a, b) => new Date(b.De).getTime() - new Date(a.De).getTime());

  return (
    <Stack gap={4} width="100%">
      <Text fontSize="md" fontWeight="semibold">
        Ingresos (Últimas 12 semanas)
      </Text>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={transformedData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid vertical={false} stroke="#F0F0F0" />
          <XAxis axisLine={false} tickLine={false} dataKey="Index" tick={{ fontSize: 13 }} />
          <YAxis axisLine={false} tickLine={false} tickFormatter={formatCurrency} tick={{ fontSize: 13 }} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="Ganancias" barSize={30} fill="#7F56D9" radius={[8, 8, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </Stack>
  );
}
