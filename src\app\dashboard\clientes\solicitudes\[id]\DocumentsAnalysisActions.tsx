'use client';
import { URL_API } from '@/constants';
import { Flex, Button, useToast } from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useTransition, useEffect } from 'react';
import { MyUser } from '@/actions/getCurrentUser';
import Swal from 'sweetalert2';
import { AdmissionRequestDocumentType, RequestDocumentStatus } from '../enums';
import { QueryLink } from '@/components/QueryLink';
import { useCountry } from './detail';

interface Document {
  id: string;
  status: RequestDocumentStatus;
  type: AdmissionRequestDocumentType;
}

export interface DocumentsAnalysis {
  documents: Document[];
}

export default function DocumentsAnalysisActions({ requestId }: { requestId: string }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTransitioning, startTransition] = useTransition();
  const [isType, setIsType] = useState(null);
  const toast = useToast();
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const { isCountryUSA } = useCountry();

  useEffect(() => {
    if (user?.accessToken) {
      const fetchType = async () => {
        const response = await fetch(`${URL_API}/onboarding/get-type/${requestId}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        const type = await response.json();
        setIsType(type.message);
      };
      fetchType();
    }
  }, [user]);

  async function handleApprove() {
    setIsSubmitting(true);
    await fetch(`${URL_API}/admission/requests/${requestId}/documents-analysis/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
    })
      .then(async (res) => {
        const data = await res.json();
        if (res.ok) {
          startTransition(() => {
            router.replace(`${pathname}`, { scroll: false });
            router.refresh();
            toast({
              title: isCountryUSA
                ? 'Documents analysis approved'
                : 'Análisis de riesgo documentos preaprobados',
              status: 'success',
            });
          });
        } else {
          throw new Error(data.message || 'Error running risk analysis');
        }
      })
      .catch((err) => {
        console.error(err);
        // Enhanced error handling for rideshare and financial assessment models:
        toast({
          title: isCountryUSA ? 'Error running risk analysis' : 'Error al ejecutar el análisis de riesgo',
          description: err.message,
          status: 'error',
          duration: 4000,
          isClosable: true,
        });
        startTransition(() => {
          router.replace(`${pathname}`, { scroll: false });
          router.refresh();
        });
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  }

  function handleReject() {
    Swal.fire({
      animation: false,
      title: '¿Estás seguro de rechazar en análisis de documentos?',
      text: 'Esta acción rechazará por completo la solicitud',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, rechazar',
      cancelButtonText: 'Cancelar',
      preConfirm: async () => {
        try {
          await fetch(`${URL_API}/admission/requests/${requestId}/documents-analysis/reject`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
        } catch (error) {
          Swal.showValidationMessage(`Request failed: ${error}`);
        }
      },
    }).then((result) => {
      if (result.isConfirmed) {
        startTransition(() => {
          router.refresh();
          router.push(`${pathname}`, { scroll: false });
          toast({
            title: 'Análisis de documentos rechazado',
            status: 'success',
          });
        });
      }
    });
  }

  const DeclineText = isCountryUSA ? 'Decline' : 'Rechazar';
  const PreApproveAnalysis = isCountryUSA ? 'Pre-approve analysis' : 'Preaprobar análisis';
  const ApprovalTypeText = isCountryUSA ? 'Approval type' : 'Tipo de aprobación';

  return (
    <>
      <Flex gap={3}>
        <Button
          sx={{
            // bg: '#5800F7 !important',
            color: '#E14942',
            h: '40px',
            marginLeft: 2.5,
          }}
          className="bg-white cursor-pointer hover:bg-gray-50 border border-[#E14942]"
          type="submit"
          isLoading={isSubmitting || isTransitioning}
          onClick={handleReject}
        >
          {DeclineText}
        </Button>
        {isType === null && (
          <QueryLink query={{ dialog: 'type-of-approbation' }}>
            <Button className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100">
              {ApprovalTypeText}
            </Button>
          </QueryLink>
        )}

        {isType !== null && (
          <>
            <div className="mx-auto my-auto">
              <span className="text-[#5800F7] font-semibold text-sm">{isType}</span>
            </div>
            <Button
              sx={{
                // bg: '#5800F7 !important',
                color: 'white',
                h: '40px',
              }}
              className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
              type="submit"
              isLoading={isSubmitting || isTransitioning}
              onClick={handleApprove}
            >
              {PreApproveAnalysis}
            </Button>
          </>
        )}
      </Flex>
    </>
  );
}
