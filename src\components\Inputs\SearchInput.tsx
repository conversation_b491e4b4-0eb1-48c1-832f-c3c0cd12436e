'use client';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { AiOutlineClose, AiOutlineSearch } from 'react-icons/ai';

interface SearchInputProps {
  onChange: (term: string) => void;
  placeholder: string;
  param: string;
}

export default function SearchInput({ onChange, placeholder, param }: SearchInputProps) {
  const [isSearchActive, setIsSearchActive] = useState(false);

  const searchParams = useSearchParams();
  const [inputValue, setInputValue] = useState(searchParams.get(param)?.toString() || '');
  const router = useRouter();
  const pathname = usePathname();

  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    inputRef.current?.focus();
  }, [param, searchParams]);

  return (
    <div className="relative">
      <input
        id="search"
        className="px-10 h-[40px] border-[#9CA3AF] border-[1px] !outline-none rounded relative focus:ring-[#5800F7] focus:border-[#5800F7]"
        type="text"
        onChange={(event) => {
          setInputValue(event.target.value);
          onChange(event.target.value);
        }}
        ref={inputRef}
        value={inputValue}
        // defaultValue={searchParams.get(param)?.toString() || ''}
        placeholder={placeholder}
        onFocus={() => setIsSearchActive(true)}
        onBlur={() => setIsSearchActive(false)}
      />
      <div
        className={
          isSearchActive
            ? 'absolute top-0  text-[#5800F7] flex items-center h-full mr-2'
            : 'absolute top-0  text-[#9CA3AF] flex items-center h-full mr-2'
        }
      >
        <button type="submit" className="px-2">
          <AiOutlineSearch size={26} />
        </button>
      </div>
      <div
        className={`${
          isSearchActive ? 'text-[#5800F7]' : ''
        } absolute top-0 right-0   flex items-center h-full mr-2 cursor-pointer`}
        onClick={() => {
          const params = new URLSearchParams(searchParams);
          params.delete(param);
          setInputValue('');
          router.replace(`${pathname}?${params.toString()}`);
        }}
      >
        {/* <Link href="/dashboard/flotilla/stock"> */}
        <div>
          <AiOutlineClose size={24} />
        </div>
        {/* </Link> */}
      </div>
    </div>
  );
}
