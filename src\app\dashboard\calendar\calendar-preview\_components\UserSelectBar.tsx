import { getHomeVisitors } from '@/actions/getUsers';
import { UserSelect } from './UserSelect';
import { Suspense } from 'react';
import { canPerform } from '@/casl/canPerform';
import { getServerAbility } from '@/casl/getServerAbility';
import { Capabilities, Sections, Subsections } from '@/constants';

const engineeringTeamEmails = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const preSelectedHomeVisitorsHeads = [
  ...engineeringTeamEmails,
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const UserSelectBar = async () => {
  const ability = await getServerAbility();

  /* if (!preSelectedHomeVisitorsHeads.includes(user?.email!)) {
    return null;
  } */

  const homeVisitors = await getHomeVisitors();
  const canViewAll = canPerform(
    ability,
    Capabilities.ViewAllCalendar,
    Sections.Calendar,
    Subsections.CalendarPreview
  );
  return (
    <div>
      <Suspense fallback={<div>Cargando...</div>}>
        {canViewAll && <UserSelect data={homeVisitors} />}
      </Suspense>
    </div>
  );
};
