import { PropsWithChildren } from 'react';
import { PersonalDataDialog } from './PersonalDataDialog';
import getAdmissionRequest from '@/actions/getAdmissionRequest';
import { UploadDocumentDialog } from './UploadDocumentDialog';
import { ScorecardDetailDialog } from './ScorecardDetailDialog';
import { AddEarnings } from './AddEarnings';
import { AddApproval } from './AddApprobal';
import { ModelScoresDialog } from './ScorecardModelStatusDialog';

interface PageProps {
  params: {
    id: string;
  };
}

export default async function RequestsLayout(props: PropsWithChildren<PageProps>) {
  const { children, params } = props;
  const { id } = params;
  const { data } = await getAdmissionRequest({ id });
  // Access accounts data from `data.palenca.accounts`
  // if it exists, otherwise use an empty array
  const accounts = data.palenca.accounts || [];
  return (
    <>
      <div className="w-full">{children}</div>
      {/* Dialogs */}
      <PersonalDataDialog data={data.personalData} requestId={id} />
      <UploadDocumentDialog requestId={id} personalData={data.personalData} />
      <ScorecardDetailDialog riskAnalysis={data.riskAnalysis} />
      <ModelScoresDialog admissionRequest={data} />
      <AddEarnings requestId={id} accounts={accounts} admissionRequest={data} />
      <AddApproval requestId={id} accounts={accounts} />
    </>
  );
}
