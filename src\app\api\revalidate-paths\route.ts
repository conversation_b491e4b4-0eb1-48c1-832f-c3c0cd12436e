import { revalidatePath } from 'next/cache';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { url } = await req.json();

    if (!url) {
      return NextResponse.json({ message: 'URL is required' }, { status: 400 });
    }

    // Revalida la página específica
    // await res.revalidate(url);
    console.log('url', url);
    revalidatePath(url);

    return NextResponse.json({ message: 'Path revalidated successfully' }, { status: 200 });
  } catch (error: any) {
    console.log('error', error);
    return NextResponse.json({ message: 'Error revalidating path', error }, { status: 500 });
  }
}
