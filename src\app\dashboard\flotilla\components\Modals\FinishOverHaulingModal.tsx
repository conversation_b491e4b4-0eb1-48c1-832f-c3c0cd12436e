'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import { useOpenFinishOverHaulingModal } from '@/zustand/modalStates';
import InputDate from '@/components/Inputs/InputDate';
import InputMultipleFiles from '@/components/Inputs/InputMultipleFiles';
// import CustomInput from '@/components/Inputs/CustomInput';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { finishOverHaulingSchema } from '@/validatorSchemas/overhaulingSchema';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import InputFile from '@/components/Inputs/InputFile';
import { useState } from 'react';
import CustomInput from '@/components/Inputs/CustomInput';
import SelectInput from '@/components/Inputs/SelectInput';

export default function FinishOverHaulingModal() {
  const finishOverHauling = useOpenFinishOverHaulingModal();
  const updateSideData = useUpdateSideData();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';
  const [nameFile, setNameFile] = useState('');
  const [hasInvoice, setHasInvoice] = useState({ value: 'no', label: 'No' });

  const { user } = useCurrentUser();
  const { id } = useParams();

  const toast = useToast();
  const router = useRouter();

  return (
    <ModalContainer
      title="Finalizar a revisión"
      removeScrollBar
      onClose={() => {
        finishOverHauling.onClose();
      }}
    >
      <FormikContainer
        initialValues={{
          dateOut: '',
          outImgs: '',
          invoiceFile: '',
          invoiceAmount: '',
          hasInvoice: { value: 'no', label: 'No' },
        }}
        // validationSchema={{}}
        // validateOnMount
        confirmBtnText="Finalizar revisión"
        validatorSchema={finishOverHaulingSchema}
        onSubmit={async (values) => {
          // console.log('values', values);
          // (values as any).hasInvoice = values.hasInvoice.value === 'Si';

          const data = {
            ...values,
            hasInvoice: values.hasInvoice.value === 'Si',
            invoiceAmount: values.invoiceAmount ? parseInt(values.invoiceAmount) : undefined,
            invoiceFile: values.invoiceFile || undefined,
          };

          try {
            const resposne = await axios.patch(`${URL_API}/stock/finishOverHauling/${id}`, data, {
              headers: {
                Authorization: `Bearer ${user.accessToken}`,
                'Content-Type': 'multipart/form-data',
              },
            });
            console.log(resposne);
            finishOverHauling.onClose();

            toast({
              title: 'Se finalizó la revisión',
              // description: '',
              status: 'success',
              duration: 3000,
              position: 'top',
              isClosable: true,
            });
            await updateSideData(user);
            router.refresh();
            setTimeout(() => {
              if (window.location.pathname.includes('inactive')) {
                router.push(
                  `/dashboard/flotilla/inactive/stock/${id}${country ? `?country=${encodeURI(country)}` : ''}`
                );
              }
            }, 3000);
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Ocurrio un error al mandar a revisión',
              status: 'error',
              duration: 9000,
              position: 'top',
              isClosable: true,
            });
          }
        }}
      >
        <div className="flex flex-col gap-3">
          <InputDate name="dateOut" label="Fecha de salida de Revisión" />

          <InputMultipleFiles
            name="outImgs"
            label="Fotografias (minimo 5)"
            buttonText="Subir archivos"
            accept="all-images"
            placeholder="Arrastra o selecciona archivos"
          />

          <SelectInput
            label="¿Tiene factura?"
            name="hasInvoice"
            options={[
              { value: 'Si', label: 'Si' },
              { value: 'No', label: 'No' },
            ]}
            onChange={(option) => {
              setHasInvoice(option);
            }}
          />
          {hasInvoice.value === 'Si' && (
            <>
              <InputFile
                name="invoiceFile"
                label="Factura"
                nameFile={nameFile}
                handleSetName={setNameFile}
                accept="pdf"
                buttonText="Subir archivo"
                placeholder="No mayor a 3mb"
              />

              <CustomInput name="invoiceAmount" label="Monto de la factura" type="number" />
            </>
          )}

          {/* <CustomInput name="comments" label="Comentarios" type="textarea" /> */}
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
