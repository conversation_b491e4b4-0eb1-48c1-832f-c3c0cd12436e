'use client';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
// import Image from 'next/image';
import { allStatus, svgColors } from '@/constants';
import { Leaf } from 'lucide-react';

interface CardProps {
  model: string;
  brand: string;
  contract: string;
  newCar: boolean;
  status: string;
  step: {
    stepName: string;
    stepNumber: number;
  };
  color: string;
  extensionCarNumber: string | undefined;
  dischargedReason?: string;
  isBlocked: boolean;
  index?: number;
  total?: number;
  isElectric: boolean;
  vehicleStatus?: string;
  // vehiclePhoto: string;
}

export const colorAndTextOnStatus: {
  [key: string]: { color: string; text: string; borderColor: string; bgColor: string };
} = {
  'in-service': {
    color: 'text-yellow-500',
    text: 'En taller',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'awaiting-insurance': {
    color: 'text-[#115e59]',
    text: 'Seguro',
    bgColor: 'bg-[#115e59]',
    borderColor: 'border-[#115e59]',
  },
  'legal-process': {
    color: 'gray',
    text: 'Legal',
    borderColor: 'border-gray-500',
    bgColor: 'bg-gray-500',
  },
  insurance: {
    color: 'text-[#115e59]',
    text: 'Insurance',
    borderColor: 'border-[#115e59]',
    bgColor: 'bg-[#115e59]',
  },
  legal: {
    color: 'text-gray-600',
    text: 'Legal',
    borderColor: 'border-gray-600',
    bgColor: 'bg-gray-600',
  },
  workshop: {
    color: 'text-yellow-500',
    text: 'Workshop',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  sold: {
    color: 'text-green-600',
    text: 'Sold',
    borderColor: 'border-green-600',
    bgColor: 'bg-green-600',
  },
  delivered: {
    color: 'text-emerald-500',
    text: 'Delivered',
    borderColor: 'border-emerald-500',
    bgColor: 'bg-emerald-500',
  },
  utilitary: {
    color: 'text-amber-500',
    text: 'Utilitary',
    borderColor: 'border-amber-500',
    bgColor: 'bg-amber-500',
  },
  adendum: {
    color: 'text-purple-500',
    text: 'Adendum',
    borderColor: 'border-purple-500',
    bgColor: 'bg-purple-500',
  },
};

export const colorAndTextOnCategory: {
  [key: string]: { color: string; text: string; borderColor: string; bgColor: string };
} = {
  withdrawn: {
    color: 'text-red-500',
    text: 'Withdrawn',
    borderColor: 'border-red-500',
    bgColor: 'bg-red-500',
  },
  sold: {
    color: 'text-green-600',
    text: 'Sold',
    borderColor: 'border-green-600',
    bgColor: 'bg-green-600',
  },
  insurance: {
    color: 'text-[#115e59]',
    text: 'Insurance',
    borderColor: 'border-[#115e59]',
    bgColor: 'bg-[#115e59]',
  },
  collection: {
    color: 'text-indigo-500',
    text: 'Collection',
    borderColor: 'border-indigo-500',
    bgColor: 'bg-indigo-500',
  },
  legal: {
    color: 'text-gray-600',
    text: 'Legal',
    borderColor: 'border-gray-600',
    bgColor: 'bg-gray-600',
  },
  workshop: {
    color: 'text-yellow-500',
    text: 'Workshop',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  revision: {
    color: 'text-orange-500',
    text: 'Revision',
    borderColor: 'border-orange-500',
    bgColor: 'bg-orange-500',
  },
  adendum: {
    color: 'text-purple-500',
    text: 'Adendum',
    borderColor: 'border-purple-500',
    bgColor: 'bg-purple-500',
  },
  'in-preparation': {
    color: 'text-pink-500',
    text: 'In Preparation',
    borderColor: 'border-pink-500',
    bgColor: 'bg-pink-500',
  },
  stock: {
    color: 'text-teal-500',
    text: 'Stock',
    borderColor: 'border-teal-500',
    bgColor: 'bg-teal-500',
  },
  assigned: {
    color: 'text-cyan-500',
    text: 'Assigned',
    borderColor: 'border-cyan-500',
    bgColor: 'bg-cyan-500',
  },
  delivered: {
    color: 'text-emerald-500',
    text: 'Delivered',
    borderColor: 'border-emerald-500',
    bgColor: 'bg-emerald-500',
  },
  utilitary: {
    color: 'text-amber-500',
    text: 'Utilitary',
    borderColor: 'border-amber-500',
    bgColor: 'bg-amber-500',
  },
};

export const colorAndTextOnSubCategory: {
  [key: string]: { color: string; text: string; borderColor: string; bgColor: string };
} = {
  'damage-payment': {
    color: 'text-[#115e59]',
    text: 'Damage Payment',
    borderColor: 'border-[#115e59]',
    bgColor: 'bg-[#115e59]',
  },
  valuation: {
    color: 'text-[#115e59]',
    text: 'Valuation',
    borderColor: 'border-[#115e59]',
    bgColor: 'bg-[#115e59]',
  },
  repair: {
    color: 'text-[#115e59]',
    text: 'Repair',
    borderColor: 'border-[#115e59]',
    bgColor: 'bg-[#115e59]',
  },
  'payment-commitment': {
    color: 'text-teal-500',
    text: 'Payment Commitment',
    borderColor: 'border-teal-500',
    bgColor: 'bg-teal-500',
  },
  'payment-extension': {
    color: 'text-blue-400',
    text: 'Payment Extension',
    borderColor: 'border-blue-400',
    bgColor: 'bg-blue-400',
  },
  'non-payment': {
    color: 'text-red-700',
    text: 'Non Payment',
    borderColor: 'border-red-700',
    bgColor: 'bg-red-700',
  },
  'incomplete-payment': {
    color: 'text-orange-500',
    text: 'Incomplete Payment',
    borderColor: 'border-orange-500',
    bgColor: 'bg-orange-500',
  },
  'in-recovery': {
    color: 'text-green-600',
    text: 'In Recovery',
    borderColor: 'border-green-600',
    bgColor: 'bg-green-600',
  },
  demand: {
    color: 'text-gray-600',
    text: 'Demand',
    borderColor: 'border-gray-600',
    bgColor: 'bg-gray-600',
  },
  'public-ministry': {
    color: 'text-gray-600',
    text: 'Public Ministry',
    borderColor: 'border-gray-600',
    bgColor: 'bg-gray-600',
  },
  complaint: {
    color: 'text-gray-600',
    text: 'Complaint',
    borderColor: 'border-gray-600',
    bgColor: 'bg-gray-600',
  },
  impounded: {
    color: 'text-gray-600',
    text: 'Impounded',
    borderColor: 'border-gray-700',
    bgColor: 'bg-gray-700',
  },
  'aesthetic-repair': {
    color: 'text-yellow-500',
    text: 'Aesthetic Repair',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'duplicate-key-missing': {
    color: 'text-yellow-500',
    text: 'Duplicate Key Missing',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'mechanical-repair': {
    color: 'text-yellow-500',
    text: 'Mechanical Repair',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'electrical-repair': {
    color: 'text-yellow-500',
    text: 'Electrical Repair',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'engine-repair': {
    color: 'text-yellow-500',
    text: 'Engine Repair',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'waiting-for-parts': {
    color: 'text-yellow-500',
    text: 'Waiting For Parts',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  'corrective-maintenance': {
    color: 'text-yellow-500',
    text: 'Corrective Maintenance',
    borderColor: 'border-yellow-500',
    bgColor: 'bg-yellow-500',
  },
  management: {
    color: 'text-cyan-500',
    text: 'Management',
    borderColor: 'border-cyan-500',
    bgColor: 'bg-cyan-500',
  },
  gps: {
    color: 'text-emerald-500',
    text: 'GPS',
    borderColor: 'border-emerald-500',
    bgColor: 'bg-emerald-500',
  },
  'total-loss': {
    color: 'text-red-500',
    text: 'Total Loss',
    borderColor: 'border-red-500',
    bgColor: 'bg-red-800',
  },
  'operational-loss': {
    color: 'text-red-500',
    text: 'Operational Loss',
    borderColor: 'border-red-500',
    bgColor: 'bg-red-500',
  },
};

export const availableStatus = Object.keys(colorAndTextOnStatus);
const Card = ({
  model,
  brand,
  contract,
  newCar,
  step,
  color,
  extensionCarNumber,
  status,
  isBlocked,
  dischargedReason,
  index,
  total,
  vehicleStatus,
  ...rest
}: CardProps) => {
  const { user } = useCurrentUser();
  const role = user.role;

  return (
    <div
      className={`
        relative flex 
        items-center 
        py-2 px-4 
        bg-white border-2 rounded-md w-80 max-w-xm h-28 
        ${isBlocked ? 'bg-red-200 border-red-300' : 'border-validationLight'} 
        overflow-hidden
        ${total && index && index === total - 1 ? 'mb-4' : 'mb-0'}
      `}
      data-cy="stock-card"
    >
      <div className="flex  gap-x-2.5  w-full justify-aroundml-3.5 overflow-hidden">
        <div
          className={`flex items-center min-w-[100px] min-h-[88px] relative
          `}
        >
          {rest.isElectric && (
            <Leaf className="absolute text-green-700 -rotate-12 left-0 top-1 " strokeOpacity={2} />
          )}

          <VehicleSVGV2 color={svgColors[color?.toUpperCase()] || 'white'} />
        </div>
        <div className="flex flex-col justify-center gap-y-2.5 ml-2">
          <h3 className="text-sm font-semibold text-primaryPurple font-inter">
            #{contract}
            {extensionCarNumber ? ` - ${extensionCarNumber}` : ''}
          </h3>
          <p className="text-base font-semibold font-inter whitespace-nowrap">
            {brand} {model}
          </p>
          {status !== allStatus.discharged && status !== allStatus.overhauling && (
            <p
              className={`text-xs font-medium font-inter ${
                isBlocked ? 'text-red-600' : 'text-validationGreen'
              }`}
            >
              {isBlocked ? 'Bloqueado' : step?.stepName}
            </p>
          )}
          {status === 'discharged' && <p className="text-xs font-medium text-red-600">{dischargedReason}</p>}
          {status === allStatus.overhauling && (
            <p className={`text-xs font-medium font-inter text-validationGreen`}>Revisión</p>
          )}
        </div>
      </div>
      {status !== 'discharged' ? (
        newCar ? (
          <div className="absolute top-2.5 right-0 flex items-center justify-center border-y-2 border-l-2 border-primaryPurple rounded-l-lg w-12 h-1/4">
            <p className="text-xs font-normal text-primaryPurple font-inter">Nuevo</p>
          </div>
        ) : (
          <div className="absolute top-2.5 right-0 flex items-center justify-center border-y-2 border-l-2 border-otherAqua rounded-l-lg w-20 h-1/5">
            <p className="text-xs font-normal text-otherAqua font-inter">Semi-nuevo</p>
          </div>
        )
      ) : null}
      {role !== 'auditor' && availableStatus.includes(status) && vehicleStatus !== allStatus.active && (
        <div
          className={`absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2
                     ${colorAndTextOnStatus[status].borderColor} rounded-l-lg w-12 h-1/4`}
        >
          <p className={`text-xs font-normal ${colorAndTextOnStatus[status].color} font-inter mb-[2px]`}>
            {colorAndTextOnStatus[status].text}
          </p>
        </div>
      )}
      {/* {status === 'in-service' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-yellow-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-yellow-500 font-inter">En taller</p>
        </div>
      )}
      {status === 'legal-process' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-gray-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-gray-500 font-inter">Proceso legal</p>
        </div>
      )}
      {status === 'awaiting-insurance' && (
        <div className="absolute bottom-2.5 right-0 flex items-center justify-end border-y-2 border-l-2 border-blue-500 rounded-l-lg w-12 h-1/4">
          <p className="text-xs font-normal text-blue-500 font-inter">En taller</p>
        </div>
      )} */}
    </div>
  );
};

export default Card;
