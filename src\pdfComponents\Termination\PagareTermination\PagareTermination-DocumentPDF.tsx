/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable prettier/prettier */
/* eslint-disable max-len */
import { Text, View, StyleSheet, Page, Document, Image } from '@react-pdf/renderer';
import PagareGris from '@/assets/images/logoGris.png';
import HeaderImg from '@/assets/images/Header.png';
import { NumerosALetras } from 'numero-a-letras';
import moment from 'moment';
import 'moment/locale/es';
import { stateDependingCity } from '@/constants';
import { get20PercentOfTotalPrice } from '../utils/getTotalPrice';

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    alignContent: 'center',
    width: '100%',
    // paddingVertical: 60,
  },
  header: {
    width: '100vw',
  },
  body: {
    marginHorizontal: '10%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    top: '0',
    marginTop: '70px',
    opacity: 0.2,
    height: '500px',
    width: '100%',
  },
  anexoTitle: {
    textAlign: 'center',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    zIndex: 1,
  },

  anexoText: {
    color: 'black',
    // fontFamily: 'Helvetica-Bold',
    fontSize: 11,
    textAlign: 'justify',
    // lineHeight: '2px',
    // zIndex: 1,
  },

  viewMain: {
    flexDirection: "column",
    rowGap: 20,
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    rowGap: 10,
  },
  names: {
    fontSize: 10,
    textAlign: 'left',
    fontFamily: 'Helvetica-Bold',
    marginLeft: 20,
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 10,
  },
});

interface PagareProps {
  city: string;
  firstName: string;
  lastName: string;
  weeklyRent: number;
  totalPays: number;
  date: string;
  fullAddress: string;
}

export default function PagareTerminationDocumentPDF({
  firstName,
  lastName,
  weeklyRent,
  city,
  totalPays,
  ...rest
}: PagareProps) {
  // get the 20% of the weekly rent * total pays
  const totalPrice = get20PercentOfTotalPrice(weeklyRent, totalPays);
  const priceParsed = totalPrice.toLocaleString('es-MX', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  city = stateDependingCity[city] || city;

  const priceWrite = NumerosALetras(totalPrice);

  const day = moment(rest.date).format('DD [de] MMMM [de] YYYY');

  // const dateParse = moment(rest.date).format('DD-MM-YYYY');

  const firstNameCapitalized = firstName.charAt(0).toUpperCase() + firstName.slice(1);
  const lastNameCapitalized = lastName.charAt(0).toUpperCase() + lastName.slice(1);

  return (
    <Document>

      <Page style={styles.page} size="A4" wrap>
        <Image style={styles.header} src={HeaderImg.src} fixed />

        <View style={styles.body} >
          <Image src={PagareGris.src} style={styles.viewBackground} />
          <Text style={styles.anexoTitle}>
            PAGARÉ QUE SE SUSCRIBE EN TÉRMINOS DE LO DISPUESTO POR EL ARTÍCULO 170 Y DEMÁS RELATIVOS Y APLICABLES
            DE LA LEY GENERAL DE TÍTULOS Y OPERACIONES DE CRÉDITO.
          </Text>
          <View style={styles.viewMain}>
            <Text style={styles.anexoText}>Por: ${priceParsed || '538,200.00'} M.N.</Text>
            <Text style={styles.anexoText}>
              Lugar y fecha de suscripción: {city}, México, a {day}.
            </Text>
            <Text style={styles.anexoText}>
              Por valor pactado, el suscrito C. {firstName.toUpperCase()} {lastName.toUpperCase()} incondicionalmente promete pagar a la orden
              de E-MKT GOODS DE MÉXICO, S.A.P.I. de C.V., sus cesionarios o sucesores, la cantidad principal de $
              {priceParsed || '53,200.00'} M.N. ({priceWrite}) al momento en el que
              dicho título de crédito le sea puesto a la vista para su pago único; señalando como domicilio para
              efectuar el pago el ubicado en Prolongación Paseo de la Reforma 1015 PISO 5 INT 140, Santa Fe
              Cuajimalpa, Cuajimalpa
            </Text>
            <Text style={styles.anexoText}>
              <Text style={styles.interesMora}>Intereses Moratorios. </Text>
              En el evento en que el Suscriptor incumpla con el pago de la totalidad de la cantidad principal
              señalada en este pagaré, se causarán intereses moratorios a una tasa de 3% ( tres por ciento)
              mensual sobre la cantidad total señalada al inicio de este documento, durante todo el tiempo en que
              dure la mora y hasta el pago total de la misma. El pago de intereses moratorios se deberá efectuar
              adicionado con el IVA correspondiente.
            </Text>
            <Text style={styles.anexoText}>
              El Suscriptor señala como domicilio donde puede ser requerido
              de pago, el ubicado en {rest.fullAddress}.
            </Text>
            <Text style={styles.anexoText}>
              Este Pagaré que consta en 1 (una) página se suscribe y entrega en el estado de {city}, México, a {day}.
            </Text>
          </View>
          <View style={styles.containerS}>
            <View style={styles.content}>
              <Text style={styles.names}>Suscriptor de este pagaré</Text>
              <Text style={styles.dateAndWho}>______________________________</Text>
              <Text style={styles.dateAndWho}>Fecha: {day}</Text>
              <Text style={styles.dateAndWho}>
                Por: {firstNameCapitalized} {lastNameCapitalized}
              </Text>
            </View>
          </View>
        </View>
      </Page>

    </Document>

  );
}
