'use client';
import DocumentDisplay from '@/components/DocumentDisplay';
import { MdDelete, MdEmail, MdOutlineContentCopy } from 'react-icons/md';
import { BsFillTelephoneFill } from 'react-icons/bs';
import { RiCloseLine, RiWhatsappFill } from 'react-icons/ri';
import { FaStar } from 'react-icons/fa';
import { AiOutlineClose } from 'react-icons/ai';
import 'animate.css';
import { IconButton, Tab, TabList, TabPanel, TabPanels, Tabs, useToast } from '@chakra-ui/react';
import ZoomImage from '../others/ZoomImage';
import { ContactUS, VehicleResponse } from '@/actions/getVehicleData';
import CustomModal from '@/components/Modals/CustomModal';
import { format, parseISO } from 'date-fns';
import { Key, ReactNode, useState } from 'react';
import InputFile from '@/components/Inputs/InputFile';
import axios from 'axios';
import { URL_API, US_COUNTRY_CODE } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import {
  addressVerificationValidatorSchema,
  deliveredImagesValidatorSchema,
  driverLicenseBackValidatorSchema,
  driverLicenseFrontValidatorSchema,
} from '@/validatorSchemas/updateAssociateModals';
import * as Yup from 'yup';
import CustomInput from '@/components/Inputs/CustomInput';
import InputPhone from '@/components/Inputs/InputPhone';
import { useFormikContext } from 'formik';
import Image from 'next/image';
import AddMoreFiles from '@/components/Inputs/AddMoreFiles';
import { emergencyContactsSchemaUS, ssnRegExp } from '@/validatorSchemas/assignAssociateSchema';
import EditPicture from '@/components/Inputs/EditPicture';
import { CiEdit } from 'react-icons/ci';
import { IoCloseSharp } from 'react-icons/io5';
import AddFiles from '@/components/Inputs/AddFiles';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import ModalContainer from '../Modals/ModalContainer';
import FormikContainer from '@/components/Formik/FormikContainer';
import { useEditAssociateModal } from '@/app/dashboard/(dashboard)/stores/useRecordsModal';
import { ExtendedProps } from './AssociateModalData';
import { CalenderFormikIntegration } from '@/components/CustomCalender';
import { useDetail } from '../detailContext';

function getLengthName(name: string) {
  return name?.length > 25 ? name.slice(0, 30) + '...' : name.slice(0, 30);
}
interface SectionsAssociateModalProps {
  onClose: () => void;
  showSignDocs: boolean;
}

type IAssociateUS = VehicleResponse['drivers'][number];
type ISectionsAssociateModalUS = IAssociateUS & ExtendedProps & SectionsAssociateModalProps;

export function SectionsAssociateModalUS(props: ISectionsAssociateModalUS) {
  const toast = useToast();
  const handleCopy = (text: string | number) => {
    navigator.clipboard.writeText(text.toString());
    toast({
      title: 'Copied',
      status: 'success',
      duration: 3000,
      position: 'top',
    });
  };

  return (
    <div
      id="modal-container"
      className={`
                w-full
                md:w-[90%]
                min-h-[650px] 
                h-[max-content] 
                max-h-[90%]
                relative bg-white
                rounded-[10px] p-[30px]
                text-[#262D33] flex flex-col
                animate__animated animate__fadeInDown animate__faster duration-100
              `}
    >
      <SectionsAssociateModalUSHeader onClose={props.onClose} />
      <DriverPersonalInfo driverProfile={props} handleCopy={handleCopy} />
      <DriverContacts driverProfile={props} handleCopy={handleCopy} />
      <DriverDocuments driverProfile={props}>
        <DriverDocuments.SignDocsSection driverProfile={props} />
        <DriverDocuments.DeliveryImageDocsSection driverProfile={props} />
        <DriverDocuments.DeliveryImageDocsUploadSection driverProfile={props} />
        <DriverDocuments.AdendumDocsSection driverProfile={props} />
      </DriverDocuments>
    </div>
  );
}

interface ISectionsAssociateModalUSHeader {
  onClose: () => void;
}
const SectionsAssociateModalUSHeader = (props: ISectionsAssociateModalUSHeader) => {
  const { onClose } = props;
  return (
    <div className="flex justify-between w-full ">
      <p className="text-[18px] font-[600] ">{'Customer profile'}</p>
      <AiOutlineClose onClick={onClose} size={22} strokeWidth="2px" cursor="pointer" />
    </div>
  );
};

interface IDriverPersonalInfo {
  driverProfile: ISectionsAssociateModalUS;
  handleCopy: (text: string | number) => void;
}
const DriverPersonalInfo = (props: IDriverPersonalInfo) => {
  const { driverProfile, handleCopy } = props;
  const { user } = useCurrentUser();
  const associateModal = useEditAssociateModal();

  const address = driverProfile.address;
  const displayCompleteAddr = `${address.addressStreet.toUpperCase()}, 
  ${address.interior}, ${address.city?.toUpperCase()} ${address.state?.toUpperCase()}  ${address.postalCode}`;

  return (
    <section className="flex mt-[30px] gap-[20px] min-h-[120px] h-full flex-wrap">
      <div className="min-w-[120px] min-h-[120px] w-[120px] h-[120px] rounded relative">
        <EditPicture
          picture={driverProfile.picture}
          onSubmit={async (file) => {
            await axios.patch(
              `${URL_API}/associate/update/picture/${driverProfile._id}`,
              {
                picture: file,
                vehicleId: driverProfile.vehicle?._id || driverProfile.vehicleId,
              },
              {
                headers: {
                  Authorization: `Bearer ${user.accessToken}`,
                  'Content-Type': 'multipart/form-data',
                },
              }
            );
          }}
        />
      </div>
      <div id="info" className="flex flex-col h-full flex-wrap ">
        <div className="flex justify-between ">
          <div className="flex items-center gap-3">
            <p className="text-[#5800F7] text-[20px] font-[600] ">
              {driverProfile.firstName} {driverProfile.lastName}
            </p>
            <MdOutlineContentCopy
              onClick={() => handleCopy(`${driverProfile.firstName} ${driverProfile.lastName}`)}
              cursor="pointer"
            />
            <div className="flex gap-2 text-[#FFAB00] items-center">
              <FaStar /> <p>4.8</p>
            </div>

            <IconButton
              icon={<CiEdit size={30} strokeWidth="1px" color={'#5800F7'} />}
              p={0}
              bgColor="transparent"
              aria-label="ciedit"
              onClick={associateModal.onOpen}
            />

            {associateModal.isOpen && (
              <DriverPersonalInfo.DriverPersonalInfoEditModal driverProfile={driverProfile} />
            )}
          </div>
        </div>
        <div className="flex flex-wrap gap-x-[35px]">
          <div className="flex items-center gap-2">
            <p>
              <span className="font-bold">{'Birthdate: '}</span>
              {format(parseISO(driverProfile.birthDay), 'MM/dd/yyyy')}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <p>
              <span className="font-bold">{'SSN: '}</span> {driverProfile?.ssn}
            </p>
            <MdOutlineContentCopy
              className="size-[16px]"
              onClick={() => handleCopy(driverProfile?.ssn as string)}
              cursor="pointer"
            />
          </div>
        </div>
        <div>
          <p>
            <span className="font-bold">{'Address: '}</span>
            {displayCompleteAddr}
          </p>
        </div>
        <div className="flex gap-3 mt-[2px] text-white ">
          {driverProfile.platforms?.uber && (
            <div className="w-[80px] h-[27px] rounded bg-[#000] flex justify-center items-center ">
              <p>{'Uber'}</p>
            </div>
          )}
        </div>
        <div className="flex gap-3">
          <p>
            <span className="font-bold">{'Back Account Linked: '}</span>
          </p>
          <p>
            {driverProfile.userFromPaymentService?.stripeCustomer?.isCustomerBankAccountLinked ? '✅' : '❌'}
          </p>
        </div>
      </div>
    </section>
  );
};

interface IDriverContacts extends IDriverPersonalInfo {}
const DriverContacts = (props: IDriverContacts) => {
  const { driverProfile, handleCopy } = props;

  return (
    <section id="contact" className="mt-[20px] flex flex-col flex-wrap w-full">
      <p className="font-[600]">{'Contact methods'}</p>
      <div className="flex flex-wrap gap-x-[30px] gap-y-[15px] ">
        <div className="flex gap-2 items-center mt-[15px]">
          <div className="w-[40px] h-[40px] bg-[#CC6FF833] rounded-[9px] flex justify-center items-center ">
            <BsFillTelephoneFill color="#CC6FF8" size={22} />
          </div>
          <p className="text-[#CC6FF8]">{driverProfile.phone}</p>
          <MdOutlineContentCopy onClick={() => handleCopy(driverProfile.phone)} cursor="pointer" />
        </div>
        <div className="flex gap-2 items-center mt-[15px]">
          <div className="w-[40px] h-[40px] bg-[#25D36633] rounded-[9px] flex justify-center items-center ">
            <RiWhatsappFill color="#25D366" size={22} />
          </div>
          <p className="text-[#25D366]">
            {driverProfile.whatsApp ? driverProfile.whatsApp : driverProfile.phone}
          </p>
          <MdOutlineContentCopy
            onClick={() => handleCopy(driverProfile.whatsApp ? driverProfile.whatsApp : driverProfile.phone)}
            cursor="pointer"
          />
        </div>
        <div className="flex gap-2 items-center mt-[15px]">
          <div className="w-[40px] h-[40px] bg-[#5CAFFC33] rounded-[9px] flex justify-center items-center ">
            <MdEmail color="#5CAFFC" size={22} />
          </div>
          <p className="text-[#5CAFFC]">{driverProfile.email}</p>
          <MdOutlineContentCopy onClick={() => handleCopy(driverProfile.email)} cursor="pointer" />
        </div>
      </div>
    </section>
  );
};

interface IDriverDocuments extends Pick<IDriverPersonalInfo, 'driverProfile'> {
  children: ReactNode;
}
const DriverDocuments = (props: IDriverDocuments) => {
  const { driverProfile, children } = props;
  const { user } = useCurrentUser();
  const router = useRouter();
  const toast = useToast();
  const { handleIsAssocOpen } = useDetail();

  const handleSubmit = async (payload: any, url: string) => {
    try {
      const response = await axios.patch(url, payload, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      toast({
        title: response.data.message,
        description: 'Updating page...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      handleIsAssocOpen();
      router.refresh();
    } catch (error: any) {
      toast({
        title: error?.response?.data?.message || 'Error updating driver license',
        duration: 3000,
        status: 'error',
        position: 'top',
      });
    }
  };

  return (
    <>
      <section id="documents" className="mt-[30px] flex flex-col  text-[#464E5F] w-full">
        <p className="font-[600] mb-[15px]">{'Documents'}</p>
        <DriverDocuments.ProofOfAddressSection driverProfile={driverProfile} handleSubmit={handleSubmit}>
          <DriverDocuments.UnSignedContract driverProfile={driverProfile} />
        </DriverDocuments.ProofOfAddressSection>
        <DriverDocuments.DrivingLicenseSection driverProfile={driverProfile} handleSubmit={handleSubmit} />
      </section>
      {children}
    </>
  );
};

interface IProofOfAddressSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {
  handleSubmit: (payload: any, url: string) => Promise<void>;
  children: ReactNode;
}
const ProofOfAddressSection = (props: IProofOfAddressSection) => {
  const { driverProfile, handleSubmit, children } = props;
  const carNumber = driverProfile.carNumber;

  const [nameFiles, setNameFiles] = useState({
    addressVerification: driverProfile.documents.addressVerification?.originalName || '',
  });

  const handleSetNames = (name: string, value: string) => {
    setNameFiles({
      addressVerification: value,
    });
  };

  return (
    <div className="grid w-full lg:grid-cols-2 gap-4 ">
      <FinishedDocAndModal
        property="addressVerification"
        driver={driverProfile}
        nameFile={nameFiles.addressVerification}
        handleSetNames={handleSetNames}
        validatorSchema={addressVerificationValidatorSchema}
        carNumber={carNumber}
        label="Upload Proof of address"
        pText="Proof of address"
        accept="pdf"
        onClose={() => {
          setNameFiles({
            addressVerification: driverProfile.documents.addressVerification?.originalName || '',
          });
        }}
        handleSubmit={handleSubmit}
      />
      {children}
    </div>
  );
};

interface IUnSignedContract extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const UnSignedContract = (props: IUnSignedContract) => {
  const { driverProfile } = props;

  return (
    <div className="flex items-end justify-between w-full md:gap-3 2xl:gap-0">
      <p>{'Unsigned contract (GENERATED)'}</p>
      {driverProfile.unSignedContractDoc && (
        <div className="w-[max-content] overflow-hidden">
          <DocumentDisplay
            url={driverProfile.unSignedContractDoc.url}
            docName={getLengthName(driverProfile.unSignedContractDoc.originalName)}
            backgroundColor="rgba(88, 0, 247, 0.2)"
            textColor="#5800F7"
          />
        </div>
      )}
    </div>
  );
};

interface IDrivingLicenseSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {
  handleSubmit: (payload: any, url: string) => Promise<void>;
}
const DrivingLicenseSection = (props: IDrivingLicenseSection) => {
  const { driverProfile, handleSubmit } = props;
  const carNumber = driverProfile.carNumber;

  const [nameFiles, setNameFiles] = useState({
    driverLicenseFront: driverProfile.documents.driverLicense.driverLicenseFront?.originalName || '',
    driverLicenseBack: driverProfile.documents.driverLicense.driverLicenseBack?.originalName || '',
  });

  const handleSetNames = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  return (
    <div className="flex gap-[30px] flex-wrap mt-[20px]">
      <div className="flex flex-col gap-[15px] flex-wrap">
        <div className="flex items-center gap-3">
          <p>{'Driving license'}</p>
          {(driverProfile.documents.driverLicense?.driverLicenseFront ||
            driverProfile.documents.driverLicense?.driverLicenseBack) && (
            <CustomModal
              header="Edit Driver's License"
              isPrimaryButton
              updateIconColor="#5800F7"
              isUpdate
              initialValues={{
                driverLicenseFront: '',
                driverLicenseBack: '',
              }}
              openButtonText="Edit License"
              confirmButtonText="Send"
              onSubmit={async (values) => {
                const payload = { ...values, carNumber };
                const url = `${URL_API}/associate/update/driverLicense/${driverProfile._id}`;
                await handleSubmit(payload, url);
              }}
              body={
                <div className="flex flex-col gap-3">
                  <InputFile
                    name="driverLicenseFront"
                    label="License Front"
                    nameFile={nameFiles.driverLicenseFront}
                    handleSetName={handleSetNames}
                    accept="all-images"
                    placeholder="File no larger than 3mb"
                    buttonText="Upload file"
                  />
                  <InputFile
                    name="driverLicenseBack"
                    label="License Back"
                    nameFile={nameFiles.driverLicenseBack}
                    handleSetName={handleSetNames}
                    accept="all-images"
                    placeholder="File no larger than 3mb"
                    buttonText="Upload file"
                  />
                </div>
              }
              onCloseModal={() => {
                setNameFiles({
                  driverLicenseFront:
                    driverProfile.documents.driverLicense.driverLicenseFront?.originalName || '',
                  driverLicenseBack:
                    driverProfile.documents.driverLicense.driverLicenseBack?.originalName || '',
                });
              }}
            />
          )}
        </div>
        <div className="flex gap-3 flex-wrap items-center">
          <DriverDocuments.DrivingLicensePresenter
            modalHeaderText="Driver License Front"
            modalOpenButtonText="Upload Driver License Front"
            documentUrl={driverProfile.documents.driverLicense?.driverLicenseFront?.url}
            documentName={driverProfile.documents.driverLicense?.driverLicenseFront?.originalName}
            documentHandleSubmit={handleSubmit}
            documentFieldName={'driverLicenseFront'}
            documentFieldValue={nameFiles.driverLicenseFront}
            handleSetNames={handleSetNames}
            schemaValidator={driverLicenseFrontValidatorSchema}
            onCloseModal={() => {
              setNameFiles({
                ...nameFiles,
                driverLicenseFront: '',
              });
            }}
            initialValues={{ driverLicenseFront: '' }}
            driverProfile={driverProfile}
          />
          <DriverDocuments.DrivingLicensePresenter
            modalHeaderText="Driver License Back"
            modalOpenButtonText="Upload Driver License Back"
            documentUrl={driverProfile.documents.driverLicense?.driverLicenseBack?.url}
            documentName={driverProfile.documents.driverLicense?.driverLicenseBack?.originalName}
            documentHandleSubmit={handleSubmit}
            documentFieldName={'driverLicenseBack'}
            documentFieldValue={nameFiles.driverLicenseBack}
            handleSetNames={handleSetNames}
            schemaValidator={driverLicenseBackValidatorSchema}
            onCloseModal={() => {
              setNameFiles({
                ...nameFiles,
                driverLicenseBack: '',
              });
            }}
            initialValues={{ driverLicenseBack: '' }}
            driverProfile={driverProfile}
          />
        </div>
      </div>
    </div>
  );
};

interface ISignDocsSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const SignDocsSection = (props: ISignDocsSection) => {
  const { driverProfile } = props;
  const carNumber = driverProfile.carNumber;

  return (
    driverProfile.signDocs &&
    driverProfile.showSignDocs && (
      <section id="signedDocs" className="mt-[30px] flex flex-col text-[#464E5F] w-full">
        <p className="font-[600] mb-[15px]">{'Signed documents'}</p>
        <div className="grid w-full sm:grid-cols-2 gap-4 ">
          <UnsfinishedDocAndModal
            property="contract"
            carNumber={carNumber}
            label="Contract"
            driver={driverProfile}
            openButtonText={'Upload Contract'}
          />
          <UnsfinishedDocAndModal
            property="contactInfo"
            carNumber={carNumber}
            label="Contact Information"
            driver={driverProfile}
            openButtonText={'Upload Contact Info'}
          />
        </div>
      </section>
    )
  );
};

interface IDeliveryImageDocsSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const DeliveryImageDocsSection = (props: IDeliveryImageDocsSection) => {
  const { driverProfile } = props;

  const toast = useToast();
  const router = useRouter();
  const { user } = useCurrentUser();

  const [isEdit, setIsEdit] = useState(false);

  const [deliveredImages, setDeliveredImages] = useState(driverProfile.deliveredImages);
  const [deleteableImgs, setDeleteableImgs] = useState<string[]>([]);
  const [addImgs, setAddImgs] = useState({} as FileList);

  return (
    driverProfile.deliveredImages?.length > 0 && (
      <section id="deliveredImages" className=" mt-[30px]">
        <div className="flex flex-col gap-3 text-primaryPurple">
          <div className="flex items-center gap-3">
            <p className=" text-textGray2">{'Delivery Photos'}</p>
            {isEdit ? (
              <div className="flex items-center gap-3">
                <IoCloseSharp
                  className="cursor-pointer"
                  size={30}
                  strokeWidth="1px"
                  onClick={() => {
                    setIsEdit(!isEdit);
                    setDeliveredImages(driverProfile.deliveredImages);
                    setDeleteableImgs([]);
                  }}
                />
                <p
                  className="cursor-pointer"
                  onClick={() => {
                    setIsEdit(!isEdit);
                    setDeliveredImages(driverProfile.deliveredImages);
                    setDeleteableImgs([]);
                  }}
                >
                  {'Cancel'}
                </p>
                <button
                  onClick={() => {
                    if (deliveredImages.length + Array.from(addImgs).length < 3) {
                      toast({
                        title: 'Insufficient images',
                        description: 'There cannot be less than 3 images',
                        status: 'error',
                        duration: 3000,
                        position: 'top',
                      });
                      return;
                    }
                    Swal.fire({
                      title: `You're sure?`,
                      icon: 'warning',
                      showCancelButton: true,
                      confirmButtonColor: '#5800F7',
                      cancelButtonColor: '#d33',
                      confirmButtonText: 'Yes, delete!',
                    }).then(async (result) => {
                      if (result.isConfirmed) {
                        try {
                          const r = await axios.patch(
                            `${URL_API}/associate/update/deliveredImages/${driverProfile._id}`,
                            {
                              deleteableImgs,
                              addImgs,
                              isEditable: true,
                              vehicleId: driverProfile.vehicle?._id || driverProfile.vehicleId,
                            },
                            {
                              headers: {
                                Authorization: `Bearer ${user.accessToken}`,
                                'Content-Type': 'multipart/form-data',
                              },
                            }
                          );

                          setIsEdit(false);
                          setDeleteableImgs([]);
                          setAddImgs({} as FileList);
                          toast({
                            title: r.data.message || 'Updated delivery images',
                            status: 'success',
                            duration: 3000,
                            position: 'top',
                          });
                          setDeliveredImages(
                            r.data.deliveredImages as {
                              url: string;
                              originalName: string;
                              docId: string;
                            }[]
                          );
                          router.refresh();
                        } catch (error) {
                          toast({
                            title: 'Error when deleting/adding images',
                            status: 'error',
                            duration: 3000,
                            position: 'top',
                          });
                        }
                      }
                    });
                  }}
                >
                  {'Confirm'}
                </button>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <CiEdit
                  className="cursor-pointer"
                  size={30}
                  strokeWidth="1px"
                  onClick={() => {
                    setIsEdit(!isEdit);
                  }}
                />
                <p>{'Edit'}</p>
              </div>
            )}
          </div>
          {driverProfile.deliveredImages.length > 0 && (
            <div className="flex flex-wrap gap-3">
              {deliveredImages.map((image: any, i: number) => (
                <div key={i} className="relative">
                  {isEdit && (
                    <>
                      <div
                        className="
                        absolute top-[-4px] right-[-10px] z-[3]
                        cursor-pointer bg-textGray2
                        rounded text-red-500
                      "
                        onClick={() => {
                          if (deliveredImages.length + Array.from(addImgs).length > 3) {
                            const find = deliveredImages.find((img: any) => img.docId === image.docId);
                            const filter = deliveredImages.filter((img: any) => img.docId !== image.docId);
                            setDeliveredImages(filter);
                            if (find) {
                              setDeleteableImgs([...deleteableImgs, find.docId]);
                            }
                          } else {
                            toast({
                              title: 'Insufficient images',
                              description: 'There cannot be less than 3 images',
                              status: 'error',
                              duration: 3000,
                              position: 'top',
                            });
                          }
                        }}
                      >
                        <MdDelete size={22} />
                      </div>
                    </>
                  )}

                  <ZoomImage imageUrl={image.url} name={image.originalName} key={i} />
                </div>
              ))}
              {Array.from(addImgs).map((file, index) => (
                <div className="relative " key={index}>
                  <div
                    className="
                        absolute top-[-4px] right-[-10px] z-[3]
                        cursor-pointer bg-textGray2
                        rounded text-red-500
                      "
                    onClick={() => {
                      const find = addImgs[index];
                      const filter = Array.from(addImgs).filter((img) => img !== find);
                      const combinedFileList = new DataTransfer();
                      filter.forEach((f) => {
                        combinedFileList.items.add(f);
                      });
                      setAddImgs(combinedFileList.files);
                    }}
                  >
                    <MdDelete size={22} />
                  </div>
                  <ZoomImage imageUrl={URL.createObjectURL(file)} name={file.name} key={index} />
                </div>
              ))}
              {isEdit && (
                <div className="ml-2">
                  <AddFiles
                    accept="all-images"
                    multiple
                    currentImages={addImgs}
                    onChange={(fls) => {
                      setAddImgs(fls);
                    }}
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </section>
    )
  );
};

interface IDeliveryImageDocsUploadSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const DeliveryImageDocsUploadSection = (props: IDeliveryImageDocsUploadSection) => {
  const { driverProfile } = props;
  const { user } = useCurrentUser();

  return (
    (!driverProfile.deliveredImages || driverProfile.deliveredImages.length < 1) && (
      <section id="deliveredImages" className=" mt-[30px]">
        <div className="flex flex-col gap-3">
          <p>{'Delivery Photos'}</p>
          <CustomModal
            header="Add delivery photos"
            openButtonText="Add delivery photos"
            confirmButtonText="Send"
            onCloseModal={() => {}}
            isPrimaryButton
            validatorSchema={deliveredImagesValidatorSchema}
            initialValues={{ deliveredImages: '' }}
            onSubmit={async (values) => {
              const result = await axios.patch(
                `${URL_API}/associate/update/deliveredImages/${driverProfile._id}`,
                values,
                {
                  headers: {
                    Authorization: `Bearer ${user.accessToken}`,
                    'Content-Type': 'multipart/form-data',
                  },
                }
              );
              return result;
            }}
            body={<DeliveryImageDocsUploadSection.Body />}
          />
        </div>
      </section>
    )
  );
};

const Body = () => {
  const [files, setFiles] = useState<FileList>({} as FileList);
  const [imgs, setImgs] = useState<ImagesType[]>([]);
  const { setFieldValue } = useFormikContext();

  const onChange = (fileList: FileList | null) => {
    if (fileList) {
      setFiles((prevFileList) => {
        if (prevFileList) {
          const newFilesArray = Array.from(fileList);
          const combinedFilesArray = [...newFilesArray];
          const combinedFileList = new DataTransfer();
          combinedFilesArray.forEach((file) => {
            combinedFileList.items.add(file);
          });
          return combinedFileList.files;
        } else {
          return fileList;
        }
      });

      const fls2 = Object.values(fileList);
      const filterNews = fls2.filter((item1) => !imgs.some((item2) => item1.name === item2.name));
      const imgs2 = filterNews?.map((file) => {
        return {
          url: URL.createObjectURL(file),
          name: file.name,
        };
      });
      setImgs([]);
      setImgs([...imgs, ...imgs2]);
    }
  };

  const removeImg = (url: string) => {
    const imgIndex = imgs.findIndex((img) => img.url === url);

    if (imgIndex !== -1) {
      const newImgs = [...imgs];
      newImgs.splice(imgIndex, 1);
      setImgs(newImgs);

      setFiles((prevFileList) => {
        if (prevFileList) {
          const newFileList = new DataTransfer();
          newImgs.forEach((img) => {
            newFileList.items.add(new File([new Blob([img.name])], img.name));
          });
          setFieldValue('deliveredImages', newFileList.files);
          return newFileList.files;
        } else {
          setFieldValue('deliveredImages', prevFileList);
          return prevFileList;
        }
      });
    }
  };

  return (
    <div className="flex flex-col ">
      {imgs.length < 1 ? (
        <InputFile
          name="deliveredImages"
          label="Delivery photos (5 images minimum)"
          nameFile=""
          multiple
          accept="all-images"
          buttonText="Increase"
          placeholder="Files no larger than 3mb"
          onChange={(fls) => {
            onChange(fls);
          }}
        />
      ) : (
        <div className={` flex flex-col`}>
          <label htmlFor="deliveredImages">{'Delivery photos (5 images minimum)'}</label>
          <div
            className={`
                flex gap-3 items-center pt-[10px]
                ${imgs.length >= 5 && 'pb-[10px] overflow-y-hidden overflow-x-scroll custom-scroll'}
              `}
            style={{ width: '100%' }}
          >
            {imgs.map((img, index) => {
              return (
                <div className="flex min-w-[80px] relative  " key={index}>
                  <div
                    className="absolute z-[5 bg-gray-300 rounded-full top-[-10px] right-[-10px] cursor-pointer "
                    onClick={() => removeImg(img.url)}
                  >
                    <RiCloseLine size={22} width="2px" className="text-red-600" />
                  </div>
                  <Image
                    width="1000"
                    height="1000"
                    src={img.url}
                    alt={img.name}
                    className="w-[80px] h-[80px] object-cover "
                  />
                </div>
              );
            })}
            <AddMoreFiles
              name="deliveredImages"
              accept="all-images"
              multiple
              currentImages={files}
              onChange={(fls) => onChange(fls)}
            />
          </div>
        </div>
      )}
    </div>
  );
};
interface IAdendumDocsSection extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const AdendumDocsSection = (props: IAdendumDocsSection) => {
  const { driverProfile } = props;

  return (
    driverProfile.adendumDocs &&
    driverProfile.adendumDocs.length > 0 && (
      <section id="adendumDocs" className=" mt-[30px]">
        <div className="flex flex-col gap-3">
          <p>{'Signed addendums: '}</p>
          {driverProfile.adendumDocs.map(
            (doc: { url: string; originalName: string }, i: Key | null | undefined) => {
              return (
                <DocumentDisplay
                  key={i}
                  url={doc.url}
                  docName={getLengthName(doc.originalName)}
                  backgroundColor="rgba(88, 0, 247, 0.2)"
                  textColor="#5800F7"
                />
              );
            }
          )}
        </div>
      </section>
    )
  );
};

interface IDrivingLicensePresenter {
  documentUrl: string;
  documentName: string;
  documentHandleSubmit: (payload: any, url: string) => Promise<void>;
  documentFieldName: string;
  documentFieldValue: string;
  handleSetNames: (name: string, value: string) => void;
  onCloseModal: () => void;
  schemaValidator: any;
  modalHeaderText: string;
  modalOpenButtonText: string;
  initialValues: any;
  driverProfile: ISectionsAssociateModalUS;
}
const DrivingLicensePresenter = (props: IDrivingLicensePresenter) => {
  const {
    documentUrl,
    documentName,
    documentHandleSubmit,
    documentFieldName,
    documentFieldValue,
    handleSetNames,
    onCloseModal,
    schemaValidator,
    modalHeaderText,
    modalOpenButtonText,
    initialValues,
    driverProfile,
  } = props;

  const handleSubmit = async (values: any) => {
    const payload = { ...values, carNumber: driverProfile.carNumber };
    const url = `${URL_API}/associate/update/driverLicense/${driverProfile._id}`;
    await documentHandleSubmit(payload, url);
  };

  {
    return documentUrl ? (
      <ZoomImage
        imageUrl={
          documentUrl ||
          'https://www.informador.mx/__export/1536274569489/sites/elinformador/img/2018/09/06/licencia_crop1536274322622.jpg_788543494.jpg'
        }
        name={documentName}
      />
    ) : (
      <CustomModal
        header={modalHeaderText || 'Add'}
        initialValues={initialValues}
        openButtonText={modalOpenButtonText || 'Upload'}
        confirmButtonText={'Send'}
        validatorSchema={schemaValidator}
        onSubmit={handleSubmit}
        body={
          <div className="flex flex-col">
            <InputFile
              name={documentFieldName}
              label={modalHeaderText}
              nameFile={documentFieldValue}
              handleSetName={handleSetNames}
              accept="all-images"
              placeholder={'File no larger than 3mb'}
              buttonText={'Upload file'}
            />
          </div>
        }
        isPrimaryButton
        onCloseModal={onCloseModal}
      />
    );
  }
};

interface IDriverPersonalInfoEditModal extends Pick<IDriverPersonalInfo, 'driverProfile'> {}
const DriverPersonalInfoEditModal = (props: IDriverPersonalInfoEditModal) => {
  const { driverProfile } = props;

  const router = useRouter();
  const toast = useToast();
  const { user } = useCurrentUser();
  const { handleIsAssocOpen } = useDetail();
  const { onClose: handleClose } = useEditAssociateModal();

  const handleSubmit = async (payload: any) => {
    try {
      const result = await axios.patch(
        `${URL_API}/associate/update/associateData/${driverProfile._id}`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      toast({
        title: result.data.message,
        description: 'Updating page...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      handleClose();
      handleIsAssocOpen();
      router.refresh();
    } catch (error: any) {
      toast({
        title: error.response.data.message,
        duration: 3000,
        status: 'error',
        position: 'top',
      });
    }
  };

  return (
    <ModalContainer title={'Edit driver'} onClose={handleClose} width="w-[75%]" removeScrollBar={false}>
      <Tabs>
        <TabList>
          <Tab>{'Page 1'}</Tab>
          <Tab>{'Page 2'}</Tab>
        </TabList>

        <TabPanels>
          <TabPanel>
            <FormikContainer
              initialValues={{
                firstName: driverProfile.firstName,
                lastName: driverProfile.lastName,
                email: driverProfile.email,
                phone: driverProfile.phone.replace('+1', ''),
                addressStreet: driverProfile.address.addressStreet,
                interior: driverProfile.address.interior,
                postalCode: driverProfile.address.postalCode,
                birthDay: driverProfile.birthDay,
                ssn: driverProfile.ssn,
              }}
              validatorSchema={Yup.object().shape({
                firstName: Yup.string(),
                lastName: Yup.string(),
                phone: Yup.string()
                  .required('Phone is a required field')
                  .length(10, 'Phone number must be 10 digits'),
                addressStreet: Yup.string().required('The street is required'),
                interior: Yup.string().optional(),
                postalCode: Yup.string()
                  .matches(/^[0-9]+$/, 'Invalid postal code')
                  .min(5, 'Add a valid code')
                  .max(5, 'Add a valid code')
                  .required('Required zip code'),
                birthDay: Yup.string().test({
                  name: 'age-validation',
                  message: 'Age must be greater than 21',
                  test: (value) => {
                    const date = new Date(value as unknown as Date);
                    const now = new Date();
                    const diff = now.getFullYear() - date.getFullYear();
                    return diff >= 21;
                  },
                }),
                ssn: Yup.string()
                  .matches(ssnRegExp, 'Invalid SSN, format should be xxx-xx-xxxx')
                  .required('This field is required'),
              })}
              onSubmit={async (values) => {
                if (!values.phone.startsWith('+1')) {
                  values.phone = '+1' + values.phone;
                }
                await handleSubmit({ ...values });
              }}
            >
              <div className="flex">
                <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
                  <CustomInput name="firstName" label="First Name" type="text" />
                  <CustomInput name="lastName" label="Last Name" type="text" />
                  <InputPhone name="phone" label="Phone" placeholder="" countryCode={US_COUNTRY_CODE} />
                  <CustomInput name="addressStreet" label="Street address" type="text" />
                  <CustomInput name="interior" label="Street address line 2" type="text" />
                  <CustomInput name="postalCode" label="Postal Code" type="text" />
                  <CustomInput name="ssn" label="SSN" type="text" />
                  <CalenderFormikIntegration
                    name="birthDay"
                    label="Birthday"
                    fieldName="birthDay"
                    defaultValue={driverProfile.birthDay}
                  />
                </div>
              </div>
            </FormikContainer>
          </TabPanel>
          <TabPanel>
            <FormikContainer
              initialValues={{
                emergencyContactName:
                  (driverProfile.contacts as ContactUS[]).length > 0
                    ? (driverProfile.contacts[0] as ContactUS).emergencyContactName
                    : '',
                emergencyContactPhone:
                  (driverProfile.contacts as ContactUS[]).length > 0
                    ? (driverProfile.contacts[0] as ContactUS).emergencyContactPhone
                    : '',
                emergencyContactRelation:
                  (driverProfile.contacts as ContactUS[]).length > 0
                    ? (driverProfile.contacts[0] as ContactUS).emergencyContactRelation
                    : '',
              }}
              validatorSchema={emergencyContactsSchemaUS}
              validateChanges={true}
              onSubmit={async (values) => {
                if (!values.emergencyContactPhone.startsWith('+1')) {
                  values.emergencyContactPhone = '+1' + values.emergencyContactPhone;
                }
                const payload = {
                  contacts: [
                    {
                      emergencyContactName: values.emergencyContactName,
                      emergencyContactPhone: values.emergencyContactPhone,
                      emergencyContactRelation: values.emergencyContactRelation,
                    },
                  ],
                };
                await handleSubmit(payload);
              }}
            >
              <div className="grid gap-4 transition-all transform duration-300">
                <CustomInput label="Emergency Contact Name" name="emergencyContactName" type="text" />
                <InputPhone
                  label="Emergency Contact Cellular phone"
                  name="emergencyContactPhone"
                  placeholder=""
                  countryCode={US_COUNTRY_CODE}
                />
                <CustomInput
                  label="Emergency Contact Relationship"
                  name="emergencyContactRelation"
                  type="text"
                />
              </div>
            </FormikContainer>
          </TabPanel>
        </TabPanels>
      </Tabs>
    </ModalContainer>
  );
};

interface ImagesType {
  url: string;
  name: string;
}

type SignDocsKeys = keyof VehicleResponse['drivers'][number]['signDocs'];
interface DocsAndModalProps {
  driver: IAssociateUS;
  carNumber: string;
  label: string;
  property: SignDocsKeys;
  accept?: 'pdf' | 'all-images';
  openButtonText?: string;
}

function UnsfinishedDocAndModal({
  driver,
  carNumber,
  label,
  property,
  accept = 'pdf',
  openButtonText = 'Upload',
}: DocsAndModalProps) {
  const [nameFile, setNameFile] = useState(driver.signDocs[property]?.originalName || '');

  const { user } = useCurrentUser();

  const onSubmit = async (values: any) => {
    const obj = {
      ...values,
      carNumber,
    };
    await axios.patch(`${URL_API}/associate/update/signedDocs/${driver._id}`, obj, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'multipart/form-data',
      },
    });
  };
  return (
    <div className="flex flex-col md:flex-row md:items-end justify-between w-full md:gap-3 2xl:gap-0 flex-wrap">
      <p>{label}</p>
      {driver.signDocs[property] ? (
        <div className="flex gap-2 ">
          <div className="w-[max-content] overflow-hidden">
            <DocumentDisplay
              url={driver.signDocs[property].url}
              docName={getLengthName(driver.signDocs[property].originalName)}
              backgroundColor="rgba(88, 0, 247, 0.2)"
              textColor="#5800F7"
            />
          </div>
          <CustomModal
            header={`${label}`}
            initialValues={{ [property]: '' }}
            openButtonText={openButtonText}
            confirmButtonText={'Send'}
            isUpdate
            updateIconColor="#5800F7"
            onSubmit={onSubmit}
            body={
              <>
                <div className="flex flex-col">
                  <InputFile
                    name={property}
                    label={label}
                    nameFile={nameFile}
                    handleSingleSetName={setNameFile}
                    accept={accept}
                    placeholder="File no larger than 3mb"
                    buttonText="Upload file"
                  />
                </div>
              </>
            }
            isPrimaryButton
            onCloseModal={() => {}}
          />
        </div>
      ) : (
        <CustomModal
          header={`${label}`}
          initialValues={{ [property]: '' }}
          openButtonText={openButtonText}
          confirmButtonText={'Send'}
          onSubmit={onSubmit}
          body={
            <>
              <div className="flex flex-col">
                <InputFile
                  name={property}
                  label={label}
                  nameFile={nameFile}
                  handleSingleSetName={setNameFile}
                  accept={accept}
                  placeholder={`File no larger than 3mb`}
                  buttonText={'Upload file'}
                />
              </div>
            </>
          }
          isPrimaryButton
          onCloseModal={() => {}}
        />
      )}
    </div>
  );
}

type DocsKeys = keyof VehicleResponse['drivers'][number]['documents'];
type OmittedProps = 'ine' | 'driverLicense'; // Reemplaza con las propiedades que deseas omitir
type DocsKeysWithoutOmitted = Exclude<DocsKeys, OmittedProps>;

interface FinishedDocAndModalProps {
  nameFile: string;
  handleSetNames: (name: string, value: string) => void;
  validatorSchema: Yup.ObjectSchema<any>;
  onClose: () => void;
  pText: string;
  driver: IAssociateUS;
  carNumber: string;
  label: string;
  property: DocsKeysWithoutOmitted;
  accept?: 'pdf' | 'all-images';
  handleSubmit: (payload: any, url: string) => Promise<void>;
}

function FinishedDocAndModal({
  driver,
  carNumber,
  label,
  pText,
  property,
  accept = 'pdf',
  onClose,
  handleSetNames,
  nameFile,
  handleSubmit,
}: FinishedDocAndModalProps) {
  const doesDocumentAlreadyExist = driver.documents[property];
  const onSubmit = async (values: any) => {
    let payload = { ...values, carNumber };
    if (doesDocumentAlreadyExist) {
      payload = { ...values, carNumber, isEdit: true };
    }
    const url = `${URL_API}/associate/update/${property}/${driver._id}`;
    await handleSubmit(payload, url);
  };

  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-end justify-between w-full gap-2 sm:gap-0 md:gap-3 2xl:gap-0 ">
      <p>{pText}</p>
      {driver.documents[property] ? (
        <div className="flex gap-2">
          <div className="w-[max-content] overflow-hidden">
            <DocumentDisplay
              url={driver.documents[property].url}
              docName={getLengthName(driver.documents[property].originalName)}
              backgroundColor="rgba(88, 0, 247, 0.2)"
              textColor="#5800F7"
            />
          </div>
          <CustomModal
            header={`${label}`}
            initialValues={{ [property]: '' }}
            openButtonText={`${label}`}
            confirmButtonText={'Send'}
            isUpdate
            updateIconColor="#5800F7"
            onSubmit={onSubmit}
            body={
              <>
                <div className="flex flex-col">
                  <InputFile
                    name={property}
                    label={label}
                    nameFile={nameFile}
                    handleSetName={handleSetNames}
                    accept={accept}
                    placeholder="File no larger than 3mb"
                    buttonText={`Upload file`}
                  />
                </div>
              </>
            }
            isPrimaryButton
            onCloseModal={onClose}
          />
        </div>
      ) : (
        <CustomModal
          header={`${label}`}
          initialValues={{ [property]: '' }}
          openButtonText={`${label}`}
          confirmButtonText={'Send'}
          onSubmit={onSubmit}
          body={
            <>
              <div className="flex flex-col">
                <InputFile
                  name={property}
                  label={label}
                  nameFile={nameFile}
                  handleSetName={handleSetNames}
                  accept={accept}
                  placeholder="File no larger than 3mb"
                  buttonText="Upload file"
                />
              </div>
            </>
          }
          isPrimaryButton
          onCloseModal={onClose}
        />
      )}
    </div>
  );
}

DriverDocuments.ProofOfAddressSection = ProofOfAddressSection;
DriverDocuments.DrivingLicenseSection = DrivingLicenseSection;
DriverDocuments.UnSignedContract = UnSignedContract;
DriverDocuments.SignDocsSection = SignDocsSection;
DriverDocuments.DeliveryImageDocsSection = DeliveryImageDocsSection;
DriverDocuments.DeliveryImageDocsUploadSection = DeliveryImageDocsUploadSection;
DriverDocuments.AdendumDocsSection = AdendumDocsSection;
DeliveryImageDocsUploadSection.Body = Body;
DriverDocuments.DrivingLicensePresenter = DrivingLicensePresenter;
DriverPersonalInfo.DriverPersonalInfoEditModal = DriverPersonalInfoEditModal;
