'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  ModalOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import React from 'react';
import InputPassword from '../Inputs/InputPassword';
import { changePassSchema } from '@/validatorSchemas/changePassSchema';
import { Form as FormikForm, Formik, FormikValues } from 'formik';
import { useNotification } from '@/app/dashboard/perfil/NotificationProvider';
import axios from 'axios';
import { MyUser } from '@/actions/getCurrentUser';

export default function ModalChangePass({ user }: { user: MyUser }) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { setIsChanged } = useNotification();

  const onSubmit = async (values: FormikValues) => {
    await axios.patch(
      `http://localhost:3000/user/${user.id}`,
      {
        password: values.password,
      },
      { headers: { 'Content-Type': 'multipart/form-data', Authorization: `bearer ${user.accessToken}` } }
    );
    setIsChanged(true);
    onClose();
  };

  const defaultValues = { password: '', confirmPassword: '' };

  return (
    <>
      <Button
        sx={{
          w: 'max-content',
          bg: '#5800F7 !important',
          color: 'white',
        }}
        _hover={{
          bgColor: 'red',
        }}
        onClick={onOpen}
      >
        Cambiar contraseña
      </Button>

      <Modal closeOnOverlayClick={false} isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Cambiar contraseña</ModalHeader>
          <ModalCloseButton />
          {/* <Box px={6} pb={6}> */}
          <Formik
            initialValues={defaultValues}
            onSubmit={onSubmit}
            validationSchema={changePassSchema}
            validateOnMount={true}
          >
            {({ isValid, dirty }) => {
              const validate = dirty && isValid;
              console.log(validate);
              return (
                <FormikForm>
                  <ModalBody pb={6}>
                    <div className="grid gap-3">
                      <p>
                        Tu contraseña debe tener mínimo 8 caracteres y al menos una mayúscula, una minúscula y
                        un número.
                      </p>
                      <InputPassword label="Crear nueva contraseña" name="password" />
                      <InputPassword label="Confirmar nueva contraseña" name="confirmPassword" />
                    </div>
                  </ModalBody>
                  <ModalFooter gap={3}>
                    <Button
                      sx={{
                        color: '#5800F7',
                        borderColor: '#5800F7 !important',
                        border: '2px',
                        h: '40px',
                      }}
                      onClick={onClose}
                    >
                      Cancelar
                    </Button>
                    <Button
                      sx={{
                        // bg: '#5800F7 !important',
                        color: 'white',
                        h: '40px',
                      }}
                      className={`
                      ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                      text-white rounded-md  h-[40px] cursor-pointer`}
                      type="submit"
                      disabled={validate}
                    >
                      Cambiar contraseña
                    </Button>
                  </ModalFooter>
                </FormikForm>
              );
            }}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}
