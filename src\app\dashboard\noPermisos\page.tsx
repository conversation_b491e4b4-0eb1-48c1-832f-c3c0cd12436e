import { redirect } from 'next/navigation';
import getUserById from '@/actions/getUserById';

export const metadata = {
  title: 'No Permisos',
  description: 'No tiene permiso para acceder a esta página',
};

export default async function NoPermisosPage() {
  const user = await getUserById();
  if (!user) return redirect('/');

  return (
    <div className="mb-4 flex flex-row justify-between items-center">
      <h1 className="text-[28px] font-bold text-[#262D33]">No tiene permiso para acceder a esta página</h1>
    </div>
  );
}
