import { MediaType } from '@/app/dashboard/clientes/solicitudes/enums';
import { Media } from '../FileUpload/FileUpload';
import useFileUpload from './useFileUpload';
import { useEffect, useState } from 'react';
import { Box, Button, Flex, Text, Grid, Image } from '@chakra-ui/react';
import { FaPlus } from 'react-icons/fa';
import { useField } from 'formik';

interface FileUploadProps {
  name: string;
  accept: string;
  mediaType: MediaType;
  maxFileSize?: number; // in bytes
  onUploadChange?: (media: Media[]) => void;
}

const FileUploader = ({
  name,
  accept,
  mediaType,
  maxFileSize = 1024 * 1024 * 5, // 5 MB
  onUploadChange,
}: FileUploadProps) => {
  const { uploadedFiles, uploadFile, deleteFile, isLoading } = useFileUpload(mediaType);
  const [, , { setValue }] = useField(name);

  const [errorMessage, setErrorMessage] = useState('');

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    setErrorMessage('');
    if (file) {
      if (file.size > maxFileSize) {
        setErrorMessage(`El tamaño del archivo no debe exceder ${maxFileSize / 1024 / 1024} MB.`);
        return;
      }
      await uploadFile(file);
    }
    event.target.value = '';
  };

  useEffect(() => {
    if (onUploadChange) {
      onUploadChange(uploadedFiles);
    }

    setValue(uploadedFiles);
  }, [uploadedFiles, onUploadChange, setValue]);

  return (
    <>
      <Grid gap={2} templateColumns="repeat(6, 1fr)">
        <label htmlFor="file-upload">
          <Button
            as="span"
            isDisabled={isLoading}
            width="64px"
            height="64px"
            className=" bg-white cursor-pointer hover:bg-gray-50 border border-[#5800F7] text-[#5800F7] font-medium"
          >
            <Flex alignItems="center" justifyContent="center" width="full" height="full">
              <FaPlus size={20} />
            </Flex>
          </Button>
        </label>
        {uploadedFiles.map((file, index) => (
          <Box key={index} className="relative">
            <img src={file.url} alt={file.fileName} className="object-cover h-[64px] w-[64px] rounded" />
            <Image
              position="absolute"
              top="calc(50% - 42px)"
              right="calc(50% - 35px)"
              boxSize="20px"
              src="/assets/icons/delete_icon.svg"
              alt="Icono"
              className="h-8 cursor-pointer"
              onClick={() => deleteFile(index)}
            />
          </Box>
        ))}
        <input
          type="file"
          accept={accept}
          onChange={handleFileChange}
          style={{ display: 'none' }}
          disabled={isLoading}
          id="file-upload"
        />
      </Grid>
      <Box mt="2">{errorMessage && <Text color="red.500">{errorMessage}</Text>}</Box>
    </>
  );
};

export default FileUploader;
