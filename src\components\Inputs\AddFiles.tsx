/* eslint-disable consistent-return */
import { Input, Button } from '@chakra-ui/react';
import { useRef, useState } from 'react';

interface FieldProps {
  accept: 'pdf' | 'all-images' | 'jpg' | 'png';
  // the function below has to remove the state or states of the name file
  handleSetName?: (name: string, value: string) => void;
  handleSingleSetName?: (value: string) => void;
  placeHolderDown?: boolean;
  multiple?: boolean;
  onChange?: (event: FileList) => void;
  currentImages: FileList;
}

const acceptTypes: { [key: string]: string } = {
  'all-images': '.jpg, .jpeg, .png, .webp',
  jpg: 'image/jpg',
  png: 'image/png',
  pdf: 'application/pdf',
};

export default function AddFiles({ accept, multiple = false, onChange, currentImages }: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = () => {
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (event: React.DragEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setIsDragging(false);

    const droppedFile = event.dataTransfer.files[0];
    if (!droppedFile) return;
    // if (!multiple) {
    //   return form.setFieldValue(name, droppedFile);
    // }

    const files = event.dataTransfer.files;

    const listFile: File[] = [];

    // Agregar archivos de la primera FileList
    // if (currentImage)
    for (let i = 0; i < currentImages.length; i++) {
      listFile.push(currentImages[i]);
    }

    // Agregar archivos de la segunda FileList
    for (let i = 0; i < files.length; i++) {
      listFile.push(files[i]);
    }

    const mergedFileList = new DataTransfer();
    listFile.forEach((file) => {
      mergedFileList.items.add(file);
    });

    if (onChange) {
      onChange(mergedFileList.files);
      // form.setFieldValue(name, mergedFileList.files);
    }
  };

  return (
    <>
      {/* {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => ( */}
      <div className="relative font-normal ">
        <Button
          // h="40px"
          // w="max-content"
          className="w-[80px] h-[80px] p-0 text-[50px] font-bold"
          borderColor={isDragging ? '#9CA3AF !important' : '#5800F7 !important '}
          color={`${isDragging ? '#9CA3AF !important' : '#5800F7 !important'} `}
          fontWeight={600}
          border={isDragging ? '2px dashed' : '2px solid'}
          sx={{
            '&::placeholder': {
              color: isDragging ? '#9CA3AF !important' : '#5800F7',
            },
            _hover: {
              borderColor: isDragging ? '#9CA3AF !important' : '#5800F7',
            },
          }}
          cursor="pointer"
          onClick={handleButtonClick}
          onDragEnter={handleDragEnter} // Manejadores de eventos de arrastre
          onDragLeave={handleDragLeave} // Manejadores de eventos de arrastre
          onDragOver={(event) => event.preventDefault()} // Evita comportamiento por defecto
          onDrop={(e) => handleFileDrop(e)}
        >
          +
        </Button>

        <Input
          h="45px"
          display="none"
          ref={fileInputRef}
          cursor="pointer"
          accept={acceptTypes[accept]}
          type="file"
          multiple={multiple}
          onChange={(event) => {
            const files = event.currentTarget.files;
            if (files) {
              const listFile: File[] = [];

              // Agregar archivos de la primera FileList
              // if (currentImage)
              for (let i = 0; i < currentImages.length; i++) {
                listFile.push(currentImages[i]);
              }

              // Agregar archivos de la segunda FileList
              for (let i = 0; i < files.length; i++) {
                listFile.push(files[i]);
              }

              // Crear un nuevo FileList con los archivos combinados
              const mergedFileList = new DataTransfer();
              listFile.forEach((file) => {
                mergedFileList.items.add(file);
              });
              if (onChange) {
                onChange(mergedFileList.files);
                // form.setFieldValue(name, mergedFileList.files);
              }
            }
          }}
        />
      </div>
      {/* )} */}
    </>
  );
}
