export enum AdmissionRequestStatus {
  created = 'created',
  earnings_analysis = 'earnings_analysis',
  documents_analysis = 'documents_analysis',
  judicial_analysis = 'judicial_analysis',
  risk_analysis = 'risk_analysis',
  home_visit = 'home_visit',
  social_analysis = 'social_analysis',
  approved = 'approved',
  rejected = 'rejected',
}
export enum MLModels {
  RIDESHARE_PERFORMANCE = 'rideshare_performance',
  FINANCIAL_ASSESSMENT = 'financial_assessment',
  PERSONAL_INFORMATION = 'personal_information',
  HOMEVISIT_INFORMATION = 'homevisit_information',
  // DRIVING_AND_LEGAL_HISTORY = 'driving_and_legal_history',
}

export enum AnalysisStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  ERROR = 'error',
}

export enum GigPlatform {
  uber = 'uber',
  didi = 'didi',
  didi_food = 'didi_food',
  cornershop = 'cornershop',
  indriver = 'indriver',
  uber_eats = 'uber_eats',
  rappi = 'rappi',
  lyft = 'lyft',
  other = 'other',
}

export enum PalencaAccountStatus {
  success = 'success',
  incomplete = 'incomplete',
  pending = 'pending',
  retry = 'retry',
  error = 'error',
  failure = 'failure',
}

export enum AdmissionRequestDocumentType {
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  proof_of_address = 'proof_of_address',
  bank_statement_month_1 = 'bank_statement_month_1',
  bank_statement_month_2 = 'bank_statement_month_2',
  bank_statement_month_3 = 'bank_statement_month_3',
}

export enum AdmissionRequestAdditionalDocumentType {
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_tax_situation = 'proof_of_tax_situation',
  selfie_photo = 'selfie_photo',
  garage_photo = 'garage_photo',
  solidarity_obligor_identity_card_front = 'solidarity_obligor_identity_card_front',
  solidarity_obligor_identity_card_back = 'solidarity_obligor_identity_card_back',
  curp = 'curp',
}

export enum AdmissionRequestDocumentTypeUS {
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement_month_1 = 'bank_statement_month_1',
  bank_statement_month_2 = 'bank_statement_month_2',
  bank_statement_month_3 = 'bank_statement_month_3',
  bank_statement_month_4 = 'bank_statement_month_4',
  bank_statement_month_5 = 'bank_statement_month_5',
  bank_statement_month_6 = 'bank_statement_month_6',
  // proofOfCompletionOfAnyRequiredSafetyCourses = 'proof_of_completion_of_safety_courses',
  // avgWeeklyIncomeOfLastTwelveWeeks = 'avgWeeklyIncomeOfLastTwelveWeeks',
  // rideShareRideHistory = 'rideShareRideHistory',
  // rideShareDates = 'rideShareDates',
  // drivingRecord = 'drivingRecord',
  // signature = 'signature',
}

export enum RequestDocumentStatus {
  pending = 'pending',
  approved = 'approved',
  pending_review = 'pending_review',
  rejected = 'rejected',
}

export enum MediaType {
  proof_of_tax_situation = 'proof_of_tax_situation',
  identity_card_front = 'identity_card_front',
  identity_card_back = 'identity_card_back',
  drivers_license_front = 'drivers_license_front',
  drivers_license_back = 'drivers_license_back',
  proof_of_address = 'proof_of_address',
  bank_statement = 'bank_statement',
  home_visit_evidence = 'home_visit_evidence',
  selfie_photo = 'selfie_photo',
  garage_photo = 'garage_photo',
  solidarity_obligor_identity_card_front = 'solidarity_obligor_identity_card_front',
  solidarity_obligor_identity_card_back = 'solidarity_obligor_identity_card_back',
  curp = 'curp',
}

export enum MediaStatus {
  pending = 'pending', // A document that has been uploaded but not yet processed
  active = 'active', // A document that is currently in use and valid
  deleted = 'deleted', // A document that has been marked for deletion
  archived = 'archived', // A document that is no longer in active use but retained for historical purposes
}

export enum RequestDocumentsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum ResidentOwnershipStatus {
  owned = 'owned',
  rented = 'rented',
}

export enum HomeVisitStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

export enum EntityType {
  admission_request = 'admission_request',
}

export enum ActionType {
  'admission_request.created' = 'admission_request.created',
  'home_visit.created' = 'home_visit.created',
  'home_visit.approved' = 'home_visit.approved',
  'home_visit.rejected' = 'home_visit.rejected',
  'admission_request.approved' = 'admission_request.approved',
  'admission_request.rejected' = 'admission_request.rejected',
  'documents_analysis.approved' = 'documents_analysis.approved',
}

export enum RiskAnalysisStatus {
  pending = 'pending',
  completed = 'completed',
}

export enum ScorecardVariableName {
  age = 'age',
  gig_platforms = 'gig_platforms',
  life_time_completed_trips = 'life_time_completed_trips',
  days_since_first_trip = 'days_since_first_trip',
  percentage_acceptance_rate = 'percentage_acceptance_rate',
  percentage_cancellation_rate = 'percentage_cancellation_rate',
  average_rating = 'average_rating',
  earnings_last_12_weeks = 'earnings_last_12_weeks',
  vehicle_condition = 'vehicle_condition',
  vehicle_type = 'vehicle_type',
  // risk_city = 'risk_city', We needs more definition on how to handle them
}

export enum RiskCategory {
  low = 'low',
  medium = 'medium',
  high = 'high',
}

export enum EarningsAnalysisStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
  approved_with_conditions = 'approved_with_conditions',
}

export enum DocumentClassification {
  mandatory = 'mandatory_documents',
  additional = 'additional_documents',
  all = 'all_documents',
}
