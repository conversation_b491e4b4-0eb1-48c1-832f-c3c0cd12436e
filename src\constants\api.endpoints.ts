export enum ApiPath {
  ADMISSION_REQUEST = `/admission/requests/`,
  HOME_VISIT = `home-visit`,
  CALENDAR_SCHEDULE = '/calendar/schedule',
  CALENDAR_EVENTS = '/calendar/events',
  CALENDAR_EVENT_STATUS = '/calendar/event/status',
  AVAILABLE_SLOTS_USERS = '/calendar/users-with-same-available-slot',
  CALENDAR_EVENT_HOME_VISITOR_CHANGE = '/calendar/event/home-visitor-change',
  LOCATION_GATHERING_MESSAGE = 'send-location-gathering-message',
  RESET_HOME_VISIT_SCHEDULE_LINK_SEND_DATE = '/reset-home-visit-schedule-link-send-date',
  PERSONAL_DATA = '/personal-data',
  HOME_IMAGE_UPLOAD_MESSAGE = '/home-image-upload-message',
}

export enum ClientPath {
  DASHBOARD = '/dashboard',
  CLIENTES = '/clientes',
  SOLICITUDES = '/solicitudes',
  FLOTILLA = '/flotilla',
  ACTIVE = '/active',
  INACTIVE = '/inactive',
}
