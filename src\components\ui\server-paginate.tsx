import { PaginationState, Table } from '@tanstack/react-table';
import React from 'react';
import { Button } from './button';
import { Input } from './input';

type Props<TData> = {
  table: Table<TData>;
  allRecords?: number;
  total?: number;
  onPageChange: (pagination: PaginationState) => void; // Nueva prop para manejar los cambios de página
};

export default function ServerPaginate<TData>({ table, allRecords, total, onPageChange }: Props<TData>) {
  const pagination = table.getState().pagination;
  const handlePageChange = (newPageIndex: number) => {
    // Al cambiar la página, actualizamos la tabla y llamamos a la función onPageChange
    table.setPageIndex(newPageIndex);

    const newPagination = { ...pagination, pageIndex: newPageIndex };

    onPageChange(newPagination);
  };

  return (
    <div className="pt-2">
      <div className="flex items-center gap-2">
        <Button
          className="border rounded p-1"
          onClick={() => handlePageChange(0)}
          disabled={!table.getCanPreviousPage()}
        >
          {'<<'}
        </Button>
        <Button
          className="border rounded p-1"
          onClick={() => handlePageChange(pagination.pageIndex - 1)}
          disabled={!table.getCanPreviousPage()}
        >
          {'<'}
        </Button>
        <Button
          className="border rounded p-1"
          onClick={() => handlePageChange(pagination.pageIndex + 1)}
          disabled={!table.getCanNextPage()}
        >
          {'>'}
        </Button>
        <Button
          className="border rounded p-1"
          onClick={() => handlePageChange(table.getPageCount() - 1)}
          disabled={!table.getCanNextPage()}
        >
          {'>>'}
        </Button>
        <span className="flex items-center gap-1">
          <div>Page</div>
          <strong>
            {pagination.pageIndex + 1} of {table.getPageCount().toLocaleString()}
            {/* {pagination.pageIndex + 1} of {} */}
          </strong>
        </span>
        <span className="flex items-center gap-1">
          | Go to page:
          <Input
            type="number"
            defaultValue={pagination.pageIndex + 1}
            onChange={(e) => {
              const page = e.target.value ? Number(e.target.value) - 1 : 0;
              handlePageChange(page);
            }}
            className="border p-1 rounded w-16"
          />
        </span>
        <select
          value={pagination.pageSize}
          onChange={(e) => {
            const newSize = Number(e.target.value);
            table.setPageSize(newSize);
            const newPagination = { ...pagination, pageSize: newSize };
            onPageChange(newPagination); // Actualizar el servidor con el nuevo tamaño de página
          }}
        >
          {[10, 20, 30, 40, 50].map((pageSize) => (
            <option key={pageSize} value={pageSize}>
              Show {pageSize}
            </option>
          ))}
        </select>
      </div>
      <div className="mt-2">
        Showing results: {total} of {allRecords} total database rows
      </div>
    </div>
  );
}
