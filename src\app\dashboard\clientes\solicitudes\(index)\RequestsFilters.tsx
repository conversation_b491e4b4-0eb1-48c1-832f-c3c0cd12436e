'use client';
import useDebouncedCallback from '@/app/hooks/useDebouncedCallback';
import { QueryLink } from '@/components/QueryLink';
import { getSearchParam } from '@/utils/params';
import { Input, InputGroup, InputLeftElement, InputRightElement, Spinner, Select } from '@chakra-ui/react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useState, useTransition } from 'react';
import { AiOutlineSearch, AiOutlineClose } from 'react-icons/ai';
import {
  AddUS,
  AgregarMX,
  FilterUS,
  FiltrosMX,
  SearchPlaceholderMX,
  SearchPlaceholderUS,
} from '../../_components/translations';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';

export default function SearcherRequests() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const search = getSearchParam(searchParams.get('search'));

  function handleSearchChange(value: string) {
    const nextParams = new URLSearchParams(searchParams);

    // Reset pagination if params change
    nextParams.delete('page');
    if (value.trim().length > 0) {
      nextParams.set('search', value);
    } else {
      nextParams.delete('search');
    }
    router.replace(`${pathname}?${nextParams.toString()}`, { scroll: false });
  }
  return (
    <div className="relative">
      <SearchInput search={search} onSearchChanged={handleSearchChange} />
    </div>
  );
}

interface SearchInputProps {
  search?: string;
  onSearchChanged: (search: string) => void;
}
function SearchInput(props: SearchInputProps) {
  {
    const ability = usePermissions();
    const { search, onSearchChanged } = props;

    const [isTransitioning, startTransition] = useTransition();
    const [searchInput, setSearchInput] = useState(search);

    const debouncedUrlUpdate = useDebouncedCallback((val) => {
      startTransition(() => {
        onSearchChanged(val);
      });
    }, 500);

    const { isCountryUSA } = useCountry();

    const FilterOptions = isCountryUSA ? FilterOptionsUS : FilterOptionsMX;

    const canAdd = canPerform(ability, Capabilities.Add, Sections.Clients, Subsections.Admissions);

    return (
      <>
        <div className="flex flex-row items-center justify-between gap-4">
          <InputGroup>
            <InputLeftElement pointerEvents="none">
              <AiOutlineSearch color="#A9A9A9" size={22} />
            </InputLeftElement>
            <Input
              placeholder={isCountryUSA ? `${SearchPlaceholderUS}` : `${SearchPlaceholderMX}`}
              value={searchInput}
              onChange={(e) => {
                setSearchInput(e.target.value);
                debouncedUrlUpdate(e.target.value);
              }}
            />

            <InputRightElement>
              {isTransitioning ? <Spinner size="sm" color="#A9A9A9" /> : undefined}
              {/* clear button */}
              {searchInput && searchInput.length > 0 && !isTransitioning ? (
                <button
                  onClick={() => {
                    setSearchInput('');
                    debouncedUrlUpdate('');
                  }}
                >
                  <AiOutlineClose color="#777777" />
                </button>
              ) : undefined}
            </InputRightElement>
          </InputGroup>
          <Select
            placeholder={isCountryUSA ? `${FilterUS}` : `${FiltrosMX}`}
            onChange={(e) => {
              debouncedUrlUpdate(e.target.value);
            }}
          >
            {FilterOptions.map((option) => {
              return (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              );
            })}
          </Select>
          <QueryLink query={{ dialog: 'create-request' }}>
            {canAdd && (
              <button className="bg-[#5800F7] text-white pr-1 h-[40px] rounded w-[180px] border-[#5800F7]">
                {isCountryUSA ? AddUS : AgregarMX}
              </button>
            )}
          </QueryLink>
        </div>
      </>
    );
  }
}

enum FilterOptions {
  created = 'created',
  approved = 'approved',
  rejected = 'rejected',
  documents_analysis = 'documents_analysis',
  earnings_analysis = 'earnings_analysis',
  risk_analysis = 'risk_analysis',
  home_visit = 'home_visit',
  social_analysis = 'social_analysis',
}

const FilterOptionsMX = [
  { value: FilterOptions.created, label: 'Creada' },
  { value: FilterOptions.approved, label: 'Aprobada' },
  { value: FilterOptions.rejected, label: 'Rechazada' },
  { value: FilterOptions.documents_analysis, label: 'Análisis de documentos' },
  { value: FilterOptions.earnings_analysis, label: 'Análisis de ingresos' },
  { value: FilterOptions.risk_analysis, label: 'Análisis de riezgo' },
  { value: FilterOptions.home_visit, label: 'Visita domiciliaria' },
  { value: FilterOptions.social_analysis, label: 'Análisis social' },
];

const FilterOptionsUS = [
  { value: FilterOptions.created, label: 'Created' },
  { value: FilterOptions.approved, label: 'Approved' },
  { value: FilterOptions.rejected, label: 'Rejected' },
  { value: FilterOptions.documents_analysis, label: 'Documents analysis' },
  { value: FilterOptions.earnings_analysis, label: 'Earnings analysis' },
  { value: FilterOptions.risk_analysis, label: 'Risk analysis' },
  { value: FilterOptions.home_visit, label: 'Home visit' },
  { value: FilterOptions.social_analysis, label: 'Social analysis' },
];
