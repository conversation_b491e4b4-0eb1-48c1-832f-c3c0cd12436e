import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import { DocumentData } from './getUsers';
import { Section } from './getPermissionSetById';

export interface MyUser {
  name: string;
  role: string;
  image: DocumentData;
  email: string;
  city: string;
  accessToken: string;
  id: string;
  area: string;
  permissions: Section[];
}

export async function getSession() {
  return getServerSession(authOptions);
}

export default async function getCurrentUser() {
  try {
    const session = await getSession();
    if (!session) return null;

    const user = session.user as unknown as MyUser;
    return user;
  } catch (error: any) {
    return null;
  }
}
