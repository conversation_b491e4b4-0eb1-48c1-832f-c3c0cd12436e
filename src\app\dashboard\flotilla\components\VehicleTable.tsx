'use client';
import { AiOutlineClose } from 'react-icons/ai';
import { getCookie, setCookie } from 'cookies-next';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { getStockVehicles, GetVehicleProps, VehicleCard } from '@/actions/getAllVehicles';
import { citiesSelect, platformOptions, statusSelected, stepsSelect, URL_API, US_CITIES } from '@/constants';
import {
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Button,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  Badge,
} from '@chakra-ui/react';
import { CountryProvider, useCountry } from '../../providers/CountryProvider';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { PaginationStock } from '@/components/PaginationStock';
import { BsThreeDotsVertical } from 'react-icons/bs';
import moment from 'moment';
import BarRevamp from './PageBar/BarInactivos';
import { FiEdit, FiEye } from 'react-icons/fi';
import {
  VehicleSubCategoryTranslations,
  VehicleSubCategoryTranslationsMX,
} from './translations/statusTranslations';

interface FlotillaCardsProps {
  page?: { name: string; api: string; count: number };
  subPage?: { name: string; api: string; count: number };
  data?: VehicleCard[];
  totalCount?: number;
  route?: string;
  subOptions?: { name: string; label: string }[];
  active?: string;
}

const getValues = (params: Record<string, string>, isCountryUSA: boolean) => {
  const filters = [];
  if (params.city) {
    let cityFound;
    if (isCountryUSA) {
      cityFound = US_CITIES.find((city) => city.value === params.city.toUpperCase());
    } else {
      cityFound = citiesSelect.find((city) => city.value === params.city);
    }
    if (cityFound) {
      filters.push({ key: 'city', value: cityFound.label });
    }
  }
  if (params.stepNumber) {
    const stepFound = stepsSelect.find((step) => step.value === params.stepNumber);
    if (stepFound) {
      filters.push({ key: 'stepNumber', value: stepFound.label });
    }
  }

  if (params.isNew) {
    const boolIsNew = params.isNew === 'true' ? 'Nuevos' : 'Seminuevos';
    filters.push({ key: 'isNew', value: boolIsNew });
  }

  if (params.status) {
    const statusFound = statusSelected.find((status) => status.value === params.status);
    if (statusFound) {
      filters.push({ key: 'status', value: statusFound.label });
    }
  }
  if (params.isElectric) {
    filters.push({ key: 'isElectric', value: params.isElectric });
  }

  if (params.platform) {
    const platformFound = platformOptions.find((platform) => platform.value === params.platform);
    if (platformFound) {
      filters.push({ key: 'platform', value: platformFound.label });
    }
  }

  if (params.reason && window.location.pathname.includes('withdrawn')) {
    const reasonValue = isCountryUSA
      ? params.reason === 'Theft'
        ? 'Theft'
        : 'Accident'
      : params.reason === 'Robo'
      ? 'Robo'
      : 'Accidente';
    filters.push({ key: 'reason', value: reasonValue });
  }

  return filters;
};

const getSearchParamsObj = (filterCookie: any) => {
  const filter = filterCookie ? JSON.parse(filterCookie) : {};
  const params: Record<string, string> = {};
  for (const [key, value] of Object.entries(filter)) {
    if (key === 'reason' && !window.location.pathname.includes('withdrawn')) {
      continue;
    }
    params[key] = value as string;
  }
  return params;
};

export default function VehicleTable({
  page = { name: '', api: '', count: 0 },
  subPage = { name: '', api: '', count: 0 },
  data = [],
  totalCount = 0,
  subOptions = [],
  active = '',
}: FlotillaCardsProps) {
  const [vehicles, setVehicle] = useState(data);
  //const [, /* loading */ setLoading] = useState(false);
  const search = useSearchParams();
  const country = search ? search.get('country') : null;
  const activeSubCat = search ? search.get('subCategory') : null;
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(
    !active ? (activeSubCat ? activeSubCat : subOptions[0]?.name) : active
  );

  const subCategoryTranslations =
    country === 'Mexico' ? VehicleSubCategoryTranslationsMX : VehicleSubCategoryTranslations;

  // const [isLoading, setIsLoading] = useState(false);
  const [, /* isError */ setIsError] = useState(false);

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  useEffect(() => {
    setCurrentPage(1);
    ///fetchData(activeTab, 0);
  }, [country]);

  useEffect(() => {
    //console.log('data:', data);
    setVehicle(data);
  }, [data]);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    // Recalculate total pages when `totalCount` updates
    setTotalPages(Math.ceil(totalCount / 10)); // Assuming 10 items per page
  }, [totalCount]);

  const handlePageChange = (pageNum: number) => {
    setCurrentPage(pageNum);
    fetchData(activeTab, pageNum - 1);
  };

  useEffect(() => {
    const stateToSave = {
      page,
      subPage,
      data: vehicles,
      totalCount,
      subOptions,
      activeTab,
    };
    localStorage.setItem('vehicleTableState', JSON.stringify(stateToSave));
  }, [page, subPage, vehicles, totalCount, subOptions, activeTab]);

  const handleTabClick = async (tab: string) => {
    setCurrentPage(1);
    if (pathname?.includes('search')) {
      try {
        setActiveTab(tab);
        const searchQuery = search ? search.get('q') : null;
        const vehicleStatus = search ? search.get('vehicleStatus') : null;
        const category = search ? search.get('category') : null;
        let endpoint = `${URL_API}/stock/search?search=${searchQuery}`;
        if (vehicleStatus) {
          endpoint += `&vehicleStatus=${encodeURI(vehicleStatus)}`;
        }
        if (category) {
          endpoint += `&category=${encodeURI(category)}`;
        }
        if (country) {
          endpoint += `&country=${encodeURI(country)}`;
        }
        endpoint += `&subCategory=${encodeURI(tab)}`;

        const res = await axios(endpoint, {
          headers: {
            Authorization: 'Bearar ' + user.accessToken,
          },
        });
        setVehicle(res.data);
        setTotalPages(Math.ceil(res?.data?.length / 10));
        const params = new URLSearchParams(search || '');
        params.set('subCategory', tab); // Update the `subCategory` query parameter
        router.replace(`${pathname}?${params.toString()}`);
        //setSubcatCount(res.data?.subcategoryCounts);
      } catch (err: any) {
        console.error(err);
        setIsError(true);
      }
    } else {
      const currentParams = new URLSearchParams(search.toString());

      currentParams.set('category', subPage.api);
      currentParams.set('subCategory', tab);

      router.replace(`${window.location.pathname}?${currentParams.toString()}`);
      setActiveTab(tab);
      fetchData(tab);
    }
  };

  const pathname = usePathname();
  const filterCookie = getCookie('filters');

  //const currentPage = pathname.split('/').pop();

  const fetchData = async (tab: string, pageNumber: number = 0) => {
    const newPageNumber = pageNumber;
    //setLoading(true);

    const res = getSearchParamsObj(filterCookie);
    //const listStatus = statusListByPage[currentPage as string];
    //const isActivePage = page?.name === 'Activos';

    const fetchObj: GetVehicleProps = {
      page: newPageNumber,
      limit: 10,
      searchParams: {
        vehicleStatus: page?.api,
        category: subPage?.api,
        subCategory: tab,
        country: country || 'Mexico',
        ...res,
      },
    };

    // if (!isActivePage) {
    //   fetchObj.excludeStatus = statusListByPage.activos;
    // }

    const result = await getStockVehicles(fetchObj);

    if (!result) {
      //setLoading(false);
      return null;
    }
    if (result.stock.length === 0) {
      //setLoading(false);
      return false;
    } else {
      setVehicle(result?.stock);
      setTotalPages(Math.ceil(result.totalCount / 10));
    }

    if (vehicles.length >= totalCount) {
      //setLoading(false);
      return false;
    }
    return false;
  };

  // const handlePageChange = (page: number) => {
  //   fetchData(activeTab, page - 1); // Backend is 0-indexed
  // };

  const [, /* bottomPosition */ setBottomPosition] = useState(0);

  return (
    <>
      <CountryProvider>
        <div
          className="
        flex flex-col h-full  
        relative 
        overflow-hidden
        w-[calc(98vw-35px)]
        md:w-full
        lg:w-[calc(98vw-335px)] 
        md:ml-[35px]
        lg:ml-0
      "
        >
          <BarRevamp
            page={page}
            subPage={subPage}
            category={subPage?.api}
            subCategory={activeTab}
            country={country}
          />
          <AppliedFilters setBottomPosition={setBottomPosition} totalCount={totalCount} />
        </div>
        <div
          style={{
            border: '1px solid white',
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '1.5%',
            overflow: 'hidden',
          }}
        >
          <div
            style={{
              marginBottom: '2%',
              width: '100%',
              overflowX: 'auto',
              display: 'flex', // Ensure buttons are aligned in a row
              flexDirection: 'row', // Explicitly set row direction
              scrollbarWidth: 'thin', // Thin scrollbar for modern browsers
              WebkitOverflowScrolling: 'touch',
            }}
          >
            {subOptions?.map((option, index) => {
              // const count = vehicles[0]?.subcategoryCounts?.[option?.name] || 0;
              const count =
                vehicles[0]?.subcategoryCounts?.[
                  option?.name as keyof (typeof vehicles)[0]['subcategoryCounts']
                ] || 0;

              const translatedLabel =
                subCategoryTranslations[option?.name as keyof typeof subCategoryTranslations] ||
                option?.label;
              return (
                <Button
                  key={index}
                  onClick={() => handleTabClick(option?.name)}
                  bgGradient={activeTab === option?.name ? 'linear(to-r, #6210FF, #A74DF9)' : 'none'}
                  color={activeTab === option?.name ? 'white' : '#586D79'}
                  fontWeight={400}
                  borderColor={activeTab === option?.name ? 'transparent' : 'gray.300'}
                  _hover={{
                    bgGradient: activeTab === option?.name ? 'linear(to-r, #6210FF, #A74DF9)' : 'gray.100',
                    //color: activeTab === option ? "white" : "black",
                    backgroundColor: activeTab !== option?.name ? 'gray.100 !important' : undefined,
                  }}
                  variant={activeTab === option?.name ? 'solid' : 'none'}
                  style={{
                    background:
                      activeTab === option?.name ? 'linear-gradient(to right, #6210FF, #A74DF9)' : 'none',
                    marginRight: '2px', // Add space between buttons
                    flexShrink: 0, // Prevent buttons from shrinking
                  }}
                >
                  {`${translatedLabel} (${count})`}
                </Button>
              );
            })}
          </div>
          <TableContainer>
            <Table colorScheme="gray" border="1px solid #e2e8f0">
              <Thead bgColor={'#FAFAFA'}>
                <Tr>
                  <Th>#</Th>
                  <Th>{country === 'Mexico' ? 'Vehículo' : 'Vehicle'}</Th>
                  <Th>VIN #</Th>
                  <Th>{country === 'Mexico' ? 'Vehículo' : 'Vehicle'} #</Th>
                  <Th>{country === 'Mexico' ? 'Placa de coche' : 'Car Plate'} #</Th>
                  <Th>{country === 'Mexico' ? 'Fecha agregada' : 'Date Added'}</Th>
                  {subPage.api === 'withdrawn' && <Th>{country === 'Mexico' ? 'Razón' : 'Reason'}</Th>}
                  <Th textAlign="center">{country === 'Mexico' ? 'Acciones' : 'Actions'}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {vehicles?.filter((vehicle: VehicleCard) => vehicle?._id).length >= 1 ? (
                  vehicles
                    .filter((vehicle: VehicleCard) => vehicle?._id)
                    .map((vehicle: VehicleCard, rowIndex: number) => (
                      <Tr
                        key={vehicle._id}
                        style={{ cursor: 'pointer' }}
                        onClick={() =>
                          router.push(
                            `/dashboard/flotilla/inactive/${subPage?.api}/${vehicle._id}${
                              country ? `?country=${encodeURI(country)}` : ''
                            }`
                          )
                        }
                      >
                        <Td>{(currentPage - 1) * 10 + rowIndex + 1}</Td>
                        <Td>
                          {vehicle.brand} {vehicle.model}
                        </Td>
                        <Td>{vehicle?.vin || 'N/A'}</Td>
                        <Td>{vehicle?.carNumber}</Td>
                        <Td>{vehicle?.carPlates?.plates || 'N/A'}</Td>
                        <Td>
                          {vehicle?.createdAt ? moment(vehicle?.createdAt).format('MMM D YYYY') : 'N/A'}
                        </Td>
                        {subPage.api === 'withdrawn' && (
                          <Td>
                            <Badge
                              bg={vehicle?.dischargedData?.reason === 'Robo' ? '#FFEFDB' : '#C6E7FF'}
                              color={vehicle?.dischargedData?.reason === 'Robo' ? '#B95000' : '#28A3FD'}
                              variant="subtle"
                              fontSize="sm"
                              px="2"
                              py="1"
                              borderRadius="lg"
                              textTransform="none"
                            >
                              {vehicle?.dischargedData?.reason || 'N/A'}
                            </Badge>
                          </Td>
                        )}
                        {/* <Td>{vehicle.status || 'N/A'}</Td> */}
                        <Td textAlign="center">
                          <Menu>
                            <MenuButton
                              onClick={(e) => e.stopPropagation()}
                              as={IconButton}
                              icon={<BsThreeDotsVertical />}
                              variant="ghost"
                            />
                            <MenuList>
                              <MenuItem onClick={(e) => e.stopPropagation()} icon={<FiEdit />}>
                                {country === 'Mexico' ? 'Editar' : 'Edit'}
                              </MenuItem>
                              <MenuItem
                                icon={<FiEye />}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  router.push(
                                    `/dashboard/flotilla/inactive/${subPage?.api}/${vehicle._id}${
                                      country ? `?country=${encodeURI(country)}` : ''
                                    }`
                                  );
                                }}
                              >
                                {country === 'Mexico' ? 'Vista' : 'View'}
                              </MenuItem>
                            </MenuList>
                          </Menu>
                        </Td>
                      </Tr>
                    ))
                ) : (
                  <Tr>
                    <Td colSpan={8} textAlign="center">
                      No vehicles available.
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
          <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'end' }}>
            <PaginationStock
              currentPage={currentPage}
              hasMorePages={currentPage < totalPages}
              pagesCount={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      </CountryProvider>
    </>
  );
}

const dynamicValues = {
  platform: 'Plataforma',
  isElectric: 'Eléctricos',
  reason: 'Motivo',
};

const AppliedFilters = (props: any) => {
  const { setBottomPosition, totalCount } = props;
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isCountryUSA } = useCountry();
  const filterCookie = getCookie('filters');
  const previousElement = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function getOffsetBottom() {
      if (previousElement.current) {
        const btmPosition = previousElement.current.getBoundingClientRect().bottom;
        setBottomPosition(btmPosition);
      }
      return 0;
    }
    getOffsetBottom();
    window.addEventListener('resize', getOffsetBottom);
    return () => {
      window.removeEventListener('resize', getOffsetBottom);
    };
  }, [setBottomPosition]);

  const removeFilterByName = (name: string) => {
    const params = new URLSearchParams(searchParams);
    params.delete(name);
    const filterCookies = getCookie('filters');
    const filt = filterCookies ? JSON.parse(filterCookies) : {};
    delete filt[name];

    setCookie('filters', JSON.stringify(filt));
    router.refresh();
  };

  const getFilters = () => {
    const searchParamsObj = getSearchParamsObj(filterCookie);
    const values = getValues(searchParamsObj, isCountryUSA);
    return values;
  };

  const filters = getFilters();

  return (
    <>
      {filters.length > 0 ? (
        <div className="flex flex-col lg:flex-row gap-2 mt-[10px] flex-wrap " ref={previousElement}>
          <p> {isCountryUSA ? 'Filters applied:' : 'Filtros aplicados:'} </p>
          <div className="flex flex-wrap gap-2">
            {filters.map((filter, i) => {
              if (filter.key === 'platform' || filter.key === 'isElectric') {
                if (filter.key === 'isElectric') {
                  console.log(
                    'filter',
                    filter,
                    filter.value === 'true' && 'Sí',
                    filter.value === 'false' && 'No'
                  );
                }
                return (
                  <button
                    key={i}
                    className="h-[30px] w-[max-content] py-2 px-3 bg-[#5800F7] rounded-[15px] text-white flex gap-3 items-center "
                    onClick={() => {
                      removeFilterByName(filter.key);
                    }}
                  >
                    {/* <p>{filter.value}</p> */}

                    <p>
                      {/* {filter.key}: {filter.value} */}
                      {dynamicValues[filter.key]}: {filter.value === 'true' && 'Sí'}{' '}
                      {filter.value === 'false' && 'No'}{' '}
                      {filter.value !== 'true' && filter.value !== 'false' && filter.value}
                    </p>

                    <AiOutlineClose color="#FFFFFF" />
                  </button>
                );
              }

              return (
                <>
                  <button
                    key={i}
                    className="h-[30px] w-[max-content] py-2 px-3 bg-[#5800F7] rounded-[15px] text-white flex gap-3 items-center "
                    onClick={() => {
                      removeFilterByName(filter.key);
                    }}
                  >
                    <p>{filter.value}</p>
                    <AiOutlineClose color="#FFFFFF" />
                  </button>
                </>
              );
            })}
          </div>
          <p> {isCountryUSA ? `Total results: ${totalCount}` : `Total de resultados: ${totalCount}`} </p>
        </div>
      ) : (
        <div id="prev" ref={previousElement}>
          {/* <p className="mt-[20px]">
            {' '}
            {isCountryUSA ? `Results: ${totalCount}` : `Resultados: ${totalCount}`}{' '}
          </p> */}
        </div>
      )}
    </>
  );
};
