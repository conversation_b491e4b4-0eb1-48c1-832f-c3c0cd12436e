export interface AssociatePaymentType {
  _id: string;
  associateEmail: string;
  associateId: string;
  balance: number;
  block: boolean;
  contractId: string;
  createdAt: Date;
  gigId: null;
  model: string;
  monexClabe: null;
  newPaymentsArr: any[];
  otherPayment: any[];
  paymentNumber: number;
  paymentsArray: PaymentsArray[];
  paymentsHistory: any[];
  region: string;
  stpClabe: null;
  updatedAt: Date;
  vehiclesId: string;
  lengthAddedBefore: number;
  adendumGenerated?: boolean;
}

export interface PaymentsArray {
  block: boolean;
  weeklyCost?: number;
}
