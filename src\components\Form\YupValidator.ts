import * as Yup from 'yup';

export const loginValidation = Yup.object().shape({
  email: Yup.string().email('Email inválido').required('El email es requerido'),
  password: Yup.string().min(3, 'Requiere minimo 3 caracteres').required('Contraseña requerida'),
});

export const resetPasswordValidation = Yup.object().shape({
  email: Yup.string().email('Email inválido').required('El email es requerido'),
});

export const changePasswordValidation = Yup.object().shape({
  password: Yup.string().min(3, 'Requiere minimo 3 caracteres').required('Contraseña requerida'),
  confirmPassword: Yup.string()
    .label('confirm password')
    .required()
    .oneOf([Yup.ref('password'), null], 'Passwords must match'),
});
