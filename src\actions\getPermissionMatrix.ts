import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';

export interface Section {
  section: string;
  subSections: SubSection[];
}

interface SubSection {
  subSection: string;
  capabilities: string[];
}

const getPermissionMatix = cache(async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/permissionSet/getPermissionMatrix`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return response?.data?.permissionMatrix as Section[];
  } catch (error: any) {
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getPermissionMatix;
