'use client';
import {
  <PERSON><PERSON>,
  <PERSON>dalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Text,
  Alert,
  AlertIcon,
  VStack,
  Box,
  List,
  ListItem,
  ListIcon,
} from '@chakra-ui/react';
import { Button } from '@/components/ui/button';
import { Md<PERSON>arning, MdError } from 'react-icons/md';
import { OCRResult } from '@/utils/ocrApi';

interface OCRConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onCancel: () => void;
  ocrResult: OCRResult;
  documentType: string;
}

export default function OCRConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  onCancel,
  ocrResult,
  documentType,
}: OCRConfirmationModalProps) {
  if (!ocrResult) {
    return null; // If ocrResult is not provided, do not render the modal
  }
  const hasErrors = ocrResult.validationErrors && ocrResult?.validationErrors.length > 0;

  // Remove duplicate error messages
  const uniqueValidationErrors = hasErrors ? [...new Set(ocrResult.validationErrors)] : [];

  const getDocumentTypeDisplayName = (type: string) => {
    const typeMap: Record<string, string> = {
      INSURANCE_POLICY: 'Póliza de Seguro',
      PLATES_ALTA_PLACAS: 'Documento de Placas',
      PLATES_FRONT: 'Placa Frontal',
      PLATES_BACK: 'Placa Trasera',
      CIRCULATION_CARD_FRONT: 'Tarjeta de Circulación (Frontal)',
      CIRCULATION_CARD_BACK: 'Tarjeta de Circulación (Trasera)',
      TENENCIA: 'Tenencia',
      FACTURE: 'Factura',
    };
    return typeMap[type] || type;
  };

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="md">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          <Text fontSize="lg" fontWeight="bold">
            Confirmación de Documento
          </Text>
        </ModalHeader>
        <ModalBody>
          <VStack spacing={4} align="stretch">
            <Alert
              status={hasErrors ? 'error' : 'info'}
              borderRadius="md"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              textAlign="center"
              p={4}
              bg={hasErrors ? 'red.50' : '#F3F0FF'}
              borderColor={hasErrors ? 'red.200' : '#A74DF9'}
              borderWidth="1px"
            >
              <AlertIcon
                boxSize="40px"
                mr={0}
                as={hasErrors ? MdError : MdWarning}
                color={hasErrors ? 'red.500' : '#5800F7'}
              />
              <Text mt={2} fontSize="md" fontWeight="medium" color={hasErrors ? 'red.700' : '#5800F7'}>
                {hasErrors
                  ? 'Se encontraron errores en el documento'
                  : 'Se detectaron advertencias en el documento'}
              </Text>
            </Alert>

            <Box>
              <Text fontSize="sm" color="gray.600" mb={2}>
                <strong>Tipo de documento:</strong> {getDocumentTypeDisplayName(documentType)}
              </Text>

              {hasErrors && uniqueValidationErrors && (
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2}>
                    Errores detectados:
                  </Text>
                  <List spacing={1} pl={4}>
                    {uniqueValidationErrors.map((error, index) => (
                      <ListItem key={index} fontSize="sm" color="red.600">
                        <ListIcon as={MdError} color="red.500" />
                        {error}
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>

            <Alert
              status="info"
              borderRadius="md"
              fontSize="sm"
              bg="#F3F0FF"
              borderColor="#A74DF9"
              borderWidth="1px"
            >
              <AlertIcon color="#5800F7" />
              <Box>
                <Text fontWeight="medium" mb={1} color="#5800F7">
                  ¿Desea continuar?
                </Text>
                <Text fontSize="xs" color="gray.600">
                  Si continúa, el documento se mantendrá pero deberá verificar cuidadosamente todos los campos
                  extraídos. Si cancela, el documento será removido y deberá subir uno nuevo.
                </Text>
              </Box>
            </Alert>
          </VStack>
        </ModalBody>
        <ModalFooter className="flex justify-between"></ModalFooter>
        <ModalFooter className="flex justify-between">
          <Button variant="outline" onClick={handleCancel}>
            Cancelar y Remover
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-[#5800F7] text-white hover:bg-[#6210FF] active:bg-[#4A00D1] border-none"
          >
            Continuar con Errores
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
