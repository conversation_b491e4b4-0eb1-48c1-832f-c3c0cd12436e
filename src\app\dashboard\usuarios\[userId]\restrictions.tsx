'use client';
import axios from 'axios';
import { useCurrentUser } from '../../providers/CurrentUserProvider';
import { URL_API } from '@/constants';
import { useEffect, useMemo, useState } from 'react';
import PrimaryButton from '@/components/PrimaryButton';
import { useParams } from 'next/navigation';
import Swal from 'sweetalert2';
import { useToast } from '@chakra-ui/react';

interface UserVehicleRestrictionsProps {
  restrictions: any[] | null;
}

export default function UserVehicleRestrictions({ restrictions }: UserVehicleRestrictionsProps) {
  const { userId } = useParams();
  const { user } = useCurrentUser();
  const [vehicles, setVehicles] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState({
    restrictedSearch: '',
    vehiclesSearch: '',
  });

  /* selected api vehicles to restrict to the user */
  const [selectedVehicles, setSelectedVehicles] = useState<
    { _id: string; checked: boolean; carNumber: string }[]
  >([]);
  const [updateSelectedVehicles, setUpdateSelectedVehicles] = useState<any[]>([]);
  const [filterByChecked, setFilterByChecked] = useState({
    vehicles: false,
    restrictions: false,
  });
  const [create, setCreate] = useState<boolean>(Boolean(restrictions) || restrictions?.length === 0);

  const toast = useToast();

  useEffect(() => {
    const getAllVehicles = async () => {
      try {
        const response = await axios.get(`${URL_API}/stock/get`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });

        const filtered = response.data.filter((vehicle: any) => {
          if (restrictions && restrictions.length > 0) {
            return !restrictions.some((restriction: any) => {
              return vehicle._id === restriction._id;
            });
          } else return vehicle;
        });

        setVehicles(filtered);
      } catch (error) {
        console.log(error);
      }
    };
    if (create || (restrictions && restrictions.length > 0)) {
      getAllVehicles();
    }
  }, [user, restrictions, create]);

  const filteredVehicles = useMemo(() => {
    const hasRestrictions = restrictions && restrictions.length > 1;
    /* display filtered when user wants to see only the selected vehicles */
    if (filterByChecked.vehicles) {
      return vehicles.filter((vehicle) =>
        selectedVehicles.some(
          (selectedVehicle) => selectedVehicle._id === vehicle._id && selectedVehicle.checked
        )
      );
    }
    /* display only vehicles by search filter */
    if (searchTerm.vehiclesSearch.trim() === '') {
      return vehicles;
    } else {
      if (hasRestrictions) {
        return vehicles.filter((vehicle) => {
          return vehicle.carNumber.toLowerCase().includes(searchTerm.vehiclesSearch.toLowerCase());
        });
        /* default filter, the initial filter, removing some of the api vehicles if already has the restriction */
      } else return vehicles;
    }
  }, [vehicles, restrictions, searchTerm, filterByChecked.vehicles, selectedVehicles]);

  const updateFilters = useMemo(() => {
    if (filterByChecked.restrictions) {
      return restrictions?.filter((restriction) =>
        updateSelectedVehicles.some(
          (updateSelected) => updateSelected._id === restriction._id && updateSelected.checked
        )
      );
    }
    if (searchTerm.restrictedSearch.trim() === '') {
      return restrictions;
    } else {
      if (restrictions && restrictions.length > 0) {
        return restrictions.filter((restriction) =>
          restriction.carNumber.toLowerCase().includes(searchTerm.restrictedSearch.toLowerCase())
        );
      } else return restrictions;
    }
  }, [restrictions, searchTerm, filterByChecked.restrictions, updateSelectedVehicles]);

  const handleSearchChange = (e: any) => {
    setSearchTerm({ ...searchTerm, [e.target.name]: e.target.value });
  };

  const createRestrictions = async (method: 'add' | 'remove') => {
    try {
      const url = `${URL_API}/user/restrictions/${
        restrictions && restrictions.length > 0 ? 'update' : 'create'
      }`;

      const axiosMethod = restrictions && restrictions.length > 0 ? 'put' : 'post';

      const restrictionsSelected = method === 'add' ? selectedVehicles : updateSelectedVehicles;

      await axios[axiosMethod](
        url,
        /* add method is just for update endpoint */
        { stockRestrictions: restrictionsSelected, userId, method },
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      setSelectedVehicles([]);
      setUpdateSelectedVehicles([]);
      setFilterByChecked({
        vehicles: false,
        restrictions: false,
      });

      toast({
        title: 'Restricciones actualizadas',
        description: 'Recargando pagina',
        status: 'success',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => {
        return window.location.reload();
      }, 3000);
    } catch (error) {
      toast({
        title: 'Error al actualizar restricciones',
        status: 'error',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
    }
  };

  const confirmSubmit = async (method: 'add' | 'remove') => {
    return Swal.fire({
      title: method === 'add' ? 'Confirmar restricciones' : 'Remover restricciones',
      // text: 'Esta acción no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: method === 'add' ? 'Sí, restringir' : 'Sí, remover',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await createRestrictions(method);
      }
    });
  };

  if (!create)
    return (
      <div className="flex flex-col gap-3">
        <p>No hay restricciones</p>
        <PrimaryButton
          onClick={() => {
            setCreate(true);
          }}
        >
          Crear
        </PrimaryButton>
      </div>
    );

  return (
    /* div with grid class with 2 columns */
    <div className=" grid grid-cols-2 gap-4 mt-4 ">
      <div className="flex flex-col gap-3">
        <div className="flex justify-between items-center mr-5">
          <div className="flex gap-4 items-center h-[50px]">
            <h3>Vehiculos restringidos</h3>
            {updateSelectedVehicles.length > 0 && (
              <PrimaryButton
                onClick={async () => {
                  await confirmSubmit('remove');
                }}
              >
                Eliminar
              </PrimaryButton>
            )}
          </div>
          <input
            name="restrictedSearch"
            className="h-[40px] border-2 border-gray-400 rounded pl-2 w-[200px] "
            type="text"
            placeholder="Buscar restringidos..."
            value={searchTerm.restrictedSearch}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex gap-4 items-center h-[45px] ">
          <p>Total seleccionados: {updateSelectedVehicles.length} </p>
          {updateSelectedVehicles.length > 0 && searchTerm.restrictedSearch === '' && (
            <PrimaryButton
              onClick={() => {
                setFilterByChecked({
                  ...filterByChecked,
                  restrictions: !filterByChecked.restrictions,
                });
              }}
            >
              {filterByChecked.restrictions ? 'Mostrar todos' : 'Mostrar seleccionados'}
            </PrimaryButton>
          )}
        </div>
        {updateFilters?.map((restriction, index) => {
          return (
            <label key={index} className="flex gap-3 w-[max-content]">
              <input
                type="checkbox"
                checked={updateSelectedVehicles.some(
                  (updateSelected) => updateSelected._id === restriction._id
                )}
                onChange={(e) => {
                  if (e.target.checked) {
                    setUpdateSelectedVehicles([...updateSelectedVehicles, { ...restriction, checked: true }]);
                  } else {
                    setUpdateSelectedVehicles(
                      // eslint-disable-next-line consistent-return
                      updateSelectedVehicles.filter((updateSelected) => {
                        if (updateSelected._id !== restriction._id) {
                          return { _id: updateSelected._id, checked: true };
                        }
                      })
                    );
                  }
                }}
              />
              <p>{restriction.carNumber}</p>
            </label>
          );
        })}
      </div>

      {/* SECOND COLUMN */}
      <div className="flex flex-col gap-3">
        <div className="flex gap-3 items-center h-[50px] justify-between ">
          <div className="flex items-center gap-3 ">
            <h3>Vehiculos no restringidos</h3>
            {selectedVehicles.length > 0 && (
              <PrimaryButton
                onClick={async () => {
                  await confirmSubmit('add');
                }}
              >
                Agregar
              </PrimaryButton>
            )}
          </div>
          <input
            className="h-[40px] border-2 border-gray-400 rounded pl-2 w-[200px] "
            type="text"
            name="vehiclesSearch"
            placeholder="Buscar vehículos..."
            value={searchTerm.vehiclesSearch}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex gap-4 items-center h-[45px] ">
          <p>Total seleccionados: {selectedVehicles.length} </p>
          {selectedVehicles.length > 0 && searchTerm.vehiclesSearch === '' && (
            <PrimaryButton
              onClick={() => {
                // setFilterByChecked(!filterByChecked);
                setFilterByChecked({
                  ...filterByChecked,
                  vehicles: !filterByChecked.vehicles,
                });
              }}
            >
              {filterByChecked.vehicles ? 'Mostrar todos' : 'Mostrar seleccionados'}
            </PrimaryButton>
          )}
        </div>

        {filteredVehicles?.map((vehicle, index) => {
          return (
            <label key={index} className="flex gap-3">
              <input
                type="checkbox"
                // checked={v}
                checked={selectedVehicles.some((selectedVehicle) => selectedVehicle._id === vehicle._id)}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedVehicles([...selectedVehicles, { ...vehicle, checked: true }]);
                  } else {
                    setSelectedVehicles(
                      // eslint-disable-next-line consistent-return
                      selectedVehicles.filter((selectedVehicle) => {
                        if (selectedVehicle._id !== vehicle._id) {
                          return { ...vehicle, checked: true };
                        }
                      })
                    );
                  }
                }}
              />
              <p>{vehicle.carNumber}</p>
            </label>
          );
        })}
      </div>
    </div>
  );
}
