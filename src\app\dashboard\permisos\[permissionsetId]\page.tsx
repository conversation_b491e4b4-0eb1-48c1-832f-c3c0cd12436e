import getPermissionMatrix from '@/actions/getPermissionMatrix';
import getPermissionSetById from '@/actions/getPermissionSetById';
import { Suspense } from 'react';
import EditPermissionForm from './EditPermissionForm';

interface EditPermisoPageProps {
  params: {
    permissionsetId: string;
  };
}

export default async function EditPermisoPage({ params: { permissionsetId } }: EditPermisoPageProps) {
  const permissionMatrix = await getPermissionMatrix();
  if (!permissionMatrix) return null;

  const permissionSet = await getPermissionSetById(permissionsetId);
  if (!permissionSet) return null;

  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <h1 className="text-[28px] font-bold text-[#262D33]">Agregar Permiso</h1>
      <div className="py-2 bg-gray-50">
        <EditPermissionForm permissionMatrix={permissionMatrix} permissionSet={permissionSet} />
      </div>
    </Suspense>
  );
}
