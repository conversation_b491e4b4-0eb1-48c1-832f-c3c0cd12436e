import * as Yup from 'yup';

export function fileValidator(errorMessage: string, fileType: 'pdf' | 'all-images') {
  return (
    Yup.mixed()
      // .test('fileSize', 'El archivo no debe pesar más de 2mb', (value) => {
      //   if (!value) return true;
      //   return value && (value as File).size <= 1024 * 1024 * 5; // Tamaño máximo de 1MB
      // })
      .test('fileType', `El archivo debe ser de tipo ${fileType === 'pdf' ? 'PDF' : 'imagen'}`, (value) => {
        if (!value) return true;
        if (fileType === 'pdf') {
          return value && (value as File).type === 'application/pdf';
        } else {
          const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
          const type = (value as File).type;
          return fileTypes.includes(type);
        }
      })
      .required(errorMessage)
  );
}

/**
 * Validador para un FileList
 * @param errorMessage Mensaje de error
 * @param fileType Tipo de archivo permitido
 * @param minimun Cantidad mínima de archivos, no requerido, por defecto es 1
 */

export function fileListValidator(errorMessage: string, fileType: 'pdf' | 'all-images', minimun: number = 1) {
  return Yup.mixed()
    .test(
      'fileList',
      `Debe seleccionar al menos ${minimun ? `${minimun} archivos` : 'un archivo'}`,
      (value) => {
        if (value instanceof FileList) {
          return value.length >= minimun;
        }

        // check if its an array of files
        if (Array.isArray(value) && value.length > 0) {
          return value.length >= minimun;
        }

        return false;
      }
    )
    .test('fileType', `El archivo debe ser de tipo ${fileType === 'pdf' ? 'PDF' : 'imagen'}`, (value) => {
      // Validar el tipo de archivo según la extensión permitida
      if (!value || !(value instanceof FileList)) {
        return true;
      }

      const file = value[0]; // Tomar el primer archivo del FileList

      if (fileType === 'pdf') {
        return file.type === 'application/pdf';
      } else {
        const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        return fileTypes.includes(file.type);
      }
    })
    .required(errorMessage);
}
