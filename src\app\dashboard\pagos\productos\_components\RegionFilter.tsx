'use-client';

import { useState } from 'react';
import SelectRegion from '../(productos)/SelectRegion';
import { ProductModal } from '@/components/Modals/ProductModal';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';

export const RegionFilter = () => {
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { country } = useCountry();
  const region = searchParams.get('region') || '';

  const handleChangeRegion = (selectedRegion: string) => {
    const params = new URLSearchParams(searchParams);
    if (selectedRegion === 'none') {
      params.delete('region');
      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
      return;
    }
    params.set('region', selectedRegion);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const ability = usePermissions();
  const canAdd = canPerform(ability, Capabilities.Add, Sections.Payments, Subsections.Products);

  return (
    <div className="flex justify-end pb-4">
      <SelectRegion region={region} setRegion={handleChangeRegion} country={country.value} />
      {canAdd && <ProductModal open={open} setOpen={setOpen} />}
    </div>
  );
};
