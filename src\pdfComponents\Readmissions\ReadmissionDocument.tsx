import { Document, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import FirstPart from './components/first-part';
import SecondPart from './components/second-part';
import Clauses from './components/clauses';
import Signs from './components/signs';

export const mockReadmissionData = {
  name: 'PEDRO CESAR MARTINEZ CASILLAS',
  contractNumber: '1033-2',
  deliveryDate: '2024-04-05',
  terminationDate: '2025-09-03',
  rfc: 'MAIG940313HDF',
  address: 'CALLE 123, COLONIA 456, CIUDAD DE MÉXICO, MÉXICO',
  zipCode: '12345',
  vin: '1HGCM82633A000000',
  price: 5000,
  promissoryPayment: 120000,
  text: 'CINCO MIL PESOS 00/100 M.N.',
};

export interface ReadmissionPdfProps {
  data?: typeof mockReadmissionData;
}

export default function ReadmissionDocument({ data = mockReadmissionData }: ReadmissionPdfProps) {
  const headerText = `CONVENIO DE TERMINACIÓN ANTICIPADA QUE EN TÉRMINOS DE LO ESTABLECIDO EN EL CAPÍTULO IX,
  ARTÍCULO 2483 DEL CÓDIGO CIVIL PARA EL DISTRITO FEDERAL (AHORA CIUDAD DE MÉXICO) CELEBRAN POR
  UNA PARTE E-MKT GOODS DE MÉXICO, S.A.P.I. DE C.V. POR CONDUCTO DE SU REPRESENTANTE LEGAL EL C.
  MAIRON ESTEBAN SANDOVAL GÓMEZ, A QUIEN EN LO SUCESIVO SE LE DENOMINARÁ “EL ARRENDADOR” O “EL
  SUBARRENDADOR” DEPENDIENDO DE LA NATURALEZA DEL CONTRATO DEL QUE SE TRATE Y POR OTRA PARTE EL
  C. MAXIMO DAVID RAMOS PEREZ, QUIEN EN LO SUCESIVO SE LE DENOMINARÁ “EL CONDUCTOR”, RESPECTO
  DEL CONTRATO DE ARRENDAMIENTO O SUBARRENDAMIENTO DE NÚMERO ${mockReadmissionData.contractNumber}, QUIENES SE OBLIGAN AL TENOR
  DE LAS SIGUIENTES DECLARACIONES Y CLAUSULAS:`.replace(/\s+/g, '  ');

  return (
    <Document>
      <Page style={readmissionDocStyles.page} size="A4" wrap>
        <View style={readmissionDocStyles.body}>
          <View style={readmissionDocStyles.viewMain}>
            <Text style={readmissionDocStyles.headerTitle}>{headerText}</Text>
          </View>

          <View style={readmissionDocStyles.viewMain}>
            <Text
              style={{
                ...readmissionDocStyles.headerTitle,
                textAlign: 'center',
                marginTop: '20px',
                fontWeight: 'bold',
                letterSpacing: 1.5,
              }}
            >
              DECLARACIONES
            </Text>
          </View>

          <FirstPart rfc={data.rfc} name={data.name} deliveryDate={data.deliveryDate} />
          <SecondPart address={data.address} />

          <Clauses
            contractNumber={data.contractNumber}
            terminationDate={data.terminationDate}
            promissoryPayment={data.promissoryPayment}
          />

          <Signs name={data.name} />
        </View>
      </Page>
    </Document>
  );
}

export const readmissionDocStyles = StyleSheet.create({
  page: {
    paddingVertical: 60,
    flexDirection: 'column',
    alignContent: 'center',
  },
  viewer: {
    width: '100%',
    height: '100vh',
  },
  header: {
    width: '100vw',
  },
  headerTitle: {
    textAlign: 'justify',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 11,
    fontWeight: 800,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
    // letterSpacing: 1.5,
  },
  body: {
    flexDirection: 'column',
    rowGap: 1,
    marginBottom: '10%',
    marginHorizontal: '15%',
  },
  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    bottom: '25px',
    left: '40%',
    height: '80px',
    width: '25%',
    transform: 'rotate(10deg)',
  },
  anexoTitle: {
    textAlign: 'center',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 14,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    zIndex: 1,
  },

  anexoText: {
    color: 'black',
    // fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    textAlign: 'justify',
    letterSpacing: '0.2',
    lineHeight: '1.2',
    // lineHeight: '2px',
    // zIndex: 1,
  },

  viewMain: {
    rowGap: 10,
    zIndex: 1,
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    // marginTop: '20px',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    rowGap: 8,
  },
  names: {
    fontSize: 10,
    textAlign: 'left',
    fontFamily: 'Helvetica-Bold',
    marginLeft: 20,
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 11,
  },
});
