'use client';

import { useHandleReadmissionData } from '@/hooks/useReadmissionDetail';
import ModalContainer from './ModalContainer';
// import ZoomImage from '../others/ZoomImage';
import { useZoom } from '@/hooks/useZoom';
import ZoomModalContainer from '../others/ZoomModalContainer';
import DocumentDisplay from '@/components/DocumentDisplay';
import { format, parseISO } from 'date-fns';

export default function DetailReadmissionModal() {
  const { onClose, readmission: data } = useHandleReadmissionData();
  const zoomHook = useZoom();
  if (!data) return null;
  // console.log(data.readmissionsReason);
  let terminationFiles = null;
  if (data.terminationFiles) terminationFiles = Object.values(data.terminationFiles);

  console.log('terminationFiles', terminationFiles);
  return (
    <>
      <ZoomModalContainer />
      <ModalContainer className="w-[750px] pb-[30px]" title="Detalle del reingreso" onClose={onClose}>
        <div className="flex flex-col gap-3">
          <p className=" text-textGray1 font-[600]">Motivo del reingreso</p>
          <p>
            {data.description} {/* Falta de pago */}
          </p>
          <table>
            <thead>
              <tr className="font-[600] ">
                <th className="h-12 px-4 text-left align-middle ">No.</th>
                <th className="h-12 px-4 text-left align-middle ">Fecha y hora de reingreso</th>
                <th className="h-12 px-4 text-left align-middle">Status</th>
                <th className="h-12 px-4 text-left align-middle ">KM de reingreso</th>
              </tr>
            </thead>
            <tbody>
              <tr className="bg-[#EAECEE]">
                <td className="py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0 rounded-l-md ">
                  1
                </td>
                <td className="py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0">
                  {format(parseISO(data.readmissionDate), 'dd/MM/yyyy hh:mm a')}
                </td>
                <td className="py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0 text-[#29CC97]">
                  Terminado
                </td>
                <td className="py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0 rounded-r-md">
                  {data.km ? `${data.km} KM` : 'No se ha completado el reingreso'}
                  {/* {data.km || '0'} KM */}
                </td>
              </tr>
            </tbody>
          </table>
          <div className="flex flex-col gap-2 font-[600] text-textGray1">
            <p>Evidencia de KM</p>
            <div className="flex gap-3">
              {data.kmImgs.length === 0 && (
                <p className="font-normal text-textGray2 text-[16px]">Aún no se ha completado el reingreso</p>
              )}
              {data.kmImgs.map((evidence, i) => {
                if (!evidence?.url) return null;
                return (
                  <div
                    key={i}
                    className={`
                    bg-cover
                    bg-center
                    bg-no-repeat
                    cursor-pointer
                    rounded
                    relative
                  `}
                    style={{
                      backgroundImage: `url(${evidence.url})`,
                      width: '100px',
                      height: '100px',
                      backgroundRepeat: 'no-repeat',
                    }}
                    onClick={() => {
                      zoomHook.setImage(evidence);
                      zoomHook.onOpen();
                    }}
                  ></div>
                );
              })}
            </div>
          </div>
          <div className="flex flex-col gap-2 font-[600] text-textGray1 ">
            <p>Evidencia de reingreso</p>
            <div className="flex gap-3 w-full overflow-x-auto pb-2  ">
              {data.evidenceImgs.length === 0 && (
                <p className="font-normal text-textGray2 text-[16px]">Aún no se ha completado el reingreso</p>
              )}
              {data.evidenceImgs.slice(0).map((evidence, i) => {
                if (!evidence?.url) return null;
                return (
                  <div
                    key={i}
                    className={`
                    bg-cover
                    bg-center
                    bg-no-repeat
                    cursor-pointer
                    rounded
                    relative

                  `}
                    style={{
                      backgroundImage: `url(${evidence.url})`,
                      minWidth: '100px',
                      height: '100px',
                      backgroundRepeat: 'no-repeat',
                    }}
                    onClick={() => {
                      zoomHook.setImage(evidence);
                      zoomHook.onOpen();
                    }}
                  ></div>
                );
              })}
            </div>
          </div>
          <div className="flex flex-col gap-2  text-textGray1">
            <p className="font-[600]">Firma de documentos</p>
            <div className="flex gap-3">
              {!data.readmissionDoc && (
                <p className="font-normal text-textGray2 text-[16px]">Aún no se ha completado el reingreso</p>
              )}
              {data.readmissionDoc && (
                <DocumentDisplay url={data.readmissionDoc.url} docName={data.readmissionDoc.originalName} />
              )}
              {data.promissoryNote && (
                <DocumentDisplay url={data.promissoryNote.url} docName={data.promissoryNote.originalName} />
              )}
              {data.contractCanceled && (
                <DocumentDisplay
                  url={data.contractCanceled.url}
                  docName={data.contractCanceled.originalName}
                />
              )}
            </div>
          </div>
          <div className="flex flex-col gap-2  text-textGray1">
            <p className="font-[600]">Documentos de terminación</p>
            <div className="flex gap-3 flex-wrap">
              {terminationFiles &&
                terminationFiles.length > 0 &&
                terminationFiles.map((file, i) => {
                  return (
                    <div key={i}>
                      {file && <DocumentDisplay url={file.url} docName={file.originalName} />}
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      </ModalContainer>
    </>
  );
}
