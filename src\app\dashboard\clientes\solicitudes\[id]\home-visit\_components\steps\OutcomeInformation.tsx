import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField, HookFormRadixUISelect } from '../HookFormField';
import { FormSection } from '../FormSection';
import { FormSectionHeader, Separator } from '../FormHeaderSection';
import { IStepperButtonsProps, StepperButton } from '../StepperButtons';
import { format } from 'date-fns';
import {
  capitalizeFirstLetter,
  HomeVisitStepsStatus,
  ownedOrRentedOptions,
  ownerAndRentedOptionsMapping,
  OWNORRENTED,
  smallFirstLetter,
  URL_API,
  yesOrNoOptions,
} from '@/constants';
import { useEffect, useState } from 'react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { allStepsCompleted, anyStepInCompletedExceptOutcome, NoSelected, Steps, toastConfigs } from '.';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { LuClock5, LuCalendar } from 'react-icons/lu';
import { useStepperNavigation } from './useStepperNavigation';
import { AdmissionRequestDocumentType, HomeVisitStatus } from '../../../../enums';
import { translations } from '../translations';
import { ImageViewer } from '@/components/ImageViewer';

enum OutcomeInformationEnum {
  Approved = 'Approved',
  Rejected = 'Rejected',
  Pending = 'Pending',
  // MX translations
  Rechazar = 'Rechazar',
  Aprobar = 'Aprobar',
  Pendiente = 'Pendiente',
}

enum ReasonOfRejectionEnum {
  WithoutRootsDocument = 'Without roots document',
  NoGarage = 'No Garage',
  InCompleteDocumentation = 'Incomplete documentation',
  ClientProvidesWrongInformation = 'Client provides wrong information',
  ClientDoesNotProvideTrust = 'Client does not provide trust',
  ClientDoesNotShowUp = 'Client does not show up',
}

// Mx translations
enum ReasonOfRejectionEnumMX {
  WithoutRootsDocument = 'Sin documento de arraigo',
  NoGarage = 'Sin garage',
  InCompleteDocumentation = 'Documentación incompleta',
  ClientProvidesWrongInformation = 'Cliente proporciona información errónea',
  ClientDoesNotProvideTrust = 'Cliente no brinda confianza',
  ClientDoesNotShowUp = 'Cliente no se presenta',
}

const reasonOfRejectionOptions = [
  {
    label: ReasonOfRejectionEnumMX.WithoutRootsDocument,
    value: ReasonOfRejectionEnum.WithoutRootsDocument,
  },
  {
    label: ReasonOfRejectionEnumMX.NoGarage,
    value: ReasonOfRejectionEnum.NoGarage,
  },
  {
    label: ReasonOfRejectionEnumMX.InCompleteDocumentation,
    value: ReasonOfRejectionEnum.InCompleteDocumentation,
  },
  {
    label: ReasonOfRejectionEnumMX.ClientProvidesWrongInformation,
    value: ReasonOfRejectionEnum.ClientProvidesWrongInformation,
  },
  {
    label: ReasonOfRejectionEnumMX.ClientDoesNotProvideTrust,
    value: ReasonOfRejectionEnum.ClientDoesNotProvideTrust,
  },
  {
    label: ReasonOfRejectionEnumMX.ClientDoesNotShowUp,
    value: ReasonOfRejectionEnum.ClientDoesNotShowUp,
  },
];

const outcomeInformationPendingOptions = [
  {
    label: `Información ${OutcomeInformationEnum.Pendiente}`,
    value: OutcomeInformationEnum.Pending,
  },
];

const outcomeInformationApprovedOrRejectedOptions = [
  {
    label: OutcomeInformationEnum.Aprobar,
    value: OutcomeInformationEnum.Approved,
  },
  {
    label: OutcomeInformationEnum.Rechazar,
    value: OutcomeInformationEnum.Rejected,
  },
];

const OutcomeInformationSchema = z.object({
  visitDate: z.string({
    required_error: translations.es.DateRequired,
  }),
  visitTime: z.string({
    required_error: translations.es.TimeRequired,
  }),
  visitorEmailAddress: z.string(),
  doesProofOfAddressMatchLocation: z.string(),
  characteristicsOfGarage: z.string(),
  behaviourOfCustomerDuringCall: z.string(),
  ownProperty: z.string(),
  status: z.string(),
  statusReason: z.string().optional(),
  visitOutcomeInformation: z.string(),
  reasonOfRejection: z.string(),
  locationInformation: z.string(),
});

interface IOutcomeInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

const approvalDropdownRequiredFields = [
  'doesProofOfAddressMatchLocation',
  'characteristicsOfGarage',
  'behaviourOfCustomerDuringCall',
  'ownProperty',
];

const finishFormRequiredFields = [
  'doesProofOfAddressMatchLocation',
  'characteristicsOfGarage',
  'behaviourOfCustomerDuringCall',
  'ownProperty',
  'status',
];

export default function OutcomeInformation(props: IOutcomeInformation) {
  const { goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, homeVisit, documentsAnalysis, locationData } = admissionRequest;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [suggestedStatus, setSuggestedStatus] = useState(homeVisit?.suggestedStatus || '');
  const [showStatusReason, setShowStatusReason] = useState(
    (homeVisit?.suggestedStatus &&
      capitalizeFirstLetter(homeVisit.status) !== OutcomeInformationEnum.Pending &&
      homeVisit?.suggestedStatus !== homeVisit?.status) ||
      false
  );

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const isAnyStepIncomplete = homeVisit?.homeVisitStepsStatus
    ? anyStepInCompletedExceptOutcome(homeVisit.homeVisitStepsStatus)
    : true;

  const form = useForm<z.infer<typeof OutcomeInformationSchema>>({
    resolver: zodResolver(OutcomeInformationSchema),
    defaultValues: {
      visitDate: homeVisit
        ? homeVisit.visitDate
          ? format(new Date(homeVisit.visitDate), 'yyyy-MM-dd')
          : format(new Date(), 'yyyy-MM-dd')
        : format(new Date(), 'yyyy-MM-dd'),
      visitTime: homeVisit ? homeVisit.visitTime : '',
      visitorEmailAddress: homeVisit ? homeVisit.visitorEmailAddress : user?.email,
      doesProofOfAddressMatchLocation: homeVisit?.doesProofOfAddressMatchLocation || NoSelected,
      characteristicsOfGarage: homeVisit ? homeVisit.characteristicsOfGarage : '',
      behaviourOfCustomerDuringCall: homeVisit ? homeVisit.behaviourOfCustomerDuringCall : '',
      ownProperty: homeVisit?.houseInformation?.ownProperty
        ? ownerAndRentedOptionsMapping[
            homeVisit.houseInformation.ownProperty as keyof typeof ownerAndRentedOptionsMapping
          ]
        : OWNORRENTED.OWNEDMX,
      visitOutcomeInformation: homeVisit ? homeVisit.comments : '',
      status: homeVisit?.status ? capitalizeFirstLetter(homeVisit.status) : OutcomeInformationEnum.Pending,
      statusReason: homeVisit?.statusReason || '',
      reasonOfRejection: homeVisit?.reasonOfRejection || NoSelected,
      locationInformation: locationData ? locationData?.location : '',
    },
  });
  const status = form.watch('status');

  const areRequiredFieldsFilled = (requiredFields: Array<string>) => {
    return requiredFields.every((field) => {
      const value = form.getValues(field as any);
      if (field === 'status') {
        return (
          value && (value === OutcomeInformationEnum.Approved || value === OutcomeInformationEnum.Rejected)
        );
      }

      return value && value !== '' && value !== NoSelected;
    });
  };

  useEffect(() => {
    async function fetchStatus() {
      if (!isAnyStepIncomplete && user?.accessToken) {
        try {
          const response = await fetch(`${URL_API}/mlservice/homevisit/${requestId}`, {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
          const data = await response.json();
          if (data.status) {
            const capitalizedStatus = capitalizeFirstLetter(data.status);
            setSuggestedStatus(capitalizedStatus);
          }
        } catch (error) {
          console.error('Error fetching status:', error);
        }
      }
    }

    if (
      (!form.getValues('status') || form.getValues('status') === OutcomeInformationEnum.Pending) &&
      areRequiredFieldsFilled(approvalDropdownRequiredFields) &&
      suggestedStatus === ''
    ) {
      fetchStatus();
    }
  });

  useEffect(() => {
    if (user?.email) {
      if (!form.getValues('visitorEmailAddress')) {
        form.setValue('visitorEmailAddress', user?.email);
      }
    }
  }, [form, user]);

  async function onSubmit(
    data: z.infer<typeof OutcomeInformationSchema>,
    checkFieldsCompletion: any,
    navigate: () => void
  ) {
    try {
      setIsSubmitting(true);

      const isDataCompleted = checkFieldsCompletion(data);

      const payload = {
        homeVisitData: {
          visitDate: data.visitDate,
          visitTime: data.visitTime,
          visitorEmailAddress: data.visitorEmailAddress,
          doesProofOfAddressMatchLocation: data.doesProofOfAddressMatchLocation,
          characteristicsOfGarage: data.characteristicsOfGarage,
          behaviourOfCustomerDuringCall: data.behaviourOfCustomerDuringCall,
          houseInformation: {
            ...homeVisit.houseInformation,
            ownProperty:
              ownerAndRentedOptionsMapping[data.ownProperty as keyof typeof ownerAndRentedOptionsMapping],
          },
          comments: data.visitOutcomeInformation,
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            outcome: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
          status: smallFirstLetter(data.status),
          reasonOfRejection: data.reasonOfRejection,
          suggestedStatus: smallFirstLetter(suggestedStatus),
          statusReason: data.statusReason,
        },
        isHomeVisitData: true,
      };

      const isApprovedOrRejected =
        payload.homeVisitData.status === HomeVisitStatus.approved ||
        payload.homeVisitData.status === HomeVisitStatus.rejected;

      const areAllStepsCompleted = allStepsCompleted(payload.homeVisitData.homeVisitStepsStatus);

      payload.homeVisitData.status =
        areAllStepsCompleted && isApprovedOrRejected ? payload.homeVisitData.status : HomeVisitStatus.pending;

      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Outcome, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error: any) {
      toast({
        title: error.message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const outcomeOptions =
    isAnyStepIncomplete || !areRequiredFieldsFilled(approvalDropdownRequiredFields)
      ? outcomeInformationPendingOptions
      : outcomeInformationApprovedOrRejectedOptions;

  const isDataNotCompleted = (data: any) => {
    return Object.entries(data).every(([key, value]) => {
      if (key === 'statusReason') {
        return true;
      }
      return value !== '' && value !== NoSelected;
    });
  };

  const isDataCompleted = (data: any) => {
    return Object.entries(data).every(([key, value]) => {
      if (key === 'statusReason') {
        return true;
      }
      if (key === 'visitOutcomeInformation' && value === '') {
        throw new Error(translations.es.VisitInformationOutcomeErrorMessage);
      }
      if (key === 'reasonOfRejection') {
        if (data.status === OutcomeInformationEnum.Rejected && value === NoSelected) {
          throw new Error(translations.es.ReasonOfRejectionErrorMessage);
        }
        if (data.status === OutcomeInformationEnum.Approved) {
          return true;
        }
      }

      return value !== '' && value !== NoSelected;
    });
  };

  const proofOfAddressPath = documentsAnalysis?.documents?.find(
    (doc: any) => doc.type === AdmissionRequestDocumentType.proof_of_address
  )?.media?.url;

  return (
    <FormSection
      homeVisit={homeVisit}
      goToNext={form.handleSubmit((data) => onSubmit(data, isDataCompleted, naviteToClientDetailsPage))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      showFinishWithoutCompleting={isAnyStepIncomplete || !areRequiredFieldsFilled(finishFormRequiredFields)}
      finishWithoutCompleting={form.handleSubmit((data) =>
        onSubmit(data, isDataNotCompleted, naviteToClientDetailsPage)
      )}
    >
      <FormSectionHeader title={translations.es.VisitInformationOutcome} />
      <Form {...form}>
        <form>
          <div className="flex py-3 gap-4 w-3/6">
            <HookFormRadixUIField
              form={form}
              fieldName="visitDate"
              formLabel={translations.es.Date}
              Icon={LuCalendar}
              isDisabled={true}
            />
            <HookFormRadixUIField
              form={form}
              fieldName="visitTime"
              formLabel={translations.es.Time}
              Icon={LuClock5}
              isDisabled={true}
            />
          </div>
          <Separator />

          <HookFormRadixUIField
            form={form}
            fieldName="visitorEmailAddress"
            formLabel={translations.es.HomeVisitorInformationEmailAddress}
            className="w-3/6 py-2"
            isDisabled={true}
          />
          <HookFormRadixUIField
            form={form}
            fieldName="locationInformation"
            formLabel={translations.es.VerifiedAddress}
            isDisabled={true}
          />

          <div className="flex pb-8 gap-4 items-center">
            <div className=" w-3/6">
              <HookFormRadixUISelect
                control={form.control}
                fieldName="doesProofOfAddressMatchLocation"
                selectOptions={yesOrNoOptions}
                formLabel={translations.es.DoesTheProofOfAddressMatchTheLocation}
              />
            </div>
            <div className=" self-end">
              <ImageViewer imagePath={proofOfAddressPath}>
                <StepperButton
                  text={translations.es.ViewDocument}
                  variant={'outline'}
                  className={'border border-primaryPurple text-primaryPurple h-9'}
                  size={'sm'}
                />
              </ImageViewer>
            </div>
          </div>
          <HookFormRadixUIField
            form={form}
            fieldName="characteristicsOfGarage"
            formLabel={translations.es.WhatAreTheCharacteristicsOfTheGarage}
            className="w-3/6 pt-4 pb-2"
          />

          <div className="flex gap-4 py-2">
            <HookFormRadixUIField
              form={form}
              fieldName="behaviourOfCustomerDuringCall"
              formLabel={translations.es.WhatWasTheBehaviourOfTheCustomerDuringTheCall}
            />
            <HookFormRadixUISelect
              control={form.control}
              fieldName="ownProperty"
              selectOptions={ownedOrRentedOptions}
              formLabel={translations.es.IsThePropertyOwnedOrRented}
              className="h-6"
            />
          </div>

          <div className="flex gap-4 py-2">
            <div className={'w-3/6'}>
              <HookFormRadixUISelect
                control={form.control}
                fieldName="status"
                selectOptions={outcomeOptions}
                formLabel={
                  <div className="flex items-center gap-2">
                    <span>{translations.es.VisitOutcome}</span>
                    {suggestedStatus && (
                      <span
                        className={`text-sm px-2 py-0.5 rounded-full ${
                          suggestedStatus.toLowerCase() === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {`Sugerido: ${
                          outcomeInformationApprovedOrRejectedOptions.find(
                            (option) => option.value === capitalizeFirstLetter(suggestedStatus)
                          )?.label || suggestedStatus
                        }`}{' '}
                      </span>
                    )}
                  </div>
                }
                className="py-2"
                onChange={(value) => {
                  form.setValue('status', value);
                  if (suggestedStatus !== '') {
                    setShowStatusReason(value.toLowerCase() !== suggestedStatus.toLowerCase());
                  }
                }}
              />
            </div>

            {status
              ? smallFirstLetter(status) === HomeVisitStatus.rejected && (
                  <HookFormRadixUISelect
                    control={form.control}
                    fieldName="reasonOfRejection"
                    selectOptions={reasonOfRejectionOptions}
                    formLabel={translations.es.ReasonOfRejection}
                    className="w-3/6 gap-1 py-2"
                  />
                )
              : null}
          </div>

          <HookFormRadixUIField
            form={form}
            fieldName="visitOutcomeInformation"
            formLabel={translations.es.PleaseWriteTheVisitInformationOutcomeInBelow}
            type="textarea"
            className="w-4/6 py-2"
            placeholder={'comentarios de visitantes'}
          />

          {showStatusReason && (
            <HookFormRadixUIField
              form={form}
              fieldName="statusReason"
              formLabel={translations.es.ReasonForSelectingDifferentStatus}
              placeholder={'Introduce el motivo'}
              type="textarea"
              className="w-4/6 py-2"
            />
          )}
        </form>
      </Form>
    </FormSection>
  );
}
