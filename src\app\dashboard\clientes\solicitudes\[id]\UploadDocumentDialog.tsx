'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>lose<PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
  ModalContent,
  <PERSON>ton,
  ModalFooter,
  Box,
  Text,
  VStack,
  useToast,
} from '@chakra-ui/react';
import { FileUpload, Media } from '@/components/FileUpload/FileUpload';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useMemo, useState, useTransition, useCallback } from 'react';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { AdmissionRequestDocumentType } from '../enums';
import {
  FileUploadAcceptByMediaTypeCatalog,
  FileUploadMaxSizeByMediaTypeCatalog,
  FileUploadTotalFilesByMediaTypeCatalog,
  MediaTypeCatalog,
  RequestDocumentTypeTranslations,
} from '../data';

function titleDocumentType(type: AdmissionRequestDocumentType) {
  return RequestDocumentTypeTranslations[type] || type;
}

function mediaTypeFromDocumentType(type: AdmissionRequestDocumentType) {
  return MediaTypeCatalog[type];
}

export function UploadDocumentDialog({
  requestId,
  personalData,
  isAdminUpload = false,
}: {
  requestId: string;
  personalData: any;
  isAdminUpload?: boolean;
}) {
  const router = useRouter();
  const toast = useToast();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [media, setMedia] = useState<Media | Media[] | null>(null);
  const [isTransitioning, startTransition] = useTransition();
  const pathname = usePathname();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const isOpen = searchParams.get('dialog') === 'upload-document';
  const documentType = searchParams.get('documentType');
  const actionType = searchParams.get('actionType');
  const actionTitle = actionType === 'replace' ? 'Reemplazar' : 'Subir';
  const mediaType = mediaTypeFromDocumentType(documentType as AdmissionRequestDocumentType);
  const maxFileSizeMB = FileUploadMaxSizeByMediaTypeCatalog[mediaType] || 5;

  function onClose() {
    setMedia(null);
    router.back();
  }

  const handleUploadChange = useCallback(
    (mediaEntity: Media[]) => {
      if (isAdminUpload) {
        setMedia(mediaEntity);
      } else {
        setMedia(mediaEntity[0] || null);
      }
    },
    [isAdminUpload]
  );

  const isButtonDisabled = useMemo(() => {
    return isSubmitting || !media || isTransitioning;
  }, [isSubmitting, media, isTransitioning]);

  async function handleSubmit() {
    if (!user) return null;
    setIsSubmitting(true);
    const updates = Array.isArray(media)
      ? media.map((m) => ({
          type: documentType,
          mediaId: m.id,
        }))
      : [
          {
            type: documentType,
            mediaId: media?.id,
          },
        ];

    const url = new URL(`${URL_API}/admission/requests/${requestId}/documents`);

    url.searchParams.append('country', personalData?.country || 'mx');

    const res = await fetch(url.toString(), {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify({ updates }),
    });
    const response = await res.json();
    if (response && response.success) {
      startTransition(() => {
        toast({
          title: 'Documento actualizado',
          status: 'success',
        });
        router.replace(`${pathname}`, { scroll: false });
        router.refresh();
      });
    }
    setIsSubmitting(false);
    return response;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{actionTitle} documento</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Box minH={40} display="flex" alignItems="center" justifyContent="center">
            <VStack spacing={6} alignItems="center">
              <Text fontSize="sm" color="gray.500">
                {actionTitle} {titleDocumentType(documentType as AdmissionRequestDocumentType)}
              </Text>
              <FileUpload
                key={`file-upload-${documentType}`}
                id={`file-upload-${documentType}-${requestId}`}
                primaryButtonText="Subir Archivo"
                accept={FileUploadAcceptByMediaTypeCatalog[mediaType]}
                mediaType={mediaType}
                onUploadChange={handleUploadChange}
                {...(!isAdminUpload && { totalFiles: FileUploadTotalFilesByMediaTypeCatalog[mediaType] })}
                maxFileSize={1024 * 1024 * maxFileSizeMB}
              />
            </VStack>
          </Box>
        </ModalBody>
        <ModalFooter gap={3}>
          <Button
            sx={{
              color: '#5800F7',
              borderColor: '#5800F7 !important',
              border: '1px',
              h: '40px',
            }}
            onClick={onClose}
          >
            Cancelar
          </Button>
          <Button
            sx={{
              // bg: '#5800F7 !important',
              color: 'white',
              h: '40px',
            }}
            className={
              !isButtonDisabled
                ? 'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'
                : 'bg-gray-400 cursor-not-allowed'
            }
            type="submit"
            disabled={isButtonDisabled}
            isLoading={isSubmitting}
            onClick={handleSubmit}
          >
            {actionTitle}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}
