'use client';
// import DocumentDisplay from '@/components/DocumentDisplay';
// import { MdEmail, MdOutlineContentCopy } from 'react-icons/md';
// import { BsFillTelephoneFill } from 'react-icons/bs';
// import { RiWhatsappFill } from 'react-icons/ri';
// import { FaStar } from 'react-icons/fa';
// import { AiOutlineClose } from 'react-icons/ai';
import 'animate.css';
// import { useToast } from '@chakra-ui/react';
// import CustomModal from '@/components/Modals/CustomModal';
// import { format, parseISO } from 'date-fns';
import { useHandleDriverData } from '@/hooks/useDriverData';
// import ZoomImage from './others/ZoomImage';
import { SectionsAssociateModal } from './Detail/AssociateModalData';
import { useParams } from 'next/navigation';

export default function DriverDataHistoryModal() {
  // const toast = useToast();
  const { id: vehicleId }: { id: string } = useParams();
  const driverHook = useHandleDriverData();
  const { driver: props, carNumber } = useHandleDriverData();

  if (!props) return null;

  const position = (window.scrollY + 50).toString() + 'px';

  const props2 = {
    ...props,
    carNumber,
    vehicleId,
    onClose: driverHook.onClose,
    showSignDocs: true,
  };

  return (
    <>
      {driverHook.isOpen ? (
        <div
          className={`
            absolute top-0 left-0 z-[100] w-full h-full
            ${driverHook.isOpen ? 'visible' : 'invisible'} 
            bg-black 
            bg-opacity-50 
            flex 
            pt-[70px]
            justify-center 
            transition-opacity
            duration-500 
            ease-in-out
          `}
        >
          <div className={`absolute w-full flex justify-center `} style={{ top: `${position}` }}>
            <SectionsAssociateModal {...props2} />
          </div>
        </div>
      ) : null}
    </>
  );
}
