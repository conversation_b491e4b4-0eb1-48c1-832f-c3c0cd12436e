import { URL_API, PhysicalVehicleStatus } from '@/constants';
import axios from 'axios';

interface SuperadminPhysicalStatusUpdateResponse {
  success: boolean;
  newPhysicalStatus?: string;
  message: string;
}

export const updatePhysicalStatusAdmin = async ({
  vehicleId,
  newStatus,
  accessToken,
  notes,
}: {
  vehicleId: string;
  newStatus: PhysicalVehicleStatus;
  accessToken: string;
  notes?: string;
}): Promise<SuperadminPhysicalStatusUpdateResponse> => {
  try {
    const payload = {
      newPhysicalStatus: newStatus,
      notes: notes || '',
      isAdminCorrection: true,
    };

    const response = await axios.post(`${URL_API}/stock/${vehicleId}/admin-update-physical-status`, payload, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `bearer ${accessToken}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Error updating physical status (admin):', error);
    throw new Error(error.response?.data?.message || 'Failed to update physical status');
  }
};
