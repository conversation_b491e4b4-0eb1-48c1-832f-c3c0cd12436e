import Image from 'next/image';
import Login from '@/components/LoginComponent';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';

export const metadata = {
  title: 'Iniciar Sesión',
};

export default async function Home() {
  const user = await getCurrentUser();
  if (user?.accessToken) {
    redirect('/dashboard');
  }
  return (
    // <main className="w-full h-[100vh] relative">
    <main className="w-full h-[100vh] relative bg-[#FAFAFF] overflow-hidden ">
      {/* <Image alt="background" src="/images/loginBackground.png" fill className="object-cover absolute z-0" /> */}
      <div className="w-full h-[100vh] flex flex-col justify-center items-center relative z-10 gap-4">
        <div
          className="
            w-[158px] h-[158px]
            border-[#9E8EFF]
            border-[20px]
            absolute
            rounded-full
            z-10
            top-[10vh]
            left-[-79px]
          "
        />

        <div className=" w-[297px] h-[140px] ">
          <Image alt="logo" width="1000" height="1000" priority src="/images/Logo.png" />
        </div>
        <div className="relative w-full flex justify-center">
          <div
            className="
                  w-[70px] h-[70px]
                  border-[15px]
                  border-[#47EB84BD]
                  absolute
                  rounded-full
                  z-0
                  top-[-35px]
                  right-[-35px]
                  "
          />
          <div
            className="
                w-full
               
                sm:w-3/4 
                md:w-1/4 md:min-w-[600px]
                bg-white 
                z-20
                relative
                min-h-[200px]
                h-[max-content]
                rounded 
                flex flex-col 
                items-center
                px-8
                pt-8
                pb-6
                text-black 
                gap-4
                text-[14px]
                border-[#EAECEE]
                border-[1px] 
                "
          >
            <Suspense fallback={<div>Loading...</div>}>
              <Login />
            </Suspense>
          </div>
        </div>
      </div>
      <div
        className="
            w-[155px] h-[155px]
            bg-[#47EB84]
            absolute
            rounded-full
            z-0
            bottom-[-40px]
            left-[-35px]
          "
      />

      <div
        className="
            w-[70px] h-[70px]
            border-[#9E8EFF]
            absolute
            rounded-full
            z-0
            bottom-[45px]
            right-[26px]
            border-[15px]
          "
      />
    </main>
  );
}
