import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField, HookFormRadixUISelect } from '../HookFormField';
import { FormSection } from '../FormSection';
import { FormSectionHeader } from '../FormHeaderSection';
import { HomeVisitStepsStatus, YesOrNoOptions, yesOrNoOptions } from '@/constants';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { NoSelected, Steps, toastConfigs } from '.';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

const DebtInformationSchema = z.object({
  ownDebt: z.string(),
  outStandingDebt: z.string(),
  doesDebtAffectPersonalFinance: z.string(),
});

export default function DebtInformation(props: any) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, personalData, homeVisit } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof DebtInformationSchema>>({
    resolver: zodResolver(DebtInformationSchema),
    defaultValues: {
      ownDebt: personalData.ownDebt || NoSelected,
      outStandingDebt: personalData.outStandingDebt || '',
      doesDebtAffectPersonalFinance: personalData.doesDebtAffectPersonalFinance || NoSelected,
    },
  });

  const ownDebt = form.watch('ownDebt');

  async function onSubmit(data: z.infer<typeof DebtInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);
      const isDataCompleted = Object.entries(data).every(([, value]) => {
        if (data.ownDebt === YesOrNoOptions.No) {
          return true;
        }
        return value !== '' && value !== NoSelected;
      });

      const payload = {
        personalData: {
          ownDebt: data.ownDebt,
          outStandingDebt: data.outStandingDebt,
          doesDebtAffectPersonalFinance: data.doesDebtAffectPersonalFinance,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            debt: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Debt, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Debt, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.DebtInformation} />
      <Form {...form}>
        <form>
          <div className="w-3/6 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="ownDebt"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.DoYouHaveAnyDebt}
            />
            {ownDebt === YesOrNoOptions.Yes && (
              <>
                <HookFormRadixUIField
                  form={form}
                  fieldName="outStandingDebt"
                  formLabel={translations.es.WhereDoYouHaveOutstandingDebt}
                  className="py-2"
                />
                <HookFormRadixUISelect
                  control={form.control}
                  fieldName="doesDebtAffectPersonalFinance"
                  selectOptions={yesOrNoOptions}
                  formLabel={translations.es.DoesYourDebtSignificantlyAffectYourPersonalFinance}
                  className="py-2"
                />
              </>
            )}
          </div>
        </form>
      </Form>
    </FormSection>
  );
}
