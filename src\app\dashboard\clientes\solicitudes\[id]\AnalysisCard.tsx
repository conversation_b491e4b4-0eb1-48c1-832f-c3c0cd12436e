'use client';
import { <PERSON>, CardBody, CardHeader, Heading, Box, Stack, Text, Link } from '@chakra-ui/react';
import { <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';
import { QueryLink } from '@/components/QueryLink';
import { useCountry } from './detail';
import EmptyState from '@/components/EmptyState';

const getColorByCategory = (category: string) => {
  switch (category) {
    case 'low':
      return '#29CC4D';
    case 'medium':
      return '#FFAB00';
    case 'high':
      return '#E14942';
    default:
      return '#EAECEE';
  }
};

const calculateCompleteChartData = (score: number, category: string) => {
  return [
    { name: 'Score', value: score, color: getColorByCategory(category) },
    { name: 'Empty', value: 100 - score, color: '#EAECEE' },
  ];
};

export interface RiskAnalysisCardProps {
  title: string;
  numericScore: number;
  category: string;
  seeDetailsText?: string;
  isPending?: boolean;
  pendingText?: string;
}

export default function AnalysisCard({
  title,
  numericScore,
  category,
  seeDetailsText,
  isPending,
  pendingText,
}: RiskAnalysisCardProps) {
  const { isCountryUSA } = useCountry();

  const pendingAnalysisText = isCountryUSA ? 'Pending analysis' : 'Análisis pendiente';

  const chartData = calculateCompleteChartData(numericScore, category);
  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Heading size="md" color="gray.600">
          {title}
        </Heading>
      </CardHeader>
      <CardBody>
        <Box px={4} pb={4}>
          <Stack spacing={1} align="center">
            {isPending ? (
              <EmptyState title={pendingText ?? pendingAnalysisText} minH={48} />
            ) : (
              <div className="mb-2">
                <PieChart width={240} height={140}>
                  <Pie
                    dataKey="value"
                    startAngle={180}
                    endAngle={0}
                    data={chartData}
                    cx={120}
                    cy={120}
                    innerRadius={70}
                    outerRadius={100}
                    stroke="none"
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <foreignObject x={75} y={80} width={100} height={50}>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '100%',
                      }}
                    >
                      <Text fontSize="24" fontWeight="semibold" color={getColorByCategory(category)}>
                        {numericScore}%
                      </Text>
                      <Text fontSize="12" color="gray.600">
                        {category.toUpperCase()} RISK
                      </Text>
                    </div>
                  </foreignObject>
                </PieChart>
              </div>
            )}
            {seeDetailsText && (
              <QueryLink query={{ dialog: 'view-model-scores' }}>
                <Link
                  fontSize="sm"
                  color="purple.500"
                  sx={{
                    textDecoration: 'underline',
                    '&:hover': {
                      textDecoration: 'none',
                    },
                  }}
                >
                  {seeDetailsText}
                </Link>
              </QueryLink>
            )}
          </Stack>
        </Box>
      </CardBody>
    </Card>
  );
}
