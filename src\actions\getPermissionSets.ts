import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';
import { PermissionSetResponse } from './getPermissionSetById';

const getPermissionSets = cache(async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/permissionSet/getAllPermissionSets`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response?.data?.permissionSets as PermissionSetResponse[];
  } catch (error: any) {
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getPermissionSets;
