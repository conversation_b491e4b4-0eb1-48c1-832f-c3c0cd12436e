'use client';
import {
  ColumnDef,
  TableOptions,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ColumnType } from './types';
// import CellAction from './CellAction';

type TableProps<TData extends Record<string, unknown>, TColumns extends ColumnType[]> = {
  data: TData[];
  columns: ColumnDef<TColumns>[];
};

export default function Table<TData extends Record<string, unknown>, TColumns extends ColumnType[]>({
  data,
  columns,
}: TableProps<TData, TColumns>) {
  const table = useReactTable<TData>(
    {
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
    } as TableOptions<TData> // Proporciona explícitamente los tipos aquí
  );

  return (
    <table className="w-full h-[max-content] caption-bottom text-sm divide-y divide-gray-200">
      <thead className="[&_tr]:border-b">
        {table.getHeaderGroups().map((headerGroup) => (
          <tr
            key={headerGroup.id}
            className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
          >
            {headerGroup.headers.map((header) => {
              return (
                <th
                  key={header.id}
                  className="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0"
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </th>
              );
            })}
          </tr>
        ))}
      </thead>
      <tbody className="[&_tr:last-child]:border-0">
        {table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => {
            // console.log(row, index, rows);
            return (
              <tr
                className="even:bg-transparent odd:bg-gray-200 "
                // className={`${
                //   index === rows.length - 1 ? 'rounded-lg' : '' // Aplicar estilo solo a la última fila
                // } even:bg-transparent odd:bg-gray-200 `}
                key={row.id}
                data-state={row.getIsSelected() && 'selected'}
              >
                {row.getVisibleCells().map((cell, i, cells) => (
                  <td
                    key={cell.id}
                    className={`
                    ${i === cells.length - 1 && 'rounded-r-lg'} 
                    ${i === 0 && 'rounded-l-lg'}
                    py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0`}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            );
          })
        ) : (
          <tr>
            <td colSpan={columns.length} className="h-24 text-center">
              No hay resultados.
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
