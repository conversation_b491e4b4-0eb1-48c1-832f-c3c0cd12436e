import { FormControl, Image } from '@chakra-ui/react';
import { ErrorMessage, useFormikContext } from 'formik';
import { CONTRACT_REGIONS, Countries, US_CITIES, US_STATES_OPTIONS } from '@/constants';
import CustomRadio from '@/components/ui/radioButton';
import SelectInput from '@/components/Inputs/SelectInput';
import { useState } from 'react';
import axios from 'axios';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { URL_API } from '@/constants';

interface LocationSelectorProps {
  onCountrySelect: (country: Countries) => void;
  isSuperAdminOrAdmin?: boolean;
  allowedRegions?: Array<{ value: string; label: string }>;
  setRegion: (region: string) => void;
  onContractNumberUpdate: (number: string) => void;
  translations: any;
}

const LocationSelector = ({
  onCountrySelect,
  isSuperAdminOrAdmin,
  allowedRegions,
  setRegion,
  onContractNumberUpdate,
  translations,
}: LocationSelectorProps) => {
  const { setFieldValue } = useFormikContext<any>();
  const [selectedCountry, setSelectedCountry] = useState<Countries | null>(null);
  const { user: currentUser } = useCurrentUser();

  const handleRegionSelect = async (region: string) => {
    if (!currentUser) return;

    try {
      // Make sure country is set when selecting a region
      if (!selectedCountry) {
        handleCountrySelect(Countries.Mexico);
      }

      const regionNumber =
        selectedCountry === Countries['United States']
          ? US_CITIES.find((city) => city.value === region)?.number
          : CONTRACT_REGIONS.find((r) => r.value === region)?.number;

      if (regionNumber) {
        // Make sure we set the region for parent component
        setRegion(region);

        try {
          const response = await axios.post(
            `${URL_API}/contract/next`,
            { region: regionNumber },
            {
              headers: {
                Authorization: `Bearer ${currentUser.accessToken}`,
              },
            }
          );
          onContractNumberUpdate(response.data.alias);
        } catch (contractError) {
          // Even if contract fetch fails, we should still have the region set
          console.error('Error fetching contract number:', contractError);
        }
      }
    } catch (error) {
      // Silent fail, region selection might not be critical path
    }
  };

  const handleCountrySelect = (country: Countries) => {
    // First immediately update local state
    setSelectedCountry(country);

    // Then update form values
    setFieldValue('country', {
      label: country === Countries['United States'] ? 'USA' : 'MX',
      value: country,
    });
    setFieldValue('state', null);
    setFieldValue('vehicleState', null);

    // Finally notify parent component to update its state immediately
    onCountrySelect(country);
  };

  return (
    <FormControl>
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-around space-around">
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => handleCountrySelect(Countries['United States'])}
          >
            <CustomRadio
              checked={selectedCountry === Countries['United States']}
              onChange={() => handleCountrySelect(Countries['United States'])}
            />
            <Image src="/images/flags/USA.svg" alt="USA Flag" className="w-8 h-6 object-cover" />
            <span className="font-medium">USA</span>
          </div>
          <div
            className="flex items-center gap-2 cursor-pointer"
            onClick={() => handleCountrySelect(Countries.Mexico)}
          >
            <CustomRadio
              checked={selectedCountry === Countries.Mexico}
              onChange={() => handleCountrySelect(Countries.Mexico)}
            />
            <Image src="/images/flags/Mexico.svg" alt="Mexico Flag" className="w-8 h-6 object-cover" />
            <span className="font-medium">Mexico</span>
          </div>
        </div>

        {selectedCountry && (
          <div className="mt-4">
            <h4 className="text-md font-medium mb-2">{translations.selectRegion}</h4>
            {selectedCountry === Countries.Mexico ? (
              <SelectInput
                name="vehicleState"
                label="Región"
                options={isSuperAdminOrAdmin ? CONTRACT_REGIONS : allowedRegions || []}
                onChange={(option) => {
                  setFieldValue('vehicleState', option);
                  handleRegionSelect(option.value);
                }}
                dataCy="region-selector"
                key="mexico-region-selector" // Add key to force re-render
              />
            ) : (
              <div className="grid grid-cols-1 gap-4">
                <SelectInput
                  name="state"
                  label="State"
                  options={US_STATES_OPTIONS}
                  onChange={(option) => {
                    setFieldValue('state', option);
                  }}
                  dataCy="state-selector"
                  placeholder={'Select'}
                  key="us-state-selector" // Add key to force re-render
                />
                <SelectInput
                  name="vehicleState"
                  label="City"
                  options={US_CITIES}
                  onChange={(option) => {
                    setFieldValue('vehicleState', option);
                    handleRegionSelect(option.value);
                  }}
                  dataCy="region-selector"
                  placeholder={'Select'}
                  key="us-city-selector" // Add key to force re-render
                />
              </div>
            )}
          </div>
        )}
      </div>
      <div className="text-red-500 mt-2">
        <ErrorMessage name="country" />
      </div>
    </FormControl>
  );
};

export default LocationSelector;
