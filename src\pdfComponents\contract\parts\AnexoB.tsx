/* eslint-disable @typescript-eslint/no-use-before-define */
import React from 'react';
import { Text, StyleSheet, View } from '@react-pdf/renderer';
import { useMemo } from 'react';
import { getYear, parseISO } from 'date-fns';
import moment from 'moment';

const styles = StyleSheet.create({
  body: {
    flexDirection: 'column',
    rowGap: 1,
  },
  anexoTitle: {
    textAlign: 'center',
    color: '#6210FF',
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    textAlign: 'right',
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
  },

  anexoText: {
    fontSize: '11px',
    lineHeight: '2px',
  },

  bulletPoint: {
    width: 14,
    fontSize: 14,
  },

  containerS: {
    marginTop: '4vh',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },

  content: {
    width: '35%',
    flexDirection: 'column',
    alignItems: 'center',
    rowGap: 5,
  },
  names: {
    fontSize: 11,
    textAlign: 'center',
  },
});

interface AnexoBProps {
  city: string;
  date: string;
  firstName: string;
  lastName: string;
  brand: string;
  model: string;
  version: string;
  vin: string;
  policyNumber: string;
  plates: string;
  km: string;
  circulationCardNumber: number;
  newCar: boolean;
}

const AnexoB = ({ city, date, ...rest }: AnexoBProps) => {
  const cityAndDateText = useMemo(() => {
    return getCityAnexoB(city, date);
  }, [city, date]);

  const year = getYear(parseISO(date));

  return (
    <View style={styles.body} break>
      <View>
        <Text style={styles.anexoTitle}>Anexo B</Text>
        <Text style={styles.anexoTitle}>Constancia de entrega </Text>

        <Text style={styles.anexoSubTitle}>
          {cityAndDateText} {year}.
        </Text>
      </View>

      <View>
        <Text style={styles.anexoText}>
          Por medio de la presente hago constatar que yo {rest.firstName} {rest.lastName} recibi el vehiculo
          de la marca {rest.brand} modelo {rest.model} y version {rest.version} con numero de serie {rest.vin}{' '}
          en conjunto con los siguientes documentos del auto:
        </Text>
      </View>

      <View>
        <Text style={styles.anexoText}>
          <Text style={styles.bulletPoint}>•</Text> Póliza de seguro con número {rest.policyNumber}.
        </Text>
      </View>

      <View>
        <Text style={styles.anexoText}>
          <Text style={styles.bulletPoint}>•</Text>Placas con número {rest.plates} del estado / ciudad de{' '}
          {getCity(city)}.
        </Text>
      </View>

      <View>
        <Text style={styles.anexoText}>
          <Text style={styles.bulletPoint}>•</Text> Tarjeta de circulación con numero{' '}
          {rest.circulationCardNumber}.
        </Text>
      </View>

      {/* <View>
        <Text style={styles.anexoText}>
          <Text style={styles.bulletPoint}>•</Text> Folio de pago de verificación __________________. (en
          ocasión)
        </Text>
      </View> */}

      <Text style={styles.anexoText}>
        El auto se entregó con {rest.newCar ? 0 : rest.km} km, desde los cuales empezará a correr el
        kilometraje incluido en mi contrato.
      </Text>

      <Text style={styles.anexoTitle}>Anomalia, golpe o falla</Text>
      <Text style={styles.anexoText}>
        Pieza o componente dañado: __________________________________________________ Descripción:
        ________________________________________________________________
        ___________________________________________________________________________ *en caso de que este
        apartado no contenga alguna información o descripción es debido a que el auto se encuentra en
        perfectas condiciones Durante el proceso de entrega del auto, el asesor realizó el inventario del
        automóvil con fotografías de todos los componentes y partes del vehículo; Por lo que recibo el auto
        tal y como lo evidencian las imágenes y esté formato de validación de entrega.
      </Text>

      <View style={styles.containerS}>
        <View style={styles.content}>
          <Text style={styles.names}>____________________________</Text>
          <Text style={styles.names}>Nombre y firma del cliente</Text>
        </View>

        <View style={styles.content}>
          <Text style={styles.names}>____________________________</Text>
          <Text style={styles.names}>Asesor responsable</Text>
        </View>
      </View>
    </View>
  );
};

export default AnexoB;

// ENCONTRAR DICCIONARIO ANEXO B

const months = [
  'Enero',
  'Febrero',
  'Marzo',
  'Abril',
  'Mayo',
  'Junio',
  'Julio',
  'Agosto',
  'Septiembre',
  'Octubre',
  'Noviembre',
  'Diciembre',
];
function getCityAnexoB(city: string, date: string) {
  const cityLowerCase = city.toLowerCase();

  // const monthText = useMemo(() => {
  const month = moment(date).month();
  const monthText = months[month];

  const day = moment(date).format('DD');

  const cities: { [key: string]: string } = {
    cdmx: `Ciudad de México, ${day} de mes de ${monthText} del`,
    edomx: `Ciudad de México, ${day} de mes de ${monthText} del`,
    gdl: `Jalisco, ${day} de mes de ${monthText} del`,
    ptv: `Jalisco, ${day} de mes de ${monthText} del`,
    mty: `Estado de Nuevo León, ${day} de mes de ${monthText} del`,
    tij: `Baja California, ${day} de mes de ${monthText} del`,
    qro: `Queretaro, ${day} de mes de ${monthText} del`,
    pbe: `Puebla, ${day} de mes de ${monthText} del`,
    mxli: `Mexicali, ${day} de mes de ${monthText} del`,
    slp: `San Luis Potosí, ${day} de mes de ${monthText} del`,
    mer: `Yucatán, ${day} de mes de ${monthText} del`,
    sal: `Coahuila, ${day} de mes de ${monthText}del`,
    her: `Sonora, ${day} de mes de ${monthText} del`,
    ags: `Aguascalientes, ${day} de mes de ${monthText} del`,
    leo: `Guanajuato, ${day} de mes de ${monthText} del`,
    torr: `Coahuila, ${day} de mes de ${monthText} del`,
    chi: `Chihuahua, ${day} de mes de ${monthText} del`,
  };

  if (cityLowerCase in cities) {
    let text = cities[cityLowerCase].replace(/\s+/g, ' ').trim();
    return text;
  } else {
    return 'Lo siento, no tenemos información sobre esa ciudad.';
  }
}

const cities: Record<string, string> = {
  cdmx: 'México',
  edomx: 'México',
  gdl: 'Guadalajara',
  ptv: 'Puerto Vallarta',
  mty: 'Monterrey',
  tij: 'Tijuana',
  qro: 'Querétaro',
  pbe: 'Puebla',
  mxli: 'Mexicali',
  slp: 'San Luis Potosí',
  mer: 'Mérida',
  sal: 'Saltillo',
  her: 'Hermosillo',
  ags: 'Aguascalientes',
  leo: 'León',
  torr: 'Torreón',
  chi: 'Chihuahua',
};

function getCity(city: string) {
  return cities[city];
}
