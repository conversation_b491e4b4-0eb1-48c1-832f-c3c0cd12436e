import React from 'react';
import { Box, Text, RadioGroup, VStack, Radio } from '@chakra-ui/react';
import { PhysicalVehicleStatus } from '@/constants';

interface StatusSelectionProps {
  options: { value: PhysicalVehicleStatus; label: string }[];
  selectedValue: PhysicalVehicleStatus | null;
  onChange: (value: PhysicalVehicleStatus) => void;
  getOptionLabel: (
    value: PhysicalVehicleStatus,
    countryValue: string,
    currentStatus: PhysicalVehicleStatus | string | null
  ) => string;
  currentStatus: PhysicalVehicleStatus | string | null;
  countryValue: string;
  translatedText: any;
  colorScheme: string;
  borderColor: string;
  textColor: string;
}

const StatusSelection: React.FC<StatusSelectionProps> = ({
  options,
  selectedValue,
  onChange,
  getOptionLabel,
  currentStatus,
  countryValue,
  translatedText,
  colorScheme,
  borderColor,
  textColor,
}) => (
  <Box mt={2} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
    <Text color={textColor} mb={3} fontWeight="medium">
      {translatedText.selectNextStatus}
    </Text>
    <RadioGroup
      onChange={(val) => onChange(val as PhysicalVehicleStatus)}
      value={selectedValue || undefined}
      colorScheme={colorScheme}
    >
      <VStack spacing={3} align="stretch">
        {options.map((option) => (
          <Radio key={option.value} value={option.value} colorScheme={colorScheme} size="md">
            {getOptionLabel(option.value, countryValue, currentStatus)}
          </Radio>
        ))}
      </VStack>
    </RadioGroup>
  </Box>
);

export default StatusSelection;
