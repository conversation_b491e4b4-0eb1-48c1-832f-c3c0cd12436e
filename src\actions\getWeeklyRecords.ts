import { URL_API } from '@/constants';
import { cache } from 'react';
import getCurrentUser from './getCurrentUser';
import axios from 'axios';

export interface WeeklyDate {
  _id: string;
  startDate: string;
  weekNumber: number;
  year: number;
}

export interface WeeklyRecord {
  _id: string;
  endDate: string;
  startDate: string;
  statusStatistics: any;
  stockVehicles: any[];
  weekNumber: number;
  year: number;
}

interface WeeklyRecordDatesParams {
  startDate?: string;
}

export const getWeeklyRecordDatesServer = cache(async (query?: WeeklyRecordDatesParams) => {
  const user = await getCurrentUser();
  if (!user) return null;

  const baseUrl = new URL(`${URL_API}/weekly-records/dates-available`);

  if (query?.startDate) baseUrl.searchParams.append('startDate', query.startDate);
  // if (query?.weekNumber) baseUrl.searchParams.append('weekNumber', query.weekNumber.toString());
  // if (query?.year) baseUrl.searchParams.append('year', query.year.toString());

  const url = baseUrl.toString();

  try {
    const res = await axios(url, {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
      },
    });
    return res.data.weeklyDates as WeeklyDate[];
  } catch (error) {
    return null;
  }
});

export const getWeeklyRecords = cache(async () => {
  const user = await getCurrentUser();
  if (!user) return null;

  const baseUrl = new URL(`${URL_API}/weekly-records`);

  const url = baseUrl.toString();

  try {
    const res = await axios(url, {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
      },
    });
    return res.data.weeklyRecords as WeeklyRecord;
  } catch (error) {
    return null;
  }
});
