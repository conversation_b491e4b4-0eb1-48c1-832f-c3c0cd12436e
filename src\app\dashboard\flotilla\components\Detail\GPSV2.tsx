/* eslint-disable consistent-return */
'use client';
import { useState, useEffect } from 'react';
import axios from 'axios';
import Spinner from '@/components/Loading/Spinner';
import { useToast } from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Capabilities, Sections, Subsections, URL_API } from '@/constants';
import PrimaryButton from '@/components/PrimaryButton';
import { useParams, useRouter } from 'next/navigation';
import RequiredBadge from '../others/RequiredBadge';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
interface GPSProps {
  gpsNumber: string;
  gpsSerie: string;
  gpsInstalled?: boolean;
  isCarBlocked: boolean;
}

export default function GPSV2({ gpsNumber, gpsInstalled }: GPSProps) {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [gpsStatus, setGpsStatus] = useState(false);
  const [isCarBlocked, setCarBlocked] = useState(false);
  const { user } = useCurrentUser();
  const router = useRouter();
  const { id } = useParams();

  const ability = usePermissions();
  const canEditGPS = canPerform(ability, Capabilities.EditGPS, Sections.Fleet, Subsections.General);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await axios.get(`${URL_API}/gps/status/${gpsNumber}`, {
          headers: {
            Authorization: `bearer ${user.accessToken}`,
          },
        });
        setGpsStatus(response.data.message.split(' ')[1]);
        if (response.data.message.split(' ')[1] === 'bloqueado') {
          setCarBlocked(true);
        }
      } catch (error) {
        console.error(error);
      }
    }
    if (gpsNumber) fetchData();
  }, []);

  const onSubmitBlock = async (gps: String, comando: String) => {
    setIsLoading(true);
    const form = {
      historyData: {
        userId: user._id,
        step: 'GPS',
        description: isCarBlocked ? 'Vehículo desbloqueado' : 'Vehículo bloqueado', // Esto se actualizará para mostrar que cambio exacto se hizo
      },
    };
    const response = await axios
      .post(
        `${URL_API}/gps`,
        { ...form, gps, comando },
        {
          headers: {
            Authorization: `bearer ${user.accessToken}`,
          },
        }
      )
      .then(() => {
        toast({
          title: 'GPS',
          position: 'top',
          description: 'Se ha bloqueado el vehículo',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });

        setTimeout(() => {
          window.location.reload();
        }, 2500);
      })
      .catch((err) => {
        console.error(err);
        toast({
          title: 'Error',
          position: 'top',
          description: err.response.data.error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      })
      .finally(() => setIsLoading(false));
    return response;
  };

  // customClass: '',

  const confirmBlock = (comand: `bloqueo` | 'desbloqueo') => {
    if (user.role !== 'superadmin')
      return toast({
        title: 'Error',
        position: 'top',
        description: 'No tienes permisos para realizar esta acción',
        status: 'info',
        duration: 5000,
        isClosable: true,
      });
    return Swal.fire({
      text: `El ${comand} del GPS requiere una doble confirmación`,
      input: 'password',
      icon: 'info',
      inputAttributes: {
        autocapitalize: 'off',
        autocorrect: 'off',
      },
      inputPlaceholder: 'Ingresa tu contraseña de administrador',
      customClass: {
        icon: ' !text-primaryPurple !border-primaryPurple  ',
        confirmButton: '!bg-primaryPurple',
      },
      reverseButtons: true,
      confirmButtonText: 'Confirmar',
      cancelButtonText: 'Cancelar',
      showCancelButton: true,
      showCloseButton: true,
      showLoaderOnConfirm: true,
      preConfirm: async (text) => {
        try {
          const urlFetch = `${URL_API}/user/verifyPassword/${user._id}`;

          try {
            const responseX = await axios.post(
              urlFetch,
              { password: text },
              { headers: { Authorization: `Bearer ${user.accessToken}` } }
            );

            return responseX.data;
          } catch (error: any) {
            return Swal.showValidationMessage(`${error.response.data.message || 'Hubo un error'}`);
          }
        } catch (error) {
          Swal.showValidationMessage(`
            Request failed: ${error}
          `);
        }
      },
      allowOutsideClick: () => !Swal.isLoading(),
    }).then((result) => {
      if (result.isConfirmed) {
        Swal.fire({
          title: `Bloquear auto`,
          text: `¿Estás seguro de ${comand} el auto?`,
          confirmButtonText: 'Confirmar',
          cancelButtonText: 'Cancelar',
          customClass: {
            confirmButton: '!bg-primaryPurple',
          },
          reverseButtons: true,
          showCancelButton: true,
          showCloseButton: true,
        }).then(async (res) => {
          if (res.isConfirmed) {
            if (gpsNumber) {
              if (comand === 'bloqueo') {
                onSubmitBlock(gpsNumber, 'bloqueo');
              } else {
                onSubmitBlock(gpsNumber, 'desbloqueo');
              }
              // try {
              //   Swal.fire({
              //     title: 'Vehiculo bloqueado!',
              //     text: 'Bloqueo exitoso.',
              //     icon: 'success',
              //   });
              // } catch (error) {
              //   Swal.fire({
              //     title: 'Hubo un error!',
              //     text: 'El vehiculo no se pudo bloquear exitosamente.',
              //     icon: 'error',
              //   });
              // }
            }
          }
        });
      }
    });
  };

  if (isLoading) return <Spinner />;

  return (
    <div
      className="
        w-[316px]
        bg-[white]
        flex flex-col
        gap-3
        py-[25px]
        px-[20px]
        border-[1px]
        border-[#EAECEE]
        font-bold
        rounded"
    >
      {gpsInstalled ? (
        <>
          <div className="flex justify-between">
            <p className="font-bold text-[24px] ">GPS</p>
          </div>
          <div className="flex flex-col gap-3 text-[18px]">
            <p>{gpsNumber}</p>
            <div className="flex justify-between text-[16px]">
              <p className="font-normal">El vehiculo se encuentra</p>
              <p>{gpsStatus ? gpsStatus : 'Cargando datos ...'}</p>
            </div>
            {isCarBlocked ? (
              <button onClick={() => confirmBlock('desbloqueo')}>
                {gpsStatus && (
                  <p className="text-[#5800F7] border-[1px] border-[#5800F7] rounded p-1">
                    Desbloquear vehículo
                  </p>
                )}
              </button>
            ) : (
              <button onClick={() => confirmBlock('bloqueo')}>
                {gpsStatus && (
                  <p className="text-red-500 border-[1px] border-red-500 rounded p-1">Bloquear vehículo</p>
                )}
              </button>
            )}
          </div>
        </>
      ) : (
        <>
          <div className="flex justify-between items-center">
            <p className="font-bold text-[24px] ">GPS</p>
            <RequiredBadge />
          </div>
          <p>No hay GPS instalado</p>
          {canEditGPS && (
            <PrimaryButton
              onClick={async () => {
                try {
                  const response = await axios.patch(`${URL_API}/stock/gps-installation/${id}`, null, {
                    headers: {
                      Authorization: `Bearer ${user.accessToken}`,
                    },
                  });

                  toast({
                    title: response.data.message || 'GPS Instalado',
                    position: 'top',
                    description: 'Actualizando pagina',
                    status: 'success',
                    duration: 3000,
                    isClosable: true,
                  });

                  setTimeout(() => {
                    router.refresh();
                  }, 3000);
                } catch (error: any) {
                  toast({
                    title: 'Error al instalar GPS',
                    position: 'top',
                    description: error.response.data.error.message || 'Hubo un error',
                    status: 'error',
                    duration: 5000,
                    isClosable: true,
                  });

                  console.error(error);
                }
              }}
            >
              Instalación realizada
            </PrimaryButton>
          )}
        </>
      )}
    </div>
  );
}
