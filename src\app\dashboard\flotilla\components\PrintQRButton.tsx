'use client';
import { useDisclosure, useToast } from '@chakra-ui/react';
import { useState, useEffect, useMemo } from 'react';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';
import { generateMultipleQRCodesPDF } from '@/utils/pdfGenerator';
import getVehiclesWithQR from '@/actions/getVehiclesWithQR';
import VehicleSelectionModal from './Modals/VehicleSelectionModal';
import VehiclePreviewModal from './Modals/VehiclePreviewModal';
import VehicleTableRow from './VehicleTableRow';

export default function PrintQRButton() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const [vehicles, setVehicles] = useState<VehicleWithQR[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedVehicles, setSelectedVehicles] = useState<Set<string>>(new Set());
  const [showPreview, setShowPreview] = useState(false);
  const [generatingPDF, setGeneratingPDF] = useState(false);

  // Load vehicles when modal opens
  useEffect(() => {
    if (isOpen && vehicles.length === 0) {
      loadVehicles();
    }
  }, [isOpen]);

  const loadVehicles = async () => {
    setLoading(true);
    try {
      const vehiclesData = await getVehiclesWithQR();
      setVehicles(vehiclesData);
    } catch (error) {
      console.error('Error loading vehicles:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron cargar los vehículos',
        status: 'error',
        duration: 3000,
        position: 'top',
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter vehicles based on search query
  const filteredVehicles = useMemo(() => {
    if (!searchQuery.trim()) return vehicles;

    const query = searchQuery.toLowerCase();
    return vehicles.filter(
      (vehicle) =>
        `${vehicle.brand} ${vehicle.model}`.toLowerCase().includes(query) ||
        vehicle.vin.toLowerCase().includes(query) ||
        vehicle.carNumber.toLowerCase().includes(query)
    );
  }, [vehicles, searchQuery]);

  // Get selected vehicles data
  const selectedVehiclesData = useMemo(() => {
    return vehicles.filter((vehicle) => selectedVehicles.has(vehicle._id));
  }, [vehicles, selectedVehicles]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedVehicles(new Set(filteredVehicles.map((v) => v._id)));
    } else {
      setSelectedVehicles(new Set());
    }
  };

  const handleSelectVehicle = (vehicleId: string, checked: boolean) => {
    const newSelected = new Set(selectedVehicles);
    if (checked) {
      newSelected.add(vehicleId);
    } else {
      newSelected.delete(vehicleId);
    }
    setSelectedVehicles(newSelected);
  };

  const handlePreview = () => {
    if (selectedVehicles.size === 0) {
      toast({
        title: 'Ningún vehículo seleccionado',
        description: 'Por favor selecciona al menos un vehículo para previsualizar',
        status: 'warning',
        duration: 3000,
        position: 'top',
      });
      return;
    }
    setShowPreview(true);
  };

  const handlePrint = async () => {
    if (selectedVehicles.size === 0) {
      toast({
        title: 'Ningún vehículo seleccionado',
        description: 'Por favor selecciona al menos un vehículo para imprimir',
        status: 'warning',
        duration: 3000,
        position: 'top',
      });
      return;
    }

    setGeneratingPDF(true);
    try {
      toast({
        title: 'Generando PDF...',
        description: `Generando PDF con ${selectedVehicles.size} códigos QR`,
        status: 'info',
        duration: 3000,
        position: 'top',
      });
      await generateMultipleQRCodesPDF(selectedVehiclesData);
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'Error',
        description: 'No se pudo generar el PDF',
        status: 'error',
        duration: 3000,
        position: 'top',
      });
    } finally {
      setGeneratingPDF(false);
    }
  };

  const isAllSelected =
    filteredVehicles.length > 0 && filteredVehicles.every((vehicle) => selectedVehicles.has(vehicle._id));
  const isIndeterminate =
    filteredVehicles.some((vehicle) => selectedVehicles.has(vehicle._id)) && !isAllSelected;

  if (showPreview) {
    return (
      <>
        <button
          onClick={onOpen}
          className="bg-[#5800F7] text-white px-4 h-[40px] rounded hover:bg-purple-800 transition-colors"
        >
          Imprimir QR
        </button>
        <VehiclePreviewModal
          isOpen={isOpen}
          onClose={onClose}
          selectedVehiclesData={selectedVehiclesData}
          generatingPDF={generatingPDF}
          setShowPreview={setShowPreview}
          handlePrint={handlePrint}
          selectedVehicles={selectedVehicles}
        />
      </>
    );
  }

  return (
    <>
      <button
        onClick={onOpen}
        className="bg-[#5800F7] text-white px-4 h-[40px] rounded hover:bg-purple-800 transition-colors"
      >
        Imprimir QR
      </button>
      <VehicleSelectionModal
        isOpen={isOpen}
        onClose={onClose}
        vehicles={vehicles}
        loading={loading}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        filteredVehicles={filteredVehicles}
        selectedVehicles={selectedVehicles}
        setSelectedVehicles={setSelectedVehicles}
        isAllSelected={isAllSelected}
        isIndeterminate={isIndeterminate}
        handleSelectAll={handleSelectAll}
        handleSelectVehicle={handleSelectVehicle}
        handlePreview={handlePreview}
        handlePrint={handlePrint}
        generatingPDF={generatingPDF}
        VehicleTableRow={VehicleTableRow}
      />
    </>
  );
}
