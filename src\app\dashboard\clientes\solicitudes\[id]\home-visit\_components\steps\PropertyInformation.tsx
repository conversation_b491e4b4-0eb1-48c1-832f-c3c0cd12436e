import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField, HookFormRadixUISelect } from '../HookFormField';
import { useState } from 'react';
import { FormSection } from '../FormSection';
import { FormSectionHeader, Separator } from '../FormHeaderSection';
import { HomeVisitStepsStatus, yesOrNoOptions, YesOrNoOptions } from '@/constants';
import { IStepperButtonsProps, StepperButton } from '../StepperButtons';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import {
  sendHomeImageUploadMessage,
  sendLocationGatheringMessage,
  updateAdmissionRequestHomeVisit,
} from '@/actions/postAdmissionRequest';
import { NoSelected, Steps, toastConfigs } from '.';
import { FiPhoneCall } from 'react-icons/fi';
import { useStepperNavigation } from './useStepperNavigation';
import { FilesSection } from '@/components/FileUpload/FileUploadHomeVisit';
import { translations } from '../translations';
import { GoItalic } from 'react-icons/go';

enum HouseType {
  House = 'House',
  Apartment = 'Apartment',
}

enum HouseTypeMXMappings {
  House = 'Casa',
  Apartment = 'Departamento',
}

export const typeOfHousingOptions = [
  { label: HouseTypeMXMappings.Apartment, value: HouseType.Apartment },
  { label: HouseTypeMXMappings.House, value: HouseType.House },
];

const TotalNoOfImages = 5;

const PropertyInformationSchema = z
  .object({
    ownProperty: z.string({ required_error: translations.es.DoYouOwnThisPropertyRequired }),
    nameOfOwner: z.string({
      required_error: translations.es.NameOfOwnerRequired,
    }),
    ownerRelative: z.string({
      required_error: translations.es.IsTheOwnerARelativeOrFamilyMemberRequired,
    }),
    ownerRelativeRelation: z.string({
      required_error: translations.es.RelationToOwnerRequired,
    }),
    ownerPhone: z
      .string({
        required_error: translations.es.OwnerPhoneRequired,
      })
      .max(10, { message: translations.es.PhoneLengthErrorMsg }),
    typeOfHousing: z.string({ required_error: translations.es.TypeOfPropertyRequired }),
    noOfBedrooms: z
      .number({
        required_error: translations.es.NumberOfBedroomsRequired,
      })
      .int()
      .positive(),
    livingRoom: z.string({
      required_error: translations.es.LivingRoomRequired,
    }),
    dinningRoom: z.string({
      required_error: translations.es.DinningRoomRequired,
    }),
    kitchen: z.string({
      required_error: translations.es.KitchenRequired,
    }),
    television: z.string({
      required_error: translations.es.TelevisionRequired,
    }),
    audioSystem: z.string({ required_error: translations.es.AudioSystemRequired }),
    stove: z.string({ required_error: translations.es.StoveRequired }),
    refrigerator: z.string({
      required_error: translations.es.RefrigeratorRequired,
    }),
    washingMachine: z.string({
      required_error: translations.es.WashingMachineRequired,
    }),
    propertyPictures: z.array(z.any()),
    proofOfPropertyOwnership: z.array(z.any()),
    locationInformation: z.string(),
    fetchedIpAddress: z.string(),
    homeImages: z.object({
      homeImagesFront: z.array(z.string()),
      homeImagesGarage: z.array(z.string()),
      homeImagesSurroundings: z.array(z.string()),
    }),
  })
  .superRefine((val, ctx) => {
    if (val.ownProperty === YesOrNoOptions.No) {
      const ownerPhoneStr = val.ownerPhone;
      if (ownerPhoneStr.length > 0) {
        if (ownerPhoneStr.length !== 10) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['ownerPhone'],
            message: translations.es.PhoneLengthErrorMsg,
          });
        } else if (!/^\d+$/.test(ownerPhoneStr)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['ownerPhone'],
            message: translations.es.PhoneFormatErrorMsg,
          });
        }
      }
    }
  });

interface IPropertyInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function PropertyInformation(props: IPropertyInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;

  const { id: requestId, homeVisit, locationData } = admissionRequest;

  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof PropertyInformationSchema>>({
    resolver: zodResolver(PropertyInformationSchema),
    defaultValues: {
      ownProperty: homeVisit?.houseInformation?.ownProperty
        ? homeVisit.houseInformation.ownProperty
        : NoSelected,
      nameOfOwner: homeVisit ? homeVisit?.houseInformation.nameOfOwner : '',
      ownerRelative: homeVisit?.houseInformation.ownerRelative || NoSelected,
      ownerRelativeRelation: homeVisit ? homeVisit?.houseInformation.ownerRelativeRelation : '',
      ownerPhone: homeVisit ? homeVisit?.houseInformation.ownerPhone : '',
      typeOfHousing: homeVisit?.houseInformation.typeOfHousing || NoSelected,
      noOfBedrooms: homeVisit?.houseInformation.noOfBedrooms || 0,
      livingRoom: homeVisit?.houseInformation.livingRoom || NoSelected,
      dinningRoom: homeVisit?.houseInformation.dinningRoom || NoSelected,
      kitchen: homeVisit?.houseInformation.kitchen || NoSelected,
      television: homeVisit?.houseInformation.television || NoSelected,
      audioSystem: homeVisit?.houseInformation.audioSystem || NoSelected,
      stove: homeVisit?.houseInformation.stove || NoSelected,
      refrigerator: homeVisit?.houseInformation.refrigerator || NoSelected,
      washingMachine: homeVisit?.houseInformation.washingMachine || NoSelected,
      propertyPictures: homeVisit ? homeVisit?.images : [],
      proofOfPropertyOwnership: homeVisit ? homeVisit?.proofOfPropertyOwnership : [],
      locationInformation: locationData ? locationData?.location : '',
      fetchedIpAddress: locationData ? locationData?.ipAddress : '',
      homeImages: homeVisit?.homeImages
        ? homeVisit?.homeImages
        : {
            homeImagesFront: [],
            homeImagesGarage: [],
            homeImagesSurroundings: [],
          },
    },
  });

  const ownProperty = form.watch('ownProperty');
  const ownerRelative = form.watch('ownerRelative');

  async function onSubmit(data: z.infer<typeof PropertyInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);

      const isDataCompleted = Object.entries(data).every(([key, value]) => {
        const mandatoryWhenOwnPropertyValueIsRented = [
          'nameOfOwner',
          'ownerRelative',
          'ownerPhone',
          'ownerRelativeRelation',
        ];

        if (Array.isArray(value)) {
          if (key === 'proofOfPropertyOwnership') {
            if (data.ownProperty === YesOrNoOptions.Yes) {
              return value.length > 0;
            }
            return true;
          }
          return value.length >= 5;
        }

        if (data.ownProperty === YesOrNoOptions.No && mandatoryWhenOwnPropertyValueIsRented.includes(key)) {
          if (key === 'ownerRelativeRelation') {
            return data.ownerRelative === YesOrNoOptions.Yes ? value !== '' : true;
          }
          return value !== '';
        }

        if (mandatoryWhenOwnPropertyValueIsRented.includes(key)) {
          return true;
        }

        if (typeof value === 'string') {
          return value !== '' && value !== NoSelected;
        }
        return true;
      });

      const payload = {
        homeVisitData: {
          houseInformation: {
            ownProperty: data.ownProperty,
            nameOfOwner: data.nameOfOwner,
            ownerRelative: data.ownerRelative,
            ownerRelativeRelation: data.ownerRelativeRelation,
            ownerPhone: data.ownerPhone,
            typeOfHousing: data.typeOfHousing,
            noOfBedrooms: data.noOfBedrooms,
            livingRoom: data.livingRoom,
            dinningRoom: data.dinningRoom,
            kitchen: data.kitchen,
            television: data.television,
            audioSystem: data.audioSystem,
            stove: data.stove,
            refrigerator: data.refrigerator,
            washingMachine: data.washingMachine,
          },
          images: form.getValues('propertyPictures'),
          proofOfPropertyOwnership: form.getValues('proofOfPropertyOwnership'),
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            property: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Property, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Property, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  const updateImages = async (formFieldName: string, propertyName: string) => {
    const images = form.getValues(formFieldName as any);
    const homeImages = form.getValues('homeImages');
    const payload = {
      homeVisitData: {
        [propertyName]: images,
        ...(formFieldName === 'propertyPictures' && { homeImages: homeImages }),
      },
      isHomeVisitData: true,
    };
    await updateAdmissionRequestHomeVisit({
      requestId: requestId,
      requestPayload: payload,
    });
    router.refresh();
  };

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.PropertyInformation} />
      <Form {...form}>
        <form>
          <div className="w-3/6 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="ownProperty"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.DoYouOwnThisProperty}
            />
          </div>
          {ownProperty === YesOrNoOptions.Yes ? (
            <FilesSection
              requestId={requestId}
              form={form}
              uploadText={translations.es.ProofOfPropertyOwnership}
              fieldToLookForImages={'proofOfPropertyOwnership'}
              formFieldName={'proofOfPropertyOwnership'}
              updateImages={async () => {
                await updateImages('proofOfPropertyOwnership', 'proofOfPropertyOwnership');
              }}
            />
          ) : (
            <>
              <div className="py-2">
                <HookFormRadixUIField
                  form={form}
                  fieldName="nameOfOwner"
                  formLabel={translations.es.NameOfOwner}
                  className="w-3/6"
                />
                <div className="flex gap-4 py-2">
                  <div className="w-3/6">
                    <HookFormRadixUISelect
                      control={form.control}
                      fieldName="ownerRelative"
                      selectOptions={yesOrNoOptions}
                      formLabel={translations.es.IsTheOwnerARelativeOrFamilyMember}
                    />
                  </div>

                  {ownerRelative === YesOrNoOptions.Yes && (
                    <HookFormRadixUIField
                      form={form}
                      fieldName="ownerRelativeRelation"
                      formLabel={translations.es.RelationToOwner}
                    />
                  )}
                </div>
                <HookFormRadixUIField
                  form={form}
                  fieldName="ownerPhone"
                  formLabel={translations.es.OwnerPhone}
                  Icon={FiPhoneCall}
                  prefixString={'+52'}
                  className="w-3/6 py-2"
                />
              </div>
            </>
          )}
          <Location form={form} requestId={requestId} locationData={locationData} />
          <div className="flex gap-4 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="typeOfHousing"
              selectOptions={typeOfHousingOptions}
              formLabel={translations.es.TypeOfProperty}
              className="h-6"
            />
            <HookFormRadixUIField
              form={form}
              fieldName="noOfBedrooms"
              formLabel={translations.es.NumberOfBedrooms}
              type="number"
            />
          </div>

          <div className="flex gap-4 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="livingRoom"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.LivingRoom}
            />

            <HookFormRadixUISelect
              control={form.control}
              fieldName="dinningRoom"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.DinningRoom}
            />
          </div>

          <div className="flex gap-4 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="kitchen"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.Kitchen}
            />

            <HookFormRadixUISelect
              control={form.control}
              fieldName="television"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.Television}
            />
          </div>

          <div className="flex gap-4 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="audioSystem"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.AudioSystem}
            />

            <HookFormRadixUISelect
              control={form.control}
              fieldName="stove"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.Stove}
            />
          </div>

          <div className="flex gap-4 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="refrigerator"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.Refrigerator}
            />

            <HookFormRadixUISelect
              control={form.control}
              fieldName="washingMachine"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.WashingMachine}
            />
          </div>
          <FileUploadLinkSend requestId={requestId} images={homeVisit?.images} />
          <FilesSection
            requestId={requestId}
            form={form}
            uploadText={translations.es.PropertyPictures}
            fieldToLookForImages={'images'}
            formFieldName={'propertyPictures'}
            updateImages={async () => {
              await updateImages('propertyPictures', 'images');
            }}
            showImageCounter={true}
            totalNoOfImages={TotalNoOfImages}
          />
        </form>
      </Form>
    </FormSection>
  );
}

interface LocationData {
  latitude: number;
  longitude: number;
  location: string;
  ipAddress: string;
  isFromIP: boolean;
}
interface ILocation {
  requestId: string;
  form: any;
  locationData: LocationData;
}

const Location = (props: ILocation) => {
  const { locationData, form, requestId } = props;

  const toast = useToast();
  const isLocationGatheringLinkEnable = !locationData;

  const [loading, setLoading] = useState(false);

  const sendLocationVerificationLink = async () => {
    if (!isLocationGatheringLinkEnable) {
      return;
    }
    setLoading(true);
    const response = await sendLocationGatheringMessage({
      requestId: requestId,
    });
    if (response && response.success) {
      toast({
        title: 'Success',
        description: 'Mensaje enviado exitosamente',
        status: 'success',
        duration: 2000,
      });
    } else {
      toast({
        title: 'Error',
        description: 'Error al enviar el mensaje. Inténtalo de nuevo.',
        status: 'error',
        duration: 2000,
      });
    }
    setLoading(false);
  };

  return (
    <>
      <Separator />
      <div className="py-2">
        <StepperButton
          text={translations.es.SendLocationVerificationLink}
          variant={'outline'}
          className={`border ${
            isLocationGatheringLinkEnable
              ? 'border-primaryPurple text-primaryPurple'
              : 'border-primaryPaleBlueGray text-primaryPaleBlueGray'
          } `}
          disabled={isLocationGatheringLinkEnable}
          onClick={sendLocationVerificationLink}
          isLoading={loading}
        />
        <div className="flex items-center py-2">
          <GoItalic size={12} className="text-primaryBlueGray " />
          <p className="text-primaryBlueGray text-xs italic">
            {translations.es.LocationPageRefreshInstruction}
          </p>
        </div>
        <div className="flex py-2 gap-4">
          <HookFormRadixUIField
            form={form}
            fieldName="locationInformation"
            formLabel={translations.es.LocationInformation}
            isDisabled={true}
          />
          <HookFormRadixUIField
            form={form}
            fieldName="fetchedIpAddress"
            formLabel={translations.es.FetchedIpAddress}
            isDisabled={true}
          />
        </div>
      </div>
      <Separator />
    </>
  );
};

const FileUploadLinkSend = (props: any) => {
  const { requestId, images } = props;

  const [loading, setLoading] = useState(false);
  const toast = useToast();

  const isHomeImagesAlreadyUploaded = images && images.length === 0;

  const sendLocationVerificationLink = async () => {
    if (!isHomeImagesAlreadyUploaded) {
      return;
    }
    setLoading(true);
    const response = await sendHomeImageUploadMessage({
      requestId: requestId,
    });
    if (response && response.success) {
      toast({
        title: 'Success',
        description: 'Mensaje enviado exitosamente',
        status: 'success',
        duration: 2000,
      });
    } else {
      toast({
        title: 'Error',
        description: 'Error al enviar el mensaje. Inténtalo de nuevo.',
        status: 'error',
        duration: 2000,
      });
    }
    setLoading(false);
  };

  return (
    <>
      <Separator />
      <div className="py-2">
        <StepperButton
          text={translations.es.SendHomeImageUploadLink}
          variant={'outline'}
          className={`border ${
            isHomeImagesAlreadyUploaded
              ? 'border-primaryPurple text-primaryPurple'
              : 'border-primaryPaleBlueGray text-primaryPaleBlueGray'
          } `}
          disabled={isHomeImagesAlreadyUploaded}
          onClick={sendLocationVerificationLink}
          isLoading={loading}
        />
      </div>
    </>
  );
};
