import { Input, FormControl, FormErrorMessage } from '@chakra-ui/react';
import { useField } from 'formik';
import { format, addDays, set } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import Swal from 'sweetalert2';
import React, { useState } from 'react';

interface InputDateProps {
  name: string;
  label: string;
  includeHours?: boolean;
  defaultValue?: string;
  limitPastDates?: boolean;
  onChange?: (value: string) => void;
  dateMsg?: string;
  allowClear?: boolean;
  message?: string;
  isDisabled?: boolean;
  useDefaultValidation?: boolean;
}

export default function CustomDatePicker({
  name,
  label,
  includeHours,
  defaultValue,
  limitPastDates = false,
  onChange,
  dateMsg,
  allowClear = false,
  message,
  isDisabled,
  useDefaultValidation,
}: InputDateProps) {
  const [field, meta, helpers] = useField(name);
  const [browserError, setBrowserError] = useState<string | null>(null);

  const mexicoCityTimeZone = 'America/Mexico_City';
  const maxYear = '2100-12-31T23:59';
  const today = new Date();
  const todayInMexicoCityTime = utcToZonedTime(today, mexicoCityTimeZone);
  const adjustedDate = set(todayInMexicoCityTime, { hours: 0, minutes: 0 });
  const tomorrowInMexicoCityTime = addDays(adjustedDate, 1);

  const tomorrowLimitString = includeHours
    ? format(tomorrowInMexicoCityTime, "yyyy-MM-dd'T'HH:mm")
    : format(tomorrowInMexicoCityTime, 'yyyy-MM-dd');

  if (defaultValue && !field.value) {
    helpers.setValue(defaultValue);
  }

  const handleClear = () => {
    if (message) {
      Swal.fire({
        title: 'Confirmación',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sí, eliminar',
        cancelButtonText: 'Cancelar',
        willOpen: () => {
          const container = document.querySelector('.swal2-container');
          if (container) {
            (container as HTMLElement).style.zIndex = '1500';
          }
        },
      }).then((result) => {
        if (result.isConfirmed) {
          helpers.setValue('');
          if (onChange) onChange('');
        }
      });
    } else {
      helpers.setValue('');
      if (onChange) onChange('');
    }
  };

  const handleInvalid = (e: React.InvalidEvent<HTMLInputElement>) => {
    e.preventDefault();
    const target = e.target;
    helpers.setTouched(true, true);

    if (target.validity.rangeUnderflow && useDefaultValidation) {
      setBrowserError('La fecha debe ser posterior al 1 de enero de 1900.');
    } else if (target.validity.rangeOverflow && useDefaultValidation) {
      setBrowserError('La fecha debe ser anterior al 31 de diciembre de 2100.');
    } else if (target.validity.typeMismatch && useDefaultValidation) {
      setBrowserError('Por favor, ingrese una fecha válida.');
    } else {
      setBrowserError('Fecha inválida');
    }
  };

  const hasError = !!((meta.touched && meta.error) || browserError);

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <label className="block text-gray-700 text-[16px]">{label}</label>
        {allowClear && field.value && (
          <span
            className="text-red-500 cursor-pointer hover:text-red-800 hover:underline text-sm"
            onClick={handleClear}
          >
            Eliminar
          </span>
        )}
      </div>
      <FormControl isInvalid={hasError}>
        <Input
          {...field}
          type={includeHours ? 'datetime-local' : 'date'}
          min={limitPastDates ? tomorrowLimitString : '1900-01-01'}
          max={includeHours ? maxYear : '2100-12-31'}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
            const newValue = e.target.value;
            helpers.setValue(newValue, false);
            setBrowserError(null);

            if (onChange) onChange(newValue);
          }}
          onBlur={(e) => {
            helpers.setTouched(true, true);
            field.onBlur(e);
          }}
          disabled={isDisabled}
          onInvalid={handleInvalid}
          className={`
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            border-2
            text-[gray]
            rounded
            px-3 
            h-[40px] 
            w-full
            outline-none 
          `}
        />
        <p className="ml-[8px] text-[14px]">{dateMsg ? dateMsg : '*Utilizar el boton de calendario'}</p>
        {hasError && <FormErrorMessage>{meta.error || browserError}</FormErrorMessage>}
      </FormControl>
    </div>
  );
}
