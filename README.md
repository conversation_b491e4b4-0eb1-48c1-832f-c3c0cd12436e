

# Admin OCN

A powerful admin dashboard built with Next.js. It contains the code for the Admin panel dealing with Vehicle Onboarding, customer (driver) approval, and user management for the platform.
## Module Overview

 - **Dashboard:** Analytics and Charts of Vehicles and Users
 - **Usuarios:** Admin Panel User Management
 - **Clientes:** Client (Driver) Module to manage all the information about drivers, approve/reject their application, their documents, processes and visits
 - **Flotilla:** Vehicle Stock Management module which shows vehicles under different statuses categorized under two major headings - active and inactive. It also provides functionality for vehicle onboarding.
 - **Pagos:** Payments module listing the invoices and payment information

## Prerequisites

Before you begin, ensure you have met the following requirements:

### Node.js and NPM

- Install Node.js (v18.x or higher) and NPM from the [official Node.js website](https://nodejs.org/)
- Verify installation by running:
  ```bash
  node --version
  npm --version
  ```

## Getting Started

Follow these steps to get your development environment running:

1. **Clone the repository**
   ```bash
   git clone https://github.com/username/admin-ocn.git
   cd admin-ocn
   ```

2. **Environment Setup**
   ```bash
   # Copy the sample environment file
   cp env.sample .env

   # Open .env and update the variables according to your setup
   # Example variables:
   # NEXT_PUBLIC_API_URL=http://localhost:3000
   # NEXT_PUBLIC_SITE_URL=http://localhost:3000
   # NEXTAUTH_URL=http://localhost:3000
   # NEXTAUTH_SECRET=your-secret-key
   ```

3. **Install Dependencies**
   ```bash
   # Install all dependencies
   npm install

   # Install development dependencies
   npm install --save-dev
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```
   The application will start on http://localhost:8080

## Available Scripts

- `npm run dev` - Start development server with hot-reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint

## Development Guidelines

### Code Style

- We use ESLint and Prettier for code formatting
- Use TypeScript for type safety


### State Management

- Use Zustand global state

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## Deployment

### Production Build

```bash
npm run build
npm start
```

## Troubleshooting

Common issues and their solutions:

1. **Build Errors**
   - Clear `.next` folder and node_modules
   - Reinstall dependencies
   - Ensure Node.js version compatibility

2. **API Connection Issues**
   - Verify environment variables
   - Check API endpoint availability
   - Confirm CORS settings

## Support

For support, please:
1. Check the documentation