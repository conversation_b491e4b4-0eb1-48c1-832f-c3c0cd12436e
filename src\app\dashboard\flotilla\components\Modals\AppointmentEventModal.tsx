/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-shadow */
import { cn } from '@/lib/utils';
import { Associate } from '@/actions/getDriverById';
import { VehicleResponse } from '@/actions/getVehicleData';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DateTime } from 'luxon';
import { useEffect, useState } from 'react';
import { useOpenAppointmentDetailModal } from '@/zustand/modalStates';
import { Button } from '@/components/ui/button';
import ModalContainer from '@/app/dashboard/flotilla/components/Modals/ModalContainer';
// import FormikContainer from '@/components/Formik/FormikContainer';
// import SelectInput from '@/components/Inputs/SelectInput';
// import { appointmentService } from '@/services/appointmentService';
import { useAppointmentModal } from '@/zustand/modalStates';
import ReescheduleAppointment from '@/app/dashboard/calendar/calendar-preview/_components/RescheduleAppointment';

export const statusColorMap: Record<string, string> = {
  canceled: '#EF4444',
  'not-attended': '#F59E0B',
};

export const MapStatus = {
  scheduled: 'Agendada',
  completed: 'Completada',
  canceled: 'Cancelada',
  'not-attended': 'No asistida',
  rescheduled: 'Reagendada',
};

interface Event {
  _id: string;
  status: string;
  title: string;
  start: Date;
  end: Date;
  associate: Associate;
  stock: VehicleResponse;
  workshop: any;
  service: ServiceType;
  data: any;
}

export interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationTimezone?: string;
  event?: Event;
}

export function AppointmentEventModal({ isOpen, onClose }: EventModalProps) {
  const { appointmentData: event } = useOpenAppointmentDetailModal();
  const [open, setOpen] = useState(isOpen);
  const onOpen = () => setOpen(true);
  const onClose2 = () => setOpen(false);
  const appointmentModal = useAppointmentModal();
  console.log('appointment modal is: open?', isOpen);
  console.log('reschedule modal is: open?', appointmentModal.isOpen);
  const onOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      onClose();
    }
  }

  useEffect(() => {
    setOpen(isOpen);
  }, [isOpen]);

  if (!event) return null;
  // console.log('event: ', event);
  // Convert Dates to Luxon DateTime objects
  const startDateTime = DateTime.fromJSDate(new Date(event.startTime)).setLocale('es');
  const endDateTime = DateTime.fromJSDate(new Date(event.endTime)).setLocale('es');

  // const stockId = event.stock?._id;
  // const nowDevice = DateTime.now().setZone('utc');

  // const appointmentStart = DateTime.fromJSDate(event.start).setZone(organizationTimezone);

  // const isPastAppointment = nowDevice.toMillis() > appointmentStart.toMillis();

  // const isDisabledEvent = event.status === 'canceled' || event.status === 'not-attended';
  const isDisabledEvent = !!statusColorMap[event.status];

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="  sm:w-3/4 lg:w-3/5 xl:w-1/3">
          <DialogHeader>
            <DialogTitle>
              Detalles de la Cita
              {isDisabledEvent && (
                <span
                  className={cn(`ml-2 text-sm font-normal`)}
                  style={{
                    color: statusColorMap[event.status],
                  }}
                >
                  {event.status === 'canceled' ? '(Cancelada)' : '(No asistida)'}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <div
            className={`grid gap-4 pt-4 grid-cols-1 ${isDisabledEvent ? 'opacity-60 pointer-events-none' : ''
              }`}
          >
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-4 space-y-4">
                <div>
                  <h3 className="font-medium">Servicio en taller {event.workshop?.name}</h3>
                  <p
                    style={{
                      color: event.service?.color || '#3B82F6',
                    }}
                  >
                    {event.service?.name || 'Nombre del tipo de servicio no obtenido'}
                  </p>
                  {event.service?.description && (
                    <p className="text-sm text-gray-500">{event.service?.description || 'No descripción obtenida'}</p>
                  )}
                </div>
                {/* SHOW STATUS OF THE APPOINTMENT */}

                <div className="flex gap-4">
                  <div>
                    <h3 className="font-medium">Estado de la cita</h3>
                    <p className="text-sm text-gray-500">
                      {MapStatus[event.status as keyof typeof MapStatus] || event.status}
                      {/* {isPastAppointment ? 'Pasada' : 'Pendiente'} */}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium">ID de la cita</h3>
                    <p className="text-sm text-gray-500">{event.id}</p>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium">Cliente</h3>
                  <p className="text-sm text-gray-500">
                    {event.associate.firstName + ' ' + event.associate.lastName}
                  </p>
                  <p className="text-sm text-gray-500">{event.associate.email}</p>
                  <p className="text-sm text-gray-500">{event.associate.phone}</p>
                </div>

                <div>
                  <h3 className="font-medium">Vehículo</h3>
                  <p className="text-sm text-gray-500">
                    {event.stock.brand + event.stock.model} | {event.stock.carNumber}
                    {event.stock.extensionCarNumber ? `- ${event.stock.extensionCarNumber}` : ''}
                  </p>
                  <p className="text-sm text-gray-500">Placas: {event.stock.carPlates.plates}</p>
                  <p className="text-sm text-gray-500">Vin: {event.stock.vin}</p>
                  <p className="text-sm text-gray-500">
                    Kilometraje registrado: {event.data.registeredKm} km
                  </p>
                </div>

                <div>
                  <h3 className="font-medium">Horario</h3>
                  <p className="text-sm text-gray-500">
                    {startDateTime.toLocaleString({
                      weekday: 'long',
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                    })}
                  </p>
                  <p className="text-sm text-gray-500">
                    {startDateTime.toFormat('HH:mm')} - {endDateTime.toFormat('HH:mm')}
                  </p>
                  {/* <p className="text-sm text-gray-500">Duración: {event.service.duration} minutos</p> */}
                  {
                    event.service?.duration && (
                      <p className="text-sm text-gray-500">
                        Duración: {event.service.duration} minutos
                      </p>
                    )
                  }
                </div>
              </div>
            </div>

            {/* Show only reschedule button and only if the appointment is not completed (scheduled or rescheduled) */}
            {event.status !== 'completed' && (
              <>
                <Button
                  className="bg-red-500 hover:bg-red-600 text-white"
                  onClick={() => {
                    appointmentModal.onOpen();
                    onClose2();
                  }}
                >
                  Reagendar
                </Button>
              </>
            )}

            {/* <div className="col-span-1 flex flex-col md:flex-row gap-x-3 justify-end space-y-4 md:space-y-0">
              {event.status !== 'completed' && (
                <>
                  {!isPastAppointment && (
                    <>
                      <Button
                        className="bg-red-500 hover:bg-red-600 text-white"
                        onClick={() => {
                          appointmentModal.onOpen();
                          onClose2();
                        }}
                      >
                        Reagendar
                      </Button>
                      <CancelAppointmentModal appointmentId={event.id} onClose={onClose2} onOpen={onOpen} />
                    </>
                  )}
                  {isPastAppointment && event.status !== 'not-attended' && event.status !== 'completed' && (
                    <Button
                      className="bg-red-500 hover:bg-red-600 text-white"
                      onClick={async () => {
                        onClose2();
                        const { isConfirmed } = await Swal.fire({
                          title: '¿Estás seguro  de marcar la cita como no asistida?',
                          text: 'Esta acción no se puede deshacer',
                          icon: 'warning',
                          showCancelButton: true,
                          confirmButtonColor: '#d33',
                          cancelButtonColor: '#3085d6',
                          confirmButtonText: 'Sí, marcar como no asistida',
                          cancelButtonText: 'Cancelar',
                        });

                        if (isConfirmed) {
                          try {
                            await appointmentService.notAttendedAppointment(event.id);

                            toast({
                              title: 'Cita marcada como no asistida con éxito',
                              description: 'Actualizando la página',
                              status: 'success',
                              position: 'top',
                              duration: 3000,
                              isClosable: true,
                            });

                            onClose();

                            setTimeout(() => {
                              window.location.reload();
                            }, 3100);
                          } catch (error: any) {
                            console.error(error);
                            toast({
                              title: 'Error al cancelar la cita',
                              description: error?.message || 'Ocurrió un error al cancelar la cita',
                              status: 'error',
                              position: 'top',
                              duration: 3000,
                              isClosable: true,
                            });
                          }
                          onOpen();
                          return null;
                        } else {
                          onOpen();
                        }
                      }}
                    >
                      No asistió
                    </Button>
                  )}

                  {(event.status === 'scheduled' || event.status === 'rescheduled') && (
                    <Button
                      className="bg-primaryPurple hover:bg-primaryBtnHover text-white"
                      onClick={() => {
                        window.open(
                          `/${params.locale}/dashboard/vehicles/${stockId}?addService=true`,
                          '_blank'
                        );
                        const serviceKeyName = `service-${stockId}`;
                        localStorage.setItem(serviceKeyName, JSON.stringify(event));
                      }}
                    >
                      Crear Servicio
                    </Button>
                  )}
                </>
              )}
            </div> */}
          </div>
        </DialogContent>
      </Dialog>
      <CreateNextAppointmentModal event={event} onCloseEventModal={onClose2} onOpenEventModal={onOpen} />
    </>
  );
}

interface CreateNextAppointmentModalProps {
  event: NonNullable<EventModalProps['event']>;
  onCloseEventModal: () => void;
  onOpenEventModal: () => void;
}
/**
 * CreateNextAppointmentModal - Componente para reagendar citas
 *
 * Este componente permite al usuario reagendar una cita existente seleccionando:
 * 1. Un taller (workshop) de la lista de talleres disponibles
 * 2. Un tipo de servicio para la nueva cita
 *
 * Una vez seleccionados estos datos, muestra el componente ReescheduleAppointment
 * que permite seleccionar una nueva fecha y hora para la cita.
 *
 * @param event - Datos de la cita actual que se va a reagendar
 * @param onCloseEventModal - Función para cerrar el modal de evento principal
 * @param onOpenEventModal - Función para abrir el modal de evento principal
 */
function CreateNextAppointmentModal({
  event,
  onCloseEventModal,
  onOpenEventModal,
}: CreateNextAppointmentModalProps) {
  console.log('event', event);
  const { /* onOpen, */ onClose, isOpen } = useAppointmentModal();

  // const [workshopOptions, setWorkshopOptions] = useState([]);
  // const [serviceTypes, setServiceTypes] = useState([]);
  // const [selectedWorkshop, setSelectedWorkshop] = useState<any>();
  // const [selectedServiceType, setSelectedServiceType] = useState<any>();

  // useEffect(() => {
  //   if (isOpen) {
  //     const fetchWorkshops = async () => {
  //       try {
  //         const { data } = await appointmentService.getAllWorkshops();
  //         setWorkshopOptions(data);
  //       } catch (error: any) {
  //         console.error(error);
  //       }
  //     };

  //     fetchWorkshops();
  //   }
  // }, [isOpen]);

  // useEffect(() => {
  //   if (selectedWorkshop && selectedWorkshop.name) {
  //     const fetchServiceTypes = async () => {
  //       try {
  //         const { data } = await appointmentService.getServiceTypes(selectedWorkshop.organizationId);
  //         setServiceTypes(data);
  //       } catch (error: any) {
  //         console.error(error);
  //       }
  //     };

  //     fetchServiceTypes();
  //   }
  // }, [selectedWorkshop]);

  if (!isOpen) {
    return null;
  }


  return (
    <>
      <ModalContainer
        title="Reagendar cita"
        onClose={() => {
          onClose();
          onOpenEventModal();
        }}
      >
        <ReescheduleAppointment
          workshopId={event.workshop._id}
          serviceType={event.service}
          appointmentId={event._id}
          onClose={() => {
            onClose();
            onCloseEventModal();
          }}
        />
      </ModalContainer>
    </>
  );
}
