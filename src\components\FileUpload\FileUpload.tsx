'use client';
import { Box, Flex, Text, VStack } from '@chakra-ui/react';
import { FaTrashAlt } from 'react-icons/fa';

import { truncateFileName } from '../../utils/text';
import { useEffect, useState } from 'react';
import useFileUpload from './useFileUpload';
import { MediaType } from '@/app/dashboard/clientes/solicitudes/enums';

// Función para generar un ID único
const generateUniqueId = (prefix: string) => {
  return `${prefix}-${Math.random().toString(36).substring(2, 11)}`;
};

export class Media {
  id?: string | undefined;

  fileName: string;

  path: string;

  type: MediaType;

  mimeType: string;

  url?: string;

  createdAt?: Date;

  updatedAt?: Date;

  constructor(props: Media) {
    this.id = props.id;
    this.fileName = props.fileName;
    this.path = props.path;
    this.type = props.type;
    this.mimeType = props.mimeType;
    this.url = props.url;
    this.createdAt = props.createdAt;
    this.updatedAt = props.updatedAt;
  }
}

interface FileUploadProps {
  accept?: string;
  primaryButtonText: string;
  mediaType: MediaType;
  showPreview?: boolean;
  totalFiles?: number;
  maxFileSize?: number; // in bytes
  onUploadChange?: (media: Media[]) => void;
  id?: string;
}

export const FileUpload = ({
  primaryButtonText,
  accept = 'image/*',
  mediaType,
  showPreview = false,
  totalFiles = 1,
  maxFileSize = 1024 * 1024 * 5, // 5 MB
  onUploadChange,
  id,
}: FileUploadProps) => {
  // Generar un ID único si no se proporciona uno
  const uniqueId = id || generateUniqueId('file-upload');

  const { uploadedFiles, uploadFile, deleteFile, isLoading, istotalFilesReached } = useFileUpload(
    mediaType,
    totalFiles
  );

  const [errorMessage, setErrorMessage] = useState('');

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    setErrorMessage('');
    if (file) {
      if (file.size > maxFileSize) {
        setErrorMessage(`El tamaño del archivo no debe exceder ${maxFileSize / 1024 / 1024} MB.`);
        return;
      }
      await uploadFile(file);
    }
    event.target.value = '';
  };

  useEffect(() => {
    if (onUploadChange) {
      onUploadChange(uploadedFiles);
    }
  }, [uploadedFiles, onUploadChange]);

  return (
    <VStack spacing={3} width="full" height="full">
      {errorMessage && <Text color="red.500">{errorMessage}</Text>}
      {uploadedFiles.map((file, index) => (
        <Box key={index} borderRadius="md" p={4} width="100%" className="border border-gray-300">
          {showPreview && file && (
            <Box borderRadius="md" p={4} width="100%" className="mt-4">
              <img src={file.url} alt={file.fileName} className="object-contain h-full w-full" />
            </Box>
          )}
          <Flex alignItems="center" justify="space-between" w="full">
            <div className="max-w-xs truncate">
              <Text fontSize="md" fontWeight="semibold" className="ml-2 text-[#5800F7]">
                {truncateFileName(file.fileName, 30)}
              </Text>
            </div>
            <FaTrashAlt
              size={20}
              className="cursor-pointer text-[#5800F7]"
              onClick={() => deleteFile(index)}
            />
          </Flex>
        </Box>
      ))}
      {!istotalFilesReached && (
        <label htmlFor={uniqueId}>
          <span className="bg-white text-[#5800F7] h-[40px] rounded py-3 px-4 border border-[#5800F7] cursor-pointer hover:bg-gray-100 font-semibold">
            {primaryButtonText}
          </span>
        </label>
      )}
      <input
        type="file"
        accept={accept}
        onChange={handleFileChange}
        style={{ display: 'none' }}
        disabled={isLoading || istotalFilesReached}
        id={uniqueId}
      />
    </VStack>
  );
};
