/* eslint-disable react-hooks/rules-of-hooks */
'use client';

import axios from 'axios';
import { RecurringPayment } from '../../types';
import { PAYMENTS_API_URL, PAYMENT_API_SECRET, canPerformPaymentActions } from '@/constants';
import { DataTable } from '@/components/DataTable';
import { useEffect, useState } from 'react';
import { ColumnDef } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CirclePlus, Pencil, Trash2, Zap, CirclePlay, CirclePause } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import Swal from 'sweetalert2';
import { removeAdendumProducts, runSubscription } from './Bar/actions';
import Spinner from '@/components/Loading/Spinner';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';

async function getRecurringPayments(id: string): Promise<RecurringPayment[]> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/subscriptions?clientId=${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : [];
}

type Props = {
  id?: string;
};

export default function RecurringPaymentTab({ id }: Props) {
  const [recurringPaymentData, setRecurringPaymentData] = useState<RecurringPayment[]>([]);
  const [loading, setLoading] = useState(false);
  const [running, setRunning] = useState(false);

  const handleSetPaymentStatus = (recurringPayment: RecurringPayment) => {
    try {
      setLoading(true);
      axios
        .patch(
          `${PAYMENTS_API_URL}/subscriptions/status/${recurringPayment.id}`,
          {
            status: !recurringPayment.isActive,
          },
          {
            headers: {
              Authorization: `Bearer ${PAYMENT_API_SECRET}`,
            },
          }
        )
        .then((res) => {
          if (res?.data?.statusCode === 200) {
            getRecurringPayments(id as string).then((response) => {
              setLoading(false);
              setRecurringPaymentData(response);
            });
          }
        });
    } catch (error) {
      setLoading(false);
      console.log(error);
    }
  };

  const recurringPaymentsColumns: ColumnDef<RecurringPayment>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            <div className="font-medium">{row?.original?.id}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'client.name',
      header: 'CLIENTE',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            <div className="font-medium">{row?.original?.client?.name}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'isActive',
      header: 'ESTADO',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            {row.original.isActive ? (
              <CirclePause
                className="hover:cursor-pointer"
                onClick={() => {
                  handleSetPaymentStatus(row.original);
                }}
              />
            ) : (
              <CirclePlay
                className="hover:cursor-pointer"
                onClick={() => {
                  handleSetPaymentStatus(row.original);
                }}
              />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'total',
      header: 'TOTAL',
      cell: ({ row }) => {
        return <div className="font-medium">{`$${row.getValue('total')}mxn`}</div>;
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'CREADO',
      cell: ({ row }) => {
        const startDate = new Date(row.original.startDate);
        const formattedStartDate = startDate.toLocaleDateString();
        const endDate = new Date(row.original.endDate);
        const formattedEndDate = endDate.toLocaleDateString();
        return (
          <div className="font-medium">
            {'Starting ' + formattedStartDate}
            <br />
            {' Ending ' + formattedEndDate}
          </div>
        );
      },
    },
    {
      accessorKey: 'payment_form',
      header: 'EVENTOS',
      cell: ({ row }) => {
        return <div className="font-medium">{`${row.getValue('payment_form')}`}</div>;
      },
    },
    {
      accessorKey: 'actions',
      header: 'SIGUENTE EVENTO',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      cell: ({ row }) => {
        const [isRemoving, setIsRemoving] = useState(false);
        const { user } = useCurrentUser();
        const shouldDisplay = canPerformPaymentActions(user.email);
        return (
          <>
            {isRemoving && <Spinner />}
            {shouldDisplay && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <CirclePlus />
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Pencil className="h-4 w-4 pr-1" />
                    Editar
                  </DropdownMenuItem>
                  {/* <DropdownMenuItem>
                <Trash2 className="h-4 w-4 pr-1" />
                Delete
              </DropdownMenuItem> */}
                  <DropdownMenuItem
                    onClick={async () => {
                      return Swal.fire({
                        title: '¿Estás seguro de ejecutar la suscripción?',
                        // text: 'No podrás revertir esta acción',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#5800F7',
                        cancelButtonColor: '#d33',
                        confirmButtonText: 'Sí, eliminar',
                        cancelButtonText: 'Cancelar',
                      }).then(async (result) => {
                        if (result.isConfirmed) {
                          setRunning(true);
                          const isCreated = await runSubscription(row.original.id);
                          setRunning(false);

                          if (isCreated.success) {
                            // Swal.fire('Ejecutado', 'La suscripción ha sido ejecutada', 'success');
                            Swal.fire({
                              title: 'La suscripción ha sido ejecutada',
                              text: 'El pago ha sido creado',
                              icon: 'success',
                            });
                          } else {
                            Swal.fire({
                              title: 'Error',
                              text: isCreated.message,
                              icon: 'error',
                            });
                          }

                          // router.refresh();
                        }
                      });
                    }}
                  >
                    <Zap className="h-4 w-4 pr-1" />
                    Ejecutar suscripción
                  </DropdownMenuItem>
                  {
                    /* shouldDisplay && */ <DropdownMenuItem
                      onClick={async () => {
                        // removeAdendumProducts(row.original.id);
                        return Swal.fire({
                          title: '¿Estás seguro de eliminar los productos de ademdums?',
                          icon: 'warning',
                          showCancelButton: true,
                          confirmButtonColor: '#5800F7',
                          cancelButtonColor: '#d33',
                          confirmButtonText: 'Sí, eliminar',
                          cancelButtonText: 'Cancelar',
                        }).then(async (result) => {
                          if (result.isConfirmed) {
                            setIsRemoving(true);
                            const isDeleted = await removeAdendumProducts(row.original.id);
                            setIsRemoving(false);
                            Swal.fire('Eliminado', 'El cliente ha sido eliminado', 'success');
                            if (isDeleted) {
                              Swal.fire('Eliminado', 'Los productos han sido eliminados', 'success');
                            } else {
                              Swal.fire('Error', 'No se pudo eliminar los productos', 'error');
                            }
                          }
                        });
                      }}
                    >
                      <Trash2 className="h-4 w-4 pr-1" />
                      Eliminar productos de ademdum
                    </DropdownMenuItem>
                  }
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </>
        );
      },
    },
  ];

  useEffect(() => {
    try {
      setLoading(true);
      getRecurringPayments(id as string).then((res) => {
        setLoading(false);
        setRecurringPaymentData(res);
      });
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  }, [id]);

  if (loading) {
    return <Skeleton className="w-full h-28"></Skeleton>;
  } else {
    return (
      <>
        {running && <Spinner />}
        <DataTable columns={recurringPaymentsColumns} data={recurringPaymentData}></DataTable>
      </>
    );
  }
}
