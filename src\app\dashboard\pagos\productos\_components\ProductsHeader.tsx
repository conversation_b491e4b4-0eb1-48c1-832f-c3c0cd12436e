'use client';
import CountrySelector from '@/app/dashboard/clientes/_components/CountrySelector';
import { RegionFilter } from './RegionFilter';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';

export const ClientSectionHeader = () => {
  const { isCountryUSA } = useCountry();
  const ProductsHeadingText = isCountryUSA ? 'Products / Services ' : 'Productos / Servicios';

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="mb-6 text-3xl font-bold">{ProductsHeadingText}</h1>
        <CountrySelector />
      </div>
      <RegionFilter />
    </>
  );
};
