import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lex, Heading, useToast } from '@chakra-ui/react';
import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useState } from 'react';
import { HookFormRadixUIField } from '../home-visit/_components/HookFormField';
import { useRouter } from 'next/navigation';
import { updateAdmissionRequestPersonalData } from '@/actions/postAdmissionRequest';

const CreditCheckSchema = z.object({
  ficoScore: z.string().min(1, { message: 'FICO score is required' }),
  criminalBackgroundCheck: z.string().min(1, { message: 'Criminal background check is required' }),
});

export const CreditCheckUS = (props: any) => {
  const { admissionRequest } = props;
  const { id: requestId, personalData } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();

  const form = useForm<z.infer<typeof CreditCheckSchema>>({
    resolver: zodResolver(CreditCheckSchema),
    defaultValues: {
      ficoScore: personalData?.ficoScore || '',
      criminalBackgroundCheck: personalData?.criminalBackgroundCheck || '',
    },
  });
  async function onSubmit(data: z.infer<typeof CreditCheckSchema>) {
    try {
      setIsSubmitting(true);
      const payload = {
        ficoScore: data.ficoScore,
        criminalBackgroundCheck: data.criminalBackgroundCheck,
      };
      const response = await updateAdmissionRequestPersonalData({
        requestId: requestId,
        payload: payload,
      });
      if (response && response.success) {
        toast({
          title: 'Credit Check updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        router.refresh();
      }
    } catch (error) {
      toast({
        title: 'Error updating Credit Check',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {'Credit Check (MicroBilt)'}
          </Heading>
        </Flex>
      </CardHeader>
      {
        <Form {...form}>
          <form className="py-4 px-6" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="flex gap-2 ">
              <HookFormRadixUIField
                form={form}
                fieldName="ficoScore"
                formLabel={'FICO Score'}
                className="flex-none w-2/6"
              />
              <HookFormRadixUIField
                form={form}
                fieldName="criminalBackgroundCheck"
                formLabel={'Criminal Background Check'}
              />
            </div>

            <Button
              variant={'outline'}
              className={'border border-primaryPurple text-primaryPurple my-2 rounded-md px-4'}
              type="submit"
              isLoading={isSubmitting}
              isDisabled={isSubmitting}
              colorScheme="purple"
              size="sm"
            >
              {'Save & Update'}
            </Button>
          </form>
        </Form>
      }
    </Card>
  );
};
