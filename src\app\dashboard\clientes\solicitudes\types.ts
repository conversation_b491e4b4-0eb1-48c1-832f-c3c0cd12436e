import { AnalysisStatus, MLModels } from './enums';

export type ModelResult = {
  modelName: MLModels;
  status: AnalysisStatus;
  modelScore: number;
  modelFeatureScores: object;
  modelWeights: object;
  modelResult: {
    message?: string;
    [key: string]: any;
  };
};

export type ModelScores = {
  [MLModels.RIDESHARE_PERFORMANCE]: ModelResult;
  [MLModels.FINANCIAL_ASSESSMENT]: ModelResult;
  [MLModels.PERSONAL_INFORMATION]: ModelResult;
  [MLModels.HOMEVISIT_INFORMATION]: ModelResult;
  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: ModelResult;
};
