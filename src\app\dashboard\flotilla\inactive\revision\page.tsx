import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';
import VehicleTable from '../../components/VehicleTable';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function RevisionPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });
  //console.log(`definitiveFilters: ${JSON.stringify(definitiveFilters, null, 2)}`);

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 10,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'inactive',
      category: definitiveFilters?.category || 'revision',
      subCategory: definitiveFilters?.subCategory || 'aesthetic-repair',
      country: definitiveFilters?.country,
    },
  });

  const subOptions = [
    { label: 'Aesthetic Repair', name: 'aesthetic-repair' },
    { label: 'Duplicate Key Missing', name: 'duplicate-key-missing' },
    { label: 'Mechanical Repair', name: 'mechanical-repair' },
    { label: 'Electrical Repair', name: 'electrical-repair' },
    { label: 'Engine Repair', name: 'engine-repair' },
    { label: 'Waiting For Parts', name: 'waiting-for-parts' },
    { label: 'Corrective Maintenance', name: 'corrective-maintenance' },
    { label: 'Management', name: 'management' },
    { label: 'GPS', name: 'gps' },
  ];

  const page = {
    name: 'Inactive',
    api: 'inactive',
    count: 12,
  };

  const subPage = {
    name: 'Revision',
    api: 'revision',
    count: 12,
  };

  if (!result) return null;

  return (
    <VehicleTable
      route="revision"
      page={page}
      subPage={subPage}
      data={result.stock}
      totalCount={result.totalCount}
      subOptions={subOptions}
    />
  );
}
