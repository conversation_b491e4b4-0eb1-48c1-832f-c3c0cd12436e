'use client';
import { Font, StyleSheet } from '@react-pdf/renderer';
import dynamic from 'next/dynamic';
import ReadmissionDocument, { ReadmissionPdfProps } from './ReadmissionDocument';

const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), {
  ssr: false,
  // loading: () => <p>Loading...</p>,
});

Font.register({ family: 'Helvetica', fonts: [] });
Font.register({ family: 'Helvetica-Oblique', fonts: [] });
Font.register({ family: 'Helvetica-BoldOblique', fonts: [] });
Font.register({ family: 'Helvetica-Bold', fontWeight: 800, fonts: [] });

// Create styles

export default function ReadmissionPdf({ data }: ReadmissionPdfProps) {
  return (
    <PDFViewer style={styles.viewer}>
      <ReadmissionDocument data={data} />
    </PDFViewer>
  );
}

const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },
  viewer: {
    width: '100%',
    height: '100vh',
  },
  header: {
    width: '100vw',
  },
  headerTitle: {
    textAlign: 'justify',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 11,
    fontWeight: 800,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
    // letterSpacing: 1.5,
  },
  body: {
    flexDirection: 'column',
    rowGap: 1,
    // marginTop: '10%',
    // marginBottom: '20px',
    // marginLeft: '10%',
    // marginRight: '10%',
    marginVertical: '10%',
    marginHorizontal: '15%',
  },
  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    bottom: '25px',
    left: '40%',
    height: '80px',
    width: '25%',
    transform: 'rotate(10deg)',
  },
  anexoTitle: {
    textAlign: 'center',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 14,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    zIndex: 1,
  },

  anexoText: {
    color: 'black',
    // fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    textAlign: 'justify',
    letterSpacing: '0.2',
    lineHeight: '1.2',
    // lineHeight: '2px',
    // zIndex: 1,
  },

  viewMain: {
    rowGap: 10,
    zIndex: 1,
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    rowGap: 8,
  },
  names: {
    fontSize: 10,
    textAlign: 'left',
    fontFamily: 'Helvetica-Bold',
    marginLeft: 20,
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 12,
  },
});
