import axios from 'axios';
import { URL_API } from '@/constants';

export interface UpdatePasswordForm {
  code: string;
  password: string;
}

export default async function changePassword(body: UpdatePasswordForm) {
  try {
    const data = {
      ...body,
    };

    const url = new URL(`${URL_API}/auth/changePassword`);

    // add body.code to query params

    if (data.code) {
      url.searchParams.append('code', data.code);
    }

    const response = await axios.post(`${url}`, data);

    return response.data;
  } catch (error: any) {
    // return new Error(error.response.data.message);
    return null;
  }
}
