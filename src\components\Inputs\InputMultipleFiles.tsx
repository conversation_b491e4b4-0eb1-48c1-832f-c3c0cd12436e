import { Input, Button } from '@chakra-ui/react';
import { ErrorMessage, Field, FieldInputProps, FormikValues, useField, useFormikContext } from 'formik';
import { useMemo, useRef, useState } from 'react';
import AddMoreFiles from './AddMoreFiles';
import ZoomImage from '@/app/dashboard/flotilla/components/others/ZoomImage';
import { MdDelete } from 'react-icons/md';

interface FieldProps {
  name: string;
  label: string;
  buttonText: string;
  accept: 'pdf' | 'all-images' | 'jpg' | 'png';
  // the function below has to remove the state or states of the name file
  placeholder: string;
  placeHolderDown?: boolean;
  onChange?: (event: FileList | null) => void;
}

const acceptTypes: { [key: string]: string } = {
  'all-images': '.jpg, .jpeg, .png, .webp',
  jpg: 'image/jpg',
  png: 'image/png',
  pdf: 'application/pdf',
};

export default function InputMultipleFiles({
  name,
  label,
  accept,
  placeholder,
  placeHolderDown,
  buttonText,
  onChange,
}: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [fileList, setFileList] = useState<FileList>({ length: 0 } as FileList);

  const [, meta] = useField(name);
  const [isDragging, setIsDragging] = useState(false);
  const hasError = meta.touched && meta.error;

  const formikForm = useFormikContext();

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = () => {
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (event: React.DragEvent<HTMLButtonElement>, form: any) => {
    event.preventDefault();
    setIsDragging(false);

    const droppedFiles = event.dataTransfer.files;

    if (!droppedFiles) return;
    if (onChange) onChange(droppedFiles);
    form.setFieldValue(name, droppedFiles);
  };

  const arrayWithBlobUrl = useMemo(() => {
    return Array.from(fileList).map((file) => ({ ...file, url: URL.createObjectURL(file) }));
  }, [fileList]);
  return (
    <>
      {fileList?.length < 1 ? (
        <Field name={name} id={name}>
          {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => (
            <div className="relative font-normal ">
              <label htmlFor={name}>{label}</label>
              <div
                className={`flex ${
                  placeHolderDown ? 'flex-col gap-1' : 'flex-row items-center gap-3'
                } mt-2 w-full overflow-hidden `}
                style={{ gridTemplateColumns: 'max-content 1fr' }}
              >
                <Button
                  h="40px"
                  minW="138px"
                  w="max-content"
                  borderColor={isDragging ? '#9CA3AF !important' : '#5800F7 !important'}
                  color="#5800F7 !important"
                  fontWeight={600}
                  border={isDragging ? '2px dashed' : '2px solid'}
                  sx={{
                    '&::placeholder': {
                      color: '#5800F7',
                    },
                    _hover: {
                      borderColor: '#5800F7',
                    },
                  }}
                  cursor="pointer"
                  onClick={handleButtonClick}
                  onDragEnter={handleDragEnter} // Manejadores de eventos de arrastre
                  onDragLeave={handleDragLeave} // Manejadores de eventos de arrastre
                  onDragOver={(event) => event.preventDefault()} // Evita comportamiento por defecto
                  onDrop={(e) => handleFileDrop(e, form)}
                >
                  {buttonText}
                </Button>
                <div className={`text-[14px]`}>
                  <p>{placeholder}</p>
                </div>
              </div>

              <Input
                h="45px"
                display="none"
                name={name}
                ref={fileInputRef}
                cursor="pointer"
                accept={acceptTypes[accept]}
                type="file"
                multiple={true}
                onChange={(event) => {
                  const files = event.currentTarget.files;
                  if (onChange) {
                    onChange(files);
                  }
                  if (files) {
                    setFileList(files);
                    form.setFieldValue(name, files);
                  }
                }}
              />

              {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
            </div>
          )}
        </Field>
      ) : (
        <>
          <p>{label}</p>
          <div className="flex gap-3 flex-wrap ">
            {arrayWithBlobUrl.map((file, index) => (
              <div className="relative " key={index}>
                <div
                  className="
                          absolute top-[-4px] right-[-10px] z-[3] 
                          cursor-pointer bg-textGray2 
                          rounded text-red-500 
                        "
                  onClick={() => {
                    const find = fileList[index];
                    const filter = Array.from(fileList).filter((img) => img !== find);
                    const combinedFileList = new DataTransfer();
                    filter.forEach((f) => {
                      combinedFileList.items.add(f);
                    });
                    formikForm.setFieldValue(name, combinedFileList.files);
                    setFileList(combinedFileList.files);
                    // setAddImgs();
                  }}
                >
                  <MdDelete size={22} />
                </div>
                {/* <img src={URL.createObjectURL(file)} alt={`Imagen ${index + 1}`} /> */}
                <ZoomImage imageUrl={file.url} name={file.name} key={index} />
                {/* <ZoomImage imageUrl={URL.createObjectURL(file)} name={file.name} key={index} /> */}
              </div>
            ))}
            <AddMoreFiles
              name={name}
              accept={accept}
              currentImages={fileList}
              multiple
              onChange={(files) => {
                setFileList(files);
                if (onChange) {
                  onChange(files);
                }
              }}
            />
          </div>
        </>
      )}
    </>
  );
}
