import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { listStatus } from '../activos/[id]/lib';
import { combineSearchParamsAndFilters } from '@/constants';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function StockPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 50,
    listStatus: ['stock', 'overhauling'],
    searchParams: definitiveFilters,
    excludeStatus: listStatus,
  });

  if (!result) return null;

  return (
    <VehicleInfiniteList route="stock" page="Stock" data={result.stock} totalCount={result.totalCount} />
  );
}
