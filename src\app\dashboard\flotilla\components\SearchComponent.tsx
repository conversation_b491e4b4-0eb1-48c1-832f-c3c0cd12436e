'use client';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import Card from '@/components/stockCard/Card';
import Spinner from '@/components/Loading/Spinner';
import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import SearchInput from './PageBar/SearchInput';

const SearchFlotilla = ({ page }: { page: string }) => {
  const search = useSearchParams();

  const searchQuery = search ? search.get('q') : null;
  const vehicleStatus = search ? search.get('vehicleStatus') : null;
  const country = search ? search.get('country') : null;
  const [isLoading, setIsLoading] = useState(false);
  const [isError, setIsError] = useState(false);
  const [data, setData] = useState<any>(null);
  const encodedSearchQuery = encodeURI(searchQuery || '');
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const getQuery2 = useCallback(
    async (query: string) => {
      setIsLoading(true);
      try {
        const res = await axios(
          `${URL_API}/stock/search?search=${query}${
            vehicleStatus ? `&vehicleStatus=${encodeURI(vehicleStatus)}` : ''
          }${country ? `&country=${encodeURI(country)}` : ''}`,
          {
            headers: {
              Authorization: 'Bearar ' + user.accessToken,
            },
          }
        );
        setData(res.data);
      } catch (err: any) {
        console.error(err);
        setIsError(true);
      } finally {
        setIsLoading(false);
      }
    },
    [user, country, vehicleStatus]
  );

  useEffect(() => {
    if (user?.accessToken) {
      getQuery2(encodedSearchQuery);
    }
  }, [encodedSearchQuery, getQuery2, user?.accessToken]);

  if (isLoading) {
    return <Spinner />;
  }
  return (
    <div>
      {isError || !data || data.every((car: any) => !car._id) ? (
        <div className="flex justify-between items-center mb-[20px] flex-wrap ">
          {page && page?.toLowerCase() === 'active' ? (
            <SearchInput page={page} vehicleStatus={page?.toLowerCase()} />
          ) : null}
        </div>
      ) : (
        <div className="flex justify-between items-center mb-[20px] flex-wrap ">
          Mostrando {data.length} de {data.length} resultados para &quot;{searchQuery}&quot;
          {page && page?.toLowerCase() === 'active' ? (
            <SearchInput page={page} vehicleStatus={page?.toLowerCase()} />
          ) : null}
        </div>
      )}
      <div className="flex flex-row flex-wrap mt-8 gap-x-5 gap-y-5">
        {isError || !data || data.every((car: any) => !car._id) ? (
          <div>
            <h1>No se encontraron vehiculos con el criterio de busqueda</h1>
          </div>
        ) : (
          data.map((car: any, key: number) => {
            return (
              <Link
                href={`/dashboard/flotilla/${page.toLowerCase()}/${car._id}${
                  country ? `?country=${encodeURI(country)}` : ''
                }`}
                key={key}
                prefetch={false}
              >
                <Card
                  {...car}
                  contract={car.carNumber}
                  extensionCarNumber={car.extensionCarNumber}
                  status={car.status}
                  dischargedReason={car.dischargedData?.reason}
                  isBlocked={car.isBlocked}
                />
              </Link>
            );
          })
        )}
      </div>
    </div>
  );
};

export default SearchFlotilla;
