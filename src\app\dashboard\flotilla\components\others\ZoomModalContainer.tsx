/* eslint-disable @next/next/no-img-element */
/* eslint-disable prettier/prettier */
import { AiOutlineClose } from 'react-icons/ai';
// import ZoomeableV2 from './ZoomeableV2';
import { FiDownload } from 'react-icons/fi';
import { useZoom } from '@/hooks/useZoom';
// import ZoomableImage from './ZoomableImage';
import { TransformComponent, TransformWrapper } from 'react-zoom-pan-pinch';
import { handleDownload } from '@/utils/handle-download';
import { useState } from 'react';
import { cn } from '@/lib/utils';

export default function ZoomModalContainer() {
  const { isOpen, image, onClose } = useZoom();

  const [downloading, setDownloading] = useState(false);

  return (
    <>
      {isOpen && (
        <div className="fixed top-0 left-0 z-[600]">
          <div
            className="
          flex 
          absolute 
          top-0 
          left-0 
          z-[600]
          justify-center 
          pt-[100px]
          w-[100vw]
          h-[100vh]
          bg-black 
          bg-opacity-50
          "
          >
            <div
              className="
          fixed 
          right-[30px]
          top-[30px]
          z-[100]
          flex gap-3
          items-center
        "
            >
              <button
                type='submit'
                disabled={downloading}
                // className="rounded-full bg-[#5800F7] p-2 flex justify-center items-center cursor-pointer "
                className={cn(
                  "rounded-full  p-2 flex justify-center items-center cursor-pointer",
                  downloading ? "bg-gray-500" : "bg-[#5800F7]"
                )}
                onClick={async () => {
                  setDownloading(true);
                  await handleDownload(image.url, image.originalName);
                  setTimeout(() => {
                    setDownloading(false);
                  }, 1200);
                }}
              >
                <FiDownload size={26} color="white" />
              </button>
              <div
                className="
            w-[60px] h-[60px] 
            flex
            gap-3
            justify-center
            items-center
            border-solid 
            border-white 
            rounded-full
            border-[4px]
            cursor-pointer
            "
                onClick={onClose}
              >
                <AiOutlineClose size={30} color="white" />
              </div>
            </div>
            <TransformWrapper
              limitToBounds={false}
              disabled={false}
              maxScale={3}
              pinch={{ disabled: false }}
              doubleClick={{ disabled: false }}
              wheel={{
                wheelDisabled: false,
                touchPadDisabled: false,
              }}
            >
              <TransformComponent>
                <img src={image.url} alt={image.originalName} className='w-[1000px] h-[800px] object-contain ' />
              </TransformComponent>
            </TransformWrapper>
          </div>
        </div>
      )}
    </>
  );
}
