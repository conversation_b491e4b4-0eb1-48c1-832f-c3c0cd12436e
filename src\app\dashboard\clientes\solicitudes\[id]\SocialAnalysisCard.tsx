import { ModelScores } from '../types';
import AnalysisCard from './AnalysisCard';
import { getFinalModelScore } from './ScorecardCard';

export function SocialAnalysisCard({
  modelScores,
  hasError,
  isPending,
  isCountryUSA,
}: {
  modelScores: ModelScores;
  hasError: boolean;
  isPending: boolean;
  isCountryUSA: boolean;
}) {
  const socialAnalysisText = isCountryUSA ? 'Social Analysis' : 'Análisis Social';
  const seeDetailsText = isCountryUSA ? 'See detail' : 'Ver detalle';
  const pendingAnalysisText = isCountryUSA ? 'Analysis pending' : 'Análisis pendiente';
  const errorInAnalysisText = isCountryUSA ? 'Error in analysis' : 'Error en el análisis';

  if (hasError) {
    return (
      <AnalysisCard
        title={socialAnalysisText}
        numericScore={0}
        category=""
        seeDetailsText={seeDetailsText}
        isPending={true}
        pendingText={errorInAnalysisText}
      />
    );
  } else if (isPending && !hasError) {
    return (
      <AnalysisCard
        title={socialAnalysisText}
        numericScore={0}
        category=""
        seeDetailsText={seeDetailsText}
        isPending={true}
        pendingText={pendingAnalysisText}
      />
    );
  } else {
    const finalScore = getFinalModelScore(modelScores);
    const numericScore = Math.round(finalScore);
    let category = '';
    if (finalScore >= 66) category = 'low';
    else if (finalScore >= 33) category = 'medium';
    else category = 'high';
    return (
      <AnalysisCard
        title={socialAnalysisText}
        numericScore={numericScore}
        category={category}
        seeDetailsText={seeDetailsText}
        isPending={false}
      />
    );
  }
}
