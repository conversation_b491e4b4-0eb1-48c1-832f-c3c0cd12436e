import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON> } from '@chakra-ui/react';
import { BsThreeDots } from 'react-icons/bs';

interface Action {
  label: (data: any) => string;
  onClick: (data: any) => void;
}

interface CellActionProps {
  data: any;
  actions: Action[];
  row?: any;
}

export default function CellAction({ data, actions }: CellActionProps) {
  return (
    <Menu>
      <MenuButton className="p-2 hover:bg-transparent " _focus={{ bg: 'transparent' }}>
        <BsThreeDots className="w-4 h-4 " />
      </MenuButton>
      <MenuList>
        {actions.map((action, i) => {
          return (
            <div key={i}>
              {i !== 0 && <MenuDivider key={i + 1} className="my-[0px]" />}

              <MenuItem
                key={i}
                minH="40px"
                onClick={() => {
                  action.onClick(data);
                }}
              >
                <p>{action.label(data)}</p>
              </MenuItem>
            </div>
          );
        })}
        {/* <MenuItem minH="48px">
          <p>Something {data.status} </p>
        </MenuItem>
        <MenuItem minH="40px">
          <p>Something {data.status} </p>
        </MenuItem> */}
      </MenuList>
    </Menu>
  );
}
