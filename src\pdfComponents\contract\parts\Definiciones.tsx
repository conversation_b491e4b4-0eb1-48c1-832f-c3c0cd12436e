import React from 'react';
import { Text, View, StyleSheet, Font } from '@react-pdf/renderer';
import { definitionsForElectricVehicle, defsData } from '../data/DefinicionesList';
import { DeclaracionesList, DeclaracionesList2 } from '../data/DeclaracionesList';
import {
  ClauslasListFunction,
  clausulaLetter,
  clausulaLetter2Func,
  clausulaLetterEV,
  clausulasLetter3Func,
} from '../data/ClausulasList';

const fontConfig = {
  family: 'Helvetica',
  fonts: [],
};

Font.register(fontConfig);

const styles = StyleSheet.create({
  definicionContainer: {
    flexDirection: 'column',
    rowGap: 15,
  },

  item: {
    flexDirection: 'row',
    alignContent: 'center',
    marginLeft: '30px',
  },
  letterPoint: {
    fontSize: 8,
    marginRight: 17,
  },

  letterPointD: {
    fontSize: 8,
    marginRight: 10,
  },
  itemContent: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  defTitle: {
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
    fontWeight: 'bold',
  },
  clausulasContainer: {
    flexDirection: 'column',
    rowGap: '10px' as unknown as number,
  },

  clasulasBolds: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
  },
});

const List = ({ children }: { children: React.ReactNode }) => <>{children}</>;

export function DefItem({ isElectric }: { isElectric: boolean }) {
  const data = isElectric ? definitionsForElectricVehicle : defsData;

  return (
    <View style={styles.definicionContainer}>
      {data.map(({ letter, title, description }, i) => {
        return (
          <View style={styles.item} key={i}>
            <View>
              <Text style={styles.letterPoint}> {letter}) </Text>
            </View>

            <Text style={styles.itemContent}>
              {' '}
              <Text style={styles.defTitle}> {title} </Text> {description}{' '}
            </Text>
          </View>
        );
      })}
    </View>
  );
}

export const DeclaItem = () => (
  <View style={styles.definicionContainer}>
    {DeclaracionesList.map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);
export const DeclaItem2 = () => (
  <View style={styles.definicionContainer}>
    {DeclaracionesList2.map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);

const titleSemi = 'PRIMERA.- OBJETO. “EL ARRENDADOR”';
const descriptionSemi = `en este acto jurídico, hace entrega a “EL ARRENDATARIO”, de  un vehículo seminuevo, mismo que se encuentra descrito en la Constancia de Entrega “ANEXO B”, el cual forma parte integral de este contrato, que incluye marca, modelo, versión, número de serie, póliza de seguro, número placas, tarjeta de circulación y la entrega de documentos generales que se encuentran detalladas en el "ANEXO A" antes mencionado, así como el uso, goce y disfrute del mismo, en términos de lo establecido en este instrumento legal.`;

const title2 = 'SEXTA.- PLAZO DEL ARRENDAMIENTO.';
function description2(months: string | number) {
  return `La vigencia de este contrato será la señalada en el “ANEXO A”, esta consta de un plazo de ${months} meses computados a partir de la firma del presente contrato, la cual no podrá ser prorrogada sino con el pleno consentimiento de ambas partes expresado en un nuevo contrato de arrendamiento. `;
}

interface ClauItemSemiProps {
  months: string | number;
  isElectric: boolean;
}

export const ClauItemSemi = ({ months, isElectric }: ClauItemSemiProps) => (
  <View style={styles.clausulasContainer}>
    <View style={styles.item}>
      <Text style={styles.itemContent} break>
        {' '}
        {titleSemi ? <Text style={styles.clasulasBolds}> {titleSemi} </Text> : ''} {descriptionSemi}{' '}
      </Text>
    </View>
    {ClauslasListFunction({ isElectric })
      .slice(1)
      .map(({ title, description }, i) => {
        if (!title && !description) return null;
        if (title === 'SEXTA.- PLAZO DEL ARRENDAMIENTO.') {
          return (
            <View style={styles.item} key={i}>
              <Text style={styles.itemContent} break>
                {' '}
                {title2 ? <Text style={styles.clasulasBolds}> {title2} </Text> : ''} {description2(months)}{' '}
              </Text>
            </View>
          );
        }
        return (
          <View style={styles.item} key={i}>
            <Text style={styles.itemContent} break>
              {' '}
              {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
            </Text>
          </View>
        );
      })}
  </View>
);

export const ClauList = () => (
  <View style={styles.definicionContainer}>
    {clausulaLetter.map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);
export const ClauListEV = () => (
  <View style={styles.definicionContainer}>
    {clausulaLetterEV.map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);

export const ClauList2 = ({ isElectric }: { isElectric: boolean }) => (
  <View style={styles.definicionContainer}>
    {clausulaLetter2Func({ isElectric }).map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);

export const ClauList3 = ({ isElectric }: { isElectric: boolean }) => (
  <View style={styles.definicionContainer}>
    {clausulasLetter3Func({ isElectric }).map(({ letter, description }, i) => {
      return (
        <View style={styles.item} key={i}>
          <View>
            <Text style={styles.letterPointD}> {letter}) </Text>
          </View>
          <Text style={styles.itemContent}> {description} </Text>
        </View>
      );
    })}
  </View>
);

export default List;
