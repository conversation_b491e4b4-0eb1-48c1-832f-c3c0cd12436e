/* eslint-disable consistent-return */
import { Input, Button } from '@chakra-ui/react';
import { ErrorMessage, Field, FieldInputProps, FormikValues, useField } from 'formik';
import { useRef, useState } from 'react';

interface FieldProps {
  name: string;
  accept: 'pdf' | 'all-images' | 'jpg' | 'png';
  // the function below has to remove the state or states of the name file
  handleSetName?: (name: string, value: string) => void;
  handleSingleSetName?: (value: string) => void;
  placeHolderDown?: boolean;
  multiple?: boolean;
  onChange?: (event: FileList) => void;
  currentImages: FileList;
}

const acceptTypes: { [key: string]: string } = {
  'all-images': '.jpg, .jpeg, .png, .webp',
  jpg: 'image/jpg',
  png: 'image/png',
  pdf: 'application/pdf',
};

export default function AddMoreFiles({
  name,
  accept,
  multiple = false,
  onChange,
  currentImages,
}: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, meta] = useField(name);
  const [isDragging, setIsDragging] = useState(false);
  const hasError = meta.touched && meta.error;
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = () => {
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (event: React.DragEvent<HTMLButtonElement>, form: any) => {
    event.preventDefault();
    setIsDragging(false);

    const droppedFile = event.dataTransfer.files[0];
    if (!droppedFile) return;
    if (!multiple) {
      return form.setFieldValue(name, droppedFile);
    }

    const files = event.dataTransfer.files;

    const listFile: File[] = [];

    // Agregar archivos de la primera FileList
    // if (currentImage)
    for (let i = 0; i < currentImages.length; i++) {
      listFile.push(currentImages[i]);
    }

    // Agregar archivos de la segunda FileList
    for (let i = 0; i < files.length; i++) {
      listFile.push(files[i]);
    }

    const mergedFileList = new DataTransfer();
    listFile.forEach((file) => {
      mergedFileList.items.add(file);
    });

    if (onChange) {
      onChange(mergedFileList.files);
      form.setFieldValue(name, mergedFileList.files);
    }
  };

  return (
    <Field name={name} id={name}>
      {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <div className="relative font-normal ">
          <Button
            // h="40px"
            // w="max-content"
            className="w-[80px] h-[80px] p-0 text-[50px] font-bold"
            borderColor={isDragging ? '#9CA3AF !important' : '#5800F7 !important '}
            color={`${isDragging ? '#9CA3AF !important' : '#5800F7 !important'} `}
            fontWeight={600}
            border={isDragging ? '2px dashed' : '2px solid'}
            sx={{
              '&::placeholder': {
                color: isDragging ? '#9CA3AF !important' : '#5800F7',
              },
              _hover: {
                borderColor: isDragging ? '#9CA3AF !important' : '#5800F7',
              },
            }}
            cursor="pointer"
            onClick={handleButtonClick}
            onDragEnter={handleDragEnter} // Manejadores de eventos de arrastre
            onDragLeave={handleDragLeave} // Manejadores de eventos de arrastre
            onDragOver={(event) => event.preventDefault()} // Evita comportamiento por defecto
            onDrop={(e) => handleFileDrop(e, form)}
          >
            +
          </Button>

          <Input
            h="45px"
            display="none"
            name={name}
            ref={fileInputRef}
            cursor="pointer"
            accept={acceptTypes[accept]}
            type="file"
            multiple={multiple}
            onChange={(event) => {
              const files = event.currentTarget.files;
              if (files) {
                const listFile: File[] = [];

                // Agregar archivos de la primera FileList
                // if (currentImage)
                for (let i = 0; i < currentImages.length; i++) {
                  listFile.push(currentImages[i]);
                }

                // Agregar archivos de la segunda FileList
                for (let i = 0; i < files.length; i++) {
                  listFile.push(files[i]);
                }

                // Crear un nuevo FileList con los archivos combinados
                const mergedFileList = new DataTransfer();
                listFile.forEach((file) => {
                  mergedFileList.items.add(file);
                });
                if (onChange) {
                  onChange(mergedFileList.files);
                  form.setFieldValue(name, mergedFileList.files);
                }
              }
            }}
          />

          {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
        </div>
      )}
    </Field>
  );
}
