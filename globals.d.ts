interface CustomerData {
  name: string;
  email: string;
  phone: string;
}

interface DayInfo {
  date: Date | null;
  disabled: boolean;
  isToday?: boolean;
}

interface ServiceType {
  _id: string;
  name: string;
  description?: string;
  duration: number;
  organization: string;
  isActive: boolean;
}

interface SolidarityObligatorDetails {
  name: string;
  phone: string;
  email: string;
  location: string;
}
