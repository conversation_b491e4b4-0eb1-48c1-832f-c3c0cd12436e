/* eslint-disable consistent-return */
'use client';
import { useReturnChangeRegion } from '@/zustand/modalStates';
import ModalContainer from './ModalContainer';
import FormikContainer from '@/components/Formik/FormikContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import { useState } from 'react';
import Swal from 'sweetalert2';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { useParams, useRouter } from 'next/navigation';
import { CONTRACT_REGIONS_IATA } from '@/constants';
import { Region } from '../vehiclesContext';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import Spinner from '@/components/Loading/Spinner';

const initialValues = {
  step: { value: '', label: 'Selecciona' },
  deleteAll: { label: 'No', value: 'no' },
};

export default function ChangeRegion() {
  const returnChangeRegion = useReturnChangeRegion();

  const [region, setRegion] = useState<Region>('' as Region);
  const [loading, setLoading] = useState(false);

  const { user } = useCurrentUser();
  const { id } = useParams();
  const router = useRouter();
  const toast = useToast();
  const onSubmit = async (values: typeof initialValues) => {
    try {
      setLoading(true);
      const response = await axios.post(
        `${URL_API}/stock/change-state`,
        {
          id,
          region,
        },
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      returnChangeRegion.onClose();
      toast({
        title: response.data.message || `Vehiculo regresado a ${values.step.value}`,
        status: 'info',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => {
        return router.refresh();
      }, 3000);
    } catch (error: any) {
      return await Swal.fire({
        icon: 'error',
        title: 'Hubo un error',
        text: error.message || '',
      });
    } finally {
      setLoading(false);
    }
  };

  const confirSubmit = async (values: typeof initialValues) => {
    Swal.fire({
      title: `¿Desea cambiar el auto de región?`,
      text: 'Esta accion no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Si, cambiar región',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  return (
    <>
      {loading && <Spinner />}
      <ModalContainer title="Cambiar auto de región" onClose={returnChangeRegion.onClose}>
        <FormikContainer
          onSubmit={async (values) => {
            await confirSubmit(values);
          }}
          initialValues={initialValues}
          // hideFooter
        >
          <div className="flex flex-col gap-3">
            <SelectInput
              name="vehicleState"
              label="Región"
              options={CONTRACT_REGIONS_IATA}
              onChange={(option) => setRegion(option.value as Region)}
              dataCy="region-selector"
            />
          </div>
        </FormikContainer>
      </ModalContainer>
    </>
  );
}
