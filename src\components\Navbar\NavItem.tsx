'use client';
import { Flex, FlexProps, Icon } from '@chakra-ui/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { IconType } from 'react-icons';

interface NavItemProps extends FlexProps {
  icon: IconType;
  children: string;
  link: string;
}
const NavItem = ({ icon, children, link, ...rest }: NavItemProps) => {
  const pathname = usePathname();

  return (
    <Link href={link} prefetch={false}>
      <Flex
        align="center"
        p="4"
        mx="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        color={link === pathname ? 'white' : 'black'}
        bg={link === pathname ? '#5800F7' : ''}
        _hover={{
          bg: '#5800F7',
          color: 'white',
        }}
        {...rest}
      >
        {icon && (
          <Icon
            mr="4"
            fontSize="16"
            _groupHover={{
              color: 'white',
            }}
            as={icon}
          />
        )}
        {children}
      </Flex>
    </Link>
  );
};

export default NavItem;
