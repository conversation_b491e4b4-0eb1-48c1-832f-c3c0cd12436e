import getCurrentUser from '@/actions/getCurrentUser';
import VehicleDetailLayout from '../../../components/Layouts/vehicleDetailLayout';
import DetailProvider from '../../../components/detailContext';
import getVehicleDetailCache from '@/actions/getVehicleData';

export const generateMetadata = async ({ params: { id } }: Params) => {
  const vehicleDetail = await getVehicleDetailCache(id);
  if (vehicleDetail) {
    return {
      title: `${vehicleDetail.brand} ${vehicleDetail.model}`,
    };
  }
  return {
    title: 'Not found',
  };
};

type Params = {
  params: {
    id: string;
  };
};

export default async function CarDetail({ params: { id } }: Params) {
  const vehicleDetail = await getVehicleDetailCache(id);

  const user = await getCurrentUser();
  if (!user) return null;
  if (!vehicleDetail) return <>No se encontro el vehiculo...</>;
  // console.log('vehicleDetail', vehicleDetail);
  const drivers = vehicleDetail.drivers;
  // console.log(drivers);
  const lastDriver = drivers[drivers.length - 1];

  return (
    <DetailProvider>
      <VehicleDetailLayout
        vehicleDetail={vehicleDetail}
        lastDriver={lastDriver}
        drivers={drivers}
        user={user}
        page="inactive/workshop"
      />
    </DetailProvider>
  );
}
