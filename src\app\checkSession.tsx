'use client';
import { useEffect, useState } from 'react';
import { signOut } from 'next-auth/react';
import { useToast } from '@chakra-ui/react';

export default function CheckSession({ children, user }: { children: React.ReactNode; user: any }) {
  const toast = useToast();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [counter, setCounter] = useState(0);
  useEffect(() => {
    const checkSessionExpiration = () => {
      return currentDate > new Date(user.expiration * 1000);
    };

    const interval = setInterval(() => {
      setCurrentDate(new Date());
    }, 1000);

    const boolean = checkSessionExpiration();
    if (!user || (boolean && counter === 0)) {
      setCounter(1);
      toast({
        status: 'info',
        title: 'Sesión expirada, redirigiendo....',
        description: 'Vuelve a iniciar sesión por favor',
        isClosable: true,
        position: 'top',
        duration: 2000,
      });
      setTimeout(() => {
        signOut();
      }, 2000);
    }

    return () => clearTimeout(interval);
    // }
  }, [currentDate, toast, user, counter]);

  return <>{children}</>;
}
