import { useState } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';

const initialState = {
  limitToBounds: false,
  disabled: false,
  maxScale: 3,
  defaultScale: 0.9,
};

export default function ZoomeableV2({ imageUrl }: { imageUrl: string }) {
  const [settings] = useState(initialState);
  const { limitToBounds, disabled, maxScale } = settings;

  return (
    <TransformWrapper
      limitToBounds={limitToBounds}
      disabled={disabled}
      maxScale={maxScale}
      pinch={{ disabled: false }}
      doubleClick={{ disabled: false }}
      wheel={{
        wheelDisabled: false,
        touchPadDisabled: false,
      }}
    >
      <TransformComponent>
        <div
          className={`
            w-[1000px]
            h-[800px]
            bg-contain
            bg-center
            bg-no-repeat
            rounded
            `}
          style={{ backgroundImage: `url(${imageUrl})`, backgroundRepeat: 'no-repeat' }}
        />
      </TransformComponent>
    </TransformWrapper>
  );
}
