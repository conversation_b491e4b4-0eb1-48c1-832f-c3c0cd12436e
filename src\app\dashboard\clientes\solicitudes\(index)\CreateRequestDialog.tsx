'use client';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalCloseButton,
  ModalHeader,
  ModalOverlay,
  ModalContent,
  Grid,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  ModalFooter,
  InputGroup,
  InputLeftAddon,
  useToast,
  Select,
} from '@chakra-ui/react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { Formik, FormikValues, Form, Field } from 'formik';
import {
  createAdmissionRequestSchema,
  createAdmissionRequestSchemaUS,
} from '@/validatorSchemas/createAdmissionRequestSchema';
import { useState } from 'react';
import {
  Countries,
  CountriesShortNames,
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  URL_API,
  US_COUNTRY_CODE,
  US_DEFAULT_STATE_OPTIONS,
  US_STATES_DEFAULT_CITIES,
  USSTATES,
  USVehiclesList,
} from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';

export const CreateRequestDialog = () => {
  const searchParams = useSearchParams();
  const isCountryUSA = searchParams.get('country') === Countries['United States'];

  if (isCountryUSA) {
    return <CreateRequestDialogUS />;
  }
  return <CreateRequestDialogMX />;
};

function CreateRequestDialogMX() {
  const router = useRouter();
  const toast = useToast();
  const pathName = usePathname();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const isOpen = searchParams.get('dialog') === 'create-request';
  const hardCodedCountryCode = '+52';

  function onClose() {
    router.back();
  }

  const defailtValues = {
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
  };

  async function handleSubmit(values: FormikValues) {
    if (!user) return null;
    setIsSubmitting(true);
    // Trim and set null if empty string
    const formattedPhone = values.phone ? `${hardCodedCountryCode}${values.phone.trim()}` : null;
    const payload = {
      firstName: values.firstName.trim() || null,
      lastName: values.lastName.trim() || null,
      phone: formattedPhone,
      email: values.email.trim() || null,
    };
    const res = await fetch(`${URL_API}/admission/requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify(payload),
    });
    setIsSubmitting(false);
    const response = await res.json();

    if (!res.ok) {
      toast({
        title: response?.message || 'Se produjo un error al crear un nuevo cliente.',
        status: 'error',
      });
      return;
    }

    if (response && response.success) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete('dialog');
      router.replace(`${pathName}?${params.toString()}`, { scroll: false });
      router.refresh();
      toast({
        title: 'Solicitud creada',
        status: 'success',
      });
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>Crear solicitud</ModalHeader>
        <ModalCloseButton />
        <Formik
          initialValues={defailtValues}
          validationSchema={createAdmissionRequestSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isValid }) => {
            const validate = isValid;
            return (
              <Form>
                <ModalBody>
                  <Grid gap={4}>
                    <FormControl isInvalid={!!(touched.firstName && errors.firstName)}>
                      <FormLabel>Nombre</FormLabel>
                      <Field name="firstName" as={Input} />
                      <FormErrorMessage>{errors.firstName}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.lastName && errors.lastName)}>
                      <FormLabel>Apellidos</FormLabel>
                      <Field name="lastName" as={Input} />
                      <FormErrorMessage>{errors.lastName}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.phone && errors.phone)}>
                      <FormLabel>Teléfono</FormLabel>
                      <InputGroup>
                        <InputLeftAddon>{hardCodedCountryCode}</InputLeftAddon>
                        <Field name="phone" as={Input} />
                      </InputGroup>
                      <FormErrorMessage>{errors.phone}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.email && errors.email)}>
                      <FormLabel>Correo electrónico</FormLabel>
                      <Field name="email" as={Input} />
                      <FormErrorMessage>{errors.email}</FormErrorMessage>
                    </FormControl>
                  </Grid>
                </ModalBody>
                <ModalFooter gap={3}>
                  <Button
                    sx={{
                      color: '#5800F7',
                      borderColor: '#5800F7 !important',
                      border: '1px',
                      h: '40px',
                    }}
                    onClick={onClose}
                  >
                    Cancelar
                  </Button>
                  <Button
                    sx={{
                      // bg: '#5800F7 !important',
                      color: 'white',
                      h: '40px',
                    }}
                    className={
                      validate
                        ? 'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'
                        : 'bg-gray-400 cursor-not-allowed'
                    }
                    type="submit"
                    disabled={validate}
                    isLoading={isSubmitting}
                  >
                    Agregar
                  </Button>
                </ModalFooter>
              </Form>
            );
          }}
        </Formik>
      </ModalContent>
    </Modal>
  );
}

function CreateRequestDialogUS() {
  const router = useRouter();
  const toast = useToast();
  const searchParams = useSearchParams();
  const pathName = usePathname();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const isOpen = searchParams.get('dialog') === 'create-request';

  function onClose() {
    router.back();
  }

  const defailtValues = {
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    state: US_DEFAULT_STATE_OPTIONS[0].value,
    city: US_STATES_DEFAULT_CITIES[USSTATES.Texas][0].value,
    postalCode: '',
    vehicleSelected: '',
  };

  async function handleSubmit(values: FormikValues) {
    if (!user) return null;
    setIsSubmitting(true);

    const formattedPhone = values.phone ? `${US_COUNTRY_CODE}${values.phone.trim()}` : null;
    const payload = {
      firstName: values.firstName.trim() || null,
      lastName: values.lastName.trim() || null,
      phone: formattedPhone,
      email: values.email.trim() || null,
      country: CountriesShortNames['United States'],
      state: values.state,
      city: values.city,
      postalCode: values.postalCode.trim() || null,
      vehicleSelected: values.vehicleSelected || null,
    };

    const res = await fetch(`${URL_API}/admission/requests`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify(payload),
    });
    setIsSubmitting(false);
    const response = await res.json();
    if (!res.ok) {
      toast({
        title: response?.message || 'An error occurred while creating a new client',
        status: 'error',
      });
      return;
    }

    if (response && response.success) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete('dialog');
      router.replace(`${pathName}?${params.toString()}`, { scroll: false });
      router.refresh();
      toast({
        title: `Successfully created user`,
        status: 'success',
      });
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{'Create request'}</ModalHeader>
        <ModalCloseButton />
        <Formik
          initialValues={defailtValues}
          validationSchema={createAdmissionRequestSchemaUS}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isValid, values }) => {
            const validate = isValid;
            return (
              <Form>
                <ModalBody>
                  <Grid gap={4}>
                    <FormControl isInvalid={!!(touched.firstName && errors.firstName)}>
                      <FormLabel>{'First Name'}</FormLabel>
                      <Field name="firstName" as={Input} />
                      <FormErrorMessage>{errors.firstName}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.lastName && errors.lastName)}>
                      <FormLabel>{'Last Name'}</FormLabel>
                      <Field name="lastName" as={Input} />
                      <FormErrorMessage>{errors.lastName}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.phone && errors.phone)}>
                      <FormLabel>{'Phone'}</FormLabel>
                      <InputGroup>
                        <InputLeftAddon>{US_COUNTRY_CODE}</InputLeftAddon>
                        <Field name="phone" as={Input} />
                      </InputGroup>
                      <FormErrorMessage>{errors.phone}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.email && errors.email)}>
                      <FormLabel>{'Email'}</FormLabel>
                      <Field name="email" as={Input} />
                      <FormErrorMessage>{errors.email}</FormErrorMessage>
                    </FormControl>
                    <FormControl isInvalid={!!(touched.state && errors.state)}>
                      <FormLabel>{'State'}</FormLabel>
                      <Field name="state" as={Select} placeholder="State">
                        {getUSStatesOptions().map((state, index) => (
                          <option key={index} value={state.value}>
                            {state.label}
                          </option>
                        ))}
                      </Field>
                      <FormErrorMessage>{errors.state}</FormErrorMessage>
                    </FormControl>

                    <FormControl isInvalid={!!(touched.city && errors.city)}>
                      <FormLabel>{'City'}</FormLabel>
                      <Field name="city" as={Select} placeholder="City">
                        {(values.state ? getUSCitiesBasedOnState(values.state) : []).map((city, index) => (
                          <option key={index} value={city.value}>
                            {city.label}
                          </option>
                        ))}
                      </Field>
                      <FormErrorMessage>{errors.city}</FormErrorMessage>
                    </FormControl>

                    <FormControl isInvalid={!!(touched.postalCode && errors.postalCode)}>
                      <FormLabel>{'Postal Code'}</FormLabel>
                      <Field name="postalCode" as={Input} />
                      <FormErrorMessage>{errors.postalCode}</FormErrorMessage>
                    </FormControl>

                    <FormControl isInvalid={!!(touched.vehicleSelected && errors.vehicleSelected)}>
                      <FormLabel>{'Vehicle selected'}</FormLabel>
                      <Field name="vehicleSelected" as={Select} placeholder="Vehicle selected">
                        {USVehiclesList.map((vehicle, index) => (
                          <option key={index} value={vehicle.value}>
                            {vehicle.label}
                          </option>
                        ))}
                      </Field>
                      <FormErrorMessage>{errors.vehicleSelected}</FormErrorMessage>
                    </FormControl>
                  </Grid>
                </ModalBody>
                <ModalFooter gap={3}>
                  <Button
                    sx={{
                      color: '#5800F7',
                      borderColor: '#5800F7 !important',
                      border: '1px',
                      h: '40px',
                    }}
                    onClick={onClose}
                    isDisabled={isSubmitting}
                  >
                    {'Cancel'}
                  </Button>
                  <Button
                    sx={{
                      color: 'white',
                      h: '40px',
                    }}
                    className={
                      validate
                        ? 'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'
                        : 'bg-gray-400 cursor-not-allowed'
                    }
                    type="submit"
                    disabled={validate}
                    isLoading={isSubmitting}
                  >
                    {'Add'}
                  </Button>
                </ModalFooter>
              </Form>
            );
          }}
        </Formik>
      </ModalContent>
    </Modal>
  );
}
