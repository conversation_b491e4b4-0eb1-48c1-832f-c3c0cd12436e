// src/casl/getServerAbility.ts
import { createAbility } from './createAbility';
import { getSession } from '@/actions/getCurrentUser';
import getUserById from '@/actions/getUserById';

export const getServerAbility = async () => {
  const session = await getSession();
  if (!session?.user) return null;

  const currentUser = await getUserById();
  const permissions = currentUser?.permissions || [];
  return createAbility(permissions);
};
