import { UseToastOptions } from '@chakra-ui/react';
import AddressInformation from './AddressInformation';
import AutoMobileInformation from './AutoMobileInformation';
import ContactInformation from './ContactInformation';
import DebtInformation from './DebtInformation';
import FamilyInformation from './FamilyInformation';
import OutcomeInformation from './OutcomeInformation';
import PersonalInformation from './PersonalInformation';
import ReferencesInformation from './ReferencesInformation';
import PropertyInformation from './PropertyInformation';
import { HomeVisitStepsStatus } from '@/constants';

export enum Steps {
  Personal = 'Personal',
  Contact = 'Contact',
  Address = 'Address',
  Family = 'Family',
  Property = 'Property',
  Automobile = 'Automobile',
  Debt = 'Debt',
  References = 'References',
  Outcome = 'Outcome',
}

export const steps = [
  { title: Steps.Personal },
  { title: Steps.Contact },
  { title: Steps.Address },
  { title: Steps.Family },
  { title: Steps.Property },
  { title: Steps.Automobile },
  { title: Steps.Debt },
  { title: Steps.References },
  { title: Steps.Outcome },
];

export const StepsComponents = [
  PersonalInformation,
  ContactInformation,
  AddressInformation,
  FamilyInformation,
  PropertyInformation,
  AutoMobileInformation,
  DebtInformation,
  ReferencesInformation,
  OutcomeInformation,
];

export const toastConfigs = (stepName: string, status: UseToastOptions['status']) => {
  const title =
    status === 'success'
      ? `Successfully updated ${stepName} Information`
      : status === 'error'
      ? `Error occured while updating ${stepName} Information`
      : 'Title';

  return {
    title: title,
    status: status,
    duration: 2000,
  };
};

export const allStepsCompleted = (homeVisitStepsStatus: Record<string, string>) => {
  const areAllStepsCompleted = Object.values(homeVisitStepsStatus).every(
    (step) => step === HomeVisitStepsStatus.complete
  );
  return areAllStepsCompleted;
};

export const anyStepInCompletedExceptOutcome = (homeVisitStepsStatus: Record<string, string>) => {
  const isAnyStepIncomplete = Object.entries(homeVisitStepsStatus).some(([key, step]) => {
    if (key === 'outcome') {
      return false;
    }
    return step === HomeVisitStepsStatus.incomplete || step === '';
  });
  return isAnyStepIncomplete;
};

export const firstIncompleteStepIndex = (homeVisitStepsStatus: Record<string, string>) => {
  if (!homeVisitStepsStatus) {
    return null;
  }
  return Object.values(homeVisitStepsStatus).findIndex((step) => {
    return step === HomeVisitStepsStatus.incomplete || step === '';
  });
};

export const NoSelected = 'Not-Selected';
