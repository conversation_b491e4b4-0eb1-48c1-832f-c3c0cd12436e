'use client';

import React, { useState } from 'react';
import {
  <PERSON>ton,
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ooter,
  ModalBody,
  ModalCloseButton,
  Text,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  Box,
  useColorModeValue,
  Select,
  Textarea,
  FormControl,
  FormLabel,
  useToast,
} from '@chakra-ui/react';
import { updatePhysicalStatusAdmin } from '@/services/physicalStatusService';
import { Countries, PhysicalVehicleStatus } from '@/constants';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { getStatusLabel } from './PhysicalStatusUpdateModal';

interface SuperadminPhysicalStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicleId: string;
  currentStatus: PhysicalVehicleStatus | string | null;
  accessToken: string;
  onStatusUpdated: () => void;
}

const SuperadminPhysicalStatusModal: React.FC<SuperadminPhysicalStatusModalProps> = ({
  isOpen,
  onClose,
  vehicleId,
  currentStatus,
  accessToken,
  onStatusUpdated,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<PhysicalVehicleStatus | ''>('');
  const [notes, setNotes] = useState('');
  const { country } = useCountry();
  const toast = useToast();

  // Initialize selectedStatus with currentStatus when modal opens
  React.useEffect(() => {
    if (isOpen && currentStatus) {
      setSelectedStatus(currentStatus as PhysicalVehicleStatus);
    }
  }, [isOpen, currentStatus]);

  // Styling
  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerColor = useColorModeValue('gray.800', 'white');
  const textColor = useColorModeValue('gray.600', 'gray.300');

  // Get all available physical statuses
  const availableStatuses = Object.values(PhysicalVehicleStatus);
  const filteredAvailableStatuses = availableStatuses.filter(
    (status) => status !== PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT
  );

  // Translations
  const translations = {
    [Countries.Mexico]: {
      modalTitle: 'Corrección de Estado Físico (Superadmin)',
      currentStatus: 'Estado Físico Actual:',
      newStatus: 'Nuevo Estado Físico:',
      selectStatus: 'Seleccione el nuevo estado',
      notes: 'Notas (opcional)',
      notesPlaceholder: 'Razón de la corrección...',
      cancel: 'Cancelar',
      confirm: 'Confirmar Corrección',
      confirming: 'Confirmando...',
      selectStatusError: 'Por favor seleccione un estado físico.',
      updateError: 'No se pudo actualizar el estado.',
      genericError: 'Ocurrió un error al actualizar el estado.',
      successTitle: 'Estado Actualizado',
      successDescription: 'El estado físico del vehículo se actualizó correctamente.',
    },
    [Countries['United States']]: {
      modalTitle: 'Physical Status Correction (Superadmin)',
      currentStatus: 'Current Physical Status:',
      newStatus: 'New Physical Status:',
      selectStatus: 'Select the new status',
      notes: 'Notes (optional)',
      notesPlaceholder: 'Reason for correction...',
      cancel: 'Cancel',
      confirm: 'Confirm Correction',
      confirming: 'Confirming...',
      selectStatusError: 'Please select a physical status.',
      updateError: 'Could not update the status.',
      genericError: 'An error occurred while updating the status.',
      successTitle: 'Status Updated',
      successDescription: 'The vehicle physical status has been updated successfully.',
    },
  };

  const t = translations[country.value as keyof typeof translations] || translations[Countries.Mexico];

  const handleConfirm = async () => {
    if (!selectedStatus) {
      setError(t.selectStatusError);
      return;
    }

    if (selectedStatus === currentStatus) {
      const sameStatusError =
        country.value === Countries['United States']
          ? 'The selected status is the same as the current status.'
          : 'El estado seleccionado es el mismo que el actual.';
      setError(sameStatusError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await updatePhysicalStatusAdmin({
        vehicleId,
        newStatus: selectedStatus as PhysicalVehicleStatus,
        accessToken,
        notes,
      });

      if (response.success && response.newPhysicalStatus) {
        onStatusUpdated();
        toast({
          title: t.successTitle,
          description: t.successDescription,
          status: 'success',
          duration: 5000,
          isClosable: true,
          position: 'top',
        });
        onClose();
      } else {
        setError(response.message || t.genericError);
      }
    } catch (err: any) {
      setError(err.message || t.genericError);
    } finally {
      setIsLoading(false);
    }
  };

  const resetModal = () => {
    setSelectedStatus((currentStatus as PhysicalVehicleStatus) || '');
    setNotes('');
    setError(null);
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} isCentered size="lg">
      <ModalOverlay bg="blackAlpha.300" />
      <ModalContent bg={bgColor} borderRadius="lg" boxShadow="xl" maxW="500px">
        <ModalHeader
          fontWeight="bold"
          color={headerColor}
          borderBottomWidth="1px"
          borderColor={borderColor}
          py={4}
        >
          {t.modalTitle}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody py={6} px={6}>
          <VStack spacing={4} align="stretch">
            {error && (
              <Alert status="error" borderRadius="md">
                <AlertIcon />
                {error}
              </Alert>
            )}

            {/* Current Status Display */}
            <Box p={3} bg="gray.50" borderRadius="md">
              <Text fontSize="sm" color={textColor} fontWeight="medium">
                {t.currentStatus}
              </Text>
              <Text fontWeight="semibold" color="gray.800">
                {getStatusLabel(currentStatus, country.value)}
              </Text>
            </Box>

            {/* New Status Selection */}
            <FormControl isRequired>
              <FormLabel fontSize="sm" fontWeight="medium" color={textColor}>
                {t.newStatus}
              </FormLabel>
              <Select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as PhysicalVehicleStatus)}
                borderColor={borderColor}
                size="md"
                _focus={{
                  borderColor: '#5800F7',
                  boxShadow: '0 0 0 1px #5800F7',
                }}
                sx={{
                  '& option': {
                    fontSize: 'sm',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    maxWidth: '100%',
                  },
                }}
              >
                {filteredAvailableStatuses.map((status) => (
                  <option key={status} value={status}>
                    {getStatusLabel(status, country.value)}
                  </option>
                ))}
              </Select>
            </FormControl>

            {/* Notes */}
            <FormControl>
              <FormLabel fontSize="sm" fontWeight="medium" color={textColor}>
                {t.notes}
              </FormLabel>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder={t.notesPlaceholder}
                borderColor={borderColor}
                _focus={{
                  borderColor: '#5800F7',
                  boxShadow: '0 0 0 1px #5800F7',
                }}
                rows={3}
              />
            </FormControl>
          </VStack>
        </ModalBody>

        <ModalFooter borderTopWidth="1px" borderColor={borderColor} py={4}>
          <Button
            onClick={handleClose}
            mr={3}
            isDisabled={isLoading}
            variant="outline"
            borderWidth="2px"
            borderColor="#5800F7"
            color="#5800F7"
            h="40px"
            _hover={{ bg: 'rgba(88, 0, 247, 0.1)' }}
          >
            {t.cancel}
          </Button>

          <Button
            onClick={handleConfirm}
            isLoading={isLoading}
            isDisabled={isLoading || !selectedStatus}
            spinner={<Spinner size="sm" color="white" />}
            loadingText={t.confirming}
            className={`
              text-white rounded-md h-[40px] px-4
              ${
                isLoading || !selectedStatus
                  ? 'bg-[#9CA3AF] cursor-not-allowed'
                  : 'bg-[#5800F7] hover:bg-[#4A00D1]'
              }
            `}
          >
            {!isLoading && t.confirm}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default SuperadminPhysicalStatusModal;
