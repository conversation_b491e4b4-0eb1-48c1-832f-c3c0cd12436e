import PermisoTable from './permisoTable';
import PermisosProvider from './PermisosProvider';
import { redirect } from 'next/navigation';
import getUserById from '@/actions/getUserById';
import SearchPermissionSets from './searchPermissionSets';
import getPermissionMatix from '@/actions/getPermissionMatrix';
import { AiOutlinePlus } from 'react-icons/ai';
import Link from 'next/link';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';
import { getServerAbility } from '@/casl/getServerAbility';

export const metadata = {
  title: 'Permisos',
  description: 'Esta es la página de permisos',
};

export default async function PermissionSetsPage() {
  const ability = await getServerAbility();
  const user = await getUserById();
  if (!user) return redirect('/');

  const permissionMatrix = await getPermissionMatix();
  if (!permissionMatrix) return null;

  const canAdd = canPerform(ability, Capabilities.Add, Sections.UserManagement, Subsections.Permissions);

  return (
    <PermisosProvider>
      <div className="mb-4 flex flex-row justify-between items-center">
        <h1 className="text-[32px] font-bold text-[#262D33]">Permisos</h1>
        <div className="flex gap-4">
          <SearchPermissionSets />
          <div className="relative">
            {canAdd && (
              <Link href="/dashboard/permisos/new">
                <button className="bg-[#5800F7] text-white pl-5 pr-1 h-[40px] rounded w-[180px]">
                  Agregar permiso
                </button>
              </Link>
            )}
            <div className="absolute top-0 left-[10px] text-white flex items-center h-full mr-2">
              <AiOutlinePlus size={22} />
            </div>
          </div>
        </div>
      </div>
      <PermisoTable />
    </PermisosProvider>
  );
}
