import { create } from 'zustand';

interface RecordsModalState {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
}

export const useRecordsModal = create<RecordsModalState>((set) => ({
  isOpen: false,
  onOpen: () => set({ isOpen: true }),
  onClose: () => set({ isOpen: false }),
}));

export const useEditAssociateModal = create<RecordsModalState>((set) => ({
  isOpen: false,
  onOpen: () => set({ isOpen: true }),
  onClose: () => set({ isOpen: false }),
}));
