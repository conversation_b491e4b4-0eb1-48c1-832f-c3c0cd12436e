'use client';
// import { MyUser } from '@/actions/getCurrentUser';
// import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import CustomModal from '@/components/Modals/CustomModal';
import { createStockSchema, createStockSchemaUS } from '@/validatorSchemas/createStockSchema';
import axios from 'axios';
import { FormikValues } from 'formik';
// import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { AiOutlineSearch, AiOutlineClose } from 'react-icons/ai';
import Spinner from '@/components/Loading/Spinner';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import {
  CONTRACT_REGIONS,
  CONTRACT_REGIONS_IATA,
  Countries,
  URL_API,
  US_CITIES,
  statusSelected,
  stepsSelect,
} from '@/constants';
import SelectInput from '@/components/Inputs/SelectInput';
import { Region, useVehiclesContext } from '../vehiclesContext';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { VehicleRegisteration } from '../PageBar/VehicleRegisteration';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';

const FilterBar = ({ page }: { page: string }) => {
  const search = useSearchParams();
  const searchFilter = search ? search.get('q') : null;
  const updateSideData = useUpdateSideData();

  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [region, setRegion] = useState<Region>('' as Region);
  const [contractNumber, setContractNumber] = useState('');
  const { user, addFilters, filterSelectedState } = useVehiclesContext();
  const [country, setCountry] = useState<any>('');
  const initialValues = {
    bill: '',
    brand: { value: '', label: 'Selecciona' },
    model: { value: '', label: 'Selecciona' },
    version: { value: '', label: 'Selecciona' },
    year: { value: '', label: 'Selecciona' },
    color: { value: '', label: 'Selecciona' },
    vin: '',
    owner: '',
    billAmount: '',
    billDate: '',
    receptionDate: '',
    km: '',
    vehicleState: {
      value: '',
      label: 'Selecciona',
      code: '',
    },
    country: {
      label: country,
      value: country,
    },
    state: {
      label: '',
      value: '',
    }, // will be used in US Vehicle Form Registeration
  };
  const initialValuesUS = {
    bill: '',
    brand: '',
    model: '',
    version: '',
    year: '',
    color: { value: '', label: 'Selecciona' },
    vin: '',
    owner: '',
    billAmount: '',
    billDate: '',
    receptionDate: '',
    km: '',
    vehicleState: {
      value: '',
      label: 'Selecciona',
      code: '',
    },
    country: {
      label: country,
      value: country,
    },
    state: {
      label: '',
      value: '',
    }, // will be used in US Vehicle Form Registeration
  };
  const [formIntialValues, setFormInitialValues] = useState<any>();
  useEffect(() => {
    if (country === Countries['United States']) {
      setFormInitialValues(initialValuesUS);
    } else {
      setFormInitialValues(initialValues);
    }
  }, [country]);

  const router = useRouter();
  const onSearch = (event: React.FormEvent) => {
    event.preventDefault();
    const encodedSearchQuery = encodeURI(searchQuery);
    router.push(`/dashboard/flotilla/search?q=${encodedSearchQuery}`);
  };

  const [nameFiles, setNameFiles] = useState({
    vehiclePhoto: '',
    bill: '',
  });

  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const toast = useToast();

  const validatePreSubmit = async (values: FormikValues) => {
    try {
      await axios.post(
        `${URL_API}/stock/validate-fields`,
        {
          vin: values.vin,
        },
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      return true;
    } catch (error: any) {
      toast({
        title: 'VIN o Serie registrado en el vehiculo : ' + error.response.data.carNumber,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
      return false;
    }
  };

  const handleSubmit = async (values: typeof initialValues, _: any, onCloseModal: () => void) => {
    const isValid = await validatePreSubmit(values);
    if (!isValid) return null;
    setIsLoading(true);
    const data = {
      ...values,
      brand: values.brand.value,
      model: values.model.value,
      version: values.version.value,
      year: values.year.value,
      vehicleState: values.vehicleState.code,
      color: values.color.value,
      km: parseInt(values.km, 10),
      status: 'stock',
      region: CONTRACT_REGIONS.find((el) => el.code === region)?.number!,
      carNumber: contractNumber,
      country: values.country.value,
      state: values.state.value,
    };
    const response = await axios
      .post(`${URL_API}/stock/add`, data, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      })
      .then(async (res) => {
        toast({
          title: 'Vehiculo agregado al inventario correctamente',
          description: 'Actualizando pagina...',
          duration: 3000,
          status: 'success',
          position: 'top',
        });
        onCloseModal();
        router.refresh();
        await updateSideData(user);
        return router.push(`/dashboard/flotilla/invoiced/${res.data.stock._id}`);
      })
      .catch((err: any) => {
        // localStorage.setItem('vehicleData', JSON.stringify(obj));
        setIsLoading(false);
        toast({
          title: err.response.data.message,
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      });
    return response;
  };

  const handleUpdateRegionFilter = async (data: FormikValues) => {
    const keys = Object.keys(data).filter((key) => data[key] !== null && data[key] !== undefined);
    // console.log('keys', keys);
    const relatedValues = keys.map((key) => {
      return { filterName: key, filterValue: data[key].value };
    });
    // console.log('relatedValues', relatedValues);
    addFilters(relatedValues);
  };

  const regionFilter = filterSelectedState.find((r) => r.filterName === 'region')?.filterValue || '';
  const stepFilter = filterSelectedState.find((r) => r.filterName === 'step')?.filterValue || '';
  const newFilter = filterSelectedState.find((r) => r.filterName === 'new')?.filterValue || '';
  const statusFilter = filterSelectedState.find((r) => r.filterName === 'status')?.filterValue || '';

  const regionSelected2 = CONTRACT_REGIONS.find((r) => r.value === regionFilter);
  const stepSelected = stepsSelect.find((r) => r.value === stepFilter);
  const newSelected =
    newFilter === 'true'
      ? { label: 'Nuevos', value: 'true' }
      : newFilter === 'false'
      ? { label: 'Seminuevos', value: 'false' }
      : undefined;
  const status = statusSelected.find((r) => r.value === statusFilter);

  const initialFilterValues = { region: regionSelected2, step: stepSelected, new: newSelected, status };
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const path = usePathname();

  useEffect(() => {
    if (searchFilter) {
      setIsSearchActive(true);
      setSearchValue(searchFilter);
    }
  }, [searchFilter]);

  useEffect(() => {
    if (user && region) {
      const regionNumber =
        country === Countries['United States']
          ? US_CITIES.find((city) => city.value === region)?.number
          : CONTRACT_REGIONS.find((r) => r.value === region)?.number;

      axios
        .post(
          `${URL_API}/contract/next`,
          { region: regionNumber },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          }
        )
        .then((res) => {
          setContractNumber(res.data.alias);
        });
    }
  }, [region, user]);

  const { user: currentUser, isSuperAdminOrAdmin } = useCurrentUser();

  const allowedRegions = CONTRACT_REGIONS_IATA.filter((r) => {
    const code = r.code as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'pbc';
    return currentUser.settings.allowedRegions.includes(code);
  });

  const handleSubmitUSVehicleRegisterationForm = async (
    values: typeof initialValuesUS,
    _: any,
    onCloseModal: () => void
  ) => {
    const isValid = await validatePreSubmit(values);
    if (!isValid) return null;
    setIsLoading(true);
    const data = {
      ...values,
      vehicleState: values.vehicleState.code,
      color: values.color.value,
      km: parseInt(values.km, 10),
      status: 'stock',
      region: US_CITIES.find((el) => el.code === region)?.number!, // Mexico Region is equal to US City
      carNumber: contractNumber,
      country: values.country.value,
      state: values.state.value,
    };

    try {
      const res = await axios.post(`${URL_API}/stock/add`, data, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      toast({
        title: 'Vehiculo agregado al inventario correctamente',
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      setIsLoading(false);
      onCloseModal();
      await updateSideData(user);
      router.refresh();
      router.push(`/dashboard/flotilla/invoiced/${res.data.stock._id}`);
    } catch (err: any) {
      setIsLoading(false);
      toast({
        title: err.response.data.message,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    }
  };

  function Filters() {
    return (
      <div className="flex flex-col w-full gap-3">
        <SelectInput
          name="region"
          label="Region"
          options={isSuperAdminOrAdmin ? CONTRACT_REGIONS : allowedRegions}
          // onChange={(option) => {
          //   setRegion(option.value as Region);
          //   // localStorage.setItem('regionSelected', JSON.stringify(option));
          // }}
        />
        <SelectInput
          name="step"
          label="Paso actual"
          options={stepsSelect}
          // onChange={(option) => {
          //   setRegion(option.value as Region);
          //   // localStorage.setItem('stepSelected', JSON.stringify(option));
          // }}
        />
        <SelectInput
          name="new"
          label="Nuevo o seminuevo"
          options={[
            { label: 'Nuevos', value: 'true' },
            { label: 'Seminuevos', value: 'false' },
          ]}
        />
        {path.split('/')[3] === 'activos' && (
          <SelectInput
            name="status"
            label="Estatus"
            options={[
              { label: 'Activos', value: 'active' },
              { label: 'En Taller', value: 'in-service' },
              { label: 'En proceso legal', value: 'legal-process' },
              { label: 'Espera de seguro', value: 'awaiting-insurance' },
            ]}
          />
        )}
        {path.split('/')[3] === 'stock' && (
          <SelectInput
            name="status"
            label="Estatus"
            options={[
              { label: 'Stock', value: 'stock' },
              { label: 'Revisión', value: 'overhauling' },
            ]}
          />
        )}
      </div>
    );
  }

  const onClose = () => {
    setNameFiles({
      vehiclePhoto: '',
      bill: '',
    });
    setCountry('');
    setContractNumber('');
    setIsLoading(false);
  };

  if (isLoading) return <Spinner />;
  return (
    <div className="flex items-center justify-between">
      <div>
        <h1 className="font-bold text-interBold32 font-inter ">{page}</h1>
      </div>
      <div className="flex flex-row justify-end w-3/4 pr-8 gap-x-2 ">
        <form className="relative" onSubmit={onSearch} onFocus={() => setIsSearchActive(true)}>
          <input
            id="search"
            className="px-10 h-[40px] border-[#9CA3AF] border-[1px] !outline-none rounded relative focus:ring-[#5800F7] focus:border-[#5800F7]"
            type="text"
            onChange={(event) => {
              setSearchQuery(event.target.value);
              setSearchValue(event.target.value);
            }}
            value={searchValue}
            placeholder="Buscar"
            onFocus={() => setIsSearchActive(true)}
            onBlur={() => setIsSearchActive(false)}
          />
          <div
            className={
              isSearchActive
                ? 'absolute top-0  text-[#5800F7] flex items-center h-full mr-2'
                : 'absolute top-0  text-[#9CA3AF] flex items-center h-full mr-2'
            }
          >
            <button type="submit" className="px-2">
              <AiOutlineSearch size={26} />
            </button>
          </div>
          <div
            className={
              isSearchActive
                ? 'absolute top-0 right-0 text-[#5800F7] flex items-center h-full mr-2'
                : 'hidden'
            }
          >
            <Link href="/dashboard/flotilla/stock">
              <AiOutlineClose size={26} />
            </Link>
          </div>
        </form>
        <CustomModal
          confirmButtonText="Filtros"
          header=""
          initialValues={initialFilterValues}
          onCloseModal={onClose}
          size="xl"
          onSubmit={handleUpdateRegionFilter}
          body={<Filters />}
          // validatorSchema={createFilterSchema}
          openButtonText="Filtrar"
        />
        {country === Countries['United States'] ? (
          <CustomModal
            confirmButtonText="Agregar"
            header="Agregar vehiculo"
            initialValues={formIntialValues}
            onCloseModal={onClose}
            isPrimaryButton={true}
            size="xl"
            onSubmit={handleSubmitUSVehicleRegisterationForm}
            body={
              <VehicleRegisteration
                country={country}
                setCountry={setCountry}
                setRegion={setRegion}
                setContractNumber={setContractNumber}
                contractNumber={contractNumber}
                isSuperAdminOrAdmin={isSuperAdminOrAdmin}
                allowedRegions={allowedRegions}
                handleSetName={handleSetName}
                nameFiles={nameFiles}
              />
            }
            validatorSchema={createStockSchemaUS}
            customSubmit
            openButtonText="Agregar"
            testId="add"
          />
        ) : (
          <CustomModal
            confirmButtonText="Agregar"
            header="Agregar vehiculo"
            initialValues={formIntialValues}
            onCloseModal={onClose}
            isPrimaryButton={true}
            size="xl"
            onSubmit={handleSubmit}
            body={
              <VehicleRegisteration
                country={country}
                setCountry={setCountry}
                setRegion={setRegion}
                setContractNumber={setContractNumber}
                contractNumber={contractNumber}
                isSuperAdminOrAdmin={isSuperAdminOrAdmin}
                allowedRegions={allowedRegions}
                handleSetName={handleSetName}
                nameFiles={nameFiles}
              />
            }
            validatorSchema={createStockSchema}
            customSubmit
            openButtonText="Agregar"
            testId="add"
          />
        )}
      </div>
    </div>
  );
};

export default FilterBar;
