import React from 'react';
import { ErrorMessage, useFormikContext } from 'formik';
import FileUploader from '../UploadZone';

interface BulkUploadStepProps {
  onFileChange: (files: FileList) => void;
  isLoading?: boolean;
  loaderDuration?: number;
}

interface BulkUploadFormValues {
  files: FileList;
}

const BulkUploadStep = ({ onFileChange, isLoading = false, loaderDuration = 3000 }: BulkUploadStepProps) => {
  const { setFieldValue } = useFormikContext<BulkUploadFormValues>();

  const handleFilesChange = (filesArray: File[]) => {
    const dt = new DataTransfer();
    filesArray.forEach((file) => dt.items.add(file));
    const fl = dt.files;
    setFieldValue('files', fl);
    onFileChange(fl);
  };

  return (
    <>
      <FileUploader
        accept=".xml"
        allowedExtensions={['xml']}
        isLoading={isLoading}
        loaderDuration={loaderDuration}
        onFilesChange={handleFilesChange}
        labels={{
          uploadingText: 'Subiendo archivos...',
          selectedFilesTitle: 'Archivos seleccionados:',
          moreLabel: 'Mostrar más',
          lessLabel: 'Mostrar menos',
          emptyPlaceholder: (
            <p className="mt-4">
              <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#6210FF] to-[#A74DF9]">
                Seleccionar
              </span>{' '}
              del Menú
            </p>
          ),
        }}
      />
      <div className="text-red-500 mt-2">
        <ErrorMessage name="files" />
      </div>
    </>
  );
};

export default BulkUploadStep;
