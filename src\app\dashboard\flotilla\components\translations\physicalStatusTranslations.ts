import { Countries } from '@/constants';

export const physicalStatusTranslations = {
  [Countries.Mexico]: {
    modalTitle: 'Actualización de Estado Físico (QR Scan)',
    currentStatus: 'Estado Físico Actual:',
    proposedStatus: 'Siguiente Estado:',
    selectNextStatus: 'Por favor, seleccione el siguiente estado:',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    selectOption: 'Seleccione una opción',
    confirming: 'Confirmando...',
    incompleteInfo: 'Información de confirmación incompleta o no se ha seleccionado una opción.',
    updateError: 'No se pudo actualizar el estado.',
    genericError: 'Ocurrió un error al confirmar el estado.',
    physicalStatus: 'Estado Físico',
    changeHistory: 'Historial de Cambios de Estado',
    show: 'Mostrar',
    hide: 'Ocultar',
    noHistory: 'No hay historial de cambios de estado disponible.',
    user: 'Usuario',
    correctStatus: 'Corregi<PERSON>',
    correction: 'Corrección',
    viewPhoto: 'Ver Foto',
    failedToLoadImage: 'Error al cargar la imagen',
    loadingImage: 'Cargando imagen...',
    noImageAvailable: 'Imagen no disponible',
    vehiclePhoto: 'Foto del Vehículo',
    workshopName: 'Taller',
    photoTexts: {
      takePhoto: 'Tomar Foto',
      retakePhoto: 'Volver a Tomar',
      photoRequired: 'Verificación Fotográfica',
      photoInstruction: 'Tomar una foto del vehículo',
      photoDescription: 'Requerido para verificación de estado',
      photoMissing: 'Se requiere una foto para confirmar el cambio de estado',
      uploading: 'Subiendo foto...',
    },
    selectRegion: 'Seleccionar Región',
    selectWorkshop: 'Seleccionar Taller',
    noWorkshopsAvailable: 'No hay talleres disponibles para esta región',
    selectRegionAndWorkshopError: 'Por favor, seleccione una región y un taller.',
    inspectionCompleted: 'Inspección Completada',
    inspectionFailed: 'Inspección Fallida',
    deliveredToCustomerAfterRepair: 'Vehículo entregado al cliente',
    collectedFromStockToRedeliver: 'Vehículo retirado del stock para ser reentregado al cliente',
    redirecting: 'Redirigiendo a los detalles del vehículo...',
    handoverWaiting: 'Esperando la selección de entrega...',
    handoverModalTitle: 'Recogida del vehículo',
    handoverCustomer: 'Cliente',
    handoverAgent: 'Agente de Flota OCN',
    handoverQuestion: '¿Quién recoge el vehículo?',
    redirectFailed: 'Redirección fallida',
  },
  [Countries['United States']]: {
    modalTitle: 'Physical Status Update (QR Scan)',
    currentStatus: 'Current Physical Status:',
    proposedStatus: 'Proposed New Status:',
    selectNextStatus: 'Please select the next status:',
    cancel: 'Cancel',
    confirm: 'Confirm',
    selectOption: 'Select an option',
    confirming: 'Confirming...',
    incompleteInfo: 'Incomplete confirmation information or no option selected.',
    updateError: 'Could not update the status.',
    genericError: 'An error occurred while confirming the status.',
    physicalStatus: 'Physical Status',
    changeHistory: 'Status Change History',
    show: 'Show',
    hide: 'Hide',
    noHistory: 'No status change history available.',
    user: 'User',
    correctStatus: 'Correct Status',
    correction: 'Correction',
    viewPhoto: 'View Photo',
    failedToLoadImage: 'Failed to load image',
    loadingImage: 'Loading image...',
    noImageAvailable: 'No image available',
    vehiclePhoto: 'Vehicle Photo',
    workshopName: 'Workshop',
    photoTexts: {
      takePhoto: 'Take Photo',
      retakePhoto: 'Retake Photo',
      photoRequired: 'Photo Verification',
      photoInstruction: 'Take a photo of the vehicle',
      photoDescription: 'Required for status verification',
      photoMissing: 'A photo is required to confirm the status change',
      uploading: 'Uploading photo...',
    },
    selectRegion: 'Select Region',
    selectWorkshop: 'Select Workshop',
    noWorkshopsAvailable: 'No workshops available for this region',
    selectRegionAndWorkshopError: 'Please select a region and a workshop.',
    inspectionCompleted: 'Inspection Completed',
    inspectionFailed: 'Inspection Failed',
    deliveredToCustomerAfterRepair: 'Vehicle delivered to customer',
    collectedFromStockToRedeliver: 'Vehicle collected from stock to be redelivered to customer',
    redirecting: 'Redirecting to vehicle details...',
    handoverWaiting: 'Waiting for handover selection...',
    handoverModalTitle: 'Vehicle Handover',
    handoverCustomer: 'Customer',
    handoverAgent: 'OCN Fleet Agent',
    handoverQuestion: 'Who is picking up the vehicle?',
    redirectFailed: 'Redirect Failed',
  },
};
