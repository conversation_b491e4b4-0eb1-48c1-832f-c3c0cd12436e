'use client';
import { createContext, useState, useContext } from 'react';

interface ContextProps {
  isAssocOpen: boolean;
  setIsAssocOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleIsAssocOpen: () => void;
}

const defaultContextValues: ContextProps = {
  isAssocOpen: false,
  setIsAssocOpen: () => {},
  handleIsAssocOpen: () => {},
};

export const DetailContext = createContext<ContextProps>(defaultContextValues);

export const useDetail = () => {
  const context = useContext(DetailContext);
  if (!context) throw new Error('There is not auth context');
  return context;
};

// Proveedor del contexto
export default function DetailProvider({ children }: any) {
  // const [isChanged, setIsChanged] = useState(false);
  const [isAssocOpen, setIsAssocOpen] = useState(false);
  const handleIsAssocOpen = () => {
    setIsAssocOpen(!isAssocOpen);
  };

  return (
    <DetailContext.Provider value={{ isAssocOpen, setIsAssocOpen, handleIsAssocOpen }}>
      {children}
    </DetailContext.Provider>
  );
}
