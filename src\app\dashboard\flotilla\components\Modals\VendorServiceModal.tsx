/* eslint-disable @next/next/no-img-element */
/* eslint-disable prettier/prettier */
import React from 'react';
// import ModalContainer from './ModalContainer';
import { useOpenVendorServiceModal } from '@/zustand/modalStates';
import { Eye } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
export interface IService {
  _id: string;
  stockId: string;
  status: string;
  firstStep: any;
  secondStep: any;
  createdAt: string;
  updatedAt: string;
}
type Options = 'good condition' | 'needs attention' | 'critical issue';
type Options2 = 'complete' | 'incomplete' | 'missing';
type OptionsValue = {
  value: 'En buen estado' | 'Necesita atención' | 'Problema crítico';
  color: string;
};
type OptionsValue2 = {
  value: 'Completo' | 'Incompleto' | 'Faltante';
  color: string;
};


const optionsMap: Record<Options, OptionsValue> = {
  'good condition': {
    value: 'En buen estado',
    color: 'bg-green-500',
  },
  'needs attention': {
    value: 'Necesita atención',
    color: 'bg-yellow-500',
  },
  'critical issue': {
    value: 'Problema crítico',
    color: 'bg-red-500',
  },
};

const options2Map: Record<Options2, OptionsValue2> = {
  'complete': {
    value: 'Completo',
    color: 'bg-green-500',
  },
  'incomplete': {
    value: 'Incompleto',
    color: 'bg-yellow-500',
  },
  'missing': {
    value: 'Faltante',
    color: 'bg-red-500',
  },
};

export const typeServiceMap: Record<string, string> = {
  preventive: 'Preventivo',
  corrective: 'Correctivo',
  other: 'Otro',
};

export const filterTypeMap: Record<string, string> = {
  'oil': 'Aceite',
  'air': 'Aire',
}

export const reviewScratchesMap: Record<string, string> = {
  'corrosion': 'Corrosión',
  'dents': 'Abolladuras',
  'scratches': 'Rayones',
}

export default function VendorServiceModal() {
  const modal = useOpenVendorServiceModal();
  const service = modal.serviceData;

  const onOpenChange = () => {
    // modal.onOpen();
    if (modal.isOpen) {
      modal.onClose();
      modal.setServiceData(null);
    } else {
      modal.onOpen();
    }
  };

  if (!service) {
    return null;
  }

  return (
    <>
      {modal.isOpen && (
        // <ModalContainer title="Servicio de Taller" onClose={modal.onClose}>
        //   <h1>Hola</h1>
        // </ModalContainer>

        <Dialog open={modal.isOpen} onOpenChange={onOpenChange}>
          <DialogTrigger asChild>
            <button className="text-gray-500 hover:text-gray-700">
              <Eye className="w-4 h-4" />
            </button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto ">
            <DialogHeader>
              <DialogTitle>Detalle del servicio:</DialogTitle>
            </DialogHeader>
            <div>
              <div>
                <div className="flex flex-col gap-3 pl-4">
                  <p className="text-lg font-semibold">Primer paso:</p>
                  <div className="grid grid-cols-[repeat(auto-fill,_minmax(200px,_1fr))] gap-4 pl-8 text-sm text-gray-600">
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Carrocería</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.bodyWork as Options]?.value}
                      </p>
                    </div>

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Golpes</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWorkDetail.blows as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.bodyWorkDetail.blows as Options]?.value}
                      </p>
                    </div>



                    {/* PAINT CONDITION */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Pintura</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWorkDetail.paintCondition as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.bodyWorkDetail.paintCondition as Options]?.value}
                      </p>
                    </div>


                    {/* reviewScratches, this is an object with: corrosion, dents, scratches, all these values are booleans */}


                    {/* refrigerant */}

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Refrigerante</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.refrigerant as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.refrigerant as Options]?.value}
                      </p>
                    </div>

                    {/* Brake fluid */}

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Líquido de frenos</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.brakeFluid as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.brakeFluid as Options]?.value}
                      </p>
                    </div>

                    {/* powerSteeringFluid */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Líquido de dirección</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.powerSteeringFluid as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.powerSteeringFluid as Options]?.value}
                      </p>
                    </div>

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Cristales y espejos</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.crystalsAndMirrors as Options]?.value}
                      </p>
                    </div>


                    {/* Dashboard */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Tablero</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.dashboard as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.crystalsAndMirrors as Options]?.value}
                      </p>
                    </div>



                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Llantas</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.tires as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Luces</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.lights as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Asientos</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.seats as Options]?.value}
                      </p>
                    </div>



                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Tablero</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.dashboard as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Sistema de control</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.controlSystem as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Sistemas electrónicos</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.electronicSystems as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Motor</h4>

                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.engine as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Transmisión</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.transmission as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Suspensión</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.suspension as Options]?.value}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Sistema eléctrico</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.bodyWork as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.electricalSystem as Options]?.value}
                      </p>
                    </div>

                    {/* exhaustSystem */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Sistema de escape</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.exhaustSystem as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.exhaustSystem as Options]?.value}
                      </p>
                    </div>

                    {/* windshieldWasherFluid */}

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Líquido limpiaparabrisas</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${optionsMap[service.firstStep.windshieldWasherFluid as Options]?.color
                          }`}
                      >
                        {optionsMap[service.firstStep.windshieldWasherFluid as Options]?.value}
                      </p>
                    </div>

                    {/* Emergency tools: complete, incomplete, missing */}

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Herramientas de emergencia</h4>
                      <p
                        className={`text-white px-2 py-1 rounded-md w-[max-content] mt-2 ${options2Map[service.firstStep.emergencyTools as Options2]?.color
                          }`}
                      >
                        {options2Map[service.firstStep.emergencyTools as Options2]?.value}
                      </p>
                    </div>


                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Revisión de exterior</h4>
                      <p className={` pl-1 py-1 rounded-md w-[max-content] mt-2`}>
                        {
                          Object.entries(
                            service.firstStep.bodyWorkDetail.reviewScratches).map(([key, value]) => {
                              return (
                                <p key={key}>
                                  <span className="font-semibold mr-2">
                                    {reviewScratchesMap[key]}
                                  </span>
                                  {value ? 'Sí' : 'No'}
                                </p>
                              )
                            })
                        }
                      </p>
                    </div>

                    {/* Emergency tools images: */}

                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Imágenes de herramientas de emergencia</h4>
                      <div className="flex gap-3 overflow-x-auto">
                        {service.firstStep.emergencyToolsImages?.map((image: string, index: number) => {
                          return (
                            <img
                              key={index}
                              src={image}
                              alt={index.toString()}
                              className="w-24 h-24 object-cover"
                            />
                          );
                        })}
                      </div>
                    </div>

                    <div>
                      {/* Body work images: */}
                      <h4 className="text-[16px] font-semibold text-black">Imágenes de golpes</h4>
                      <div className="flex gap-3 overflow-x-auto">
                        {service.firstStep.bodyWorkDetail.blowsImages?.map((image: string, index: number) => {
                          return (
                            <img
                              key={index}
                              src={image}
                              alt={index.toString()}
                              className="w-24 h-24 object-cover"
                            />
                          );
                        })}
                      </div>
                    </div>


                    {/* Body work images: */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Imágenes de pintura</h4>
                      <div className="flex gap-3 overflow-x-auto">
                        {service.firstStep.bodyWorkDetail.
                          paintConditionImages?.map((image: string, index: number) => {
                            return (
                              <img
                                key={index}
                                src={image}
                                alt={index.toString()}
                                className="w-24 h-24 object-cover"
                              />
                            );
                          })}
                      </div>
                    </div>


                    {/* Dashboard images */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Imágenes del tablero</h4>
                      <div className="flex gap-3 overflow-x-auto">
                        {service.firstStep.dashboardImages?.map((image: string, index: number) => {
                          return (
                            <img
                              key={index}
                              src={image}
                              alt={index.toString()}
                              className="w-24 h-24 object-cover"
                            />
                          );
                        })}
                      </div>
                    </div>


                    {/* Seats images */}
                    <div>
                      <h4 className="text-[16px] font-semibold text-black">Imágenes de asientos</h4>
                      <div className="flex gap-3 overflow-x-auto">
                        {service.firstStep.seatsImages?.map((image: string, index: number) => {
                          return (
                            <img
                              key={index}
                              src={image}
                              alt={index.toString()}
                              className="w-24 h-24 object-cover"
                            />
                          );
                        })}
                      </div>
                    </div>

                  </div>
                </div>

                <div className="flex flex-col gap-3 pl-4 mt-10 border-t border-gray-300 pt-4 pb-8">
                  <p className="text-lg font-semibold">
                    Segundo paso:
                    {!service.secondStep && (
                      <span className="text-indigo-500"> (Todavía no se ha completado)</span>
                    )}
                  </p>
                  {service.secondStep && (
                    <>
                      <div className="grid grid-cols-[repeat(auto-fill,_minmax(200px,_1fr))] gap-4 pl-8">
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Tipo de servicio</h4>
                          <p>{typeServiceMap[service.secondStep.serviceDetail.serviceType]}</p>
                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Siguiente servicio</h4>
                          <p>{service.secondStep.pendingServices.nextServiceDate}</p>
                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Costo total</h4>
                          <p>{service.secondStep.costsAndTimes?.totalCost || 0}</p>

                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de aceite</h4>
                          <p>{service.secondStep.serviceDetail.specifications.oilChange ? 'Sí' : 'No'}</p>
                        </div>
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Aceites cambiados</h4>
                          <p>
                            {service.secondStep.serviceDetail.specifications.oilChangeType &&
                              Object.entries(
                                service.secondStep.serviceDetail.specifications.oilChangeType
                              ).map(([key, value]) => {
                                return <p key={key}>{value as any}</p>;
                              })}
                          </p>
                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de filtro</h4>
                          <p>{service.secondStep.serviceDetail.specifications.filterChange ? 'Sí' : 'No'}</p>
                        </div>
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Filtros cambiados</h4>
                          <div className="pl-4">
                            {service.secondStep.serviceDetail.specifications.filterType &&
                              Object.entries(service.secondStep.serviceDetail.specifications.filterType).map(
                                ([key]) => {
                                  return (
                                    // <p key={key}>{key}</p>
                                    <p key={key}>{filterTypeMap[key] || key}</p>
                                  );
                                }
                              )}
                          </div>
                        </div>


                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de bujías</h4>
                          <p>{service.secondStep.serviceDetail.specifications.tuneUp ? 'Sí' : 'No'}</p>
                        </div>


                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de batería</h4>
                          <p>{service.secondStep.serviceDetail.specifications.batteryChange ? 'Sí' : 'No'}</p>
                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de frenos</h4>
                          <p>{service.secondStep.serviceDetail.specifications.brakes ? 'Sí' : 'No'}</p>
                        </div>

                        {/* brakesDetail */}
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Detalle de frenos</h4>
                        </div>

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Cambio de suspensión</h4>
                          <p>{service.secondStep.serviceDetail.specifications.suspension ? 'Sí' : 'No'}</p>
                        </div>

                        {/* suspensionDetail shockAbsorbers */}

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Detalle de suspensión</h4>
                          <p>
                            {service.secondStep.serviceDetail.specifications.suspensionDetail?.shockAbsorbers
                              ? 'Sí'
                              : 'No'}
                          </p>
                        </div>

                        {/* springsAndBushings */}
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Detalle de suspensión</h4>
                          <p>
                            {service.secondStep.serviceDetail.specifications.suspensionDetail
                              ?.springsAndBushings
                              ? 'Sí'
                              : 'No'}
                          </p>
                        </div>

                        {/* boltsTightening */}

                        <div>
                          <h4 className="text-[16px] font-semibold text-black">Apriete de tornillos</h4>
                          <p>
                            {service.secondStep.serviceDetail.specifications.suspensionDetail?.boltsTightening
                              ? 'Sí'
                              : 'No'}
                          </p>
                        </div>

                        {/* recommendations, this is an array of strings */}

                        <div>

                          <h4 className="text-[16px] font-semibold text-black">Recomendaciones</h4>
                          <div className="pl-4">
                            {service.secondStep.pendingServices
                              .recommendations?.map((rec: string, index: number) => {
                                return <p key={index}>{rec}</p>;
                              })}
                          </div>
                        </div>


                        {/* FILTER IMAGES */}
                        <div>
                          <h4 className="text-[16px] font-semibold text-black">
                            Imagenes del cambio de filtro
                          </h4>
                          <div className="flex gap-3 overflow-x-auto">
                            {(
                              service.secondStep.serviceDetail.specifications
                                .filterImages as unknown as string[]
                            )?.map((image, index) => {
                              return (
                                <img
                                  key={index}
                                  src={image}
                                  alt={index.toString()}
                                  className="w-24 h-24 object-cover"
                                />
                              );
                            })}
                          </div>
                        </div>


                        <div>
                          <h4 className="text-[16px] font-semibold text-black">
                            Imagenes del cambio de bujías
                          </h4>
                          <div className="flex gap-3 overflow-x-auto">
                            {(
                              service.secondStep.serviceDetail.specifications
                                .tuneUpImages as unknown as string[]
                            )?.map((image, index) => {
                              return (
                                <img
                                  key={index}
                                  src={image}
                                  alt={index.toString()}
                                  className="w-24 h-24 object-cover"
                                />
                              );
                            })}
                          </div>
                        </div>


                        <div>
                          <h4 className="text-[16px] font-semibold text-black">
                            Imagenes del cambio de aceite
                          </h4>
                          <div className="flex gap-3 overflow-x-auto">
                            {(
                              service.secondStep.serviceDetail.specifications.oilImages as unknown as string[]
                            )?.map((image, index) => {
                              return (
                                <img
                                  key={index}
                                  src={image}
                                  alt={index.toString()}
                                  className="w-24 h-24 object-cover"
                                />
                              );
                            })}
                          </div>
                        </div>


                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
