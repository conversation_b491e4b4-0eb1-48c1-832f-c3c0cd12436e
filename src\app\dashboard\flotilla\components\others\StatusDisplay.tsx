import React from 'react';
import { Flex, Text } from '@chakra-ui/react';

interface StatusDisplayProps {
  label: string;
  value: string;
  labelColor?: string;
  valueColor?: string;
}

const StatusDisplay: React.FC<StatusDisplayProps> = ({
  label,
  value,
  labelColor = 'gray.600',
  valueColor = 'gray.800',
}) => (
  <Flex justifyContent="space-between" alignItems="center" p={3} bg="gray.50" borderRadius="md">
    <Text color={labelColor} fontWeight="medium">
      {label}
    </Text>
    <Text fontWeight="semibold" color={valueColor}>
      {value}
    </Text>
  </Flex>
);

export default StatusDisplay;
