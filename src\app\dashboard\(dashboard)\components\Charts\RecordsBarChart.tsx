'use client';
import React from 'react';
import { Bar, getElementsAtEvent } from 'react-chartjs-2';
import 'chart.js/auto';
import { useRecordsModal } from '../../stores/useRecordsModal';

interface BarChartProps {
  data: any;
  responsive?: boolean;
  stepYSize?: number;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  barThickness?: number;
  setFilter: any;
}

export default function RecordsBarChart({
  data,
  responsive = true,
  backgroundColor,
  borderColor,
  borderRadius,
  borderWidth,
  barThickness,
  stepYSize = 100,
  setFilter,
}: BarChartProps) {
  const barRef = React.useRef<any>();

  const recordsModal = useRecordsModal();

  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: data.label,
        data: data.indicators,
        backgroundColor: backgroundColor || 'rgba(88, 0, 247, 0.2)',
        // backgroundColor: ['#ff6384', '#36a2eb', '#cc65fe', '#ffce56'],
        borderColor: borderColor || 'rgba(88, 0, 247, 1)',
        borderWidth: borderWidth || 1,
        borderRadius: borderRadius || 5,
        barThickness: barThickness || 90,
        filters: data.filters,
      },
    ],
  };

  const maxValue = Math.max(...data.indicators);

  const totalYLabels = Math.ceil((maxValue + stepYSize) / stepYSize);
  const stepSize =
    totalYLabels > 10 ? Math.ceil((maxValue + stepYSize) / 10 / stepYSize) * stepYSize : stepYSize;

  const options: any = {
    // plugins: {
    //   legend: {
    //     display: false,
    //   },
    // },
    response: true,
    maintainAspectRatio: !responsive, // false for responsive witdh chart
    scales: {
      y: {
        stacked: true,
        beginAtZero: true,
        suggestedMin: 0,
        suggestedMax: maxValue + 100,
        ticks: {
          stepSize: stepSize,
          // callback: (value: any) => `$${value / 1000}k`,
        },
      },
    },
  };

  const onClick = (event: React.MouseEvent<HTMLCanvasElement, MouseEvent>) => {
    const barElement = getElementsAtEvent(barRef.current, event);

    if (barElement.length === 0) return;

    const dataIndex = barElement[0].datasetIndex;
    const dataPoint = barElement[0].index;

    const selectBar = chartData.datasets[dataIndex].filters[dataPoint];
    setFilter(selectBar);
    recordsModal.onOpen();
  };

  return (
    <>
      <Bar data={chartData} options={options} ref={barRef} onClick={onClick} />
    </>
  );
}
