/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-hooks/rules-of-hooks */
'use client';
import { URL_API } from '@/constants';
import { flexRender, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useCurrentUser } from '../../providers/CurrentUserProvider';
import { ColumnDef } from '@tanstack/react-table';
import { formatDateTime } from '@/utils/dates';
import { Button } from '@/components/ui/button';
import ModalContainer from './Modals/ModalContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import FormikContainer from '@/components/Formik/FormikContainer';
import { FileUpload, Media } from '@/components/FileUpload/FileUpload';
import { MediaType } from '@/app/dashboard/clientes/solicitudes/enums';

import { useVehicleDetailData } from './Providers/VehicleDetailDataProvider';
import { cn } from '@/lib/utils';
import { useToast } from '@chakra-ui/react';
import { useOpenGestoresModal } from '@/zustand/modalStates';
import Spinner from '@/components/Loading/Spinner';

const columns: ColumnDef<any>[] = [
  {
    header: 'ID',
    accessorKey: '_id',
    cell: ({ row }) => {
      const toast = useToast();
      return (
        <p
          className="w-[100px] cursor-pointer"
          onClick={() => {
            navigator.clipboard.writeText(row.original._id);
            toast({
              title: 'ID copiado al portapapeles',
              position: 'top',
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }}
        >
          {row.original._id.slice(0, 8)}
        </p>
      );
    },
  },
  {
    header: 'Tipo de procedimiento',
    accessorKey: 'tramiteId.name',
    cell: ({ row }) => {
      return <div className={cn('font-bold')}>{row.original.tramiteId?.name || 'No encontrado'}</div>;
    },
  },
  {
    header: 'Fecha y hora de Registro',
    accessorKey: 'createdAt',
    cell: ({ row }) => {
      const createdAtDateStr = row.getValue('createdAt');
      let formatted = '';
      if (createdAtDateStr) {
        formatted = formatDateTime(row.original.createdAt);
      }
      return <div className="font-bold">{formatted}</div>;
    },
  },
  {
    header: 'Gestor',
    accessorKey: 'gestorId.name',
  },
  {
    header: 'Status',
    accessorKey: 'status',
  },
  {
    header: 'Acciones',
    id: 'actions',
    cell: ({ row }) => {
      return (
        <Button
          variant="ghost"
          className="text-primary hover:text-primary/80 hover:bg-primary/10"
          onClick={() => {
            const tramite = row.original;
            alert(`Detalles del trámite: ${tramite.tramiteId.name}`);
          }}
        >
          Ver detalle
        </Button>
      );
    },
  },
];

function GestorDetailModal({
  isOpen,
  onClose,
  tramite,
}: {
  isOpen: boolean;
  onClose: () => void;
  tramite: any;
}) {
  if (!isOpen || !tramite) return null;

  return (
    <ModalContainer title={`Detalle del trámite: ${tramite.tramiteId.name}`} onClose={onClose}>
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-500">ID del trámite</h4>
            <p className="font-medium">{tramite._id}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-500">Estado</h4>
            <p className="font-medium">{tramite.status}</p>
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-500">Gestor</h4>
          <p className="font-medium">{tramite.gestorId.name}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium text-gray-500">Fecha de solicitud</h4>
          <p className="font-medium">{formatDateTime(tramite.createdAt)}</p>
        </div>

        {tramite.completedAt && (
          <div>
            <h4 className="text-sm font-medium text-gray-500">Fecha de finalización</h4>
            <p className="font-medium">{formatDateTime(tramite.completedAt)}</p>
          </div>
        )}

        {tramite.tramiteId.description && (
          <div>
            <h4 className="text-sm font-medium text-gray-500">Descripción</h4>
            <p className="text-gray-700">{tramite.tramiteId.description}</p>
          </div>
        )}

        {tramite.tramiteId.documents && tramite.tramiteId.documents.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-500 mb-2">Documentos requeridos</h4>
            <div className="space-y-2">
              {tramite.tramiteId.documents.map((doc: { name: string; format: string }, index: number) => (
                <div
                  key={index}
                  className="flex justify-between items-center p-2 bg-gray-50 rounded-md border border-gray-200"
                >
                  <span>{doc.name}</span>
                  <span
                    className={`px-2 py-1 rounded text-xs font-medium ${
                      doc.format === 'digital' ? 'bg-blue-100 text-blue-800' : 'bg-amber-100 text-amber-800'
                    }`}
                  >
                    {doc.format}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </ModalContainer>
  );
}

export default function GestoresTableDetail() {
  const [services, setServices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { id } = useParams<{ id: string }>();
  const { user } = useCurrentUser();
  const { vehicleData } = useVehicleDetailData();
  const [refresh, setRefresh] = useState(0);
  const [selectedTramite, setSelectedTramite] = useState<any>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const columnsWithActions: ColumnDef<any>[] = [
    ...columns.slice(0, -1),
    {
      header: 'Acciones',
      id: 'actions',
      cell: ({ row }) => {
        return (
          <Button
            variant="ghost"
            className="text-primary hover:text-primary/80 hover:bg-primary/10"
            onClick={() => {
              setSelectedTramite(row.original);
              setIsDetailModalOpen(true);
            }}
          >
            Ver detalle
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data: services,
    columns: columnsWithActions,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  useEffect(() => {
    const fetchGestoresProcedimientos = async () => {
      try {
        setIsLoading(true);
        const { data: response } = await axios.get(`${URL_API}/gestores/procedimientos/vehicle/${id}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        setServices(response.data || []);
      } catch (error) {
        console.log('error fetching gestores procedures:', error);
        setServices([]);
      } finally {
        setIsLoading(false);
      }
    };
    fetchGestoresProcedimientos();
  }, [id, user.accessToken, refresh, vehicleData?.vehicleState]);

  if (isLoading) return <div>Cargando...</div>;

  return (
    <>
      <div className="flex justify-between">
        <p className="text-[24px] mb-[5px]">Gestoría de documentos</p>
        <CreateNextAppointmentModal onRefresh={() => setRefresh((r) => r + 1)} />
      </div>

      <div className="w-full max-h-[300px] overflow-y-auto">
        <table className="w-full h-[max-content] caption-bottom text-sm divide-y divide-gray-200">
          <thead className="[&_tr]:border-b">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr
                key={headerGroup.id}
                className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
              >
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0"
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className="[&_tr:last-child]:border-0">
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className="even:bg-transparent odd:bg-gray-200"
                  data-state={row.getIsSelected() && 'selected'}
                >
                  {row.getVisibleCells().map((cell, index, cells) => (
                    <td
                      key={cell.id}
                      className={`
                        ${index === cells.length - 1 && 'rounded-r-lg'}
                        ${index === 0 && 'rounded-l-lg'}
                        py-2 px-4 text-left align-middle [&:has([role=checkbox])]:pr-0
                      `}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="h-24 text-center">
                  No hay resultados.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Modal de detalles */}
      <GestorDetailModal
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        tramite={selectedTramite}
      />
    </>
  );
}

function CreateNextAppointmentModal({ onRefresh }: { onRefresh: () => void }) {
  const { user } = useCurrentUser();
  const { onOpen, onClose, isOpen } = useOpenGestoresModal();
  const [gestorOptions, setGestorOptions] = useState<any[]>([]);
  const [serviceTypes, setServiceTypes] = useState<any[]>([]);
  const [selectedGestor, setSelectedGestor] = useState<any>(null);
  const [selectedServiceType, setSelectedServiceType] = useState<any>(null);
  const [isLoadingGestores, setIsLoadingGestores] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<Record<string, Media[]>>({});
  const [requiredDocuments, setRequiredDocuments] = useState<any[]>([]);

  const toast = useToast();
  const { vehicleData } = useVehicleDetailData();

  useEffect(() => {
    if (isOpen && user?.accessToken) {
      const fetchGestores = async () => {
        setIsLoadingGestores(true);
        try {
          const { data: response } = await axios.get(`${URL_API}/gestores/${vehicleData.vehicleState}`, {
            headers: { Authorization: `Bearer ${user.accessToken}` },
          });
          setGestorOptions(response?.data || []);
        } catch (error) {
          console.error('Error fetching gestores:', error);
          setGestorOptions([]);
        } finally {
          setIsLoadingGestores(false);
        }
      };
      fetchGestores();
    }
  }, [isOpen, user?.accessToken]);

  useEffect(() => {
    if (selectedGestor?.tramites) {
      setServiceTypes(selectedGestor.tramites);
      setSelectedServiceType(null);
      setUploadedDocuments({});
      setRequiredDocuments([]);
    } else {
      setServiceTypes([]);
    }
  }, [selectedGestor]);

  const [deliveredPhysicalDocs, setDeliveredPhysicalDocs] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (selectedServiceType) {
      const docsRequiringUpload =
        selectedServiceType.documents?.filter((doc: any) => doc.isAdminUpload === true) || [];

      setRequiredDocuments(docsRequiringUpload);
      const initialUploadedDocs: Record<string, Media[]> = {};
      const initialDeliveredDocs: Record<string, boolean> = {};

      docsRequiringUpload.forEach((doc: any) => {
        if (doc.format !== 'físico') {
          initialUploadedDocs[doc.name] = [];
        } else {
          initialDeliveredDocs[doc.name] = false;
        }
      });

      setUploadedDocuments(initialUploadedDocs);
      setDeliveredPhysicalDocs(initialDeliveredDocs);
    } else {
      setRequiredDocuments([]);
      setUploadedDocuments({});
      setDeliveredPhysicalDocs({});
    }
  }, [selectedServiceType]);

  const createGestorRequest = async (values: any) => {
    try {
      setIsLoading(true);
      const missingDocuments = requiredDocuments.filter((doc) => {
        const isPhysical = doc.format === 'físico';
        if (isPhysical) {
          return !deliveredPhysicalDocs[doc.name];
        }
        return !uploadedDocuments[doc.name] || uploadedDocuments[doc.name].length === 0;
      });

      if (missingDocuments.length > 0) {
        const physicalDocs = missingDocuments.filter((doc) => doc.format === 'físico');
        const digitalDocs = missingDocuments.filter((doc) => doc.format !== 'físico');

        let errorMessage = '';
        if (digitalDocs.length > 0) {
          errorMessage += `Por favor, sube los siguientes documentos: ${digitalDocs
            .map((d) => d.name)
            .join(', ')}. `;
        }
        if (physicalDocs.length > 0) {
          errorMessage += `Por favor, marca como entregados los siguientes documentos físicos: ${physicalDocs
            .map((d) => d.name)
            .join(', ')}`;
        }

        toast({
          title: 'Documentos faltantes',
          description: errorMessage,
          status: 'error',
          duration: 5000,
        });
        setIsLoading(false);
        return;
      }

      const physicalDocsInfo: Record<string, { delivered: boolean }> = {};
      requiredDocuments.forEach((doc) => {
        if (doc.format === 'físico' && deliveredPhysicalDocs[doc.name]) {
          physicalDocsInfo[doc.name] = { delivered: true };
        }
      });

      await axios.post(
        `${URL_API}/gestores/tramites`,
        {
          ...values,
          vehicleId: vehicleData._id,
          physicalDocuments: physicalDocsInfo,
          uploadedDocuments: requiredDocuments
            .filter((doc) => doc.format !== 'físico' && uploadedDocuments[doc.name]?.length > 0)
            .map((doc) => {
              const uploadedDoc = uploadedDocuments[doc.name][0];
              return {
                documentId: uploadedDoc.id,
                name: doc.name,
                uploadDate: new Date(),
                documentType: doc.name,
              };
            }),
        },
        {
          headers: { Authorization: `Bearer ${user.accessToken}` },
        }
      );

      toast({
        title: 'Trámite solicitado',
        description: 'El trámite ha sido solicitado correctamente',
        status: 'success',
        duration: 3000,
      });
      onRefresh();
      onClose();
    } catch (error) {
      toast({
        title: 'Error al solicitar trámite',
        description: 'Ha ocurrido un error al procesar la solicitud',
        status: 'error',
        duration: 3000,
      });
      console.error('Error creating gestor request:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) return <Spinner />;

  return (
    <>
      <Button className="bg-primary text-white" onClick={onOpen}>
        Solicitar trámite
      </Button>

      {isOpen && (
        <ModalContainer title="Solicitud de documentos" onClose={onClose}>
          <FormikContainer
            hideFooter
            onSubmit={async (values) => {
              console.log(values);
              return Promise.resolve();
            }}
            initialValues={{
              gestor: selectedGestor
                ? { label: selectedGestor.name, value: selectedGestor._id }
                : { label: isLoadingGestores ? 'Cargando...' : 'Selecciona Gestor', value: '' },
              serviceType: selectedServiceType
                ? { label: selectedServiceType.name, value: selectedServiceType._id }
                : { label: 'Selecciona Trámite', value: '' },
            }}
          >
            <div className="flex flex-col gap-4">
              <SelectInput
                label="Gestor"
                name="gestor"
                options={gestorOptions.map((gestor) => ({
                  label: gestor.name,
                  value: gestor._id,
                  data: gestor,
                }))}
                onChange={(option, form) => {
                  const gestor = gestorOptions.find((g) => g._id === option.value);
                  setSelectedGestor(gestor || null);
                  form.setFieldValue('serviceType', { label: 'Selecciona Trámite', value: '' });
                }}
              />

              <SelectInput
                label="Trámite"
                name="serviceType"
                disabled={!selectedGestor || serviceTypes.length === 0}
                options={serviceTypes.map((tramite) => ({
                  label: tramite.name,
                  value: tramite._id,
                  data: tramite,
                }))}
                onChange={(option) => {
                  const tramite = serviceTypes.find((t) => t._id === option.value);
                  setSelectedServiceType(tramite || null);
                }}
              />

              {selectedServiceType && (
                <div className="mt-4 border border-gray-200 rounded-md p-4 bg-gray-50">
                  <div className="flex justify-between items-center mb-3">
                    <h3 className="text-lg font-semibold">Información del Trámite</h3>
                    {selectedServiceType.state && (
                      <span className="px-2 py-1 bg-primary/10 text-primary rounded text-xs font-medium">
                        {selectedServiceType.state}
                      </span>
                    )}
                  </div>

                  <div className="mb-3">
                    <h4 className="text-md font-medium mb-1">Descripción</h4>
                    <p className="text-gray-700">{selectedServiceType.description}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <h4 className="text-md font-medium mb-1">Costo</h4>
                      <p className="text-gray-700 font-medium">
                        {new Intl.NumberFormat('es-MX', {
                          style: 'currency',
                          currency: 'MXN',
                        }).format(selectedServiceType.cost)}
                      </p>
                    </div>
                    <div>
                      <h4 className="text-md font-medium mb-1">Duración</h4>
                      <p className="text-gray-700 font-medium">{selectedServiceType.duration} días hábiles</p>
                    </div>
                  </div>

                  {selectedServiceType.documents && selectedServiceType.documents.length > 0 && (
                    <div>
                      <h4 className="text-md font-medium mb-2">Documentos Requeridos</h4>
                      <div className="space-y-2">
                        {selectedServiceType.documents.map(
                          (doc: { name: string; format: string }, index: number) => (
                            <div
                              key={index}
                              className="flex justify-between items-center p-2 bg-white rounded-md border border-gray-200"
                            >
                              <span className="font-medium">{doc.name}</span>
                              <span
                                className={`px-2 py-1 rounded text-xs font-medium ${
                                  doc.format === 'digital'
                                    ? 'bg-blue-100 text-blue-800'
                                    : 'bg-amber-100 text-amber-800'
                                }`}
                              >
                                {doc.format}
                              </span>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {requiredDocuments.length > 0 && (
                <div className="mt-4 border border-gray-200 rounded-md p-4 bg-gray-50">
                  <h4 className="text-md font-medium mb-2">Documentos Requeridos</h4>
                  <div className="space-y-4">
                    {requiredDocuments.map((doc, index) => (
                      <div key={index} className="border border-gray-200 rounded-md p-3 bg-white">
                        <h5 className="text-sm font-medium mb-2">
                          {doc.name}
                          <span
                            className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
                              doc.format === 'digital'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-amber-100 text-amber-800'
                            }`}
                          >
                            {doc.format === 'digital' ? 'Digital' : 'Físico'}
                          </span>
                        </h5>
                        {doc.format === 'físico' ? (
                          <div className="flex items-center mt-2">
                            <input
                              type="checkbox"
                              id={`delivered-${doc.name}`}
                              className="h-4 w-4 text-primary border-gray-300 rounded"
                              checked={deliveredPhysicalDocs[doc.name] || false}
                              onChange={(e) => {
                                setDeliveredPhysicalDocs((prev) => ({
                                  ...prev,
                                  [doc.name]: e.target.checked,
                                }));
                              }}
                            />
                            <label htmlFor={`delivered-${doc.name}`} className="ml-2 text-sm text-gray-700">
                              Marcar como entregado
                            </label>
                            {deliveredPhysicalDocs[doc.name] && (
                              <div className="ml-2 text-sm text-green-600">
                                ✓ Documento marcado como entregado
                              </div>
                            )}
                          </div>
                        ) : (
                          <>
                            <FileUpload
                              primaryButtonText={`Subir ${
                                doc.name.length > 20 ? doc.name.substring(0, 20) + '...' : doc.name
                              }`}
                              accept="application/pdf,image/*"
                              mediaType={MediaType.proof_of_address}
                              totalFiles={1}
                              maxFileSize={1024 * 1024 * 5}
                              onUploadChange={(media) => {
                                setUploadedDocuments((prev) => ({
                                  ...prev,
                                  [doc.name]: media,
                                }));
                              }}
                            />
                            {uploadedDocuments[doc.name]?.length > 0 && (
                              <div className="mt-2 text-sm text-green-600">
                                Documento subido correctamente
                              </div>
                            )}
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {selectedGestor && selectedServiceType && (
                <Button
                  type="submit"
                  className="bg-primary text-white mt-4"
                  onClick={() =>
                    createGestorRequest({
                      gestorId: selectedGestor._id,
                      tramiteId: selectedServiceType._id,
                    })
                  }
                >
                  Solicitar trámite
                </Button>
              )}
            </div>
          </FormikContainer>
        </ModalContainer>
      )}
    </>
  );
}
