import { formatAddress } from '@/app/dashboard/clientes/solicitudes/[id]/PersonalDataCard';
import { associateTranslationsMX, associateTranslationsUS } from '@/constants/translations';
import { Card, CardBody, Flex, Heading, Stack, Text } from '@chakra-ui/react';
import { FaPhone, FaEnvelope } from 'react-icons/fa';

interface DriverData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  birthdate: string;
  postalCode?: string;
  city?: string;
  state?: string;
  neighborhood?: string;
  street?: string;
  streetNumber?: string;
  department?: string;
  nationalId?: string;
  taxId?: string;
  country?: string;
  source?: string;
}

export default function DriverInfoCard({
  data,
  countryCondition = false,
}: {
  data: DriverData;
  countryCondition: boolean;
}) {
  const translations = countryCondition ? associateTranslationsUS : associateTranslationsMX;

  const {
    firstName,
    lastName,
    email,
    phone,
    birthdate,
    country,
    nationalId,
    taxId,
    postalCode,
    city,
    state,
    neighborhood,
    street,
    streetNumber,
    department,
    source,
  } = data;

  return (
    <Card variant="outline" borderRadius="10" borderColor="#AFAFAF" p={4}>
      <Flex justifyContent="space-between" alignItems="center" pb={2}>
        <Heading size="sm" color="#0A293B" fontWeight="600">
          {firstName} {lastName}
        </Heading>
        <Flex align="center" gap={4}>
          <Flex align="center" color="#5A7190">
            <FaPhone size={14} />
            <Text ml={2} fontSize="sm" fontWeight={400}>
              {phone || '--'}
            </Text>
          </Flex>
          <Flex align="center" color="#5A7190">
            <FaEnvelope size={14} />
            <Text ml={2} fontSize="sm" fontWeight={400}>
              {email || '--'}
            </Text>
          </Flex>
        </Flex>
      </Flex>

      <CardBody p={0} pt={3}>
        <Stack spacing={2}>
          <Flex>
            <Text fontSize="sm" color="gray.500" minW="150px">
              {translations.countryText}
            </Text>
            <Text fontSize="sm" fontWeight="bold" color="gray.800">
              {country?.toUpperCase() || '--'}
            </Text>
          </Flex>

          <Flex>
            <Text fontSize="sm" color="gray.500" minW="150px">
              {translations.dateOfBirthText}
            </Text>
            <Text fontSize="sm" fontWeight="bold" color="gray.800">
              {birthdate || '--'}
            </Text>
          </Flex>

          {!countryCondition && (
            <>
              <Flex>
                <Text fontSize="sm" color="gray.500" minW="150px">
                  CURP
                </Text>
                <Text fontSize="sm" fontWeight="bold" color="gray.800">
                  {nationalId ? nationalId : '--'}
                </Text>
              </Flex>
              <Flex>
                <Text fontSize="sm" color="gray.500" minW="150px">
                  RFC
                </Text>
                <Text fontSize="sm" fontWeight="bold" color="gray.800">
                  {taxId ? taxId : '--'}
                </Text>
              </Flex>
            </>
          )}

          <Flex>
            <Text fontSize="sm" color="gray.500" minW="150px">
              {translations.AddressText}
            </Text>
            <Text fontSize="sm" fontWeight="bold" color="gray.800">
              {formatAddress({
                postalCode,
                city,
                state,
                neighborhood,
                street,
                streetNumber,
                department,
                country,
              })}
            </Text>
          </Flex>

          <Flex>
            <Text fontSize="sm" color="gray.500" minW="150px">
              {translations.sourceText}
            </Text>
            <Text fontSize="sm" fontWeight="bold" color="gray.800">
              {source || '--'}
            </Text>
          </Flex>
        </Stack>
      </CardBody>
    </Card>
  );
}
