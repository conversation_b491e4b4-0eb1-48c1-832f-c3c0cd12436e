'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useEffect, useState } from 'react';
import { DateTimePicker } from '../../../../../components/ui/date-time-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BlockLeadAssignationReason } from '@/constants';
import { toast } from 'sonner';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';

// Define types
interface BlockLeadAssignation {
  _id: string;
  agent: {
    name: string;
    email: string;
  };
  blockedFrom: string;
  blockedUntil: string;
  reason: string;
}

interface OverlappingBlock {
  agentId: string;
  agentName: string;
  overlappingBlocks: Array<{
    blockId: string;
    serialNumber: number;
    blockedFrom: string;
    blockedUntil: string;
    reason: string;
  }>;
}

interface BlockAgentEditModalProps {
  refetchData: boolean;
  setRefetchData: React.Dispatch<React.SetStateAction<boolean>>;
  editRecord?: BlockLeadAssignation | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BlockAgentEditModal({
  refetchData,
  setRefetchData,
  editRecord,
  open,
  onOpenChange,
}: BlockAgentEditModalProps) {
  const [blockedFrom, setBlockedFrom] = useState<Date | null>(
    editRecord ? new Date(editRecord.blockedFrom) : null
  );
  const [blockedUntil, setBlockedUntil] = useState<Date | null>(
    editRecord ? new Date(editRecord.blockedUntil) : null
  );
  const [reason, setReason] = useState<string>(editRecord?.reason || '');
  const [loading, setLoading] = useState(false);
  const [overlappingBlocks, setOverlappingBlocks] = useState<OverlappingBlock[] | null>(null);

  const { data: session } = useSession();
  const user = session?.user;

  useEffect(() => {
    if (editRecord && open) {
      setBlockedFrom(new Date(editRecord.blockedFrom));
      setBlockedUntil(new Date(editRecord.blockedUntil));
      setReason(editRecord.reason);
    }
  }, [editRecord, open]);

  const handleSubmit = async () => {
    if (!blockedFrom || !blockedUntil || !reason) {
      toast.error('Todos los campos son obligatorios.');
      return;
    }

    setLoading(true);
    setOverlappingBlocks(null); // Reset previous overlaps

    try {
      const url = `${URL_API}/leadAssignment/block-leads/${editRecord?._id}`;
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user?.accessToken}`,
        },
        body: JSON.stringify({
          userId: user?.id,
          blockId: editRecord?._id,
          blockedFrom,
          blockedUntil,
          reason,
        }),
      });

      if (response.status === 409) {
        const data = await response.json();
        setOverlappingBlocks(data.overlappingBlocks);
        setLoading(false);
        return;
      }

      if (!response.ok) {
        toast.error('Fallo al actualizar el bloqueo');
        setLoading(false);
        return;
      }

      toast.success('Bloqueo actualizado exitosamente.');
      setRefetchData(!refetchData);
      onOpenChange(false);
      resetForm();
    } catch (error) {
      console.error(error);
      toast.error('Error al procesar la solicitud');
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setBlockedFrom(null);
    setBlockedUntil(null);
    setReason('');
    setOverlappingBlocks(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Editar bloqueo</DialogTitle>
          <DialogDescription>Actualizar las fechas o el motivo del bloqueo</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Agent Name (Read-only) */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="agent" className="text-right font-medium">
              Agente
            </label>
            <div className="col-span-3">
              <p className="text-sm text-muted-foreground">{editRecord?.agent?.name}</p>
            </div>
          </div>

          {/* Blocked From */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="start" className="text-right font-medium">
              Bloqueado desde
            </label>
            <DateTimePicker
              selectedDate={blockedFrom}
              onDateChange={setBlockedFrom}
              className="col-span-3"
              btnDisabled={true}
            />
          </div>

          {/* Blocked Until */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="end" className="text-right font-medium">
              Bloqueado hasta
            </label>
            <DateTimePicker
              selectedDate={blockedUntil}
              onDateChange={setBlockedUntil}
              disabledHoursBefore={blockedFrom}
              className="col-span-3"
            />
          </div>

          {/* Reason */}
          <div className="grid grid-cols-4 items-center gap-4">
            <label htmlFor="reason" className="text-right font-medium">
              Razón
            </label>
            <Select onValueChange={setReason} value={reason}>
              <SelectTrigger id="reason" className="col-span-3">
                <SelectValue placeholder="Selecciona una razón" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(BlockLeadAssignationReason).map((r) => (
                  <SelectItem key={r} value={r}>
                    {r}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Show overlapping blocks if exists */}
        {overlappingBlocks && (
          <div className="bg-red-100 p-4 rounded-md border border-red-300 text-sm mt-4">
            <ul className="space-y-3">
              {overlappingBlocks.map((agentBlock) => (
                <li key={agentBlock.agentId}>
                  <ul className="ml-4 mt-1 space-y-1 list-disc">
                    {agentBlock.overlappingBlocks.map((block) => (
                      <li key={block.blockId} className="text-red-700">
                        <span>{`${agentBlock.agentName} ya está bloqueado desde el ${new Date(
                          block.blockedFrom
                        ).toLocaleString()} hasta el ${new Date(
                          block.blockedUntil
                        ).toLocaleString()}. con el número de serie ${block.serialNumber}`}</span>
                      </li>
                    ))}
                  </ul>
                </li>
              ))}
            </ul>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Guardando...' : 'Actualizar'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
