import { Form } from 'formik';
import InputEmail from '../Inputs/InputEmail';
import InputPassword from '../Inputs/InputPassword';
import Link from 'next/link';

interface LoginFormProps {
  valid?: boolean;
  dirty?: boolean;
}

export default function LoginForm({ valid, dirty }: LoginFormProps) {
  const validate = dirty && valid;

  return (
    <Form className="grid w-full gap-3">
      <p className="flex justify-center text-2xl text-black">Iniciar Sesión</p>
      <InputEmail name="email" placeholder="Ingresa tu correo" label="Correo electronico" />
      <div className="grid gap-2">
        <InputPassword name="password" label="Contraseña" />
        <div className="my-2">
          <input type="checkbox" />
          <label className="ml-2" htmlFor="">
            Mantener la sesión iniciada
          </label>
        </div>
      </div>
      <button
        type="submit"
        disabled={!validate}
        className={`
          ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
          text-white rounded-md  h-[40px] cursor-pointer`}
      >
        Iniciar sesión
      </button>
      <Link
        className="text-[14px] text-[#5800F7] font-bold flex justify-center mt-6"
        href="/recover-password"
        prefetch={false}
      >
        Recuperar contraseña
      </Link>
    </Form>
  );
}
