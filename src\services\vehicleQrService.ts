import { URL_API, PhysicalVehicleStatus } from '@/constants'; // Corrected import
import axios from 'axios';

interface InitiateQrScanResponse {
  actionAvailable: boolean;
  currentPhysicalStatus?: string;
  nextPhysicalStatusToDisplay?: string;
  nextStepOptions?: { value: PhysicalVehicleStatus; label: string }[];
  confirmationToken?: string;
  message: string;
}

interface ConfirmQrStatusChangeResponse {
  success: boolean;
  newPhysicalStatus?: string;
  message: string;
}

export const initiateQrScanAction = async (
  vehicleId: string,
  qrScanToken: string,
  accessToken: string
): Promise<InitiateQrScanResponse> => {
  try {
    const response = await axios.post(
      `${URL_API}/stock/${vehicleId}/initiate-qr-scan-action`,
      { qrScanToken, source: 'admin-panel' },
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `bearer ${accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Error initiating QR scan action:', error);
    throw new Error(error.response?.data?.message || 'Failed to initiate QR scan action');
  }
};

export const confirmQrStatusChange = async ({
  vehicleId,
  confirmedNextStatus,
  confirmationToken,
  accessToken,
  photoPath,
  vendorRegion,
  vendorWorkshopName,
}: {
  vehicleId: string;
  confirmedNextStatus: PhysicalVehicleStatus;
  confirmationToken: string;
  accessToken: string;
  photoPath: string;
  vendorRegion?: string;
  vendorWorkshopName?: string;
}): Promise<ConfirmQrStatusChangeResponse> => {
  try {
    const payload: any = {
      confirmedNextStatus,
      confirmationToken,
      photoPath,
    };

    if (confirmedNextStatus === PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP) {
      if (vendorRegion) payload.vendorRegion = vendorRegion;
      if (vendorWorkshopName) payload.vendorWorkshopName = vendorWorkshopName;
    }

    const response = await axios.post(`${URL_API}/stock/${vehicleId}/confirm-qr-status-change`, payload, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `bearer ${accessToken}`,
      },
    });
    return response.data;
  } catch (error: any) {
    console.error('Error confirming QR status change:', error);
    throw new Error(error.response?.data?.message || 'Failed to update status');
  }
};
