import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';
import VehicleTable from '../../components/VehicleTable';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function StockPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({
    searchParams,
    filters,
    paramCategory: 'withdrawn',
  });
  //console.log(`definitiveFilters: ${JSON.stringify(definitiveFilters)}`);

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 10,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'inactive',
      category: definitiveFilters?.category || 'withdrawn',
      subCategory: definitiveFilters?.subCategory || 'total-loss',
      country: definitiveFilters?.country,
    },
  });

  const subOptions = [
    { name: 'total-loss', label: 'Total Loss' },
    { name: 'operational-loss', label: 'Operational Loss' },
  ];

  const page = {
    api: 'inactive',
    name: 'Inactive',
    count: 12,
  };

  const subPage = {
    api: 'withdrawn',
    name: 'Withdrawn',
    count: 12,
  };

  if (!result) return null;

  return (
    <VehicleTable
      route="withdrawn"
      page={page}
      subPage={subPage}
      data={result.stock}
      totalCount={result.totalCount}
      subOptions={subOptions}
    />
  );
}
