'use client';
import { createContext, useState, useContext } from 'react';

// Crea el contexto

interface ContextProps {
  isChanged: boolean;
  setIsChanged: React.Dispatch<React.SetStateAction<boolean>>;
}

export const NotificationContext = createContext<ContextProps | null>(null);

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) throw new Error('There is not auth context');
  return context;
};

// Proveedor del contexto
export default function NotificationProvider({ children }: any) {
  const [isChanged, setIsChanged] = useState(false);

  return (
    <NotificationContext.Provider value={{ isChanged, setIsChanged }}>
      {children}
    </NotificationContext.Provider>
  );
}
