'use client';
import { Button } from '@/components/ui/button';
import React from 'react';
import Swal from 'sweetalert2';
import { removeClient } from '../actions';
import { useRouter } from 'next/navigation';

interface Props {
  clientId: string;
}

export default function RemoveClient({ clientId }: Props) {
  const router = useRouter();
  return (
    <>
      <Button
        variant="destructive"
        className="rounded-sm"
        onClick={() => {
          return Swal.fire({
            title: '¿Estás seguro?',
            text: 'No podrás revertir esta acción',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#5800F7',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Sí, eliminar',
            cancelButtonText: 'Cancelar',
          }).then(async (result) => {
            if (result.isConfirmed) {
              const isDeleted = await removeClient(clientId);

              // Swal.fire('Eliminado', 'El cliente ha sido eliminado', 'success');

              if (isDeleted.success) {
                Swal.fire('Eliminado', 'El cliente ha sido eliminado', 'success');
              } else {
                Swal.fire('Error', isDeleted.message, 'error');
              }

              router.refresh();
            }
          });
        }}
      >
        Eliminar
      </Button>
    </>
  );
}
