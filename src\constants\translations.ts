export const associateTranslationsMX = {
  active: 'Activo',
  inactive: 'Inactivo',
  assignText: 'Asignar Conductor',
  verifyingText: 'Verificando...',
  confirmText: 'Confirmar Conductor',
  continueText: 'Siguiente',
  previousText: 'Anterior',
  searchText: 'Buscar conductora...',
  submitConfirmMessage: '¿Estas seguro de querer subir el asociado sin los documentos completos?',
  submitConfirmText: 'Al cancelar se reseteara el formulario',
  confirmButtonText: 'Si',
  successTitle: 'Conductor asignado',
  successDescription: 'Actualizando pagina...',
  apiError: 'Hubo un error',
  driverNotAssigned: 'No se ha asignado el conductor',
  emailError: 'Este conductor está asignado a otro vehículo',
  selectDriver: 'Por favor seleccione un conductor',
  noDriver: 'No se encontraron conductoras',
  driverTranslation: 'Conductor',
  curpTranslation: 'CURP',
  countryText: 'Pa<PERSON>',
  dateOfBirthText: 'Fecha de nacimento',
  AddressText: 'Dirección',
  sourceText: 'Fuente',
  getRecordsText: (admissionRequests: any, pagination: any) => {
    return `Mostrando ${admissionRequests?.length || 0} de ${
      pagination?.totalItems || 0
    } conductores aprobados`;
  },
  noAccounts: 'No hay cuentas disponibles',
  status: 'Estado',
  platform: 'Plataforma',
  revenueAnalysis: 'Análisis de ingresos',
  preApproved: 'Preaprobado',
  preApprovedConditions: 'Preaprobado con condiciones',
  rejected: 'Rechazado',
  homeVisit: 'Visita a domicilio',
  date: 'Fecha',
  homeVisitor: 'Visitante casero',
  noHomeVisit: 'No se ha registrado visita a domicilio',
  mandatoryDocs: 'Documentos Obligatorios',
  additionalDocs: 'Documentos Adicionales',
  name: 'Nombre',
  document: 'Documento',
  docAnalysis: 'Análisis de documentos',
  screenshots: 'Capturas de pantalla',
  download: 'Descargar',
  noFiles: 'No se subieron archivos',
  statusError: 'El estado de la solicitud de admisión ha sido cambiado',
};

export const associateTranslationsUS = {
  active: 'Active',
  inactive: 'Inactive',
  assignText: 'Assign Driver',
  verifyingText: 'Verifying...',
  confirmText: 'Confirm Driver',
  continueText: 'Continue',
  previousText: 'Previous',
  searchText: 'Search driver...',
  submitConfirmMessage: 'Are you sure you want to upload the associate without the complete documents?',
  submitConfirmText: 'Upon canceling, the form will be reset.',
  confirmButtonText: 'Yes',
  successTitle: 'Assigned driver',
  successDescription: 'Updating page...',
  apiError: 'There was an error',
  driverNotAssigned: 'Driver has not been assigned',
  emailError: 'This driver is assigned to another vehicle',
  selectDriver: 'Please select a driver',
  noDriver: 'No drivers found',
  driverTranslation: 'Driver',
  curpTranslation: 'National ID',
  countryText: 'Country',
  dateOfBirthText: 'Date of birth',
  AddressText: 'Address',
  sourceText: 'Source',
  getRecordsText: (admissionRequests: any, pagination: any) => {
    return `Showing ${admissionRequests?.length || 0} of ${pagination?.totalItems || 0} approved drivers`;
  },
  noAccounts: 'No accounts available',
  status: 'Status',
  platform: 'Platform',
  revenueAnalysis: 'Revenue analysis',
  preApproved: 'Pre-approved',
  preApprovedConditions: 'Pre-approved with conditions',
  rejected: 'Rejected',
  homeVisit: 'Home visit',
  date: 'Date',
  homeVisitor: 'Home visitor',
  noHomeVisit: 'No home visit recorded',
  mandatoryDocs: 'Mandatory Documents',
  additionalDocs: 'Additional Documents',
  name: 'Name',
  document: 'Document',
  docAnalysis: 'Document Analysis',
  screenshots: 'Screenshots',
  download: 'Download',
  noFiles: 'No files uploaded',
  statusError: 'Admission request status has been changed',
};

export const vehicleRegistrationTranslationsMX = {
  addVehicle: 'Agregar Vehiculo',
  bulkUpload: 'Iniciar carga masiva',
  selectCountry: 'Selecciona un país',
  selectMethod: 'Selecciona un método',
  continue: 'Continuar',
  submit: 'Entregar',
  back: 'Atrás',
  selectRegion: 'Seleccionar región',
  normalRegister: 'Registro Normal',
  bulkXML: 'Carga XML Masiva',
  pleaseUploadFile: 'Por favor, sube al menos un archivo',
  uploadInProgress: 'La carga del archivo está en proceso',
  uploadNotification: 'Se enviará una notificación al finalizar',
  error: 'Error',
  uploadFailed: 'Error al subir los archivos',
};

export const vehicleRegistrationTranslationsUS = {
  addVehicle: 'Add Vehicle',
  bulkUpload: 'Bulk Upload',
  selectCountry: 'Select a country',
  selectMethod: 'Select a method',
  continue: 'Continue',
  submit: 'Submit',
  back: 'Back',
  selectRegion: 'Select Region',
  normalRegister: 'Normal Registration',
  bulkXML: 'Bulk XML Upload',
  pleaseUploadFile: 'Please upload at least one file',
  uploadInProgress: 'File upload is in progress',
  uploadNotification: "You will receive a notification when it's complete",
  error: 'Error',
  uploadFailed: 'Failed to upload files',
};

export const vehicleSearchTranslationsMX = {
  searchPlaceholder: 'Busqueda global...',
  noResults: 'No se encontraron vehículos',
  queryLength: 'Escribe al menos 3 caracteres para buscar',
};

export const vehicleSearchTranslationsUS = {
  searchPlaceholder: 'Global search...',
  noResults: 'No vehicles found',
  queryLength: 'Type at least 3 characters to search',
};

export const documentUploadTranslationsMX = {
  selectDocumentCategory: 'Selecciona la categoría del documento',
  uploadDocuments: 'Subir Documentos',
  insurancePolicy: 'Póliza de Seguros',
  tenencia: 'Tenencia',
  circulationCardFront: 'Tarjeta de Circulación (Frente)',
  circulationCardBack: 'Tarjeta de Circulación (Reverso)',
  platesAltaPlacas: 'Placas Alta Placas',
  platesFront: 'Placas (Frente)',
  platesBack: 'Placas (Reverso)',
  facture: 'Factura',
  upload: 'Subir',
  next: 'Siguiente',
  bulkUploadTitle: 'Carga masiva de documentos',
  selectLocation: 'Selecciona ubicación',
  uploadingFiles: 'Subiendo archivos...',
  selectedFiles: 'Archivos seleccionados:',
  showMore: 'Mostrar más',
  showLess: 'Mostrar menos',
  selectFilesMessage: 'Seleccionar archivos',
  back: 'Atrás',
  selectRegion: 'Selecciona una región',
  select: 'Selecciona',
  uploading: 'Subiendo...',
  circulationCardBackInfo:
    'Asegúrese de que el reverso tenga un número de serie adecuado y un código de barras que pueda abrir una página desde la cual se pueda extraer el VIN (solo disponible para CDMX).',
  defaultDocumentInfo:
    'El documento debe contener el VIN (Número de Identificación del Vehículo) para asignarlo al vehículo correspondiente.',
  errorProcessing: 'Error al procesar documentos.',
  maxFilesError: (maxFiles: number) => `Máximo ${maxFiles} archivos permitidos.`,
  fileSizeError: (fileSizeMB: number) =>
    `${fileSizeMB} archivo(s) exceden el límite de tamaño de ${fileSizeMB}MB.`,
  invalidFileFormat: (allowedFormats: string) =>
    `Formato(s) de archivo no válido. Solo se permiten archivos ${allowedFormats}.`,
  startingUpload: 'Iniciando la carga de documentos...',
  uploadSuccess: 'Todos los archivos se cargaron correctamente.',
  uploadError: 'Error al cargar archivos.',
  userNotAuthenticated: 'Usuario no autenticado. Por favor, inicie sesión nuevamente.',
  uploadingText: 'Subiendo archivos...',
  selectedFilesTitle: 'Archivos seleccionados:',
  moreLabel: 'Mostrar más',
  lessLabel: 'Mostrar menos',
  filesRequirements: (maxFiles: number, maxFileSizeMB: number): string => {
    return `Máximo ${maxFiles} archivos, ${maxFileSizeMB}MB por archivo`;
  },
  allowedFormats: (allowedExtensions: string[]): string => {
    return `Formatos permitidos: ${allowedExtensions.map((ext) => `.${ext}`).join(', ')}`;
  },
  emptyPlaceholder: 'Selecciona archivos',
  duplicateFileError: 'Algunos archivos no se agregaron debido a duplicados',
  // Fields for requiredFields display
  requiredFields:
    'Por favor, asegúrese de que el contenido del documento sea de buena calidad y que la siguiente información esté claramente presente y visible en el documento',
  vin: 'VIN',
  policyNumber: 'Número de Póliza',
  insurer: 'Aseguradora',
  validity: 'Vigencia',
  broker: 'Corredor',
  payment: 'Pago',
  number: 'Número',
  plates: 'Placas',
  serialNumber: 'Número de serie',
  billNumber: 'Número de Factura',
  billDate: 'Fecha de Factura',
  billAmount: 'Monto de Factura',
};

export const documentUploadTranslationsUS = {
  selectDocumentCategory: 'Select Document Category',
  uploadDocuments: 'Upload Documents',
  insurancePolicy: 'Insurance Policy',
  tenencia: 'Tenencia',
  circulationCardFront: 'Circulation Card Front',
  circulationCardBack: 'Circulation Card Back',
  platesAltaPlacas: 'Plates Alta Placas',
  platesFront: 'Plates Front',
  platesBack: 'Plates Back',
  facture: 'Facture',
  upload: 'Upload',
  next: 'Next',
  bulkUploadTitle: 'Bulk Upload Vehicle Documents',
  selectLocation: 'Select Location',
  uploadingFiles: 'Uploading files...',
  selectedFiles: 'Selected files:',
  showMore: 'Show more',
  showLess: 'Show less',
  selectFilesMessage: 'Select files',
  back: 'Back',
  selectRegion: 'Select a region',
  select: 'Select',
  uploading: 'Uploading...',
  circulationCardBackInfo:
    'Please ensure that the back has a proper series number and a barcode that can open a page from which VIN can be extracted (only available for CDMX).',
  defaultDocumentInfo:
    'Document must contain the VIN (Vehicle Identification Number) to map it to the relevant vehicle.',
  errorProcessing: 'Error triggering document processing.',
  maxFilesError: (maxFiles: number) => `Maximum ${maxFiles} files allowed.`,
  fileSizeError: (fileSizeMB: number) => `${fileSizeMB} file(s) exceed the ${fileSizeMB}MB size limit.`,
  invalidFileFormat: (allowedFormats: string) =>
    `Invalid file format(s). Only ${allowedFormats} files are allowed.`,
  startingUpload: 'Starting document upload...',
  uploadSuccess: 'All files uploaded successfully.',
  uploadError: 'Error uploading files.',
  userNotAuthenticated: 'User not authenticated. Please log in again.',
  uploadingText: 'Uploading files...',
  selectedFilesTitle: 'Selected files:',
  moreLabel: 'Show more',
  lessLabel: 'Show less',
  filesRequirements: (maxFiles: number, maxFileSizeMB: number): string => {
    return `Maximum ${maxFiles} files, ${maxFileSizeMB}MB per file`;
  },
  allowedFormats: (allowedExtensions: string[]): string => {
    return `Allowed formats: ${allowedExtensions.map((ext) => `.${ext}`).join(', ')}`;
  },
  emptyPlaceholder: 'Select files',
  duplicateFileError: 'Some files were not added due to duplicates',
  // Fields for requiredFields display
  requiredFields:
    'Please make sure that document content is of good quality and following information is clearly present and visible in the document',
  vin: 'VIN',
  policyNumber: 'Policy Number',
  insurer: 'Insurer',
  validity: 'Validity',
  broker: 'Broker',
  payment: 'Payment',
  number: 'Number',
  plates: 'Plates',
  serialNumber: 'Serial Number',
  billNumber: 'Bill Number',
  billDate: 'Bill Date',
  billAmount: 'Bill Amount',
};

export const connectionTranslationsMX = {
  lostTitle: 'Conexión perdida',
  lostDescription:
    'Se perdió la conexión a internet. Por favor inténtalo de nuevo cuando tu conexión esté estable.',
  restoredTitle: 'Conexión restaurada',
  restoredDescription: 'Tu conexión a internet se ha restablecido.',
};

export const connectionTranslationsUS = {
  lostTitle: 'Connection lost',
  lostDescription: 'Internet connection lost. Please try again when your connection is stable.',
  restoredTitle: 'Connection restored',
  restoredDescription: 'Your internet connection is back.',
};
