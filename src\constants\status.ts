import {
  VehicleCategoryTranslations,
  VehicleCategoryTranslationsMX,
  VehicleSubCategoryTranslations,
  VehicleSubCategoryTranslationsMX,
} from '@/app/dashboard/flotilla/components/translations/statusTranslations';

type SubCategoryKey = keyof typeof VehicleSubCategoryTranslations;

export type CategoryOption = {
  label: string;
  value: 'workshop' | 'legal' | 'insurance' | 'sold' | 'utilitary' | 'delivered' | 'adendum';
};

export const categoriesOptions: CategoryOption[] = [
  { label: 'Taller', value: 'workshop' },
  { label: 'Legal', value: 'legal' },
  { label: 'Seguro', value: 'insurance' },
  { label: 'Vendido', value: 'sold' },
  { label: 'Utilitario', value: 'utilitary' },
  { label: 'Entregado', value: 'delivered' },
  { label: 'Adendum', value: 'adendum' },
];

export const categoryToStatusMapping = {
  workshop: 'in-service',
  legal: 'legal-process',
  insurance: 'awaiting-insurance',
  sold: 'sold',
  utilitary: 'utilitary',
  delivered: 'delivered',
  adendum: 'adendum',
} as const;

export const subCategoriesOnCategorySelected = {
  'in-service': [
    { label: 'Aesthetic Repair', value: 'aesthetic-repair' },
    { label: 'Duplicate key missing', value: 'duplicate-key-missing' },
    { label: 'Mechanical Repair', value: 'mechanical-repair' },
    { label: 'Electrical Repair', value: 'electrical-repair' },
    { label: 'Engine Repair', value: 'engine-repair' },
    { label: 'Waiting for parts', value: 'waiting-for-parts' },
    { label: 'Corrective Maintenance', value: 'corrective-maintenance' },
  ],
  'legal-process': [
    { label: 'Demand', value: 'demand' },
    { label: 'Public Ministry', value: 'public-ministry' },
    { label: 'Complaint', value: 'complaint' },
    { label: 'Impounded', value: 'impounded' },
  ],
  'awaiting-insurance': [
    { label: 'Valuation', value: 'valuation' },
    { label: 'Damage payment', value: 'damage-payment' },
    { label: 'Repair', value: 'repair' },
  ],
  sold: [],
  utilitary: [],
  delivered: [],
  adendum: [],
};

export const getTranslationMap = (country: string | null) => {
  const isMX = country === 'Mexico';
  return {
    categories: isMX ? VehicleCategoryTranslationsMX : VehicleCategoryTranslations,
    subCategories: isMX ? VehicleSubCategoryTranslationsMX : VehicleSubCategoryTranslations,
  };
};

export function findSubCategory(category: CategoryOption['value'], country: string | null) {
  const statusValue = categoryToStatusMapping[category];
  const subCategories = subCategoriesOnCategorySelected[statusValue] || [];
  const translationMap = getTranslationMap(country).subCategories;

  return subCategories.map((subCategory) => ({
    label: translationMap[subCategory.value as SubCategoryKey] || subCategory.label,
    value: subCategory.value,
  }));
}
