import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input, InputProps } from '@/components/ui/input';
import { TextArea } from '@/components/ui/textarea';
import { Control, ControllerRenderProps, FieldValues } from 'react-hook-form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { IconType } from 'react-icons';
import { forwardRef } from 'react';

interface IHookFormRadixUIField {
  form: any;
  fieldName: string;
  formLabel: string;
  placeholder?: string;
  type?: string;
  id?: string;
  className?: string;
  Icon?: IconType;
  prefixString?: string;
  isDisabled?: boolean;
  defaultErrorBoxStyles?: boolean;
  formFieldClassName?: string;
}

export const HookFormRadixUIField = (props: IHookFormRadixUIField) => {
  const {
    form,
    fieldName,
    formLabel,
    placeholder,
    type,
    id,
    className,
    Icon,
    prefixString,
    isDisabled,
    defaultErrorBoxStyles,
    formFieldClassName,
  } = props;

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field }) => (
        <FormItem className={cn('flex-1 flex flex-col', className)}>
          <FormLabel className="basis-1/3 text-primaryBlueGray text-sm">{formLabel}</FormLabel>
          <FormControl className={cn(formFieldClassName)}>
            {Icon ? (
              <InputWithIconStyling
                id={id}
                placeholder={placeholder}
                type={type}
                field={field}
                Icon={Icon}
                prefixString={prefixString}
                disabled={isDisabled}
              />
            ) : type === 'textarea' ? (
              <TextArea
                id={id}
                className="basis-2/3 h-60"
                placeholder={placeholder ? placeholder : 'Input'}
                {...field}
              />
            ) : type === 'date' ? (
              <CustomDateInput id={id} placeholder={placeholder ? placeholder : 'Date'} {...field} />
            ) : (
              <Input
                id={id}
                className="basis-2/3"
                placeholder={placeholder}
                type={type ? type : 'text'}
                disabled={isDisabled}
                {...field}
                onChange={(e) => {
                  const value = type === 'number' ? e.target.valueAsNumber || '' : e.target.value;
                  field.onChange(value);
                }}
              />
            )}
          </FormControl>
          <div className={defaultErrorBoxStyles ? 'h-4 min-h-[0.8rem]' : ''}>
            <FormMessage />
          </div>
        </FormItem>
      )}
    />
  );
};

// eslint-disable-next-line react/display-name
export const CustomDateInput = forwardRef<HTMLInputElement, InputProps>((props, ref) => {
  return (
    <div className="relative w-full max-w-sm">
      <input
        type="date"
        className="custom-date pl-10 py-1 w-full border border-gray-300 rounded-md  focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 "
        ref={ref}
        {...props}
        // disabled={true}
      />
      <style jsx>{`
        .custom-date {
          position: relative;
        }
        .custom-date::-webkit-calendar-picker-indicator {
          position: absolute;
          left: 10px;
          top: 50%;
          transform: translateY(-50%);
          color: gray;
        }
      `}</style>
    </div>
  );
});

interface IInputWithIcon {
  id?: string;
  placeholder?: string;
  type?: string;
  field: ControllerRenderProps<FieldValues, string>;
  containerClassName?: string;
  inputClassName?: string;
  Icon: IconType;
  prefixString?: string;
  disabled?: boolean;
}

export const InputWithIconStyling = (props: IInputWithIcon) => {
  const { id, placeholder, type, field, containerClassName, inputClassName, Icon, prefixString, disabled } =
    props;

  return (
    <div
      className={cn(
        'flex gap-2 items-center w-full px-2 rounded-md border border-input text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        containerClassName
      )}
    >
      {<Icon size={25} />}
      {prefixString ? prefixString : null}
      <Input
        id={id}
        className={cn('w-full h-full py-2 border-0 focus-visible:outline-none', inputClassName)}
        placeholder={placeholder}
        type={type ? type : 'text'}
        defaultStyles={false}
        disabled={disabled}
        {...field}
        onChange={(e) => {
          const value = type === 'number' ? e.target.valueAsNumber || '' : e.target.value;
          field.onChange(value);
        }}
      />
    </div>
  );
};

interface IHookFormRadixUISelect {
  control: Control<any>;
  fieldName: string;
  formLabel: string | JSX.Element;
  selectOptions: Array<{ label: string; value: string }>;
  className?: string;
  onChange?: (value: string) => void;
}

export const HookFormRadixUISelect = (props: IHookFormRadixUISelect) => {
  const { control, fieldName, selectOptions, formLabel, className, onChange } = props;

  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => (
        <FormItem className={cn('flex-1 flex flex-col', className)}>
          <FormLabel className="basis-1/3 text-primaryBlueGray text-sm">{formLabel}</FormLabel>
          <Select
            onValueChange={(value) => {
              field.onChange(value);
              onChange?.(value);
            }}
            value={field.value}
            defaultValue={field.value}
          >
            <FormControl className="basis-2/3">
              <SelectTrigger>
                <SelectValue placeholder="No" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {selectOptions.map((option) => (
                <SelectItem key={option.label} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
