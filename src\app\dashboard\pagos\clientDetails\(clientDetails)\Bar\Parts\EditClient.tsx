'use client';
import { User } from '@/app/dashboard/pagos/types';
import SelectInput from '@/components/Inputs/SelectInput';
import Spinner from '@/components/Loading/Spinner';
import CustomModal from '@/components/Modals/CustomModal';
import { /* cfdiUseOptions, */ taxSystemOptions } from '@/constants/sat';
import { useToast } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { editClient } from '../actions/edit-client';
import CustomInput from '@/components/Inputs/CustomInput';
import * as Yup from 'yup';
import { rfcRegExp } from '@/validatorSchemas/assignAssociateSchema';

interface EditClientProps {
  clientData: User;
}

export default function EditClient({ clientData }: EditClientProps) {
  const toast = useToast();
  const router = useRouter();
  const [isSending, setIsSending] = useState(false);

  return (
    <>
      {isSending && <Spinner />}
      <CustomModal
        header="Editar información fiscal"
        openButtonText="Editar cliente"
        confirmButtonText="Guardar cambios"
        updateIconColor="#5800F7"
        customSubmit
        validatorSchema={Yup.object().shape({
          legal_name: Yup.string().required('Nombre legal es requerido'),
          rfc: Yup.string().matches(rfcRegExp, 'RFC inválido').required('RFC es requerido'),
          tax_system: Yup.object().shape({
            // label: Yup.string().required('Regimen fiscal es requerido'),
            value: Yup.string().required('Regimen fiscal es requerido'),
          }),
          zip: Yup.string()
            .matches(/^[0-9]+$/, 'Ingresa solo números')
            .min(5, 'Agrega un codigo valido')
            .max(5, 'Agrega un codigo valido')
            .required('Codigo postal requerido'),
        })}
        onSubmit={async (values, helpers, onClose) => {
          setIsSending(true);

          onClose();
          const objValues = {
            ...values,
            tax_system: values.tax_system.value,
          };

          const res = await editClient(clientData.id, objValues);
          setIsSending(false);

          if (res.success) {
            helpers.resetForm();
            router.refresh();
            return toast({
              title: 'Cliente actualizado',
              description: 'La información fiscal del cliente ha sido actualizada',
              status: 'success',
              position: 'top',
              duration: 5000,
              isClosable: true,
            });
          }

          return toast({
            title: 'Error al actualizar el cliente',
            description: res.message || 'Ocurrió un error al actualizar la información fiscal del cliente',
            status: 'error',
            position: 'top',
            duration: 5000,
            isClosable: true,
          });
        }}
        // isPrimaryButton
        isUpdate
        plusButton={false}
        initialValues={{
          ...clientData,
          tax_system: taxSystemOptions.find((item) => item.value === clientData.tax_system)!,
        }}
        body={
          <>
            <div className="flex flex-col gap-4">
              <SelectInput label="Regimen Fiscal" name="tax_system" options={taxSystemOptions} />
              <CustomInput
                label="Nombre Legal"
                name="legal_name"
                type="text"
                onChange={(e) => {
                  return e.toUpperCase();
                }}
              />
              <CustomInput
                label="RFC / Identificador Tributario"
                name="rfc"
                type="text"
                onChange={(e) => {
                  return e.toUpperCase();
                }}
              />

              <CustomInput label="Codigo Postal" name="zip" type="number" />
            </div>
          </>
        }
        onCloseModal={() => console.log('close')}
      />
    </>
  );
}
