'use client';
import { useNotification } from './NotificationProvider';
import { HiOutlineCheckCircle } from 'react-icons/hi';
import { AiOutlineClose } from 'react-icons/ai';

export default function NotificationMessage() {
  const { isChanged, setIsChanged } = useNotification();
  return (
    <>
      {isChanged && (
        <div
          className="
            w-full 
          bg-[#BFF0E0] 
          text-[#29CC97] 
          border-[#29CC97] 
            border-[1px] 
            h-[40px] 
            rounded 
            flex 
            flex-row
            items-center 
            justify-between
            mb-4
            pl-6
            pr-3
            "
        >
          <div className="flex flex-row">
            <HiOutlineCheckCircle size={24} />
            <p className="ml-2 font-bold">Tu contraseña se cambió correctamente</p>
          </div>
          <AiOutlineClose size={24} className="cursor-pointer" onClick={() => setIsChanged(false)} />
        </div>
      )}
    </>
  );
}
