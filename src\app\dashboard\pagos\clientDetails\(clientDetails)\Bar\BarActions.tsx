'use client';

import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { usersPaymentActionsAllowed } from '@/constants';
import RemoveClient from './Parts/RemoveClient';
import { User } from '../../../types';
import ValidateTaxInfo from './Parts/ValidateTaxInfo';

interface BarActionsProps {
  clientData: User;
}

export default function BarActions({ clientData }: BarActionsProps) {
  const { user } = useCurrentUser();

  const isSuperAdmin = user.role.toLowerCase() === 'superadmin';
  const isAgent = user.role.toLowerCase() === 'agent';

  return (
    <>
      <div className="flex gap-2">
        {(isSuperAdmin || isAgent) && <ValidateTaxInfo clientData={clientData} />}

        {usersPaymentActionsAllowed.includes(user.email) && <RemoveClient clientId={clientData.id} />}
      </div>
    </>
  );
}
