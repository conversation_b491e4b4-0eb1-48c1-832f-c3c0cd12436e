import React from 'react';
import { Checkbox } from '@chakra-ui/react';
import { PermissionModuleMexicanLabels } from '@/constants';

interface PermissionMatrixProps {
  permissionSets: Section[];
  selected: Record<string, boolean>;
  setSelected: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  disabledPermissions: Record<string, boolean>;
}

interface Section {
  section: string;
  subSections: {
    subSection: string;
    capabilities: string[];
  }[];
}

const PermissionMatrix: React.FC<PermissionMatrixProps> = ({
  permissionSets,
  selected,
  setSelected,
  disabledPermissions,
}) => {
  const togglePermission = (key: string) => {
    setSelected((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const mexicanLabels = PermissionModuleMexicanLabels;

  return (
    <div>
      {(() => {
        let sectionCounter = 0;

        return permissionSets.map((section) => {
          let subSectionCounter = 1;
          sectionCounter++;
          return (
            <div key={section.section} className="mb-4">
              <div className="text-lg font-semibold capitalize mb-2">
                {sectionCounter}: {mexicanLabels[section.section]}:
              </div>
              {section.subSections.map((sub) => (
                <div key={`${section.section}_${sub.subSection}`} className="ml-4 mb-2">
                  {sub.subSection && (
                    <div className="font-medium font-semibold text-sm capitalize mb-1">
                      {sectionCounter}.{subSectionCounter++} {mexicanLabels[sub.subSection]}:
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2 text-sm">
                    {sub.capabilities.map((cap) => {
                      const key = `${section.section}.${sub.subSection}.${cap}`;
                      return (
                        <Checkbox
                          key={key}
                          isChecked={!!selected[key] || !!disabledPermissions[key]}
                          onChange={() => togglePermission(key)}
                          disabled={!!disabledPermissions[key]}
                        >
                          {mexicanLabels[cap]}
                        </Checkbox>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          );
        });
      })()}
    </div>
  );
};

export default PermissionMatrix;
