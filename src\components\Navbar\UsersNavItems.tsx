'use client';

import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { Capabilities, Paths, Roles, Sections, Subsections } from '@/constants';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FaChevronDown, FaRegUserCircle, FaUser, FaUserFriends, FaUsers } from 'react-icons/fa';

const UsersNavItems = () => {
  const ability = usePermissions();

  const pathname = usePathname();

  const navButton = {
    icon: <FaRegUserCircle size={18} />,
    name: 'Gestión de Usuarios',
  };

  const subNavLinks = [
    {
      link: Paths.userManagement_users,
      icon: <FaUser size={16} />,
      name: '<PERSON><PERSON>rio<PERSON>',
      key: Subsections.Users,
    },
    {
      link: Paths.userManagement_permissions,
      icon: <FaUsers size={16} />,
      name: 'Permisos',
      key: Subsections.Permissions,
    },
  ];

  const isActive = subNavLinks.some((item) => pathname.includes(item.link));

  const { user: currentUser } = useCurrentUser();

  return (
    <details className="group ml-4 transition-all duration-150">
      <summary
        className={`flex cursor-pointer items-center rounded-lg px-4 py-2 transition-all duration-500 ${
          isActive ? 'bg-primaryPurple text-white' : ''
        }`}
      >
        {isActive ? navButton.icon : <FaUserFriends size={18} />}
        <span
          className={`ml-3.5 text-[20px] font-semibold leading-tight transition duration-300 ${
            isActive ? 'text-white' : 'text-gray-600'
          }`}
        >
          Gestión de Usuarios
        </span>
        <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180">
          <FaChevronDown color={isActive ? 'white' : 'black'} />
        </span>
      </summary>

      <nav className="ml-8 mt-1.5 flex flex-col transition-all duration-500">
        {subNavLinks.map(
          (item) =>
            (currentUser.role === Roles.Superadmin ||
              canPerform(ability, Capabilities.View, Sections.UserManagement, item.key)) && (
              <Link href={item.link} key={item.link} prefetch={false}>
                <button
                  className={`flex items-center rounded-lg px-4 py-2 text-sm font-medium ${
                    pathname === item.link
                      ? 'bg-primaryPurple text-white'
                      : 'hover:bg-gray-100 hover:text-gray-700'
                  }`}
                  style={{ width: '96%' }}
                >
                  {item.icon}
                  <span className="ml-3">{item.name}</span>
                </button>
              </Link>
            )
        )}
      </nav>
    </details>
  );
};

export default UsersNavItems;
