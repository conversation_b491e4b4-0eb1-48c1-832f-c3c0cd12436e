/* eslint-disable react-hooks/rules-of-hooks */
import { ColumnDef } from '@tanstack/react-table';
import { Payment } from '../types';

import { Check, X, CirclePlus, BookCheck } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Copy } from 'lucide-react';
import { toast } from 'sonner';
import InvoiceReceiptSheet from '@/components/Sheets/InviceReceiptSheet/InvoiceReceiptSheet';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';
import { canPerformPaymentActions, PAGOS_PAYMENT_URL } from '@/constants';
import { validate } from 'uuid';
import { getCookie, setCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';
import React from 'react';
import { useCurrentUser } from '../../providers/CurrentUserProvider';
import { usePaymentTransactionState } from '@/hooks/usePaymentTransaction';

export const paymentColumns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row?.original?.id}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'clientId',
    header: ({ column, table }) => {
      const router = useRouter();

      const filterCookies = JSON.parse(getCookie('payments-page-filters') || '{}');

      const clientId = filterCookies.clientId || '';

      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">CLIENTE</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Buscar por id de cliente</h4>
                <Input
                  className="mt-4"
                  value={clientId || ''}
                  // onChange={(event) => column?.setFilterValue(event.target.value)}
                  onChange={(e) => {
                    column.setFilterValue(e.target.value);

                    const isUuid = validate(e.target.value);
                    if (isUuid) {
                      // console.log('is_uuid', is_uuid);

                      filterCookies.clientId = e.target.value;
                      setCookie('payments-pagination', JSON.stringify({ pageIndex: 0, pageSize: 10 }));
                      table.setPagination({ pageIndex: 0, pageSize: 10 });
                      setCookie('payments-page-filters', JSON.stringify(filterCookies));
                      router.refresh();
                    } else {
                      if ('clientId' in filterCookies) {
                        delete filterCookies.clientId;
                      }
                      setCookie('payments-pagination', JSON.stringify({ pageIndex: 0, pageSize: 10 }));
                      table.setPagination({ pageIndex: 0, pageSize: 10 });
                      setCookie('payments-page-filters', JSON.stringify(filterCookies));
                      router.refresh();
                    }
                  }}
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex flex-col items-start">
          <div className="font-medium">{row?.original?.client?.name}</div>
          <div className="font-medium">
            {row?.original?.client?.id}
            <Copy
              className="h-4 w-4 pr-1 hover:cursor-pointer"
              onClick={() => {
                navigator.clipboard.writeText(row?.original?.client?.id);
                toast('ID copiado al portapapeles');
              }}
            />
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'contractNumber',
    header: 'CONTRACT NUMBER',
    cell: ({ row }) => {
      return (
        <div className="flex flex-col items-center">
          <div className="font-medium">{row?.original?.client?.contractNumber}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">ESTADO</Label>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row.getValue('status')}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total',
    header: 'MONTO',
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('total')}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'CREADO',
    cell: ({ row }) => {
      const date = new Date(row.getValue('createdAt'));
      return <div className="font-medium">{date.toString()}</div>;
    },
  },
  {
    accessorKey: 'isPaid',
    header: 'PAGADO',
    cell: ({ row }) => {
      //const user = row.original;
      const isPaid = row.getValue('isPaid');
      if (isPaid) {
        return <Check />;
      } else {
        return <X />;
      }
    },
  },
  {
    accessorKey: 'subscription.isActive',
    header: 'FUENTE',
    cell: ({ row }) => {
      // const isActive = row?.original?.subscription?.isActive;
      const isSubscription = row?.original?.type === 'subscription';

      if (isSubscription) {
        return <Badge className="bg-green-300">Recurrencia</Badge>;
      } else {
        return <Badge className="bg-blue-300">Una Vez</Badge>;
      }
    },
  },
  {
    accessorKey: 'relations',
    header: 'RELACIONES',
    cell: ({ row }) => {
      const { invoiceId, receiptId } = row?.original;
      return <InvoiceReceiptSheet invoiceId={invoiceId} receiptId={receiptId} />;
    },
  },
  {
    accessorKey: 'actions',
    header: 'ACCIONES',
    cell: ({ row }) => {
      const router = useRouter();
      const { onOpen, setPaymentId, setPaymentAmount } = usePaymentTransactionState();
      const { user } = useCurrentUser();

      const confirmMarkAsPaid = async () => {
        console.log('confirmMarkAsPaid');
        setPaymentId(row?.original?.id);
        setPaymentAmount(row.getValue('total'));
        onOpen();
      };

      const shouldDisplayMarkAsPaidOnly = canPerformPaymentActions(user.email);

      return (
        <>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <CirclePlus />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {!row.original.isPaid && shouldDisplayMarkAsPaidOnly && (
                <DropdownMenuItem
                  onClick={async () => {
                    await confirmMarkAsPaid();
                    router.refresh();
                  }}
                >
                  <BookCheck className="h-4 w-4 pr-1" />
                  Marcar como pagado
                </DropdownMenuItem>
              )}

              <DropdownMenuItem
                onClick={() => {
                  navigator.clipboard.writeText(`${PAGOS_PAYMENT_URL}` + row.original.id);
                  toast('URL de pago copiada al portapapeles');
                }}
              >
                <Copy className="h-4 w-4 pr-1" />
                Copiar al portapapeles
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </>
      );
    },
  },
];
