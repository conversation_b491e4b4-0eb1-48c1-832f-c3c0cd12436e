import HeaderImg from '@/pdfComponents/contract/assets/Header.png';
import { Document, Page, Text, View, StyleSheet, Font, Image } from '@react-pdf/renderer';
import moment from 'moment';
import { ReactNode } from 'react';
import { getContractEndDate } from './data/Lunes';

Font.register({ family: 'Helvetica', fonts: [] });
Font.register({ family: 'Helvetica-Oblique', fonts: [] });
Font.register({ family: 'Helvetica-BoldOblique', fonts: [] });
Font.register({ family: 'Helvetica-Bold', fonts: [] });

const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },
  body: {
    marginLeft: '10%',
    marginRight: '10%',
  },
  headText: {
    width: '100%',
    fontSize: '14px',
    textAlign: 'center',
    fontFamily: 'Helvetica-Bold',
    padding: '5px 0px',
  },
  definicionesText: {
    fontSize: '12px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
    padding: '4px 0px',
  },
  viewer: {
    width: '100%',
    height: '100vh',
  },

  defContainer: {
    marginTop: '20px',
  },

  declContainer: {
    marginTop: '20px',
    flexDirection: 'column',
    rowGap: 20,
  },

  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },
  tableDescription: {
    fontSize: 8,
    marginTop: '10px',
  },
  pageNumber: {
    fontSize: '11px',
    textAlign: 'right',
    paddingTop: '15px',
    marginRight: '40px',
  },

  showContractNumber: {
    fontSize: '10px',
    textAlign: 'center',
    position: 'absolute',
    width: '100%',
    bottom: '30px',
    color: 'grey',
  },
  header: {
    width: '100vw',
  },
  calendarPage: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },

  letterPoint: {
    fontSize: 8,
    marginRight: 17,
    padding: '2px 0px',
  },
  section: {
    marginBottom: 10,
  },
  label: {
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
    padding: '4px 0px',
  },
  subSection: {
    marginBottom: 5,
    paddingLeft: 10,
  },
  table: {
    display: 'flex',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableCol: {
    width: '50%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    padding: 5,
  },
  tableCell: {
    margin: 5,
    fontSize: 12,
  },
  tableCellHeading: {
    fontSize: '12px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },
  tableCellText: {
    fontSize: 10,
    padding: '2px 0px',
  },
  textBold: {
    fontSize: '10px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
    padding: '4px 0px',
  },
  SignatureContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    marginTop: '20px',
  },
  SignatureNames: {
    fontSize: 8,
    textAlign: 'center',
    fontFamily: 'Helvetica',
    paddingBottom: '2px',
  },
  SignatureContent: {
    width: '35%',
    flexDirection: 'column',
    justifyContent: 'center',
    rowGap: 5,
  },
});

const HeadingAndDescription = ({ SectionNo, heading, description, children }: any) => {
  return (
    <>
      <Text style={styles.letterPoint}>
        {SectionNo}
        {'  '}
        <Text
          style={{
            fontSize: 8,
            fontFamily: 'Helvetica-Bold',
            padding: '0 12px',
          }}
        >
          {heading}
        </Text>
        {` `}
        {description}
      </Text>

      {children}
    </>
  );
};

// disable the linter on this
// eslint-disable-next-line
HeadingAndDescription.SubSection = ({ bullet, heading, description, children }: any) => {
  return (
    <>
      <View style={styles.subSection}>
        <Text style={styles.letterPoint}>
          {bullet}
          {heading && <Text style={{ textDecoration: 'underline' }}>{heading} </Text>}
          {description}
        </Text>
        <View style={styles.subSection}>{children}</View>
      </View>
    </>
  );
};

// eslint-disable-next-line
HeadingAndDescription.SubSectionBullets = ({ bullet, description }: any) => {
  return (
    <Text style={styles.letterPoint}>
      {bullet} {description}
    </Text>
  );
};

const Sections = {
  Definitions: { no: 1, name: 'Definitions' },
  APPLICATIONANDELIGIBILITY: { no: 2, name: 'APPLICATION AND ELIGIBILITY' },
  BASICTERMSOFUSEOFPROGRAMVEHICLESANDPROGRAMSERVICES: {
    no: 3,
    name: 'BASIC TERMS OF USE OF PROGRAM VEHICLES AND PROGRAM SERVICES',
  },
  FEESPAYMENTSANDPURCHASEOPTION: {
    no: 4,
    name: 'FEES, PAYMENTS AND PURCHASE OPTION',
  },
  INSURANCEANDLIABILITY: {
    no: 5,
    name: 'INSURANCE AND LIABILITY',
  },

  DISCLAIMERSANDLIMITATIONSOFLIABILITY: {
    no: 6,
    name: 'DISCLAIMERS AND LIMITATIONS OF LIABILITY',
  },

  DISPUTERESOLUTION: {
    no: 7,
    name: 'DISPUTE RESOLUTION',
  },

  TERMSANDTERMINATION: {
    no: 8,
    name: 'TERMS AND TERMINATION',
  },
  REPOSSESSIONANDRECOVERYOFPROGRAMVEHICLES: {
    no: 9,
    name: 'REPOSSESSION AND RECOVERY OF PROGRAM VEHICLES',
  },
  GENERALPROVISIONS: {
    no: 10,
    name: 'GENERAL PROVISIONS',
  },
};

const Definitions = () => {
  const SectionNo = Sections.Definitions.no;
  const arr = [
    {
      heading: 'AAA',
      description: 'will have the meaning provided in Section 7.1.',
    },
    {
      heading: 'Agent',
      description:
        'means any third party contractor hired by OneCarNow! to provide services including but not limited to retrieving the Program Vehicles.',
    },
    {
      heading: 'Agreement',
      description: 'means this Subscription Agreement.',
    },
    {
      heading: 'Applicant',
      description:
        'means the individual who properly indicates their acceptance to this Agreement by checking the appropriate boxes and clicking the order confirmation buttons on the “Order Confirmation” page of OneCarNow!’s Website or Web-Based App. An Applicant who takes the foregoing steps does not automatically become a Subscriber until he or she is approved by OneCarNow!.',
    },
    {
      heading: 'Application for Subscription',
      description:
        'means the application generated when an Applicant clicks “Accept” on the “Accept terms and conditions” page of the Website or Web-Based App, or via email to become (or reinstate oneself as) a Subscriber of the Program.',
    },
    {
      heading: 'Authorized Driver',
      description:
        'means a person who meets the Program Vehicle Operator Eligibility Criteria and who has been approved in writing by OneCarNow! to operate Program Vehicles in connection with Subscriber’s Program Account.',
    },
    {
      heading: 'Business Day',
      description:
        'shall mean any day other than Saturday, Sunday, a federal holiday, or a day on which the New York Stock Exchange is closed.',
    },
    {
      heading: 'Cover Page',
      description: 'shall mean the OneCarNow! Subscription Agreement Cover Page attached hereto.',
    },
    {
      heading: 'Credit Score Deposit',
      description:
        "shall mean a payment equal to a one (1) month Subscription Fee and automatically payable upon approval of Applicant as an Authorized Driver if Applicant's credit score does not exceed the certain threshold criteria as determined solely in OneCarNow!'s discretion as set forth herein. The Credit Score Deposit shall be held by OneCarNow! on a non-interest bearing basis and may be applied by OneCarNow! to offset any and all charges for which Subscriber may be liable under this Agreement.",
    },
    {
      heading: 'Diminution in Value',
      description:
        'means the difference between the fair market value of a Program Vehicle immediately before and immediately after damage to such Program Vehicle.',
    },
    {
      heading: 'Dispute',
      description: 'will have the meaning provided in Section 7.1.',
    },

    {
      heading: 'Eligibility Criteria',
      description: 'means the criteria set forth in Section 2.1.',
    },

    {
      heading: 'Fair Market Value',
      description:
        'means the US Dollar purchase price of such the Program Vehicle in an arm’s-length transaction between an informed and willing buyer and an informed and willing seller in a free market, determined by an independent appraiser selected by OneCarNow!, and assuming that the Program Vehicle is unencumbered by any lease and is in the condition required hereunder.',
    },

    {
      heading: 'FAQ’s',
      description: 'will have the meaning provided in Section 10.1.',
    },

    {
      heading: 'Initial Term',
      description: 'will have the meaning provided in Section 8.1.',
    },

    {
      heading: 'Insurance',
      description: 'will have the meaning provided in Section 5.1.',
    },

    {
      heading: 'Law(s)',
      description:
        'means, collectively, all federal, state, and local statutes, laws, regulations, ordinances, rules, requirements, or other governmental restrictions or any similar forms of decision or guidance of any governmental authority, whether now or hereafter in effect and in each case as amended.',
    },

    {
      heading: 'Local Vehicle Storage Compound',
      description:
        'means the facility that is specified by OneCarNow! or its Agent where a Program Vehicle is retrieved and returned by Subscriber (for the avoidance of doubt, such return or retrieval may be conducted by such Agent).',
    },

    {
      heading: 'Loss(es)',
      description: 'will have the meaning provided in Section 6.2.',
    },

    {
      heading: 'Lost Revenue',
      description:
        'means a reasonable estimate of all Program revenue OneCarNow! loses because of damage to or Loss of a Program Vehicle',
    },

    {
      heading: 'OneCarNow!',
      description:
        'means OneCarNow!, corporation, with its principal place of business at Prol. P.º de la Reforma 1015, Lomas de Santa Fe, Zedec Sta Fé, Cuajimalpa de Morelos, 01219 Ciudad de México, CDMX, Mexico.',
    },

    {
      heading: 'OneCarNow! Privacy Policy',
      description:
        'means that certain privacy policy located at the following webpage: https://www.onecarnow.com/mx/politica-de-privacidad.',
    },

    {
      heading: 'Payment Method',
      description:
        "means Subscriber’s credit card, debit card, or Automated Clearing House ('ACH') payment account information on file with the Program.",
    },

    {
      heading: 'Platform',
      description:
        'means each applicable third-party platform utilized by the Subscriber in the use of the Program Vehicle, including, but not limited to, Uber, Lyft, DoorDash, Postmates, Grubhub, Uber Eats, and Instacart.',
    },

    {
      heading: 'Program',
      description:
        'means the OneCarNow! vehicle subscription program (including, but not limited to, all services provided in connection with such program).',
    },

    {
      heading: 'Program Account',
      description:
        'means the Subscriber’s account established with OneCarNow! upon OneCarNow!’s acceptance of the Subscriber’s Application for Subscription, which account is accessible to Subscriber through the Website and Web-Based App.',
    },

    {
      heading: 'Program Subscription Approval',
      description:
        'means OneCarNow!’s written approval of an Applicant’s Application for Subscription and approval of Applicant to be a Subscriber in the Program.',
    },

    {
      heading: 'Program Vehicle',
      description:
        'means the particular vehicle made available to the Subscriber during the Term as part of the Program. The term “Program Vehicle” includes, but is not limited to, each respective Program Vehicle’s components, parts, software, and optional accessories including, but not limited to, any cables, chords, or other returnable accessories present in a Program Vehicle when Subscriber takes possession of such Program Vehicle.',
    },

    {
      heading: 'Program Vehicle Operator Eligibility Criteria',
      description: 'will have the meaning provided in Section 2.2.',
    },

    {
      heading: 'Prohibited Uses',
      description: 'will have the meaning provided in Section 3.11.',
    },

    {
      heading: 'Purchase Price',
      description: 'will have the meaning provided in Section 4.9.',
    },

    {
      heading: 'Renewal Term',
      description: 'will have the meaning provided in Section 8.1.',
    },

    {
      heading: 'Return',
      description:
        '(or any derivation thereof) means the act of Subscriber returning to OneCarNow! or its agent the Program Vehicle currently in Subscriber’s possession or control.',
    },

    {
      heading: 'Return Request',
      description:
        'means any request made by OneCarNow! to Subscriber for the Return of, in the manner instructed by OneCarNow!, a Program Vehicle.',
    },

    {
      heading: 'Subscriber',
      description:
        'means the individual Applicant who has received Program Subscription Approval by OneCarNow! and who has accepted this Agreement by clicking “Accept” on the “Accept terms and conditions” page of the Website or Web-Based App, or email correspondence.',
    },

    {
      heading: 'Subscription Fee',
      description:
        'means the fee paid by Subscriber on a weekly basis during the Term. The exact amount of the Subscription Fee will be in the amount set forth on the Cover Page, applicable to the Subscription Plan Subscriber has selected.',
    },

    {
      heading: 'Subscription Plan',
      description:
        'will have the meaning provided on the Cover Page and refers to the Program plan selected by Subscriber from the Program plan options offered by OneCarNow!.',
    },

    {
      heading: 'Term',
      description: 'will have the meaning provided in Section 8.1.',
    },

    {
      heading: 'Violations',
      description:
        'means, collectively, any fees, fines, tickets, citations, violations and tolls (including, but not limited to, parking, speeding or criminal offenses or violations) incurred in connection with a Program Vehicle while it is under Subscriber’s possession or control and any resulting or related fines, fees, expenses, penalties or other amounts due in connection with any such fees, fines, tickets, citations, violations and tolls.',
    },

    {
      heading: 'Web-Based App',
      description: 'means the Program’s mobile-based application available on the Website.',
    },

    {
      heading: 'Website',
      description: 'means the Program’s website available at: https://www.onecarnow.com/us.',
    },
  ];
  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.Definitions.name} `}</Text>
      {arr.map((item, index) => {
        return (
          <View key={item.heading}>
            <Text style={styles.letterPoint}>
              {`${SectionNo}.${index + 1}  `}
              <Text
                style={{
                  fontSize: 8,
                  fontFamily: 'Helvetica-Bold',
                  padding: '0 12px',
                }}
              >
                {item.heading}
                {`   `}
              </Text>
              {item.description}
            </Text>
          </View>
        );
      })}
    </View>
  );
};

const ApplicationAndEligibility = () => {
  const SectionNo = Sections.APPLICATIONANDELIGIBILITY.no;
  const arr = {
    'Application Process': {
      heading: 'Application Process',
      description: `In order to become a Subscriber of the Program and to have access to a Program Vehicle, Subscriber must, as determined in OneCarNow!’s sole discretion:`,
      subBulletPoints: {
        a: `apply to become a Subscriber of the Program by accurately, truthfully, and fully completing the Application for Subscription through the Website or Web-Based App, or email invitation.`,
        b: `deliver all information and documents that OneCarNow! requests in the Application for Subscription process or otherwise;`,
        c: `meet the Eligibility Criteria; and`,
        d: ` have Subscriber’s Application for Subscription accepted by OneCarNow!.`,
      },
    },
    'Subscription Eligibility Criteria': {
      heading: 'Subscription Eligibility Criteria',
      description: `To be eligible as a Subscriber of the Program, Applicant must meet, as determined in OneCarNow!’s sole discretion, the Program’s eligibility criteria as listed below (the “Eligibility Criteria”) at the time Applicant submits the Application for Subscription and at all times thereafter during the Term:`,
      subBulletPoints: {
        a: {
          description: `Unless Section 2.2(b) below applies to Subscriber, Subscriber must meet the “Program Vehicle Operator Eligibility Criteria” which are, collectively, as follows:`,
          i: {
            no: 'i. ',
            description: `have a residential address within the state of Florida;`,
          },
          ii: {
            no: 'ii. ',
            description: `be at least twenty-one (21) years of age (unless otherwise provided by law);`,
          },
          iii: {
            no: 'iii. ',
            description: `have the capability to pick up, Return, and have Program Vehicles serviced at the location specified by OneCarNow!;`,
          },
          iv: {
            no: 'iv. ',
            description: `have a valid U.S. driver’s license and satisfy OneCarNow!’s processes and procedures for identification and verification;`,
          },
          v: {
            no: 'v. ',
            description: `have a valid credit card or debit card and an active bank account with a bank in the United States;`,
          },
          vi: {
            no: 'vi. ',
            description: `have continuous access to a mobile phone or computer that allows access to email and to the Web-Based App and/or the Website;`,
          },
          vii: {
            no: 'vii. ',
            description: `have a LexisNexis RiskView score or TransUnion credit score acceptable to OneCarNow! as determined by OneCarNow! in its sole discretion;`,
            '1': `Applicant must have a credit score above [680] as reported by TransUnion; or`,
            '2': `Applicants with a credit score between [640-679] as reported by TransUnion will automatically be charged the Credit Score Deposit as defined herein. Applicant hereby acknowledges and consents to this automatic payment for the Credit Score Deposit once their application is approved; and`,
            '3': `Applicant acknowledges any dispute or disagreement concerning Applicant's credit score as reported by TransUnion shall be made directly to TransUnion and that OneCarNow! has no responsibility or ability to change Applicant's reported credit score;`,
          },
          viii: {
            no: 'viii. ',
            description: `have a satisfactory driving record (as determined by OneCarNow! in their sole discretion) and does not have any one (1) of the following major violations: driving while intoxicated or under the influence of alcohol or drugs; failure to stop or report an accident or a hit and run; homicide, manslaughter, or assault arising out of the operation of a motor vehicle; driving while license is suspended or revoked; reckless driving; driving in possession of an open container of an alcoholic beverage; speed contest or racing; or attempting to elude an officer of the law;`,
          },
          ix: {
            no: 'ix. ',
            description: `have no outstanding liens on driver’s license and have not been convicted of a driving under the influence (DUI) or driving while impaired (DWI) or similar offense, refusing a breathalyzer, driving with a suspended license, leaving the scene of an accident, or causing a fatality in an accident;`,
          },
          x: {
            no: 'x. ',
            description: `meet all other rules, regulations and requirements of each applicable Platform;`,
          },
          xi: {
            no: 'xi. ',
            description: `have met the following Platform criteria:`,
            '1': `have provided at least eight hundred (800) rides through any Platform;`,
            '2': `have performed under any Platform for at least one (1) year; or`,
            '3': `have an average score of at least 4.8 stars on each applicable Platform;`,
          },
          xii: {
            no: 'xii. ',
            description: `provide proof of parking availability for the Program Vehicle via virtual or in-person home visit;`,
          },
          xiii: {
            no: 'xiii. ',
            description: `provide proof of Taxpayer Identification Number or Social Security number;`,
          },
          xiv: {
            no: 'xiv. ',
            description: `have completed a background check;`,
          },
          xv: {
            no: 'xv. ',
            description: `have provided OneCarNow! with Subscriber’s bank statements for the prior three months; and`,
          },
          xvi: {
            no: 'xvi. ',
            description: `have been approved in writing by OneCarNow! to operate Program Vehicles, which approval can be accepted or rejected by OneCarNow! based on any reasonable and lawful purpose.`,
          },
        },
        b: {
          description: `If Subscriber is unable to drive due to a disability, Subscriber must:`,
          i: {
            no: 'i. ',
            description: `have a residential address within the state of Florida;`,
          },
          ii: {
            no: 'ii. ',
            description: `be at least twenty-one (21) years of age (unless otherwise provided by law);`,
          },
          iii: {
            no: 'iii. ',
            description: `have the capability to pick up, Return, and have Program Vehicles serviced at the location specified by OneCarNow!;`,
          },
          iv: {
            no: 'iv. ',
            description: `have a valid U.S. driver’s license and satisfy OneCarNow!’s processes and procedures for identification and verification;`,
          },
          v: {
            no: 'v. ',
            description: `have a LexisNexis RiskView score or TransUnion credit score acceptable to OneCarNow! as determined by OneCarNow! in its sole discretion;`,
          },
          vi: {
            no: 'vi. ',
            description: `have a valid credit card or debit card and an active bank account with a bank in the United States;`,
          },
          vii: {
            no: 'vii. ',
            description: `have continuous access to a mobile phone that allows access to email and to the Web-Based App and/or the Website;`,
          },
          viii: {
            no: 'viii. ',
            description: `have an Authorized Driver listed on Subscriber’s Program Account at all times;`,
          },
          ix: {
            no: 'ix. ',
            description: `meet all other rules, regulations and requirements of each applicable Platform;`,
          },
          x: {
            no: 'x. ',
            description: `provide proof of parking availability for the Program Vehicle;`,
          },
          xi: {
            no: 'xi. ',
            description: `provide proof of Taxpayer Identification Number or Social Security number;`,
          },
          xii: {
            no: 'xii. ',
            description: `have completed a background check;`,
          },
          xiii: {
            no: 'xiii. ',
            description: `have provided OneCarNow! with Subscriber’s bank statements for the prior year; and`,
          },
          xiv: {
            no: 'xiv. ',
            description: `meet all other rules, regulations and requirements of each applicable Platform; and`,
          },
          xv: {
            no: 'xv. ',
            description: `have been approved in writing by OneCarNow!, which approval can be accepted or rejected by OneCarNow! based on any reasonable and lawful purpose.`,
          },
        },
      },
    },
    'Eligibility for Program Vehicle Operation': {
      heading: 'Eligibility for Program Vehicle Operation',
      description: `For the avoidance of doubt: (a) only Subscriber and the Authorized Driver currently listed on Subscriber’s Account, if he or she currently meets the Program Vehicle Operator Eligibility Criteria, are allowed to operate Program Vehicles during the Term, and (b) if Subscriber applied to the Program pursuant to Section 2.2(b), Subscriber is not permitted to operate Program Vehicles.`,
    },
    'Continuing Obligation to Meet Eligibility Criteria and Update Contact and Application Information.': {
      heading:
        'Continuing Obligation to Meet Eligibility Criteria and Update Contact and Application Information.',
      description:
        'Subscriber represents and warrants that Subscriber meets and will continue to meet the Eligibility Criteria during the Term (as defined in Section 8.1) of this Agreement and that Subscriber will report, as soon as practicable, but no more than twenty-four (24) hours after such change, any change in Subscriber’s satisfaction of the Eligibility Criteria and any change in information Subscriber provided during the application process to OneCarNow!. Subscriber will, as soon as safely practicable, cease operating Program Vehicles in the event any Program Vehicle Operator Eligibility Criteria are no longer met.',
    },

    'Authorized Driver.': {
      heading: 'Authorized Driver.',
      a: {
        heading: 'Generally',
        description: `Subscriber may have one (1) Authorized Driver listed on Subscriber’s Program Account at a time. Subscriber can request that an Authorized Driver be de-listed from Subscriber’s Program Account and invite a different person to apply to be listed as an Authorized Driver on Subscriber’s Program Account at any time. In order to operate a Program Vehicle, an Authorized Driver must: (1) currently meet the Program Vehicle Operator Eligibility Criteria, with the exception of the requirement to have a valid credit card, debit card or US bank account, (2) have acknowledged this Agreement in writing (including via email or through the Web-Based App or the Website), and (3) be currently listed by OneCarNow! on Subscriber’s Program Account as an Authorized Driver.`,
      },
      b: {
        heading: 'Authorized Driver Application Process',
        description: `Unless otherwise contrary to applicable state law, a person will apply to be an Authorized Driver only upon an invitation extended via email, Web-Based App, or the Website. Each such prospective Authorized Driver will apply to become an Authorized Driver by accurately, truthfully, and fully completing the application process for Authorized Drivers available via the Web-Based App, the Website or email and delivering all information and documents that OneCarNow! requests in the application process or otherwise. By applying to be an Authorized Driver, such applicant agrees to be bound by and comply with the terms of this Agreement applicable to Authorized Drivers. After submitting such application, Authorized Driver will only become an Authorized Driver upon OneCarNow!’s approval, which approval may be given in OneCarNow!’s sole discretion. Under no circumstances will an Authorized Driver be considered a subscriber solely by virtue of being approved as an Authorized Driver. An Authorized Driver is only permitted to operate Program Vehicles provided to Subscriber in connection with, and pursuant to the terms and conditions of, this Agreement.`,
      },
      c: {
        heading: 'Continuing Obligation to Meet the Authorized Driver Eligibility Criteria.',
        description:
          'Authorized Driver will report promptly to Subscriber (but no later than twenty-four (24) hours after such change) any change to his or her Program Vehicle Operator Eligibility Criteria so that Subscriber can notify OneCarNow! within twenty-four (24) hours of Subscriber receiving such notification. Authorized Driver will, as soon as safely practicable, cease operating Program Vehicles upon any change to his or her Program Vehicle Operator Eligibility Criteria.',
      },
      d: {
        heading: 'Authorized Driver Can be Removed At Any Time.',
        description: `An Authorized Driver’s status as an Authorized Driver (including any right to operate the Program Vehicles) will immediately terminate upon the following:`,
        '1': {
          description: `at Subscriber’s request;`,
        },
        '2': {
          description: `if, as determined in OneCarNow!’s sole discretion, Authorized Driver fails to abide by the terms of this Agreement applicable to Authorized Driver; or`,
        },
        '3': {
          description: ` if this Agreement is terminated pursuant to Section *******.	No Rights to Become a Subscriber in the Program or Use Program Vehicles Without Approval. Applicant is not eligible and will have no right to become a Subscriber of the Program unless and until Applicant receives written notice that Applicant’s application for a Subscription Plan has been approved. Program Subscription Approval is not guaranteed. If Applicant receives Program Subscription Approval, the Applicant will become a Subscriber, and OneCarNow! will provide Subscriber with a code that Subscriber can enter into the Web-Based App, the Website or via email in order to select and schedule delivery of Subscriber’s Program Vehicle.`,
        },
      },
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline', paddingTop: '4px' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.APPLICATIONANDELIGIBILITY.name} `}</Text>
      <View>
        <Text style={styles.letterPoint}>
          {`${SectionNo}.${1}  `}
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Helvetica-Bold',
              padding: '0 12px',
            }}
          >
            {arr['Application Process'].heading}
            {`   `}
          </Text>
          {arr['Application Process'].description}
        </Text>
        <View style={styles.subSection}>
          <Text style={styles.letterPoint}>
            {`a.  `}
            <Text>
              {arr['Application Process'].subBulletPoints.a}
              {`   `}
            </Text>
          </Text>

          <Text style={styles.letterPoint}>
            {`b.  `}
            <Text>
              {arr['Application Process'].subBulletPoints.b}
              {`   `}
            </Text>
          </Text>
          <Text style={styles.letterPoint}>
            {`c.  `}
            <Text>
              {arr['Application Process'].subBulletPoints.c}
              {`   `}
            </Text>
          </Text>
          <Text style={styles.letterPoint}>
            {`d.  `}
            <Text>
              {arr['Application Process'].subBulletPoints.d}
              {`   `}
            </Text>
          </Text>
        </View>
      </View>

      <View>
        <Text style={styles.letterPoint}>
          {`${SectionNo}.${2}  `}
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Helvetica-Bold',
              padding: '0 12px',
            }}
          >
            {arr['Subscription Eligibility Criteria'].heading}
            {`   `}
          </Text>
          {arr['Subscription Eligibility Criteria'].description}
        </Text>
        <View style={styles.subSection}>
          <View>
            <Text style={styles.letterPoint}>
              {`a.  `}
              {arr['Subscription Eligibility Criteria'].subBulletPoints.a.description}
            </Text>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.i.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.i.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.ii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.ii.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.iii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.iii.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.iv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.iv.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.v.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.v.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vi.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vi.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vii.description}
              </Text>
              <View style={styles.subSection}>
                <Text style={styles.letterPoint}>
                  {'1. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vii[1]}
                </Text>
                <Text style={styles.letterPoint}>
                  {'2. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vii[2]}
                </Text>
                <Text style={styles.letterPoint}>
                  {'3. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.vii[3]}
                </Text>
              </View>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.viii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.viii.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.ix.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.ix.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.x.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.x.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xi.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xi.description}
              </Text>
              <View style={styles.subSection}>
                <Text style={styles.letterPoint}>
                  {'1. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xi[1]}
                </Text>
                <Text style={styles.letterPoint}>
                  {'2. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xi[2]}
                </Text>
                <Text style={styles.letterPoint}>
                  {'3. '} {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xi[3]}
                </Text>
              </View>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.x.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xiii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xiii.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xiv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xiv.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xv.description}
              </Text>
            </View>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xvi.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.a.xvi.description}
              </Text>
            </View>
          </View>
          <View>
            <Text style={styles.letterPoint}>
              {`b.  `}
              {arr['Subscription Eligibility Criteria'].subBulletPoints.b.description}
            </Text>
            <View style={styles.subSection}>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.i.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.i.description}
              </Text>

              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.ii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.ii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.iii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.iii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.iv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.iv.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.v.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.v.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.vi.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.vi.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.vii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.vii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.viii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.viii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.ix.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.ix.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.x.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.x.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xi.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xi.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xiii.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xiii.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xiv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xiv.description}
              </Text>
              <Text style={styles.letterPoint}>
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xv.no}{' '}
                {arr['Subscription Eligibility Criteria'].subBulletPoints.b.xv.description}
              </Text>
            </View>
          </View>
        </View>
      </View>

      <View>
        <Text style={styles.letterPoint}>
          {`${SectionNo}.${3}  `}
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Helvetica-Bold',
              padding: '0 12px',
            }}
          >
            {arr['Eligibility for Program Vehicle Operation'].heading}
            {` `}
          </Text>
          {arr['Eligibility for Program Vehicle Operation'].description}
        </Text>
      </View>

      <View>
        <Text style={styles.letterPoint}>
          {`${SectionNo}.${4}  `}
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Helvetica-Bold',
              padding: '0 12px',
            }}
          >
            {
              arr[
                'Continuing Obligation to Meet Eligibility Criteria and Update Contact and Application Information.'
              ].heading
            }
            {` `}
          </Text>
          {
            arr[
              'Continuing Obligation to Meet Eligibility Criteria and Update Contact and Application Information.'
            ].description
          }
        </Text>
      </View>

      <View>
        <Text style={styles.letterPoint}>
          {`${SectionNo}.${5}  `}
          <Text
            style={{
              fontSize: 8,
              fontFamily: 'Helvetica-Bold',
              padding: '0 12px',
            }}
          >
            {arr['Authorized Driver.'].heading}
          </Text>
        </Text>
        <View style={styles.subSection}>
          <Text style={styles.letterPoint}>
            {`a.  `}
            <Text style={{ textDecoration: 'underline' }}>{arr['Authorized Driver.'].a.heading}</Text>{' '}
            {arr['Authorized Driver.'].a.description}
          </Text>
        </View>

        <View style={styles.subSection}>
          <Text style={styles.letterPoint}>
            {`b.  `}
            <Text style={{ textDecoration: 'underline' }}>{arr['Authorized Driver.'].b.heading}</Text>{' '}
            {arr['Authorized Driver.'].b.description}
          </Text>
        </View>

        <View style={styles.subSection}>
          <Text style={styles.letterPoint}>
            {`c.  `}
            <Text style={{ textDecoration: 'underline' }}>{arr['Authorized Driver.'].c.heading}</Text>{' '}
            {arr['Authorized Driver.'].c.description}
          </Text>
        </View>

        <View style={styles.subSection}>
          <Text style={styles.letterPoint}>
            {`d.  `}
            <Text style={{ textDecoration: 'underline' }}>{arr['Authorized Driver.'].d.heading}</Text>{' '}
            {arr['Authorized Driver.'].d.description}
          </Text>
          <View style={styles.subSection}>
            <Text style={styles.letterPoint}>
              {`1.  `}
              {arr['Authorized Driver.'].d['1'].description}
            </Text>
            <Text style={styles.letterPoint}>
              {`2.  `}
              {arr['Authorized Driver.'].d['2'].description}
            </Text>
            <Text style={styles.letterPoint}>
              {`3.  `}
              {arr['Authorized Driver.'].d['3'].description}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const BasicTermsOfUseProgram = () => {
  const SectionNo = Sections.BASICTERMSOFUSEOFPROGRAMVEHICLESANDPROGRAMSERVICES.no;

  const data = {
    'No Right to Program Vehicles Other Than as Part of a Subscription.': {
      heading: 'No Right to Program Vehicles Other Than as Part of a Subscription.',
      description: `Upon becoming a Subscriber, Subscriber will be allowed to access and use only the specific Program Vehicle which has been designated for Subscriber’s use during the Term of this Agreement and pursuant to the terms and conditions of this Agreement. Subscriber and Authorized Drivers will have access to and will operate the Program Vehicle only pursuant to the terms of this Agreement, and except as expressly set forth in Section 4.9 or otherwise permitted by OneCarNow! in writing, neither Subscriber nor any Authorized Driver will have or acquire any other right, title, or interest in or to the Program Vehicle. After the expiration or termination of the Term for any reason and after Subscriber has Returned the Program Vehicle per OneCarNow!’s instructions, OneCarNow! does not guarantee the future availability of the Program Vehicle, or any other vehicle, should Subscriber choose to reinstate his or her expired or terminated Subscription in the Program by re-submitting an Application for Subscription.`,
    },
    'Choice of Vehicles.': {
      heading: 'Choice of Vehicles.',
      description: `The particular vehicles from which Subscriber may select a Program Vehicle shall be set forth in email or on the Web-Based App or the Website and determined in the sole discretion of OneCarNow! and may be affected by Subscriber’s selected Subscription Plan. The vehicles available for use under the Program as a Program Vehicle pursuant to each Subscription Plan may be changed by OneCarNow! in its sole discretion and without notice to Subscriber at any time. OneCarNow! will use its best efforts to obtain the Program Vehicle model requested by Subscriber as promptly as possible in relation to Subscriber’s request, but does not guarantee that a particular (or any) Program Vehicle will be available at any particular time.`,
    },
    'Delivery and Return of Program Vehicle.': {
      heading: 'Delivery and Return of Program Vehicle.',
      description: ` Unless otherwise specified by OneCarNow!, all Program Vehicle pick up and Returns must occur at the location designated by OneCarNow!. It is Subscriber’s responsibility to ensure that Subscriber delivers the Program Vehicle to the Local Vehicle Storage Compound or such other destination as instructed by OneCarNow! at the proper or scheduled time in order to facilitate the pick up or Return of the Program Vehicle (as applicable). In the event a Program Vehicle is not returned to the Local Vehicle Storage Compound at the conclusion of the Term or upon termination of the Subscription, Subscriber may be charged costs in connection with transporting the Program Vehicle involved back to the Local Vehicle Storage Compound, unless otherwise waived by OneCarNow!. OneCarNow! may require Subscriber to Return a Program Vehicle for any reason or no reason at any time. Subscriber will comply with any Return Request and will make arrangements to have each such Program Vehicle Returned to the Local Vehicle Storage Compound or to a location designated by OneCarNow! or its Agent pursuant to the instructions provided by OneCarNow! in connection with such Return Request, as soon as safely practicable, but in no event more than twelve (12) hours after a Return Request has been made, unless otherwise provided in this Agreement or in another writing by OneCarNow!.`,
      a: {
        heading: 'Unsuccessful Return.',
        description: `Upon termination or completion of the Subscription Plan, when the Subscriber Returns the Program Vehicle to a OneCarNow! Local Vehicle Storage Compound on a given date, and the arranged Return can not be completed due to any act or omission of the Subscriber, Subscriber will be charged for all fees and expenses associated with the unsuccessful Return, including, but not limited to, initial attempted Return costs.`,
      },
      b: {
        heading: 'Delayed Pick Up or Return.',
        description: `In the event that Subscriber arranges for the pick up or Return of the Program Vehicle with OneCarNow! or its agents, and such arranged pick up or Return is delayed thirty (30) minutes or more due to any act or omission of Subscriber, Subscriber will be charged a $[	] delayed pick up or Return fee.`,
      },
    },
    'Visual Inspection.': {
      heading: 'Visual Inspection.',
      description: `Subscriber will perform a “walk around” visual inspection of the exterior of the Program Vehicle with a OneCarNow! representative at OneCarNow! or at an applicable Local Vehicle Storage Compound prior to taking possession of such Program Vehicle in order to confirm that there is no visible damage to the Program Vehicle at the time Subscriber takes possession of such Program Vehicle. Any such damage must be reported by the Subscriber via email or through the Web-Based App or the Website.`,
    },
    'Returning the Program Vehicle.': {
      heading: 'Returning the Program Vehicle.',
      description: `Upon expiration of the Term, Subscriber will Return the Program Vehicle by Returning the Program Vehicle to the destination designated by OneCarNow!, provided that OneCarNow! and Subscriber have not agreed to the sale of the Program Vehicle pursuant to Section 4.9. After the expiration of the Term, Subscriber may only select a different Program Vehicle by re-applying again via email or through the Web-Based App or the Website. In any event, Subscriber must Return the Program Vehicle on or before the last day of the Term. Subscriber is responsible for ensuring that each Program Vehicle is Returned in a clean condition and in the same condition (ordinary wear and tear excepted) as when Subscriber originally took possession of such Program Vehicle. To the extent reasonably practicable, Subscriber shall cause all sensitive information and personal information (whether of Subscriber, Authorized Drivers, or others as applicable) to be deleted or removed from the Program Vehicle prior to Return, including, without limitation, by following OneCarNow!'s guidance on such deletion or removal.`,
    },

    'Fuel or Electric Charge.': {
      heading: 'Fuel or Electric Charge.',
      description: `Each Program Vehicle will be delivered or made available to Subscriber with its fuel or electric charge filled to its capacity, when (and to the extent) possible. Subscriber is responsible for Returning the Program Vehicle with a full tank of fuel or electric charge. If Subscriber Returns the Program Vehicle without a full tank of fuel or electric charge, Subscriber will pay the cost of replacing such fuel as described on the Cover Page.`,
    },

    'Vehicle Maintenance.': {
      heading: 'Vehicle Maintenance.',
      description: `OneCarNow! will maintain the Program Vehicles pursuant to the applicable manufacturer recommendations and maintenance schedules to the best of its abilities. OneCarNow! has the right to issue a Return Request to Subscriber and remove the Program Vehicle from service and availability at any time and for any or no reason, including, but not limited to, for maintenance or recall reasons. If OneCarNow! issues a Return Request with respect to the Program Vehicle for maintenance or recall issues, OneCarNow! shall make available to Subscriber a similar vehicle of like kind and quality, which similar vehicle shall constitute a replacement Program Vehicle during the period in which maintenance and repairs on the Program Vehicle are being made, but no longer than the Term. Only OneCarNow! or its designee is permitted to modify or repair the Program Vehicle. Subscriber and Authorized Driver will not operate the Program Vehicle if he or she believes it may be unsafe to operate. As soon as safely practicable, Subscriber and Authorized Driver will report to OneCarNow! any Program Vehicle condition he or she believes to be unsafe or that may require maintenance or repair (including, but not limited to, warning lights that stay on after ignition or that indicate that service or maintenance is required; any chimes, indicators or alerts; any mechanical sounds, performance sounds or performance changes; any evidence of leaking fluids from the Program Vehicle; any tire damage or excess wear on the tire; any cracked, broken or missing mirrors; any cracks or chips in the windshield; any other damage to the exterior of the Program Vehicle; any inoperable signals; any unusual noises when the Program Vehicle is operated; and any other condition that may render the Program Vehicle unsafe or illegal to operate). Subscriber is responsible for delivering the Program Vehicle to OneCarNow!, when or if the Program Vehicle requires maintenance or repair. Failure to report such problems or deliver the Program Vehicle may result in the immediate termination of this Agreement by OneCarNow!.`,
    },

    'Roadside Assistance.': {
      heading: 'Roadside Assistance',
      description: `If Subscriber or Authorized Driver is in need of roadside assistance, including, but not limited to, in connection with a Program Vehicle breakdown, Subscriber or Authorized Driver will first take any reasonable action to ensure safety at the scene and, then, will contact OneCarNow! by calling [OneCarNow! Phone Number].`,
    },

    'Electronic Surveillance.': {
      heading: 'Electronic Surveillance.',
      description: `The Program Vehicles are equipped with electronic surveillance and telematics technologies, including but not limited to a global positioning satellite (“GPS”), satellite navigation system, sensing and diagnostic modules (“SDM”), data communication modules (“DCM”), and event data recorders (“EDR”) and/or other technologies that can collect and transmit data (such as location data) about the Program Vehicles (collectively, the “Telematics Systems”), which Telematics Systems are used for the purposes of (i) recording the Program Vehicle’s location upon non-payment or delayed or non-return of the Program Vehicle; (ii) transmitting information concerning a crash in which the Program Vehicle has been involved to a central communications system when a crash occurs, as well as providing other collision detections and notifications; (iii) providing service warnings; (iv) tracking air bag deployment and other incident data; (v) providing diagnostic data such as fuel and oil levels; (vi) providing navigational services; and (vii) tracking stolen or unreturned Program Vehicles. If the Program Vehicle is not returned at the Return date and time, OneCarNow! may notify Subscriber of OneCarNow!’s intent to use electronic self-help to retrieve the Program Vehicle, and Subscriber agrees that electronic self-help may be employed by OneCarNow! as described in this section. OneCarNow! reserves the right to resort to electronic self-help on or after the Return date, in OneCarNow!’s sole discretion. Subscriber may contact OneCarNow! at our number listed on this Agreement with any concerns Subscriber has regarding this Agreement. The Telematics System may (in whole or in part) have been installed or affixed after the Program Vehicle’s manufacture. The Telematics System utilizes wireless technology to transmit data and, therefore, privacy cannot be guaranteed and is specifically disclaimed by Subscriber and/or Authorized Driver. Subscriber expressly consents to the installation of the Telematics System in the Program Vehicle and Subscriber acknowledges and agrees that: (a) Subscriber has no expectation of privacy related to Subscriber’s use of the Vehicle; (b) OneCarNow! is not responsible for the operability of the Telematics System; (c) to the extent permissible by law, OneCarNow!, and any affiliates, and third parties acting on OneCarNow!’s behalf: (i) may have access to the information supplied by the Telematics System, including, but not limited to: location, automatic crash notification, operational, mileage, diagnostic vehicle health data, tolling (if permitted by law), performance, and driver behavior (collectively, the “Telematics Data”); and (ii) may monitor the Program Vehicle or disclose such information to the extent permitted by law; and (d) it is Subscriber’s obligation to inform any and all Authorized Drivers and passengers of the terms of this paragraph. Use of the Telematics System (which may occur automatically by using the Program Vehicle) is subject to the OneCarNow! Privacy Policy. By entering into this Agreement, Subscriber consents to such OneCarNow! Privacy Policy and to the collection, use, monitoring and disclosure of the Telematics Data (including, but not limited to the disclosure of aggregated data for marketing purposes) to the extent permitted by law. Subscriber acknowledges that disabling any of the Telematics Systems will result in a breach of this Agreement and is cause for termination of this Agreement and OneCarNow!’s retrieval of the Program Vehicle.`,
    },
    'Use of Program Vehicles.': {
      heading: 'Use of Program Vehicles.',
      description: `Subscriber and Authorized Driver shall always use and operate Program Vehicles in accordance with all applicable Laws.`,
    },
    'Prohibited Uses.': {
      heading: 'Prohibited Uses.',
      description: `Subscriber and Authorized Driver shall not leave the Program Vehicle parked while unlocked or with the keys inside. The operation or use of the Program Vehicle under the following conditions is prohibited (the “Prohibited Uses”):`,
      a: {
        description: `By anyone who has provided false information or who has made or makes false or misleading representations in connection with the use of the Program Vehicle or Program services (including, but not limited to, regarding his or her name, age, address, driving record or other eligibility matters);`,
      },
      b: {
        description: `By anyone who is not both: (i) Subscriber or the current Authorized Driver listed on Subscriber’s Account, and (ii) a person who meets the Program Vehicle Operator Eligibility Criteria;`,
      },
      c: {
        description: `In violation of applicable Law including, but not limited to:`,
        i: {
          no: 'i. ',
          description: `by any person who is under the influence of (A) alcohol or (B) any drug or medication under the effects of which the operation of a vehicle is prohibited or not recommended;`,
        },
        ii: {
          no: 'ii. ',
          description: `in any drag race, speed race, rally or other competition;`,
        },
        iii: {
          no: 'iii. ',
          description: ` for any driver’s training activity;`,
        },
        iv: {
          no: 'iv. ',
          description: `in the commission of any crime or for any other illegal or improper activity or purpose;`,
        },
        v: {
          no: 'v. ',
          description: `for transporting any number of passengers in excess of the number of functioning seatbelts in the Program Vehicle;`,
        },
        vi: {
          no: 'vi. ',
          description: `for transporting baggage or other items that would cause the Program Vehicle to exceed its manufacturer-recommended or legal weight limits;`,
        },
        vii: {
          no: 'vii. ',
          description: `by any person who does not have a valid driver’s license (or whose driver’s license has restrictions that are not complied with by such person when driving any Program Vehicle);`,
        },
        viii: {
          no: 'viii. ',
          description: `by any person who is driving while distracted, including, but not limited to, driving while texting, emailing, using a cell phone without a hands-free device or otherwise engaging in similar activities, whether or not prohibited by applicable Law;`,
        },
        ix: {
          no: 'ix. ',
          description: `for transporting any hazardous, toxic, flammable, dangerous or illegal materials;`,
        },
        x: {
          no: 'x. ',
          description: `for transporting any medical or hazardous substance or waste, or persons for hire;`,
        },
        xi: {
          no: 'xi. ',
          description: `as a "school bus" as defined in the Code of Federal Regulations, or any other applicable state or municipal statute or regulation;`,
        },
      },
      d: {
        description: `for towing or pushing anything beyond the manufacturer-recommend towing or pushing capacity for such Program Vehicle or without complying with the manufacturer recommendations for towing and pushing by such Program Vehicle;`,
      },
      e: {
        description: `for transporting the Program Vehicle to or for operating the Program Vehicle outside of the continental U.S.;`,
      },
      f: {
        description: `on unpaved, unimproved or impassable roads or on roads that are not regularly maintained by the transportation department or a municipality;`,
      },
      g: {
        description: ` to smoke or vape any substance in or around the Program Vehicle;`,
      },
      h: {
        description: `to the extent prohibited by any Platform, to transport animals or other pets without such animals or pets being contained in carriers (other than service animals (but not “emotional support animals”), which are permitted to be transported in the Program Vehicles outside of pet carriers); and`,
      },
      i: {
        description: `in connection with any imprudent, negligent, abusive, intentionally destructive, reckless, wanton or abnormal manner or behavior.`,
      },
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.BASICTERMSOFUSEOFPROGRAMVEHICLESANDPROGRAMSERVICES.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['No Right to Program Vehicles Other Than as Part of a Subscription.'].heading}
        description={data['No Right to Program Vehicles Other Than as Part of a Subscription.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Choice of Vehicles.'].heading}
        description={data['Choice of Vehicles.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['Delivery and Return of Program Vehicle.'].heading}
        description={data['Delivery and Return of Program Vehicle.'].description}
      >
        <HeadingAndDescription.SubSection
          bullet={`a.  `}
          heading={data['Delivery and Return of Program Vehicle.'].a.heading}
          description={data['Delivery and Return of Program Vehicle.'].a.description}
        />
        <HeadingAndDescription.SubSection
          bullet={`b.  `}
          heading={data['Delivery and Return of Program Vehicle.'].b.heading}
          description={data['Delivery and Return of Program Vehicle.'].b.description}
        />
      </HeadingAndDescription>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data['Visual Inspection.'].heading}
        description={data['Visual Inspection.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['Returning the Program Vehicle.'].heading}
        description={data['Returning the Program Vehicle.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${6}`}
        heading={data['Fuel or Electric Charge.'].heading}
        description={data['Fuel or Electric Charge.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${7}`}
        heading={data['Vehicle Maintenance.'].heading}
        description={data['Vehicle Maintenance.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${8}`}
        heading={data['Roadside Assistance.'].heading}
        description={data['Roadside Assistance.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${9}`}
        heading={data['Electronic Surveillance.'].heading}
        description={data['Electronic Surveillance.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${10}`}
        heading={data['Use of Program Vehicles.'].heading}
        description={data['Use of Program Vehicles.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${11}`}
        heading={data['Prohibited Uses.'].heading}
        description={data['Prohibited Uses.'].description}
      >
        <HeadingAndDescription.SubSection
          bullet={`a.  `}
          description={data['Prohibited Uses.'].a.description}
        />
        <HeadingAndDescription.SubSection
          bullet={`b.  `}
          description={data['Prohibited Uses.'].b.description}
        />
        <HeadingAndDescription.SubSection
          bullet={`c.  `}
          description={data['Prohibited Uses.'].c.description}
        >
          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.i.no}
            description={data['Prohibited Uses.'].c.i.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.ii.no}
            description={data['Prohibited Uses.'].c.ii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.iii.no}
            description={data['Prohibited Uses.'].c.iii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.iv.no}
            description={data['Prohibited Uses.'].c.iv.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.v.no}
            description={data['Prohibited Uses.'].c.v.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.vi.no}
            description={data['Prohibited Uses.'].c.vi.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.vii.no}
            description={data['Prohibited Uses.'].c.vii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.viii.no}
            description={data['Prohibited Uses.'].c.viii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.ix.no}
            description={data['Prohibited Uses.'].c.ix.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.x.no}
            description={data['Prohibited Uses.'].c.x.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={data['Prohibited Uses.'].c.xi.no}
            description={data['Prohibited Uses.'].c.xi.description}
          />
        </HeadingAndDescription.SubSection>

        <HeadingAndDescription.SubSection
          bullet={`d.  `}
          description={data['Prohibited Uses.'].d.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`e.  `}
          description={data['Prohibited Uses.'].e.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`f.  `}
          description={data['Prohibited Uses.'].f.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`g.  `}
          description={data['Prohibited Uses.'].g.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`h.  `}
          description={data['Prohibited Uses.'].h.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`i.  `}
          description={data['Prohibited Uses.'].i.description}
        />
      </HeadingAndDescription>
    </View>
  );
};

const FeesPaymentsAndPurchaseOption = () => {
  const SectionNo = Sections.FEESPAYMENTSANDPURCHASEOPTION.no;

  const data = {
    'Subscription Fee.': {
      heading: 'Subscription Fee.',
      description: `Subscriber will be responsible for paying the first applicable Subscription Fee upon Program Subscription Approval for a Program Vehicle, as applicable and within OneCarNow!'s sole discretion. Thereafter, Subscriber will pay the applicable Subscription Fee every week until the Agreement is terminated or otherwise expires. Subscription Fees will be automatically charged to the Payment Method on the date due. Subscription Fees (regardless of the Subscription Plan) include the Insurance, Program Vehicle maintenance, and delivery of each Program Vehicle with a full detail wash and a half full tank of fuel or a half full electric charge, as applicable. The amount of a Subscription Fee due to OneCarNow! by Subscriber on certain due dates may differ from the amount described on the Cover Page. AT ANY TIME, SUBSCRIBER CAN CONTACT OneCarNow! THROUGH THE WEB-BASED APP, THE WEBSITE OR BY CALLING [OneCarNow! Phone Number] <NAME_EMAIL>. IN ORDER TO OBTAIN INFORMATION ABOUT SUBSCRIBER’S ACCOUNT, THE PROGRAM VEHICLE OR OTHER PROGRAM DETAILS.`,
    },

    'Other Charges.': {
      heading: 'Other Charges.',
      description: `OneCarNow! imposes a mileage limitation as outlined on the Cover Page on each Program Vehicle. If Subscriber drives a Program Vehicle in excess of the monthly allotted mileage, a fee will be imposed on Subscriber of {additional_mileage_fee} per excess mile.`,
      a: {
        description: `Subscriber may be subject to an initial deposit to be paid upon execution of this Agreement.`,
      },
      b: {
        description: ` Subscriber may be subject to a Return fee, if the Program Vehicle is Returned by an Agent.`,
      },
      c: {
        description: `Subscriber may be subject to late charges if the Program Vehicle is Returned after the specified Return date.`,
      },
      d: {
        description: `Subscriber may be subject to repossession or recovery fees if the Program Vehicle is not returned to OneCarNow!.`,
      },
      e: {
        description: ` Subscriber may be subject to an Order Change Fee in the amount of $[	] if Subscriber makes a change to the Subscription Plan, including any change to the Program Vehicle, after forty-eight (48) hours after execution of this Agreement.`,
      },
      f: {
        description: `Subscriber may be subject to fees for excessive wear and tear of the Program Vehicle, which determination of excessive wear and tear shall be in the sole discretion of OneCarNow!. For the avoidance of doubt, "excessive wear and tear" includes, but is not limited to: damaged glass, damaged body panels, lights, fenders, paint, dysfunctional accessories, extremely worn tire tread, any damage to the interior, missing equipment that was in or on the vehicle when delivered and has not been replaced with equipment of equal quality and design, missing wheel covers, jack or wheel wrench, any mechanical damage that interferes with the safe and lawful operation of the Program Vehicle, and any other damage whether or not covered by insurance.`,
      },
      g: {
        description: `Subscriber may be subject to late payment charges if a Subscriber’s monthly payment is not received within five (5) business days after the date on which an invoice is sent.`,
      },
      h: {
        description: `To the extent permitted by applicable Law, OneCarNow! reserves the right to hold Subscriber fully responsible for any and all damages, Losses, claims, liabilities, costs, fees, fines, expenses and penalties arising from Subscriber’s Subscription in the Program, or Subscriber’s or Authorized Driver’s possession, access or operation which may include, but may not be limited to: (i) physical or mechanical damage, (ii) Loss due to theft, (iii) physical damage resulting from vandalism, (iv) bodily injury or property damage of Subscriber, Authorized Driver, or any third party, (v) third party claims, (vi) actual charges for towing, storage, impound or other Violations paid by OneCarNow!, (vii) administrative charges including, but not limited to, the costs associated with facilitating any payments for Violations, collection of any fees dues hereunder, appraisal of any damage and other costs and expenses incident to any damage or Loss, (viii) Lost Revenue, and (ix) Diminution in Value. Subscriber also acknowledges and agrees that OneCarNow! is not responsible for any medical or other costs associated with any injury sustained by Subscriber or any other person as a result of any accident while the Program Vehicle is in Subscriber’s possession and Subscriber hereby waives any and all claims and agree to indemnify and hold OneCarNow! harmless against any costs or damages arising out of such claims. `,
      },
    },
    'Taxes.': {
      heading: 'Taxes',
      description: `The Subscription Fee and all other fees and rates listed on the Cover Page are inclusive of all applicable state and local taxes.`,
    },

    'Tickets and Tolls.': {
      heading: 'Tickets and Tolls.',
      description: `Subscriber is solely responsible for any Violations. Subscriber will report to OneCarNow! any Violation incurred in connection with the Program Vehicle within twenty-four (24) hours of Subscriber becoming aware of such Violation. In addition, Subscriber will make arrangements to pay any assessor of a Violation directly and timely. For example, in the case of a toll, Subscriber or Authorized Driver must pay the toll in cash at the applicable toll plaza (where permitted) or must make arrangements to pre-pay any applicable tolls, such as by contacting the applicable toll operator and pre-paying online, by phone, or by mail. To the extent permitted by applicable law, if OneCarNow! receives notice of a Violation that Subscriber has not paid directly, Subscriber specifically authorizes OneCarNow! or a third party appointed by OneCarNow! to automatically charge the Payment Method for any amounts due as a result of such Violation.`,
    },

    'Enforcing this Agreement.': {
      heading: 'Enforcing this Agreement.',
      description: ` Subscriber is responsible for, and agrees to pay, any legal fees, court costs, or other costs and expenses associated with OneCarNow! enforcement of this Agreement against Subscriber and any (current or previous) Authorized Driver, whether upon termination or otherwise.`,
    },

    'Permission to Charge to the Payment Method.': {
      heading: 'Permission to Charge to the Payment Method.',
      description: `To the extent permissible by Law, Subscriber is responsible for paying all amounts incurred in connection with Subscriber’s use of the Program Vehicle and related services or this Agreement (including, but not limited to, any amounts incurred by or in connection with any (current or previous) Authorized Driver) when due, including, but not limited to, Subscription Fees, refueling costs, Violations, and other costs and fees as provided in this Agreement. Subscriber specifically authorizes OneCarNow! or a third party appointed by OneCarNow! to automatically charge the Payment Method for any amounts due hereunder both upon Program Subscription Approval and at any time thereafter, as determined necessary by OneCarNow!. Unless otherwise provided by law, OneCarNow! will charge Subscriber the initial Subscription Fee and any other necessary or related fees upon Program Subscription Approval for a Program Vehicle, prior to the Program Vehicle being delivered. If the Payment Method expires, is declined or is otherwise unsuccessful and Subscriber’s balance remains unpaid after Program Subscription Approval, the delivery will be canceled. For the avoidance of doubt, OneCarNow! has the authority and ability to allow variances to the above payment terms, in OneCarNow!'s sole discretion. In addition to the above, in the case of a Subscriber from whom OneCarNow! requires a Credit Score Deposit, such Credit Score Deposit must be paid, in full, at least five (5) Business Days prior to the delivery of the Program Vehicle, or else OneCarNow! may cancel this Agreement in its entirety, in OneCarNow!'s sole discretion.`,
    },

    'Payment Defaults.': {
      heading: 'Payment Defaults.',
      description: `Subscriber expressly consents and agrees that OneCarNow! may charge the Payment Method (including, but not limited to, without advance notice to Subscriber) in order to collect on any amounts due under this Agreement.`,
      a: {
        description: `In addition, amounts due that are not paid within seven (7) days after the due date for payment will be referred for collections and the Program Vehicle will be subject to repossession.`,
      },
      b: {
        description: `Notwithstanding the provisions of Section 4.7(a), in the case of a Subscriber for whom OneCarNow! is holding a Credit Score Deposit received from such Subscriber at the outset of this Agreement, OneCarNow! may charge amounts due that are not paid within thirty (30) days after notice to Subscriber of the default in payment or Payment Method against the Credit Score Deposit. For the avoidance of doubt, Subscribers considered to be in default pursuant to this Section 4.7(b) shall not be subject to the repossession remedies set forth in Section 9, provided that the amount charged against the Credit Score Deposit is sufficient to satisfy the unpaid charges due to OneCarNow!. For clarity, OneCarNow! may apply the Credit Score Deposit against any amounts for which OneCarNow! determines Subscriber is liable under this Agreement, including without limitation any charges as described in this Article 4. Upon the termination of this Agreement for any reason, OneCarNow! shall refund to Subscriber any excess portion of the Credit Score Deposit once all amounts owed by Subscriber have been satisfied; and Subscriber acknowledges and agrees that Subscriber shall remain liable for any amounts owed which were not offset by the Credit Score Deposit (if any).`,
      },
      c: {
        description: `Subscriber expressly authorizes, agrees, and specifically consents to allowing OneCarNow!, its Agent, and/or its outside collection agencies, outside counsel, or other agents to contact Subscriber in connection with any and all matters relating to unpaid past due charges billed by OneCarNow! to Subscriber pursuant to the terms of this Agreement.`,
      },
    },
    'Methods of Contacting Subscriber.': {
      heading: 'Methods of Contacting Subscriber.',
      description: `Subscriber agrees that contact by or on behalf of OneCarNow! may be made to any mailing address, telephone number, cellular phone number, e-mail address, or any other electronic address that Subscriber has provided or may in the future provide to OneCarNow! for each of the Subscriber and for any Authorized Driver. Subscriber agrees and acknowledges that any e-mail address or any other electronic address that Subscriber provides to OneCarNow! is Subscriber’s private address and is not accessible to unauthorized third parties. Subscriber agrees that in addition to individual persons attempting to communicate directly with Subscriber, any type of contact described above may be made using, among other methods, pre-recorded or artificial voice messages delivered by an automatic telephone dialing system, pre-set e-mail messages delivered by an automatic e-mailing system, or any other pre-set electronic messages delivered by any other automatic electronic messaging system. Subscriber agrees that any telephone and cellular phone calls may be monitored and/or recorded by or on behalf of OneCarNow! in furtherance of the Program services. Subscriber agrees to update OneCarNow! with any changes to contact information of the kind set forth in this paragraph.`,
    },

    'Purchase Option.': {
      heading: 'Purchase Option.',
      description: `At the end of the Term, provided that (i) Subscriber is not in default and (ii) the Subscriber has maintained possession of the Program Vehicle pursuant to this Agreement for at least thirty-six (36) months, Subscriber and OneCarNow! may agree to a sale of the Program Vehicle AS-IS AND WHERE-IS, WITHOUT ANY RECOURSE, REPRESENTATION OR WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, for a price equal to the Fair Market Value of the Program Vehicle, plus applicable sales and other taxes (the “Purchase Price”). If OneCarNow! and Subscriber agree to the sale of the Program Vehicle under this Section 4.9, Subscriber shall cause, at Subscriber’s expense, an independent appraiser selected by OneCarNow! to determine the Fair Market Value of the Program Vehicle. No later than [seven (7)] days after the independent appraisal, Subscriber shall pay OneCarNow! (a) the Purchase Price for the Program Vehicle, and (b) any other amounts then due under this Agreement (including the costs or expenses of Subscriber, if any, in connection with such purchase). Promptly following Subscriber’s valid exercise of the purchase option and payment of the Purchase Price, OneCarNow! will deliver an executed bill of sale and any and all obligations and liabilities of OneCarNow! with respect to such Program Vehicle shall cease.`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.FEESPAYMENTSANDPURCHASEOPTION.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Subscription Fee.'].heading}
        description={data['Subscription Fee.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Other Charges.'].heading}
        description={data['Other Charges.'].description}
      >
        <HeadingAndDescription.SubSection
          bullet={`a.  `}
          description={data['Other Charges.'].a.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`b.  `}
          description={data['Other Charges.'].b.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`c.  `}
          description={data['Other Charges.'].c.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`d.  `}
          description={data['Other Charges.'].d.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`e.  `}
          description={data['Other Charges.'].e.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`f.  `}
          description={data['Other Charges.'].f.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`g.  `}
          description={data['Other Charges.'].g.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`h.  `}
          description={data['Other Charges.'].h.description}
        />
      </HeadingAndDescription>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['Taxes.'].heading}
        description={data['Taxes.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data['Tickets and Tolls.'].heading}
        description={data['Tickets and Tolls.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['Enforcing this Agreement.'].heading}
        description={data['Enforcing this Agreement.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${6}`}
        heading={data['Permission to Charge to the Payment Method.'].heading}
        description={data['Permission to Charge to the Payment Method.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${7}`}
        heading={data['Payment Defaults.'].heading}
        description={data['Payment Defaults.'].description}
      >
        <HeadingAndDescription.SubSection
          bullet={`a.  `}
          description={data['Payment Defaults.'].a.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`b.  `}
          description={data['Payment Defaults.'].b.description}
        />

        <HeadingAndDescription.SubSection
          bullet={`c.  `}
          description={data['Payment Defaults.'].c.description}
        />
      </HeadingAndDescription>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${8}`}
        heading={data['Methods of Contacting Subscriber.'].heading}
        description={data['Methods of Contacting Subscriber.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${9}`}
        heading={data['Purchase Option.'].heading}
        description={data['Purchase Option.'].description}
      />
    </View>
  );
};

const InsuranceAndLiability = () => {
  const SectionNo = Sections.INSURANCEANDLIABILITY.no;
  const data = {
    'Maintained Insurance.': {
      heading: 'Maintained Insurance.',
      description: `OneCarNow! will procure and maintain insurance coverage through a third-party insurer in the amount of or exceeding the applicable state motor vehicle minimum financial responsibility requirements covering use of the Program Vehicle by Subscriber and the current Authorized Driver listed by OneCarNow! on Subscriber’s Program Account, subject to certain exclusions and limitations as provided below and subject to the terms in the applicable insurance policies (the “Maintained Insurance”). Where required by law, the Maintained Insurance includes PIP, or “no-fault” coverage, to the minimum level required by the jurisdiction in which the accident occurs or the claim is adjudicated. To the extent permitted by Law, any personal auto coverage that Subscriber or Authorized Driver has available will be primary over any coverage that OneCarNow! may procure, including, but not limited to, PIP or no-fault coverage. To the extent permitted by Law, OneCarNow! has the sole discretion to accept or waive and reject the inclusion of uninsured motorist, underinsured motorist, supplementary no fault, or any other optional coverage, and OneCarNow! is hereby authorized to sign any forms or acknowledgments on behalf of Subscriber or any (current or previous) Authorized Driver accepting or rejecting (at OneCarNow!’s sole discretion) such coverage. Any separate policy of insurance provided by any Platform for which the Subscriber is rendering services will also be primary to the Maintained Insurance, unless the applicable law requires the Maintained Insurance to be primary. IMPORTANT. PLEASE READ CAREFULLY. The Maintained Insurance only extends coverage to the Subscriber or Authorized Driver when the Subscriber or Authorized Driver is offline and not rendering services on behalf of a Platform (“P0”) and when Subscriber or Authorized Driver is logged into the corresponding application and waiting to accept a ride (“P1”). The Maintained Insurance does not extend coverage when the Subscriber has accepted a ride pursuant to the terms of the Platform for which it is rendering services and is in route to the rider (“P2”). The Maintained Insurance does not extend coverage when Subscriber or Authorized Driver has a passenger in the vehicle and is in route to the destination (“P3”). Subscriber expressly agrees that it will only work for Platforms that provide insurance to Subscriber for P2 and P3, with minimum limits of $1,000,000 for property damage and bodily injury. In the event Subscriber or Authorized Driver works for a Platform that does not extend coverage to the Subscriber for P2 and P3 with minimum limits of $1,000,000 for property damage and bodily injury, Subscriber agrees to hold harmless and fully indemnify OneCarNow! for any liability Subscriber or OneCarNow! may face as a result of any potential gap in insurance coverage, and any liability in excess of the minimum limits. Subscriber further agrees to fully indemnify OneCarNow! for any claim for damages made against OneCarNow! that are not covered by the Maintained Insurance. By signing this Agreement, Subscriber expressly agrees that it is Subscriber’s sole responsibility to ensure that Subscriber or Authorized Driver is covered by the minimum limits of $1,000,000 for property damage and bodily injury for each period of time (P0-P3) the Subscriber is operating the Program Vehicle.  By signing this Agreement, the Subscriber expressly acknowledges that the Maintained Insurance may not be adequate to fully cover Subscriber’s or any (current or previous) Authorized Driver’s liability. OneCarNow! recommends that Subscriber and Authorized Driver consult with their respective insurance agents before using Program services (including the Program Vehicle). OneCarNow! further recommends that Subscriber and Authorized Driver carefully review the terms of insurance provided by any Platform so that Subscriber fully understands what insurance coverage is provided to Subscriber and when those coverages apply. Subscriber and Authorized Driver are encouraged to obtain additional personal insurance coverage that covers their liability that includes a specific endorsement covering Subscriber for any liability Subscriber may incur while driving for a Platform. In addition, Subscriber understands that engaging in a Prohibited Use or other violation of this Agreement may void any Maintained Insurance coverage that Subscriber may be entitled to through Subscriber’s participation in the Program. Subscriber expressly agrees that the insurance coverage available to the Subscriber is dictated by the terms of the applicable policies and is not fully set forth herein.  For purposes of this Agreement, with respect to Subscriber’s indemnification obligations under this Agreement, “OneCarNow!” shall include its affiliates and their respective officers, directors, equity owners, employees, agents and representatives, and “liability” shall include any and all losses, claims, judgements, damages, settlements, assessments, fees, fines, penalties, costs and expenses (including attorneys’ fees, court costs and costs of pursuing insurance).`,
    },
    'OneCarNow! is not an Insurer.': {
      heading: 'OneCarNow! is not an Insurer.',
      description: `By entering into this agreement, Subscriber expressly agrees that OneCarNow! is neither an insurance company nor an insurance agent. Subscriber further agrees that OneCarNow! is not in the business of producing insurance. Subscriber also agrees that OneCarNow! is not in the business of adjusting claims, and any claims adjustment will be handled by a third-party claims adjuster.`,
    },
    'Applicable Deductible.': {
      heading: 'Applicable Deductible.',
      description: `Subscriber agrees that, in the event of damage, Subscriber is responsible for the applicable deductible, and OneCarNow! or the third-party insurer may immediately charge Subscriber up to the amount of any applicable deductible set forth in the responding insurance policy, if the damage equals or exceeds the deductible and to the extent permitted by applicable law.`,
    },
    'Indemnity.': {
      heading: 'Indemnity',
      description: `To the extent permitted by applicable Law, Subscriber will defend, indemnify, hold harmless and reimburse OneCarNow! (including its affiliates), and all of such parties’ respective directors, officers, shareholders, employees and agents, including but not limited to OneCarNow!'s lessors or financing parties (collectively, “OneCarNow! Parties”) from and for all claims, actions, damages, Losses, liabilities, fees, fines, penalties, costs or expenses, including, but not limited to, attorneys’ fees and court costs, incurred by any of such parties or paid by any of them to any person in respect of Subscriber’s or any (current or previous) Authorized Driver’s access, use or operation of the Program Vehicle or participation in the Program.`,
    },
    'Reporting of Accidents.': {
      heading: 'Reporting of Accidents.',
      description: `Subscriber will report the occurrence of any accident, damage or Loss, including, but not limited to, theft, of Program Vehicles to OneCarNow!, all third-party insurers, and to the applicable law enforcement agencies as soon as safely practicable after Subscriber becomes aware of such incident; however, at minimum, Subscriber will report any such incident to OneCarNow!, the third-party insurers, and the police as soon as practicable after learning of such incident. As part of such report, Subscriber will provide a written description of the incident and the insurance information of the other parties involved, if applicable. Subscriber will promptly provide a copy of such report to OneCarNow!. Subscriber or Authorized Driver, as applicable, will make a reasonable effort to obtain evidence from any witnesses to the incident, if applicable. Subscriber understands that failure to provide prompt notification to the third-party insurer may exclude coverage for any Loss.`,
    },
    'Cooperation with Loss Investigation.': {
      heading: 'Cooperation with Loss Investigation.',
      description: `Subscriber and any (current or previous) Authorized Driver agree to fully cooperate in any investigation that OneCarNow! or any insurer may choose to conduct if Program Vehicles are damaged in any way. Subscriber and any (current or previous) Authorized Driver, as appropriate, will immediately send each request, demand, order, notice, summons or other pleading Subscriber or such Authorized Driver receives in connection with any incident involving Program Vehicles to OneCarNow!, and the third-party insurer. Neither Subscriber nor any (current or previous) Authorized Driver may assume any obligation, make or commit to make any payment or incur any expense on OneCarNow!’s behalf without OneCarNow!’s prior written consent. Subscriber understands that failure to cooperate with the third-party insurer’s investigation may exclude coverage for any Loss.`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.INSURANCEANDLIABILITY.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Maintained Insurance.'].heading}
        description={data['Maintained Insurance.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['OneCarNow! is not an Insurer.'].heading}
        description={data['OneCarNow! is not an Insurer.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['Applicable Deductible.'].heading}
        description={data['Applicable Deductible.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data['Indemnity.'].heading}
        description={data['Indemnity.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['Reporting of Accidents.'].heading}
        description={data['Reporting of Accidents.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${6}`}
        heading={data['Cooperation with Loss Investigation.'].heading}
        description={data['Cooperation with Loss Investigation.'].description}
      />
    </View>
  );
};

const DisclaimersAndLimitations = () => {
  const SectionNo = Sections.DISCLAIMERSANDLIMITATIONSOFLIABILITY.no;

  const data = {
    'Disclaimer of Warranties.': {
      heading: 'Disclaimer of Warranties.',
      description: `SUBSCRIBER AND AUTHORIZED DRIVER ACCESS, USE, OPERATE AND TAKE POSSESSION OF THE PROGRAM VEHICLE AND ANY OPTIONAL ACCESSORIES “AS IS”, AND ONECARNOW! HEREBY EXCLUDES ANY AND ALL WARRANTIES, BOTH EXPRESS AND IMPLIED, WITH RESPECT TO THE PROGRAM VEHICLE, THE PROGRAM (INCLUDING THE OPERATION, PERFORMANCE AND RESULTS THEREOF), THE WEB-BASED APP, THE WEBSITE, AND ANY OPTIONAL ACCESSORIES, INCLUDING, BUT NOT LIMITED TO, ANY IMPLIED WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.`,
    },

    'Disclaimer of Liability.': {
      heading: 'Disclaimer of Liability.',
      description: `UNDER NO CIRCUMSTANCES SHALL THE ONECARNOW! PARTIES BE LIABLE FOR ANY AND ALL CLAIMS, ACTIONS, LOSSES, LIABILITIES, DAMAGES, FEES, FINES, EXPENSES, COSTS AND PENALTIES (COLLECTIVELY, “LOSSES”) INCURRED OR SUSTAINED BY, OR IMPOSED UPON, ANY OF THE ONECARNOW! PARTIES, SUBSCRIBER, OR ANY (CURRENT OR PREVIOUS) AUTHORIZED DRIVERS BASED UPON, ARISING OUT OF, WITH RESPECT TO, OR BY REASON OF SUBSCRIBER’S OR ANY (CURRENT OR PREVIOUS) AUTHORIZED DRIVER’S CRIMINAL, WILLFUL, OR NEGLIGENT ACTION OR OMISSION IN CONNECTION WITH THIS AGREEMENT, THE ACCESS, USE OR OPERATION OF THE PROGRAM VEHICLE, OR IN CONNECTION WITH PARTICIPATION IN THE PROGRAM.`,
    },

    'Disclaimer of Responsibility for Personal Property.': {
      heading: 'Disclaimer of Responsibility for Personal Property.',
      description: `THE ONECARNOW! PARTIES ARE NOT LIABLE FOR ANY PERSON’S LOST, STOLEN, OR DAMAGED PROPERTY IN AND FROM THE PROGRAM VEHICLE INCLUDING, BUT NOT LIMITED TO, DAMAGE OR LOSS IN CONNECTION WITH SECTION 3.11 OF THIS AGREEMENT.`,
    },

    'Disclaimer of Special, Consequential and Other Damages.': {
      heading: 'Disclaimer of Special, Consequential and Other Damages.',
      description: `THE ONECARNOW! PARTIES ARE NOT LIABLE FOR ANY AND ALL INDIRECT, INCIDENTAL, SPECIAL, PUNITIVE, EXEMPLARY, ECONOMIC, INCIDENTAL, LOSS OF INCOME, LOSS OF PROFITS, LOSS OF USE, LOSS OF EARNINGS, INCONVENIENCE, CONSEQUENTIAL OR OTHER DAMAGES SUSTAINED IN CONNECTION WITH THIS AGREEMENT, THE PROGRAM, OR THE PROGRAM VEHICLE. WITHOUT LIMITING THE FOREGOING, THE ONECARNOW! PARTIES WILL HAVE NO LIABILITY FOR (A) ANY LOSS OF, OR DAMAGE TO, ANY GOODS OR OTHER PERSONAL PROPERTY IN OR ON PROGRAM VEHICLES OR IN OR ON ANY THIRD PARTY VEHICLE, (B) ANY LOSSES (INCLUDING WITH RESPECT TO PERSONAL INJURY, DEATH OR PROPERTY DAMAGE) IN RELATION TO SUBSCRIBER, ANY (CURRENT OR PREVIOUS) AUTHORIZED DRIVER, OR ANY THIRD PARTY, (C) ANY LOSSES INCURRED BY SUBSCRIBER OR ANY (CURRENT OR PREVIOUS) AUTHORIZED DRIVER AS A RESULT OF ANY CLAIMS MADE BY A THIRD PARTY, OR (D) ANY LOSSES INCURRED BY SUBSCRIBER OR ANY (CURRENT OR PREVIOUS) AUTHORIZED DRIVER IN CONNECTION TO EITHER (I) THE NON-AVAILABILITY, SUPPLY, OPERATION OR USE OF THE PROGRAM VEHICLE, OR (II) ANY ACCESSORIES IN OR TO THE PROGRAM VEHICLE, WHETHER SUPPLIED BY ONECARNOW! OR BY SUBSCRIBER OR AUTHORIZED DRIVER (INCLUDING, BUT NOT LIMITED TO, LUGGAGE RACKS, BICYCLE RACKS, AND CHILD SAFETY SEATS). IN ALL CASES, SUBSCRIBER AND AUTHORIZED DRIVER ARE RESPONSIBLE FOR THE SAFE INSTALLATION OF SUCH ACCESSORIES AND MUST CHECK THE CONDITION OF ACCESSORIES IN OR TO PROGRAM VEHICLES BEFORE EACH USE; UNLESS, IN EACH CASE, SUCH LOSS OR DAMAGE IS INCURRED DUE TO ONECARNOW!’S NEGLIGENCE OR FAILURE TO COMPLY WITH APPLICABLE LAW.`,
    },

    'No Disclaimer or Limitation in Contravention with Law.': {
      heading: 'No Disclaimer or Limitation in Contravention with Law.',
      description: `Nothing in this Section 6 disclaims any warranty that cannot be disclaimed pursuant to applicable Law. Nothing in this Section 6 limits or disclaims any liability that cannot be limited or disclaimed pursuant to applicable Law.`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.INSURANCEANDLIABILITY.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Disclaimer of Warranties.'].heading}
        description={data['Disclaimer of Warranties.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Disclaimer of Liability.'].heading}
        description={data['Disclaimer of Liability.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['Disclaimer of Responsibility for Personal Property.'].heading}
        description={data['Disclaimer of Responsibility for Personal Property.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data['Disclaimer of Special, Consequential and Other Damages.'].heading}
        description={data['Disclaimer of Special, Consequential and Other Damages.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['No Disclaimer or Limitation in Contravention with Law.'].heading}
        description={data['No Disclaimer or Limitation in Contravention with Law.'].description}
      />
    </View>
  );
};

const DisputeResolution = () => {
  const SectionNo = Sections.DISPUTERESOLUTION.no;

  const data = {
    'Binding Mutual Arbitration.': {
      heading: 'Binding Mutual Arbitration.',
      description: `Any dispute, claim or controversy in connection with, arising out of or relating to a Subscription in the Program, this Agreement or the breach, termination, enforcement, interpretation or validity of this Agreement, including, but not limited to, the determination of the scope or applicability of this agreement to arbitrate (a “Dispute”), will be determined by arbitration in the State in which this Agreement was executed, before a single arbitrator. The arbitration will be administered by the American Arbitration Association (“AAA”) in accordance with AAA Consumer Arbitration Rules. Judgment on the award may be entered in any court having jurisdiction. This provision will not preclude Parties from seeking provisional remedies in aid of arbitration from a court of appropriate jurisdiction.`,
    },

    'Conduct of Arbitration.': {
      heading: 'Conduct of Arbitration.',
      description: `The arbitration will be commenced by the claimant Party filing a demand for arbitration with the administrator of AAA and serving the demand on the opposing Party. Within thirty (30) calendar days of the date the demand for arbitration is filed, the Parties will select an arbitrator by following the AAA Consumer Arbitration Rules’ appointment procedures. Except as may be required by Law, neither Party nor the arbitrator may disclose the existence, content or results of any arbitration under this Agreement without the prior written consent of both Parties. The arbitrator’s award will be in writing accompanied by a reasoned opinion and a written statement of the essential findings and conclusions on which the award is based.`,
    },

    Costs: {
      heading: 'Costs',
      description: `The arbitrator will determine how the costs and expenses of the arbitration will be allocated between the Parties and may award attorneys’ fees (in any case, subject to the attorneys’ fees provisions hereunder, as applicable).`,
    },

    Remedies: {
      heading: 'Remedies',
      description: `The arbitrator will have the power to award any party any remedies that would be available to that Party in his or her individual capacity under this Agreement or otherwise in a court of law for the Dispute presented to and decided by the arbitrator.`,
    },

    'Arbitration is on an Individual Basis Only; Class Action Waiver.': {
      heading: 'Arbitration is on an Individual Basis Only; Class Action Waiver.',
      description: `THE PARTIES AGREE TO ARBITRATE SOLELY ON AN INDIVIDUAL BASIS, AND THAT THIS AGREEMENT DOES NOT PERMIT CLASS ARBITRATION OR ANY CLAIMS BROUGHT AS A PLAINTIFF OR CLASS MEMBER IN ANY CLASS OR REPRESENTATIVE ARBITRATION PROCEEDING. THE ARBITRATOR MAY NOT CONSOLIDATE MORE THAN ONE PERSON’S CLAIMS AND MAY NOT OTHERWISE PRESIDE OVER ANY FORM OF A REPRESENTATIVE OR CLASS PROCEEDING. NOTWITHSTANDING THE ARBITRATOR’S POWER TO RULE ON HIS OR HER OWN JURISDICTION AND THE VALIDITY OR ENFORCEABILITY OF THE AGREEMENT TO ARBITRATE, THE ARBITRATOR HAS NO POWER TO RULE ON THE VALIDITY OR ENFORCEABILITY OF THE AGREEMENT TO ARBITRATE SOLELY ON AN INDIVIDUAL BASIS. IN THE EVENT THE PROHIBITION ON CLASS ARBITRATION IS DEEMED INVALID OR UNENFORCEABLE, THE REMAINING PORTIONS OF THIS SECTION 7.5 WILL REMAIN IN FORCE.`,
    },

    'Applicable Law.': {
      heading: 'Applicable Law.',
      description: `This Agreement and the rights of the Parties hereunder will be governed by and construed in accordance with the Laws of the State of Florida, exclusive of conflict or choice of law rules. The Parties acknowledge that this Agreement evidences a transaction involving interstate commerce. Notwithstanding the provision in the preceding sentence with respect to applicable substantive Law, any arbitration conducted pursuant to the terms of this Agreement will be governed by the Federal Arbitration Act (9 U.S.C., Secs. 1-16).`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.INSURANCEANDLIABILITY.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Binding Mutual Arbitration.'].heading}
        description={data['Binding Mutual Arbitration.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Conduct of Arbitration.'].heading}
        description={data['Conduct of Arbitration.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data.Costs.heading}
        description={data.Costs.description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data.Remedies.heading}
        description={data.Remedies.description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['Arbitration is on an Individual Basis Only; Class Action Waiver.'].heading}
        description={data['Arbitration is on an Individual Basis Only; Class Action Waiver.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${6}`}
        heading={data['Applicable Law.'].heading}
        description={data['Applicable Law.'].description}
      />
    </View>
  );
};

const TermsAndTermination = () => {
  const SectionNo = Sections.TERMSANDTERMINATION.no;

  const data = {
    'Term.': {
      heading: 'Term.',
      description: `This Agreement will commence at the time that Applicant has been accepted as a Subscriber and OneCarNow! has received Subscriber’s initial payment, and will continue in effect for a period of thirty (30) days (the “Initial Term”), unless terminated pursuant to this Agreement. Upon expiration of the Initial Term, this Agreement shall automatically renew for additional thirty (30) day terms unless either Party provides written notice of nonrenewal at least seven (7) days prior to the end of the then-current term (each a “Renewal Term” and together with the Initial Term, the “Term”), or unless sooner terminated pursuant to this Agreement. If the Term is renewed for any Renewal Term(s) pursuant to this Section, the terms and conditions of this Agreement during each such Renewal Term shall be the same as the terms and conditions in effect immediately prior to such renewal. If either Party provides timely notice of its intent not to renew this Agreement, then, unless otherwise sooner terminated in accordance with its terms, this Agreement shall terminate on the expiration of the then-current Term.`,
    },
    'Termination.': {
      heading: 'Termination.',
      description: `This Agreement will be terminated as follows:`,
      a: {
        description: `Upon termination by either Party as set forth in this Agreement; and/or`,
      },
      b: {
        description: `Immediately, in OneCarNow!’s sole discretion, if:`,
        i: {
          no: 'i',
          description: `Subscriber fails to meet the Eligibility Criteria;`,
        },
        ii: {
          no: 'ii',
          description: `Subscriber fails to abide by the terms of this Agreement (including, but not limited to, a failure to pay any of the amounts due under this Agreement by the applicable due date);`,
        },
        iii: {
          no: 'iii',
          description: `Any (current or previous) Authorized Driver fails to abide by the terms of this Agreement applicable to Authorized Driver; and`,
        },
        iv: {
          no: 'iv',
          description: `If the Program Vehicle sustains any damage exceeding $5,000 while in use by Subscriber.`,
        },
      },
    },
    'Effect of Termination.': {
      heading: 'Effect of Termination.',
      description: `Upon termination of this Agreement, any rights Subscriber and Authorized Driver have to use the Program Vehicle and to participate in the Program will immediately terminate, and Subscriber or Authorized Driver, as applicable, will (as soon as safely practicable) cease operation of the Program Vehicle and make arrangements to have such Program Vehicle Returned to OneCarNow! pursuant to OneCarNow!’s instructions regarding such Return as soon as safely practicable (but in no event after one (1) Business Day), provided that OneCarNow! and Subscriber have not agreed to the sale of the Program Vehicle pursuant to Section 4.9. Termination of this Agreement will not affect or diminish any obligations of Subscriber’s until the Program Vehicle is Returned to OneCarNow! and is within OneCarNow!’s possession. Additionally, termination of this Agreement and Return of the Program Vehicle will not affect any provisions of this Agreement which survive termination of this Agreement pursuant to Section 10.10 herein. No Subscription Fees or any other amounts previously paid by Subscriber will be returned or refunded to Subscriber in the event the Agreement is terminated prior to the end of the Term. Further, if Subscriber terminates this Agreement before the end of the Term or if the Agreement is terminated before the end of the Term by OneCarNow! due to Subscriber’s breach of this Agreement, Subscriber shall still be responsible for the payment of any and all Subscription Fees and/or other charges for which they would have been responsible under this Agreement through the end of the Term. With respect to any termination of this Agreement, Subscriber will remain responsible for any fees, costs or expenses incurred prior to termination of this Agreement (including, but not limited to, all applicable amounts due described in Section 4 (Fees and Payments)).`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.INSURANCEANDLIABILITY.name} `}</Text>
      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Term.'].heading}
        description={data['Term.'].description}
      />
      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Termination.'].heading}
        description={data['Termination.'].description}
      >
        <HeadingAndDescription.SubSection bullet={`a.  `} description={data['Termination.'].a.description} />

        <HeadingAndDescription.SubSection bullet={`b.  `} description={data['Termination.'].b.description}>
          <HeadingAndDescription.SubSectionBullets
            bullet={`i.  `}
            description={data['Termination.'].b.i.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={`ii.  `}
            description={data['Termination.'].b.ii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={`iii.  `}
            description={data['Termination.'].b.iii.description}
          />

          <HeadingAndDescription.SubSectionBullets
            bullet={`iv.  `}
            description={data['Termination.'].b.iv.description}
          />
        </HeadingAndDescription.SubSection>
      </HeadingAndDescription>
      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['Effect of Termination.'].heading}
        description={data['Effect of Termination.'].description}
      />
    </View>
  );
};

const RepossessionAndRecovery = () => {
  const SectionNo = Sections.REPOSSESSIONANDRECOVERYOFPROGRAMVEHICLES.no;

  const data = {
    RepossessionAndRecovery: {
      description: `To the extent permitted by Law, OneCarNow! reserves the right to recover or repossess the Program Vehicle at any time, without notice, at Subscriber’s expense if Subscriber or any (current or previous) Authorized Driver violates this Agreement or upon termination or expiration of this Agreement and the Program Vehicle has still not been Returned. Subscriber agrees to pay third party repossession costs and fees associated with OneCarNow! repossession or recovery of any Program Vehicle pursuant to this Section 9. Subscriber and Authorized Driver hereby agree to waive all claims for damages or other Losses related to OneCarNow!’s repossession or recovery of the Program Vehicle.`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.REPOSSESSIONANDRECOVERYOFPROGRAMVEHICLES.name} `}</Text>

      <Text style={styles.letterPoint}>{data.RepossessionAndRecovery.description}</Text>
    </View>
  );
};

const GeneralProvisions = () => {
  const SectionNo = Sections.GENERALPROVISIONS.no;

  const data = {
    'Entire Agreement; Survival; Conflict with Law.': {
      heading: 'Entire Agreement; Survival; Conflict with Law.',
      description: `The Schedules to this Agreement, the Cover Page, the OneCarNow! Privacy Policy, the Program FAQs accessible either by email, the Web-Based App, or the Website (collectively, the “FAQs”) and the OneCarNow! Web-Based App and Website Terms of Service (which Terms of Service can be found at [URL link to Terms of Service]), each as amended, modified or supplemented from time to time, are incorporated by reference into this Agreement. To the extent there is a direct conflict between a provision or term of this Agreement and a Law applicable to this Agreement or a rental of a Program Vehicle, the applicable Law will govern. This Agreement may be executed in two or more counterparts, and it is not necessary that the signatures of all the parties hereto appear on the same counterpart, but such counterparts together will constitute a single effective Agreement. Further, counterparts to this Agreement may be executed by hand or by any electronic signature or consent, and executed counterparts may be delivered via facsimile, electronic mail or other similar transmission method, and any executed counterpart so delivered shall be valid and effective for all purposes. Any edits made to this Agreement so as to include Vehicle identification information or specifics may be made by OneCarNow! in their sole discretion and will be incorporated herein, without further approval or execution.`,
    },
    'Acknowledgment that Subscriber has Read this Agreement.': {
      heading: 'Acknowledgment that Subscriber has Read this Agreement.',
      description: `By accepting this Agreement, Subscriber represents and warrants to OneCarNow! that Subscriber has received all explanations that Subscriber has reasonably requested concerning the content of this Agreement, including, but not limited to, all schedules, and that Subscriber has carefully reviewed and understands Subscriber’s commitments and obligations hereunder.`,
    },
    'No Assignment by Subscriber or Authorized Driver.': {
      heading: 'No Assignment by Subscriber or Authorized Driver.',
      description: `The rights and obligations of Subscriber and Authorized Driver under this Agreement are not assignable or transferable, in whole or in part by Subscriber or Authorized Driver. Any attempt by Subscriber or Authorized Driver to assign or transfer this Agreement, in whole or in part, without OneCarNow! written consent will be void and of no force and effect.`,
    },
    'Assignment by OneCarNow! Permissible.': {
      heading: 'Assignment by OneCarNow! Permissible.',
      description: `OneCarNow! may assign or transfer this Agreement, in whole or in part, or any of its rights or responsibilities pursuant to this Agreement (including but not limited to its right to recover any Fees, Charges or other payment streams) to one or more affiliates, agents, contractors, or third parties without notice to or consent of Subscriber. Further, OneCarNow! may allow affiliates, agents, contractors, purchasers or other third parties to join in and be bound to this Agreement, in OneCarNow!’s sole discretion. For example, but without limiting the foregoing, OneCarNow! may appoint an affiliate or other third party to repossess or recover Program Vehicles or to process tickets and tolls or perform maintenance, repairs or other services with respect to Program Vehicles.`,
    },
    'No Waiver.': {
      heading: 'No Waiver.',
      description: `No delay or omission by OneCarNow! in OneCarNow!’s exercise of any right or power occurring upon any noncompliance or default by Subscriber or an Authorized Driver with respect to any of the terms of this Agreement shall impair any such right or power or be construed to be a waiver thereof. Any waiver by OneCarNow! of any covenant, condition, or agreement to be performed by Subscribers or any (current or previous) Authorized Driver shall not be deemed to be a waiver of any prior or subsequent breach of the same, or of any other covenant, condition, or agreement hereunder. Unless stated otherwise, all remedies provided for in this Agreement shall be cumulative and in addition to and not in lieu of any other remedies available to either Party at Law, in equity, or otherwise.`,
    },
    'Severability.': {
      heading: 'Severability.',
      description: `If any term, provision, covenant or condition of this Agreement is held invalid or unenforceable for any reason, the remainder of the provisions will continue in full force and effect as if this Agreement had been executed with the invalid portion eliminated. The Parties further agree to substitute for the invalid provision a valid provision that most closely approximates the intent and economic effect of the invalid provision.`,
    },
    'Modification.': {
      heading: 'Modification.',
      description: `OneCarNow! reserves the right to modify, change, or revise this Agreement (in whole or in part) (each a “Modification”), including, but not limited to, the schedules to this Agreement or the Vehicle identification information on the Cover Page, at any time and from time to time. In particular, if or when specific Vehicle identification information (including but not limited to a vehicle identification number or a license plate number) is obtained by OneCarNow! after the execution of this Agreement, such information will be incorporated into this Agreement and the Cover Page once obtained by OneCarNow!, which Modification does not require Subscriber’s consent, and a revised Agreement containing such Modification will be sent by OneCarNow! to Subscriber prior to the delivery of the Vehicle. For the avoidance of doubt, any Agreement revised so as to include Vehicle identification information shall not require re-execution by the Parties, and shall take immediate effect. Further, all terms of this Agreement, as originally presented to and executed by Subscriber, shall remain valid and binding upon Subscriber despite the Modifications made. Subscriber will inform the current Authorized Driver listed by OneCarNow! on Subscriber’s Program Account of any Modification to the Agreement. Subscriber’s and such Authorized Driver’s continued use of Program services after any Modification to this Agreement will constitute their continued consent to the Agreement as modified.`,
    },
    'Notices.': {
      heading: 'Notices.',
      description: `Any notices or communications required or permitted to be given to Subscriber shall be in writing and shall be sufficiently given if emailed to Subscriber or if posted on the Web-Based App or the Website. Any notices or communications required or permitted to be given to OneCarNow! shall be in the form required by this Agreement for such notice.`,
    },
    'Headings; Number and Gender.': {
      heading: 'Headings; Number and Gender.',
      description: `The headings in this Agreement are for reference only and will not affect the interpretation of this Agreement. Whenever the context requires, words in the singular or plural form include the plural and singular form, respectively, and words denoting gender include the masculine, feminine, and neuter.`,
    },
    'Survival.': {
      heading: 'Survival.',
      description: `The following provisions will survive termination of this Agreement: Sections 1, 2.1, 2.2, 2.3, 2.5, 2.6, 3.1, 3.3, 3.8, 3.10, 3.11 and 4 through 10.`,
    },
  };

  return (
    <View>
      <Text
        style={[{ textDecoration: 'underline' }, styles.definicionesText]}
      >{`${SectionNo}. ${Sections.GENERALPROVISIONS.name} `}</Text>

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${1}`}
        heading={data['Entire Agreement; Survival; Conflict with Law.'].heading}
        description={data['Entire Agreement; Survival; Conflict with Law.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${2}`}
        heading={data['Acknowledgment that Subscriber has Read this Agreement.'].heading}
        description={data['Acknowledgment that Subscriber has Read this Agreement.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${3}`}
        heading={data['No Assignment by Subscriber or Authorized Driver.'].heading}
        description={data['No Assignment by Subscriber or Authorized Driver.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${4}`}
        heading={data['Assignment by OneCarNow! Permissible.'].heading}
        description={data['Assignment by OneCarNow! Permissible.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${5}`}
        heading={data['No Waiver.'].heading}
        description={data['No Waiver.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${6}`}
        heading={data['Severability.'].heading}
        description={data['Severability.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${7}`}
        heading={data['Modification.'].heading}
        description={data['Modification.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${8}`}
        heading={data['Notices.'].heading}
        description={data['Notices.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${9}`}
        heading={data['Headings; Number and Gender.'].heading}
        description={data['Headings; Number and Gender.'].description}
      />

      <HeadingAndDescription
        SectionNo={`${SectionNo}.${10}`}
        heading={data['Survival.'].heading}
        description={data['Survival.'].description}
      />
    </View>
  );
};

const Signatures = ({ userData }: any) => {
  const { firstName, lastName } = userData;
  return (
    <View style={styles.SignatureContainer}>
      <View>
        <Text style={styles.SignatureNames}>______________________________________</Text>
      </View>
      <View style={styles.SignatureContent}>
        <Text style={styles.SignatureNames}>{`${firstName.toUpperCase()} ${lastName.toUpperCase()}`}</Text>
        <Text style={styles.SignatureNames}>{'In your own right'}</Text>
      </View>
    </View>
  );
};

const MotorVehicleRentalAgreement = ({ children }: { children: ReactNode }) => {
  return (
    <Page style={styles.page} size="A4" wrap>
      <Image style={styles.header} src={HeaderImg.src} fixed />
      <Text style={styles.headText} fixed>
        {'OneCarNow! Subscription Agreement Individual Terms'}
      </Text>
      <View style={styles.body}>
        <View>
          <Text
            style={[
              {
                textAlign: 'center',
                padding: '8px',
                fontSize: '12px',
                fontFamily: 'Helvetica-Bold',
              },
            ]}
          >
            {' '}
            {'ONECARNOW! MOTOR VEHICLE RENTAL AGREEMENT'}
          </Text>
          <Text style={styles.textBold}>
            {' '}
            {
              'THIS CONTRACT CONTAINS A BINDING MUTUAL ARBITRATION PROVISION (INCLUDING A CLASS ACTION WAIVER). BY ACCEPTING THIS AGREEMENT SUBSCRIBER WAIVES SUBSCRIBER’S RIGHT TO A JURY TRIAL AND AGREE TO BINDING ARBITRATION'
            }{' '}
          </Text>

          <Text style={[{ textDecoration: 'underline' }, styles.textBold]}>
            {
              'IMPORTANT DISCLOSURE RELATED TO RECURRING PAYMENT CHARGES TO SUBSCRIBER’S BANK ACCOUNT, CREDIT OR DEBIT CARD'
            }
          </Text>

          <Text style={[{ padding: '4px 0' }, styles.letterPoint]}>
            <Text style={styles.textBold}>
              {
                'SUBSCRIBER AGREES THAT ONECARNOW! CAN CHARGE SUBSCRIBER’S BANK ACCOUNT, CREDIT CARD OR DEBIT CARD, AS APPLICABLE, UPON PROGRAM SUBSCRIPTION APPROVAL FOR A PROGRAM VEHICLE, IN CONNECTION WITH THE SUBSCRIPTION PLAN AND EVERY WEEK THEREAFTER FOR THE TERM OF THIS AGREEMENT. IF SUBSCRIBER WANTS TO CANCEL THIS AGREEMENT AND PREVENT FUTURE CHARGES, SUBSCRIBER MUST CONTACT ONECARNOW! WITHIN 48 HOURS OF EXECUTING THIS AGREEMENT BY EMAILING ONECARNOW! AT  '
              }
            </Text>
            {'<EMAIL>.'}
          </Text>
          <Text style={styles.letterPoint}>
            {`This Agreement (as defined in Section 1.3 below) is by and between OneCarNow! (as defined in Section 1.14 below) and Subscriber (as defined in Section 1.33 below). By accepting this Agreement, as it may be amended from time to time as provided below, Subscriber accepts and agrees to comply with the terms and conditions of this Agreement. In this Agreement Subscriber and OneCarNow! may be referred to together as the “Parties” and each individually as a “Party.”`}
          </Text>
        </View>
        <Definitions />
        <ApplicationAndEligibility />
        <BasicTermsOfUseProgram />
        <FeesPaymentsAndPurchaseOption />
        <InsuranceAndLiability />
        <DisclaimersAndLimitations />
        <DisputeResolution />
        <TermsAndTermination />
        <RepossessionAndRecovery />
        <GeneralProvisions />
        {children}
      </View>
    </Page>
  );
};

const SubscriptionInformation = ({ userData }: any) => {
  const { deliverDate, firstName, lastName, contractNumber, state, address } = userData;
  const contractStartDate = deliverDate.split('T')[0];

  const deliverDateWithoutTime = moment(deliverDate.split('T')[0]);
  const contractEndDate = getContractEndDate(deliverDateWithoutTime);

  const { addressStreet, exterior, interior } = address;

  const OCN_ADDRESS = '78 SW 7th Street, Miami, FLORIDA, USA 33130, office 7-103.';
  const OCN_EMAIL = '@<EMAIL>';

  return (
    <View style={styles.defContainer}>
      <Text style={styles.definicionesText}>{'Subscription Information'}</Text>
      <Text style={styles.label}>
        {'Signing Date: '} <Text style={styles.letterPoint}>{contractStartDate}</Text>{' '}
      </Text>
      <Text style={styles.label}>
        {'Subscriber: '} <Text style={styles.letterPoint}>{firstName + ' ' + lastName}</Text>{' '}
      </Text>
      <Text style={styles.label}>
        {'Unique Identifier: '} <Text style={styles.letterPoint}>{contractNumber}</Text>{' '}
      </Text>
      <Text style={styles.label}>
        {'Subscriber State: '} <Text style={styles.letterPoint}>{state?.value}</Text>{' '}
      </Text>
      <Text style={styles.label}>
        {'Subscriber Address: '}{' '}
        <Text style={styles.letterPoint}>{addressStreet + ' ' + exterior + ' ' + interior} </Text>{' '}
      </Text>
      <Text style={styles.label}>
        {'OneCarNow! Address: '} <Text style={styles.letterPoint}>{OCN_ADDRESS}</Text>
      </Text>
      <Text style={styles.label}>
        {'OneCarNow! Email: '} <Text style={styles.letterPoint}>{OCN_EMAIL}</Text>
      </Text>
      <Text style={styles.label}>{'OneCarNow! Phone Number: '}</Text>
      <Text style={styles.label}>
        {'Pick-Up Address: '} <Text style={styles.letterPoint}>{'Located on offer letter'}</Text>
      </Text>
      <Text style={styles.label}>
        {'Contract Inspection Date: '} {contractStartDate}
      </Text>
      <Text style={styles.label}>
        {'Date due for vehicle return: '} {contractEndDate}
      </Text>

      <View style={{ padding: '4px 0px' }}>
        <Text style={styles.letterPoint}>
          {`At ${contractStartDate} the subscriber confirmed the following statements:`}
        </Text>
        <View style={{ rowGap: 12 }}>
          <Text style={{ fontSize: 8 }}>
            {'\u2022' + ' '}{' '}
            {
              'I agree to the contract including the terms and conditions, including the cancellation policy, arbitration policy, and class action waiver. I agree to the privacy policy.'
            }
          </Text>
          <Text style={{ fontSize: 8 }}>
            {'\u2022' + ' '}{' '}
            {
              'I authorize OneCarNow! to collect (recurring) payments from my account by the selected payment method.'
            }
          </Text>
        </View>
      </View>
    </View>
  );
};

const NoticeToTheSubscriber = () => {
  return (
    <View style={{ rowGap: 2 }}>
      <Text style={styles.definicionesText}>{'NOTICE TO THE SUBSCRIBER: '}</Text>
      <Text style={{ fontSize: 8, fontFamily: 'Helvetica-Bold' }}>
        {'1. Do not sign this agreement before you read it or if it contains any blank space. '}
      </Text>
      <Text style={{ fontSize: 8, fontFamily: 'Helvetica-Bold' }}>
        {'2. You are entitled to a completely filled in copy of this agreement when you sign it. '}
      </Text>
      <Text
        style={{
          fontSize: 8,
          fontFamily: 'Helvetica-Bold',
          padding: '4px 0px',
        }}
      >
        {
          'I acknowledge that a copy of this agreement will be delivered to me upon the execution of this agreement. '
        }
      </Text>
    </View>
  );
};

const VehicleInformation = ({ userData }: any) => {
  const { brand, model, color, vin, plates, year } = userData;
  return (
    <View>
      <Text style={styles.definicionesText}>{'Vehicle Information: '}</Text>
      <Text style={styles.label}>
        Make/Model: {brand} {''} {model}{' '}
      </Text>
      <Text style={styles.label}>Model year: {year}</Text>
      <Text style={styles.label}>Color: {color}</Text>
      <Text style={styles.label}>Miles allowed per month: {' 4,200 miles'}</Text>
      <Text style={styles.label}>VIN: {vin}</Text>
      <Text style={styles.label}>
        {' '}
        {'License plate: '} {plates}
      </Text>
      <Text style={styles.label}> {'Other Vehicle Identifiers: '} </Text>
    </View>
  );
};
const CapitalizedCostTablePDF = (props: any) => {
  const {
    capitalizedCost,
    adjustedCapitalizedCost,
    grossCapitalizedCost,
    residualValue,
    capitalizedCostReduction,
    depreciation,
    costOfInsurance,
    subscriptionCharge,
    totalBaseWeeklyPayments,
    subscriptionTerm,
    baseWeeklyPayment,
    weeklySalesUseTaxReimbursement,
    propExciseRegTaxReimbursement,
    totalWeeklyPayment,
    additionalEarlyTerminationCharge,
  } = props;

  const arr = [
    {
      heading: 'Capitalized Cost',
      description: 'The sum of the adjusted capitalized cost and any capitalized cost reduction.',
      cost: capitalizedCost,
    },
    {
      heading: 'Adjusted Capitalized Cost',
      description:
        'The amount which is capitalized in connection with the subscription agreement and is used in...',
      cost: adjustedCapitalizedCost,
    },

    {
      heading: 'Gross Capitalized Cost.',
      description:
        'The agreed upon value of the Program Vehicle and any items you pay over the subscription term (such as service contracts, insurance, and any outstanding prior credit or subscription balance. You may receive a separate written itemization of this amount, if requested.',
      cost: grossCapitalizedCost,
    },
    {
      heading: 'Estimated Residual Value',
      description:
        'The estimated value of the Program Vehicle at the end of the subscription used in calculating your base weekly payment.',
      cost: residualValue,
    },
    {
      heading: 'Capitalized Cost Reduction',
      description:
        'The amount of any net trade-in allowance, rebate, non-cash credit, or cash you pay that reduces the gross capitalized cost.',
      cost: capitalizedCostReduction,
    },

    {
      heading: 'Depreciation and any Amortized Amounts',
      description:
        "The amount charged for the Program Vehicle's decline in value through normal use and for other items paid over the subscription term.",
      cost: depreciation,
    },

    {
      heading: 'Approximate Cost of Insurance Included in Subscription Charge',
      description: '',
      cost: costOfInsurance,
    },

    {
      heading: 'Subscription Charge',
      description: 'The amount charged in addition to the depreciation and any amortized amounts',
      cost: subscriptionCharge,
    },

    {
      heading: 'Total of Base Weekly Payments',
      description: 'The depreciation and any amortized amounts plus the subscription charge.',
      cost: totalBaseWeeklyPayments,
    },
    {
      heading: 'Subscription Term',
      description: 'The number of weeks in your subscription.',
      cost: subscriptionTerm,
    },
    {
      heading: 'Base Weekly Payment',
      description: '',
      cost: baseWeeklyPayment,
    },
    {
      heading: 'Weekly Sales/Use Tax Reimbursement',
      description: '',
      cost: weeklySalesUseTaxReimbursement,
    },
    {
      heading: 'Prop./Excise/Reg. Tax Reimbursement',
      description:
        "A monthly proration of a good-faith estimate of state and / or local personal property, excise or registration-based taxes imposed by governmental authorities based upon a vehicle's value, and such taxes and methodologies for assessing any vehicle’s value are subject to change",
      cost: propExciseRegTaxReimbursement,
    },
    {
      heading: 'Total Weekly Payment',
      description: '',
      cost: totalWeeklyPayment,
    },
    {
      heading: 'Additional Early Termination Charge',
      description:
        '(an additional amount the unamortized portion of which will be used in determining your early termination liability)',
      cost: additionalEarlyTerminationCharge,
    },
  ];

  return (
    <View>
      <View style={styles.table}>
        {arr.map((item) => {
          return (
            <View key={item.heading} style={styles.tableRow}>
              <View style={styles.tableCol}>
                <Text style={[styles.tableCell, styles.tableCellHeading]}>{item.heading}</Text>
                <Text style={[styles.tableCell, styles.letterPoint]}>{item.description}</Text>
              </View>
              <View style={styles.tableCol}>
                <Text style={[styles.tableCell, styles.letterPoint]}>{item.cost}</Text>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const TermsComp = () => {
  const arr = [
    {
      heading: 'Excessive Wear and Use',
      description:
        'You may be charged for excessive wear based on our standards for normal use and for mileage in excess of 4,200 miles per month at the rate of [$0.15] per mile. You may also be charged a late fee of $[         ] per day after the Date/Time Due In.',
    },
    {
      heading: 'Purchase Option at End of Subscription Term',
      description:
        'You have an option to purchase the Vehicle at the end of the subscription term. Please refer to your subscription agreement for additional information on purchase options.',
    },
    {
      heading: 'Other Important Terms',
      description:
        'See your subscription agreement for additional information on additional applicable fees and terms. If the Vehicle is not returned, you may be charged repossession and other fees. You may be charged the cost per gallon of fuel necessary to refuel any Vehicle returned with less than a half full tank of fuel or half full electric charge, which cost of fuel will be the actual per-gallon cost of premium fuel at a service station selected by OneCarNow! on the day the refueling occurs.',
    },
    {
      heading: 'Early Termination/Order Change',
      description: `Early termination may require you to pay a substantial charge. You may terminate this Agreement (including the Subscription Plan) or make changes to this Agreement (such as a change to the Program Vehicle, or the delivery date or location) within 48 hours after executing the Agreement and before pick up of the Program Vehicle at no charge or penalty to you.
        If you terminate this Agreement (including the Subscription Plan) more than 48 hours after executing this Agreement, but before delivery of the Program Vehicle, you will be charged only 
        an “Early Termination Fee” equal to one (1) week’s payment (and you will not be liable for the charges through the entire term of this Agreement under Section 8.3 of this Agreement).
        If you make a change to this Agreement (such as a change to the Program Vehicle, or the delivery date or location) more than 48 hours after executing this Agreement, but before delivery of the Program Vehicle, you will be charged only an “Order Change Fee” of $[	      ].
        If you make a change to this Agreement or to the Program Vehicle after the Program Vehicle has been delivered to you, you will be subject to an “Early Termination Fee” equal to four (4) week’s payment.
        If you terminate this Agreement (including the Subscription Plan) more than 48 hours after executing this Agreement, and after the Program Vehicle has been delivered to you, you will be responsible for the charges through the entire term of this Agreement under Section 8.3 of this Agreement plus an Early Termination Fee equal to four (4) week’s payment, and you will remain liable for any and all other amounts due under this Agreement, even if you end this Agreement (including the Subscription Plan) early.
        You shall not be liable for an early termination fee if you have deceased before the end of the Subscription Plan.`,
    },
  ];

  return (
    <>
      {arr.map((item) => {
        return (
          <View key={item.heading}>
            <Text style={styles.definicionesText}>{item.heading}</Text>
            <Text style={styles.letterPoint}>{item.description}</Text>
          </View>
        );
      })}
    </>
  );
};

const PaymentInfoPDF = ({ userData }: any) => {
  const { rentingProduct } = userData;

  const weeklyPayment = Number(rentingProduct.subTotal || 0);
  const methodOfPayment = 'Direct debit';

  return (
    <View>
      <Text style={styles.definicionesText}>Payment Information</Text>
      <Text style={styles.label}>Amount Due At Program Subscription Approval (itemized below)*: </Text>
      <View>
        <Text style={styles.label}>Weekly Payments:</Text>
        <Text style={styles.letterPoint}>
          {`Your first weekly payment of ${weeklyPayment} USD is due at time of subscriber's application approval, followed by payments of             
      ${weeklyPayment} USD due on Monday of each subsequent week.`}
        </Text>
      </View>

      <Text style={styles.label}>*Itemization of Amount Due Upon Program Subscription Approval</Text>
      <Text style={styles.label}>First weekly payment: {weeklyPayment}</Text>
      <Text style={styles.label}>Non-Refundable Initial Deposit:</Text>
      <Text style={styles.label}>Method of payment: {methodOfPayment}</Text>
      <Text style={styles.label}>Total initial payment: </Text>
    </View>
  );
};

const AdditionalFeesPDF = () => {
  const data = {
    section_1: [
      'i. Violation/Ticket Processing Fee (per violation/ticket): $7',
      'ii. Toll processing fee (per toll): $1',
      'iii. Chargeback Fee (per chargeback): $5',
      `iv. Unsuccessful or Delayed Pickup/Return: Actual costs incurred due
          to an unsuccessful or delayed pick up or return, calculated in
          accordance with Section 3.3.`,
      'v. Dunning Fee: $5 per reminder sent.',
      'vi. Order Change Fee: $100.00',
      'vii. Late Payment Fee: $5 per day',
    ],
    section_2: [
      "Subscriber agrees to use the vehicle in a safe and responsible manner, in accordance with the law, and to follow the manufacturer's instructions for vehicle use and maintenance.",
      'Financial Information for First Thirty Days of Subscription (additional subscription periods will substantially conform with the below financial information)',
    ],
  };

  return (
    <>
      <View>
        <Text style={styles.definicionesText}>Additional Potential Fees</Text>
        {data.section_1.map((item) => {
          return (
            <Text key={item} style={styles.letterPoint}>
              {item}
            </Text>
          );
        })}
      </View>

      <View>
        {data.section_2.map((item) => {
          return (
            <Text key={item} style={styles.label}>
              {item}
            </Text>
          );
        })}
      </View>
    </>
  );
};

const CoverPageComp = () => {
  const Heading = `This Cover Page may be modified in accordance with the terms of the OneCarNow! Subscription Agreement, including but not limited to Section 10.`;
  return (
    <View>
      <Text style={styles.definicionesText}>{Heading}</Text>
      <Text style={{ paddingTop: '2px', paddingBottom: '6px' }}>
        {` =========================================== `}
      </Text>
    </View>
  );
};

export function DocumentComponentUS({ form }: any) {
  return (
    <Document>
      <Page style={styles.page} size="A4" wrap>
        <Image style={styles.header} src={HeaderImg.src} fixed />
        <Text style={styles.headText} fixed>
          {'OneCarNow! Subscription Agreement Individual Terms'}
        </Text>

        <View style={styles.body}>
          <SubscriptionInformation userData={form} />
          <NoticeToTheSubscriber />
          <VehicleInformation userData={form} />
          <PaymentInfoPDF userData={form} />
          <AdditionalFeesPDF />
          <CapitalizedCostTablePDF />
          <TermsComp />
          <CoverPageComp />
        </View>
      </Page>
      <MotorVehicleRentalAgreement>
        <Signatures userData={form} />
      </MotorVehicleRentalAgreement>
    </Document>
  );
}
