/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import React, { createContext, useContext, useEffect, useReducer, useState } from 'react';
import reducer, { Filter } from './vehiclesReducer';
import { MyUser } from '@/actions/getCurrentUser';
import { VehicleCard } from '@/actions/getAllVehicles';

type State = {
  originalData: VehicleCard[];
  filteredData: VehicleCard[];
  filters: Filter[] | [];
};

interface RegionObject {
  value: string;
  label: string;
}

interface ContextProps {
  state: State;
  filterState: (region: Region) => void;
  removeFilters: () => void;
  regionSelected: RegionObject;
  filterSelectedState: Filter[];
  user: MyUser;
  addFilters: (filter: Filter[]) => void;
  removeFilterByName: (name: string) => void;
}

const VehicleContext = createContext<ContextProps | undefined>(undefined);

interface ProviderProps {
  children: React.ReactNode;
  initialState: State;
  user: MyUser;
}

export const useVehiclesContext = () => {
  const context = useContext(VehicleContext);
  if (!context) throw new Error('There is not vehicles context');
  return context;
};

export type Region =
  | '0'
  | '1'
  | '2'
  | '3'
  | '4'
  | '5'
  | '6'
  | '7'
  | '8'
  | '9'
  | '10'
  | '11'
  | '12'
  | '13'
  | '14'
  | '15'
  | '16'
  | '17'
  | '18'
  | '19'
  | '20'
  | '21';
export type Step = '0' | '1' | '2' | '3' | '4' | '5';

export const VehiclesContext = ({ children, initialState, user }: ProviderProps) => {
  const filtersSelected: Filter[] = JSON.parse(localStorage.getItem('filtersSelected') || JSON.stringify([]));
  const [filterSelectedState, setFilterSelectedState] = useState<Filter[]>(filtersSelected);

  const regionSelected: RegionObject = JSON.parse(
    localStorage.getItem('regionSelected') || JSON.stringify({ value: '0', label: 'Selecciona' })
  );

  const [state, dispatch] = useReducer(reducer, initialState);

  useEffect(() => {
    if (filtersSelected?.length > 0) {
      const regionFilter = filtersSelected.find((item) => item.filterName === 'region');
      if (regionFilter) {
        const regionValue = regionFilter.filterValue;
        if (Number(regionValue) > 0) {
          const filterFilters = filtersSelected.filter((item) => item.filterName !== 'region');
          if (filterFilters.length > 0) {
            addFilters(filterFilters);
            window.location.reload();
          } else {
            localStorage.removeItem('filtersSelected');
            window.location.reload();
          }
        }
      }
    }

    dispatch({ type: 'ADD_FILTERS', filter: filterSelectedState });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const filterState = (region: Region) => {
    // console.log('REGION', region);

    dispatch({ type: 'FILTER_BY_REGION', region });
  };

  const addFilters = (filters: Filter[]) => {
    const filtered = filters.filter((item) => item.filterValue !== '0');

    // console.log('all filters: ', filtered);

    setFilterSelectedState(filtered);
    dispatch({ type: 'ADD_FILTERS', filter: filtered });

    localStorage.setItem('filtersSelected', JSON.stringify(filtered));
  };

  const removeFilterByName = (name: string) => {
    const filters = filterSelectedState.filter((item) => item.filterName !== name);

    setFilterSelectedState(filters);
    dispatch({ type: 'ADD_FILTERS', filter: filters });

    localStorage.removeItem('filtersSelected');
    localStorage.setItem('filtersSelected', JSON.stringify(filters));
  };

  const removeFilters = () => {
    localStorage.removeItem('regionSelected');
    dispatch({ type: 'REMOVE FILTERS' });
  };

  return (
    <VehicleContext.Provider
      value={{
        state,
        filterState,
        removeFilters,
        regionSelected,
        user,
        filterSelectedState,
        addFilters,
        removeFilterByName,
      }}
    >
      {children}
    </VehicleContext.Provider>
  );
};

export default VehiclesContext;
