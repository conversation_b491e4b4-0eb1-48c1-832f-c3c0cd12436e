import getCurrentUser from '@/actions/getCurrentUser';
import getVehiclesWithQR from '@/actions/getVehiclesWithQR';
import VehiclesWithQRTable from '../components/VehiclesWithQRTable';
import PrintQRButton from '../components/PrintQRButton';

export const metadata = {
  title: 'Códigos Qr de vehículos',
  description: 'Códigos Qr de vehículos',
};

export default async function CodigosQrPage() {
  const user = await getCurrentUser();

  if (!user) return null;

  // Fetch vehicles with QR codes from the API
  const vehicles = await getVehiclesWithQR();

  return (
    <div className="w-full">
      {/* Header with count and Print QR button */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold text-gray-800">Códigos QR ({vehicles.length})</h1>
        </div>
        <PrintQRButton />
      </div>

      {/* Table without the Print QR button (now in header) */}
      <VehiclesWithQRTable vehicles={vehicles} showPrintButton={false} />
    </div>
  );
}
