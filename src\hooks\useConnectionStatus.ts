import { useEffect } from 'react';
import { useToast } from '@chakra-ui/react';
import { Countries } from '@/constants';
import { connectionTranslationsMX, connectionTranslationsUS } from '@/constants/translations';

/**
 * Hook to notify user when network connection is lost or restored.
 * @param country The selected country to pick appropriate translations
 */
export function useConnectionStatus(country: Countries) {
  const toast = useToast();

  useEffect(() => {
    const texts =
      country === Countries['United States'] ? connectionTranslationsUS : connectionTranslationsMX;
    function handleOffline() {
      toast({
        title: texts.lostTitle,
        description: texts.lostDescription,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }

    function handleOnline() {
      toast({
        title: texts.restoredTitle,
        description: texts.restoredDescription,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }

    window.addEventListener('offline', handleOffline);
    window.addEventListener('online', handleOnline);

    return () => {
      window.removeEventListener('offline', handleOffline);
      window.removeEventListener('online', handleOnline);
    };
  }, [toast, country]);
}
