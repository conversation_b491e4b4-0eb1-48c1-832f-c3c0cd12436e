'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalBody,
  ModalCloseButton,
  VStack,
  <PERSON>ner,
  Alert,
  AlertIcon,
  useColorModeValue,
} from '@chakra-ui/react';
import { confirmQrStatusChange } from '@/services/vehicleQrService';
import { Countries, URL_API, VENDOR_WORKSHOPS } from '@/constants';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { PHYSICAL_STATUS_MAP, PHYSICAL_STATUS_MAP_ES, PhysicalVehicleStatus } from '@/constants';
import { physicalStatusTranslations } from '../translations/physicalStatusTranslations';
import StatusDisplay from '../others/StatusDisplay';
import VendorWorkshopSelector from '../others/VendorWorkshopSelector';
import CameraSection from '../others/CameraSection';
import StatusSelection from '../StatusSelection';
import { useCamera } from '@/hooks/useCamera';

export const getStatusLabel = (
  statusValue: PhysicalVehicleStatus | string | null,
  country?: string
): string => {
  if (!statusValue) return 'N/A';
  const statusKey = String(statusValue).toUpperCase();
  if (country === Countries['United States']) {
    return PHYSICAL_STATUS_MAP[statusKey] || String(statusValue);
  }

  return PHYSICAL_STATUS_MAP_ES[statusKey] || String(statusValue);
};

// Custom function for modal option labels only
export const getModalOptionLabel = (
  statusValue: PhysicalVehicleStatus | string | null,
  countryParam?: string,
  currentVehicleStatus?: PhysicalVehicleStatus | string | null
): string => {
  if (!statusValue) return 'N/A';

  const statusKey = String(statusValue).toUpperCase() as PhysicalVehicleStatus;
  const deliveredToCustomerStatuses = [
    PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER,
    PhysicalVehicleStatus.REDELIVERED_TO_CUSTOMER,
    PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER,
  ];

  if (
    deliveredToCustomerStatuses.includes(currentVehicleStatus as PhysicalVehicleStatus) &&
    statusKey === PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED
  ) {
    return getStatusLabel(statusKey, countryParam);
  }

  if (statusKey === PhysicalVehicleStatus.AVAILABLE_IN_STOCK) {
    return countryParam === Countries['United States']
      ? physicalStatusTranslations[Countries['United States']].inspectionCompleted
      : physicalStatusTranslations[Countries.Mexico].inspectionCompleted;
  } else if (statusKey === PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED) {
    return countryParam === Countries['United States']
      ? physicalStatusTranslations[Countries['United States']].inspectionFailed
      : physicalStatusTranslations[Countries.Mexico].inspectionFailed;
  }

  return getStatusLabel(statusValue, countryParam);
};

interface PhysicalStatusUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicleId: string;
  currentStatus: PhysicalVehicleStatus | string | null;
  nextStatusToDisplay: PhysicalVehicleStatus | string | null;
  nextStepOptions?: { value: PhysicalVehicleStatus; label: string }[];
  confirmationToken: string | null;
  message: string | null;
  actionAvailable: boolean;
  accessToken: string;
  onStatusConfirmed: (newStatus: PhysicalVehicleStatus) => void;
}

const PhysicalStatusUpdateModal: React.FC<PhysicalStatusUpdateModalProps> = ({
  isOpen,
  onClose,
  vehicleId,
  currentStatus,
  nextStatusToDisplay,
  nextStepOptions,
  confirmationToken,
  actionAvailable,
  accessToken,
  onStatusConfirmed,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedNextStatus, setSelectedNextStatus] = useState<PhysicalVehicleStatus | null>(null);
  const { country } = useCountry();

  // New state for vendor workshops
  const [selectedVendorRegion, setSelectedVendorRegion] = useState<string | null>(null);
  const [selectedVendorWorkshop, setSelectedVendorWorkshop] = useState<string | null>(null);
  const [availableWorkshops, setAvailableWorkshops] = useState<
    { name: string; address: string; phone: string }[]
  >([]);

  const translatedText =
    physicalStatusTranslations[country.value as keyof typeof physicalStatusTranslations] ||
    physicalStatusTranslations[Countries.Mexico];

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headerColor = useColorModeValue('gray.700', 'white');
  const textColor = useColorModeValue('gray.600', 'gray.200');
  const strongTextColor = useColorModeValue('gray.800', 'white');

  const primaryButtonColorScheme = 'purple';

  const {
    isCameraOpen,
    isCameraAvailable,
    isUploading,
    photoPreviewUrl,
    photoPath,
    openCamera,
    closeCamera,
    takePhoto,
    setPhotoPreviewUrl,
    setPhotoPath,
    videoRef,
    facingMode,
    toggleFacingMode,
  } = useCamera({
    vehicleId,
    accessToken,
    uploadUrl: `${URL_API}/stock/upload-qr-photo`,
  });

  // Effect to set initial selected status if options are present and nextStatusToDisplay is not
  useEffect(() => {
    if (isOpen) {
      setError(null); // Reset error on open
      if (nextStepOptions && nextStepOptions.length > 0) {
        setSelectedNextStatus(null); // Reset selection when options are present
      } else if (nextStatusToDisplay) {
        setSelectedNextStatus(nextStatusToDisplay as PhysicalVehicleStatus);
      } else {
        setSelectedNextStatus(null);
      }
      // Reset vendor workshop states
      setSelectedVendorRegion(null);
      setSelectedVendorWorkshop(null);
      setAvailableWorkshops([]);
    }
  }, [isOpen, nextStepOptions, nextStatusToDisplay]);

  // Effect to update available workshops when region changes
  useEffect(() => {
    if (selectedVendorRegion) {
      const regionData = VENDOR_WORKSHOPS.find((r) => r.region === selectedVendorRegion);
      setAvailableWorkshops(regionData ? regionData.workshops : []);
      setSelectedVendorWorkshop(null); // Reset workshop selection
    } else {
      setAvailableWorkshops([]);
      setSelectedVendorWorkshop(null);
    }
  }, [selectedVendorRegion]);

  // Cleanup media stream when component unmounts or modal closes
  useEffect(() => {
    if (!isOpen) {
      closeCamera();
    }
  }, [isOpen]);

  const handleConfirm = async () => {
    const statusToConfirm =
      nextStepOptions && nextStepOptions.length > 0
        ? selectedNextStatus
        : (nextStatusToDisplay as PhysicalVehicleStatus);

    if (!statusToConfirm || !confirmationToken) {
      setError(translatedText.incompleteInfo);
      return;
    }

    if (!photoPath) {
      setError(translatedText.photoTexts.photoMissing);
      return;
    }

    if (
      statusToConfirm === PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP &&
      (!selectedVendorRegion || !selectedVendorWorkshop)
    ) {
      setError(translatedText.selectRegionAndWorkshopError);
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      const payload: any = {
        vehicleId,
        confirmedNextStatus: statusToConfirm,
        confirmationToken,
        accessToken,
        photoPath,
      };

      if (statusToConfirm === PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP) {
        payload.vendorRegion = selectedVendorRegion;
        payload.vendorWorkshopName = selectedVendorWorkshop;
      }

      const response = await confirmQrStatusChange(payload);
      if (response.success && response.newPhysicalStatus) {
        onStatusConfirmed(response.newPhysicalStatus as PhysicalVehicleStatus);
        onClose();
      } else {
        setError(response.message || translatedText.genericError);
      }
    } catch (err: any) {
      setError(err.message || translatedText.genericError);
    } finally {
      setIsLoading(false);
    }
  };

  const effectiveNextStatus =
    nextStepOptions && nextStepOptions.length > 0 ? selectedNextStatus : nextStatusToDisplay;
  const showVendorDropdowns = effectiveNextStatus === PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP;

  return (
    <Modal isOpen={isOpen} onClose={onClose} isCentered size="lg">
      <ModalOverlay bg="blackAlpha.300" />
      <ModalContent bg={bgColor} borderRadius="lg" boxShadow="xl">
        <ModalHeader
          fontWeight="bold"
          color={headerColor}
          borderBottomWidth="1px"
          borderColor={borderColor}
          py={4}
        >
          {translatedText.modalTitle}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody py={6} px={6}>
          <VStack spacing={4} align="stretch">
            {currentStatus && (
              <StatusDisplay
                label={translatedText.currentStatus}
                value={getStatusLabel(currentStatus, country.value)}
                labelColor={textColor}
                valueColor={strongTextColor}
              />
            )}

            {actionAvailable && nextStatusToDisplay && !(nextStepOptions && nextStepOptions.length > 0) && (
              <StatusDisplay
                label={translatedText.proposedStatus}
                value={getStatusLabel(nextStatusToDisplay, country.value)}
                labelColor={textColor}
                valueColor={strongTextColor}
              />
            )}

            {actionAvailable && nextStepOptions && nextStepOptions.length > 0 && (
              <StatusSelection
                options={nextStepOptions}
                selectedValue={selectedNextStatus}
                onChange={setSelectedNextStatus}
                getOptionLabel={getModalOptionLabel}
                currentStatus={currentStatus}
                countryValue={country.value}
                translatedText={translatedText}
                colorScheme={primaryButtonColorScheme}
                borderColor={borderColor}
                textColor={textColor}
              />
            )}

            {/* Vendor Workshop Dropdowns */}
            {showVendorDropdowns && (
              <VendorWorkshopSelector
                regions={VENDOR_WORKSHOPS}
                selectedRegion={selectedVendorRegion}
                onRegionChange={setSelectedVendorRegion}
                availableWorkshops={availableWorkshops}
                selectedWorkshop={selectedVendorWorkshop}
                onWorkshopChange={setSelectedVendorWorkshop}
                translations={translatedText}
                borderColor={borderColor}
                textColor={textColor}
                primaryButtonColorScheme={primaryButtonColorScheme}
              />
            )}

            {error && (
              <Alert status="error" borderRadius="md" variant="left-accent">
                <AlertIcon />
                {error}
              </Alert>
            )}

            {/* Camera Section */}
            <CameraSection
              isCameraOpen={isCameraOpen}
              isCameraAvailable={isCameraAvailable}
              isUploading={isUploading}
              photoPreviewUrl={photoPreviewUrl}
              photoTexts={translatedText.photoTexts}
              openCamera={openCamera}
              takePhoto={takePhoto}
              setPhotoPreviewUrl={setPhotoPreviewUrl}
              setPhotoPath={setPhotoPath}
              videoRef={videoRef}
              borderColor={borderColor}
              textColor={textColor}
              facingMode={facingMode}
              toggleFacingMode={toggleFacingMode}
            />
          </VStack>
        </ModalBody>
        <ModalFooter borderTopWidth="1px" borderColor={borderColor} py={4}>
          <Button
            onClick={onClose}
            mr={3}
            isDisabled={isLoading}
            variant="outline"
            borderWidth="2px"
            borderColor="#5800F7"
            color="#5800F7"
            h="40px"
            _hover={{ bg: 'rgba(88, 0, 247, 0.1)' }}
          >
            {translatedText.cancel}
          </Button>

          {actionAvailable && confirmationToken && (
            <Button
              onClick={handleConfirm}
              isLoading={isLoading}
              isDisabled={
                isLoading ||
                !photoPath ||
                (nextStepOptions && nextStepOptions.length > 0 && !selectedNextStatus) ||
                (showVendorDropdowns && (!selectedVendorRegion || !selectedVendorWorkshop))
              }
              spinner={<Spinner size="sm" color="white" />}
              loadingText={translatedText.confirming}
              className={`
                text-white rounded-md h-[40px] px-4
                ${
                  isLoading ||
                  !photoPath ||
                  (nextStepOptions && nextStepOptions.length > 0 && !selectedNextStatus) ||
                  (showVendorDropdowns && (!selectedVendorRegion || !selectedVendorWorkshop))
                    ? 'bg-[#9CA3AF] cursor-not-allowed'
                    : 'bg-[#5800F7] hover:bg-[#4A00D1]'
                }
              `}
            >
              {!isLoading &&
                (nextStepOptions && nextStepOptions.length > 0
                  ? selectedNextStatus
                    ? translatedText.confirm
                    : translatedText.selectOption
                  : translatedText.confirm)}
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PhysicalStatusUpdateModal;
