/* eslint-disable consistent-return */
'use client';
import ChangePasswordForm from '@/components/Form/ChangePasswordForm';
import { useRouter } from 'next/navigation';
import { Formik, FormikState, FormikValues } from 'formik';
import { changePasswordValidation } from '../Form/YupValidator';
import { IconContext } from 'react-icons/lib';
import { useEffect, useMemo, useState } from 'react';
import Spinner from '../Loading/Spinner';
import changePassword from '@/actions/changePassword';
import { useToast } from '@chakra-ui/react';

type HandleSubmitType = (
  values: FormikValues,
  {
    resetForm,
  }: {
    resetForm: (nextState?: Partial<FormikState<FormikValues>> | undefined) => void;
  }
) => Promise<any>;

export default function AuthChangePassword({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const router = useRouter();
  // const { data: session, status } = useSession();
  const code = searchParams?.code;

  const [isInitialValid, setIsInitialValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const defaultValues = useMemo(() => {
    return {
      password: '',
      confirmPassword: '',
    };
  }, []);

  if (!code) {
    router.push('/');
  }

  useEffect(() => {
    changePasswordValidation
      .validate(defaultValues)
      .then(() => setIsInitialValid(true))
      .catch(() => setIsInitialValid(false));
  }, [defaultValues, isInitialValid]);

  const toast = useToast();

  const handleSubmit: HandleSubmitType = async (values, { resetForm }) => {
    setIsLoading(true);
    if (code) {
      const result = await changePassword({ code: code as string, password: values.password });

      if (!result) {
        return toast({
          title: 'Error',
          description: 'Error al cambiar la contraseña',
          status: 'error',
          position: 'top',
          duration: 3000,
          isClosable: true,
        });
      } else {
        resetForm();
        toast({
          title: 'Tu contraseña ha sido cambiada exitosamente',
          description: 'Redirigiendo al inicio de sesión',
          status: 'success',
          position: 'top',
          duration: 3000,
          isClosable: true,
        });
        setTimeout(() => {
          return router.push('/');
        }, 3000);
      }
    }
  };
  if (isLoading) {
    return <Spinner />;
  }

  return (
    <IconContext.Provider value={{ style: { strokeWidth: '1px' } }}>
      <Formik
        initialValues={defaultValues}
        validationSchema={changePasswordValidation}
        onSubmit={handleSubmit}
        validateOnMount={true}
      >
        {({ isValid, dirty }) => <ChangePasswordForm valid={isValid} dirty={dirty} />}
      </Formik>
    </IconContext.Provider>
  );
}
