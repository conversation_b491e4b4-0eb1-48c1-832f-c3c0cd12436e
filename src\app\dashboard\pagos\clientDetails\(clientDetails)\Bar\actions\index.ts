'use server';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';

export async function removeClient(clientId: string) {
  try {
    const res = await axios.delete(`${PAYMENTS_API_URL}/clients/${clientId}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    return {
      success: true,
      message: res.data.data?.message || 'Cliente eliminado correctamente',
    };
  } catch (error: any) {
    console.log(error.response.data);
    return {
      success: false,
      message: error.response?.data?.message || 'Error al eliminar el cliente',
    };
  }
}

export async function runSubscription(id: string) {
  try {
    const res = await axios.post(
      `${PAYMENTS_API_URL}/subscriptions/create-single-payment/${id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      }
    );
    return {
      success: true,
      message: res.data.data?.message || 'Suscripción ejecutada correctamente',
    };
  } catch (error: any) {
    console.log(error.response.data);
    return {
      success: false,
      message: error.response?.data?.message || 'Error al ejecutar la suscripción',
    };
  }
}

export async function removeAdendumProducts(id: string) {
  try {
    const res = await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/${id}/remove-temporal-products`,
      {},
      {
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      }
    );
    return {
      success: true,
      message: res.data.data?.message || 'Productos eliminados correctamente',
    };
  } catch (error: any) {
    console.log(error.response.data);
    // return false;
    return {
      success: false,
      message: error.response?.data?.message || 'Error al eliminar los productos',
    };
  }
}
