import * as Yup from 'yup';
// import { fileListValidator, fileValidator } from './filesValidators';

export const uploadSignedDocsSchemaAdmin = Yup.object().shape({
  // contract: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // promissoryNote: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // deliveryReceipt: fileValidator('Debe seleccionar un archivo', 'pdf'),
});

export const uploadSignedDocsSchema = Yup.object().shape({
  // contract: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // promissoryNote: fileValidator('Debe seleccionar un archivo', 'pdf'),
  // deliveredImages: fileListValidator('Debe seleccionar el total de archivos requeridos', 'all-images', 5),
});
