import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';
import VehicleTable from '../../components/VehicleTable';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function WorkshopPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters: filters });
  //console.log(`definitiveFilters: ${definitiveFilters}`);

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 10,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'inactive',
      category: definitiveFilters?.category || 'workshop',
      subCategory: definitiveFilters?.subCategory || 'aesthetic-repair',
      country: definitiveFilters?.country,
    },
  });

  const subOptions = [
    { label: 'Aesthetic Repair', name: 'aesthetic-repair' },
    { label: 'Duplicate Key Missing', name: 'duplicate-key-missing' },
    { label: 'Mechanical Repair', name: 'mechanical-repair' },
    { label: 'Electrical Repair', name: 'electrical-repair' },
    { label: 'Engine Repair', name: 'engine-repair' },
    { label: 'Waiting For Parts', name: 'waiting-for-parts' },
    { label: 'Corrective Maintenance', name: 'corrective-maintenance' },
  ];

  const page = {
    name: 'Inactive',
    api: 'inactive',
    count: 12,
  };

  const subPage = {
    name: 'Workshop',
    api: 'workshop',
    count: 12,
  };

  if (!result) return null;

  return (
    <VehicleTable
      route="workshop"
      page={page}
      subPage={subPage}
      data={result.stock}
      totalCount={result.totalCount}
      subOptions={subOptions}
    />
  );
}
