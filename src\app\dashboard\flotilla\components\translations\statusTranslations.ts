export const StatusTranslations = {
  active: 'Active',
  inactive: 'Inactive',
};

export const StatusTranslationsMX = {
  active: 'Activo',
  inactive: 'Inactivo',
};

export const VehicleCategoryTranslations = {
  withdrawn: 'Withdrawn',
  sold: 'Sold',
  insurance: 'Insurance',
  collection: 'Collection',
  legal: 'Legal',
  workshop: 'Workshop',
  revision: 'Revision',
  adendum: 'Adendum',
  'in-preparation': 'In Preparation',
  stock: 'Stock',
  assigned: 'Assigned',
  delivered: 'Delivered',
  utilitary: 'Utilitary',
};

export const VehicleCategoryTranslationsMX = {
  withdrawn: 'Dado de Baja',
  sold: 'Vendido',
  insurance: 'Seguro',
  collection: 'Cobranza',
  legal: 'Legal',
  workshop: 'Taller',
  revision: 'Revisión',
  adendum: 'Adendum',
  'in-preparation': 'En Preparación',
  stock: 'Stock',
  assigned: 'Asignado',
  delivered: 'Entregado',
  utilitary: 'Utilitario',
};

export const VehicleSubCategoryTranslations = {
  'damage-payment': 'Damage Payment',
  valuation: 'Valuation',
  repair: 'Repair',
  'payment-commitment': 'Payment Commitment',
  'payment-extension': 'Payment Extension',
  'non-payment': 'Non Payment',
  'incomplete-payment': 'Incomplete Payment',
  'in-recovery': 'In Recovery',
  demand: 'Demand',
  'public-ministry': 'Public Ministry',
  complaint: 'Complaint',
  impounded: 'Impounded',
  'aesthetic-repair': 'Aesthetic Repair',
  'duplicate-key-missing': 'Duplicate Key Missing',
  'mechanical-repair': 'Mechanical Repair',
  'electrical-repair': 'Electrical Repair',
  'engine-repair': 'Engine Repair',
  'waiting-for-parts': 'Waiting For Parts',
  'corrective-maintenance': 'Corrective Maintenance',
  management: 'Management',
  gps: 'GPS',
  'total-loss': 'Total Loss',
  'operational-loss': 'Operational Loss',
};

export const VehicleSubCategoryTranslationsMX = {
  'damage-payment': 'Pago por Daños',
  valuation: 'Valuación',
  repair: 'Reparación',
  'payment-commitment': 'Compromiso de Pago',
  'payment-extension': 'Extensión de Pago',
  'non-payment': 'Sin Pago',
  'incomplete-payment': 'Pago Incompleto',
  'in-recovery': 'En Recuperación',
  demand: 'Demanda',
  'public-ministry': 'Ministerio Público',
  complaint: 'Querella',
  impounded: 'Corralón',
  'aesthetic-repair': 'Reparación Estética',
  'duplicate-key-missing': 'Falta Duplicado de Llave',
  'mechanical-repair': 'Reparación Mecánica',
  'electrical-repair': 'Reparación Eléctrica',
  'engine-repair': 'Reparación de Motor',
  'waiting-for-parts': 'En Espera de Refacciones',
  'corrective-maintenance': 'Mantenimiento Correctivo',
  management: 'Gestoría',
  gps: 'GPS',
  'total-loss': 'Pérdida Total',
  'operational-loss': 'Pérdida Operacional',
};

export const BadgeTranslationsMX = {
  expired: 'Caducado',
  latest: 'Último',
  required: 'Requerido',
};

export const BadgeTranslations = {
  expired: 'Expired',
  latest: 'Latest',
  required: 'Required',
};
