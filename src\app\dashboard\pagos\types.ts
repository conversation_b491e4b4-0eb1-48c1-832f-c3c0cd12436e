export type User = {
  id: string;
  contractNumber: string | null;
  name: string;
  lastName: string;
  email: string;
  phone: string;
  rfc: string;
  street: string | null;
  monexClabe: string | null;
  country: string;
  tax_system: string | null;
  legal_name: string;
  zip: string;
  is_valid_tax_info: boolean;
  region: string;
  gigId: string;
  associateId: string;
  isActive: boolean;
  facturapiId: string | null;
  createdAt: string;
  stripeCustomer?: {
    id: string;
    isCustomerBankAccountLinked: boolean;
  };
};

export type Subcription = {
  id: string;
  isActive: boolean;
  total: number | null;
  subTotal: number | null;
  tax: number | null;
  client: User;
};

export type Payment = {
  id: string;
  type: 'subscription' | 'isolated';
  isPaid: boolean;
  status: string | null;
  total: number | null;
  subTotal: number | null;
  tax: number | null;
  invoiceId: string | undefined;
  receiptId: string | undefined;
  paidAt: string | null;
  createdAt: string | null;
  updatedAt: string | null;
  client: User;
  subscription: Subcription | null;
};

export type RecurringPayment = {
  id: string;
  paymentNumber: number;
  isActive: boolean;
  total: number;
  subTotal: number;
  tax: number;
  startDate: string;
  endDate: string;
  region: string;
  createdAt: string;
  updatedAt: string;
  use_cfdi: string;
  use_cfdi_description: string;
  payment_form: string;
  products: Product[];
  client: User;
};

export type Product = {
  id: string;
  name: string;
  description: string | null;
  price: number;
  subTotal: number;
  tax: number;
  product_key: string;
  unit_key: string;
  measurementUnit: string;
  taxRate: number;
  taxFactor: string;
  taxType: string;
  withHolding: string;
  ivaIncluded: boolean;
  region: string;
  createdAt: string;
  quantity?: number;
  discount?: number;
  total?: number;
};

export type Invoice = {
  id: string;
  organization: string;
  created_at: string;
  date: string;
  livemode: boolean;
  payment_form: string;
  payment_method: string;
  currency: string;
  exchange: number;
  uuid: string;
  customer: Customer;
  total: number;
  use: string;
  folio_number: number;
  series: string;
  is_ready_to_stamp: boolean;
  items: InvoiceItem[];
  cfdi_version: number;
  address: InvoiceAddress;
  amount_due: number;
  verification_url: string;
  status: string;
  type: string;
  issuer_type: string;
  issuer_info: InvoiceIssuerInfo;
  cancellation_status: string;
  stamp: InvoiceStamp;
  export: string;
};

export type Customer = {
  id: string;
  legal_name: string;
  tax_system: string;
  tax_id: string;
  address: Address;
};

export type Address = {
  country: string;
  zip: string;
};

export type InvoiceItem = {
  quantity: number;
  discount: number;
  product: InvoiceProduct;
};

export type InvoiceProduct = {
  description: string;
  product_key: string;
  unit_key: string;
  unit_name: string;
  price: number;
  tax_included: boolean;
  taxes: InvoiceProductTax[];
  sku: string;
  taxability: string;
};

export type InvoiceProductTax = {
  base: string | null;
  rate: number;
  type: string;
  withholding: boolean;
  factor: string;
  ieps_mode: string;
};

export type InvoiceAddress = {
  street: string | null;
  exterior: string | null;
  interior: string | null;
  neighborhood: string | null;
  city: string | null;
  municipality: string | null;
  state: string | null;
  country: string | null;
  zip: string | null;
};

export type InvoiceStamp = {
  date: string;
  sat_signature: string;
  sat_cert_numbers: string;
  signature: string;
  complement_string: string;
};

export type InvoiceIssuerInfo = {
  legal_name: string;
  tax_id: string;
  tax_system: string;
  address: InvoiceAddress;
};

export type Receipt = {
  api_version: number;
  livemode: boolean;
  folio_number: number;
  branch: string | null;
  payment_form: string;
  items: InvoiceProduct[];
  currency: string;
  exchange: number;
  total: number;
  invoice: string | null;
  expires_at: string;
  key: string;
  status: string;
  created_at: string;
  date: string;
  id: string;
  self_invoice_url: string;
};

export type PaymentSchedule = {
  termNo: string;
  streamNo: string;
  contractNumber: string;
  associateId: string;
  paymentId: string;
  amount: number;
  dueDate: string;
  lateFeeApplied: boolean;
  lateFeePaid: boolean;
  paymentStatus: string;
  totalContactValue: number;
  amountOutstanding: number;
  duration: any;
  payments: any[];
  fee: number;
};
