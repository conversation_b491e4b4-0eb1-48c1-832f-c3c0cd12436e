import { Capabilities } from '@/constants';
import { AppAbility } from './createAbility';

export const canPerformAny = (ability: AppAbility | null, section: string): boolean => {
  if (!ability) return false;

  return ability.rules.some((rule) => {
    if (typeof rule.subject === 'string') {
      return rule.subject.startsWith(`${section}:`) && rule.action === Capabilities.View;
    }
    return false;
  });
};
