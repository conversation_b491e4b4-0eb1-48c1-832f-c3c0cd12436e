import SessionProvider from './SessionProvider';
// import Providers from '@/Providers/ChakraProvider';
import ChakraProvider from '@/Providers/ChakraProvider';
import './globals.css';
import { Inter } from 'next/font/google';
import { Metadata } from 'next';
import icon from '@/assets/OCNLogo.svg';
import { Toaster } from '@/components/ui/sonner';
import { cn } from '@/lib/utils';
import 'react-photo-view/dist/react-photo-view.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Administrador by OCN',
  description: 'Administrador by OCN',
  icons: {
    icon: icon.src,
  },
  manifest: '/manifest.json',
  // themeColor: '#000000',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <SessionProvider>
        <body className={cn(inter.className, '!min-h-[100svh]')}>
          <ChakraProvider>{children}</ChakraProvider>
          <Toaster position="bottom-center" richColors />
        </body>
      </SessionProvider>
    </html>
  );
}
