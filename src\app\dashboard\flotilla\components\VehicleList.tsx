// 'use client';
// import Link from 'next/link';
// import Card from '@/components/stockCard/Card';
// // import { useState /* , useEffect, useReducer */ } from 'react';
// // import FilterBar from './FilterBar/FilterBar';
// // import { useVehiclesContext } from './vehiclesContext';
// // import { GrClose } from 'react-icons/gr';
// // import { AiOutlineClose } from 'react-icons/ai';
// // import { CONTRACT_REGIONS, statusSelected, stepsSelect } from '@/constants';
// import { getStockVehicles, VehicleCard } from '@/actions/getAllVehicles';
// import InfiniteScroll from 'react-infinite-scroll-component';
// import Bar from './PageBar/Bar';
// import { useState } from 'react';
// import { listStatus } from '../activos/[id]/lib';

// // function returnValueFilter(filterArray: any[]) {
// //   return filterArray.map((item) => {
// //     if (item.filterName === 'region') {
// //       return CONTRACT_REGIONS.find((r) => r.value === item.filterValue);
// //     }
// //     if (item.filterName === 'step') {
// //       return stepsSelect.find((r) => r.value === item.filterValue);
// //     }
// //     if (item.filterName === 'new') {
// //       return JSON.parse(item.filterValue || 'null') === true
// //         ? { label: 'Nuevo', value: '3' }
// //         : { label: 'Seminuevo', value: '3' };
// //     }
// //     if (item.filterName === 'status') {
// //       return statusSelected.find((r) => r.value === item.filterValue);
// //     }
// //     // if(item.filterName === 'R')
// //     return undefined;
// //   });
// // }

// interface FlotillaCardsProps {
//   page: string;
//   data: VehicleCard[];
//   totalCount: number;
//   route: string;
// }

// export default function StockVehicleList({ page, data, totalCount, route }: FlotillaCardsProps) {
//   // const { filterSelectedState, removeFilterByName, state } = useVehiclesContext();
//   // const results = returnValueFilter(filterSelectedState);
//   const [numberPage, setNumberPage] = useState(0);
//   const [dataState, setDataState] = useState(data);
//   const [hasMore, setHasMore] = useState(true);
//   console.log('data', data.length, data);

//   const fetchData = async () => {
//     // console.log('fetchData');
//     const newPage = numberPage + 1;
//     setNumberPage(newPage);

//     // const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

//     // await sleep(3000);

//     const result = await getStockVehicles({ page: newPage, limit: 50, listStatus });
//     if (!result) return null;
//     if (result.stock.length === 0) {
//       setHasMore(false);
//       return false;
//     } else {
//       setDataState([...dataState, ...result.stock]);
//     }

//     if (dataState.length >= totalCount) {
//       setHasMore(false);
//       return false;
//     }
//     return false;

//     // if (dataState.length >= totalCount) {
//     //   setHasMore(false);
//     //   return false;
//     // }

//     // setDataState([...dataState, ...result.stock]);
//     // return true;
//   };
//   console.log(
//     'hasMore',
//     dataState.length < totalCount,
//     dataState.length,
//     totalCount,
//     totalCount / 50,
//     numberPage
//   );
//   return (
//     <div className="flex flex-col h-full ">
//       <Bar page={page} />
//       <p> Resultados: {totalCount} </p>

//       <InfiniteScroll
//         dataLength={dataState.length} //This is important field to render the next data
//         next={fetchData}
//         hasMore={hasMore}
//         // hasMore={dataState.length < totalCount}
//         // loader={<h4>Loading...</h4>}
//         className="h-full"
//         style={{ height: '100% !important' }}
//         loader={<h4>Cargando...</h4>}
//         endMessage={
//           <p className="text-center mt-10 mb-6 " /* style={{ textAlign: 'center' }} */>
//             <b>You have seen it all</b>
//           </p>
//         }
//         // below props only if you need pull down functionality
//         // refreshFunction={this.refresh}

//         refreshFunction={fetchData}
//         pullDownToRefresh
//         pullDownToRefreshThreshold={50}
//         pullDownToRefreshContent={<h3 style={{ textAlign: 'center' }}>&#8595; Pull down to refresh</h3>}
//         releaseToRefreshContent={<h3 style={{ textAlign: 'center' }}>&#8593; Release to refresh</h3>}
//       >
//         <div className="flex flex-row flex-wrap h-full mt-8 gap-x-5 gap-y-5 ">
//           {dataState.map((car, i) => {
//             return <Row car={car} index={i} route={route} key={i} />;
//           })}
//         </div>
//       </InfiniteScroll>
//     </div>
//   );
// }

// function Row({ car, route }: { car: VehicleCard; index: number; route: string }) {
//   return (
//     <Link href={`/dashboard/flotilla/${route}/${car._id}`} prefetch={false}>
//       <Card
//         color={car.color}
//         contract={car.carNumber}
//         brand={car.brand}
//         model={car.model}
//         status={car.status}
//         newCar={car.newCar}
//         step={car.step}
//         extensionCarNumber={car.extensionCarNumber}
//         // key={key}
//         dischargedReason={car.dischargedData?.reason}
//         isBlocked={car.isBlocked}
//       />
//     </Link>
//   );
// }
