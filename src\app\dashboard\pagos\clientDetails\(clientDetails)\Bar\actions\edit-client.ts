'use server';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';

export async function editClient(clientId: string, body: Record<string, any> = {}) {
  try {
    const url = new URL(`${PAYMENTS_API_URL}/clients/${clientId}`);

    console.log('tax_system', body.tax_system);
    const res = await axios.patch(`${url}`, body, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    return {
      success: true,
      message: res.data.data?.message || 'Cliente actualizado correctamente',
    };
  } catch (error: any) {
    console.log(error.response?.data);
    return {
      success: false,
      message: error.response?.data?.message || 'Hubo un error al actualizar el cliente',
    };
  }
}
