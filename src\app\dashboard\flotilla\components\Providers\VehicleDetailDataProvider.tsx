// create a provider to share data between components
// import { Associate } from '@/actions/getDriverById';
import { Driver, VehicleResponse } from '@/actions/getVehicleData';
import React, { createContext, useContext, useState, Dispatch, SetStateAction } from 'react';

export interface AssociateData extends Driver {
  contractData: VehicleResponse['drivers'][number]['contractData'];
}

interface DataProviderProps {
  children: React.ReactNode;
  associateData: AssociateData;
  vehicleData: VehicleResponse;
}

interface DataProviderValue {
  associateData: AssociateData;
  setAssociateData: Dispatch<SetStateAction<AssociateData>>;
  vehicleData: VehicleResponse;
  setVehicleData: Dispatch<SetStateAction<VehicleResponse>>;
}

const DataContext = createContext<DataProviderValue | undefined>(undefined);

export default function VehicleDetailDataProvider({
  children,
  associateData: assoData,
  vehicleData: vehiData,
}: DataProviderProps) {
  const [associateData, setAssociateData] = useState(assoData);
  const [vehicleData, setVehicleData] = useState(vehiData);

  return (
    <DataContext.Provider value={{ associateData, setAssociateData, vehicleData, setVehicleData }}>
      {children}
    </DataContext.Provider>
  );
}

export function useVehicleDetailData() {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}
