'use client';
// import AgreementTerminationPDFViewer from '@/pdfComponents/Termination/AgreementTermination/AgreementTermination-PdfViewer';
// import ContractTerminationPDFViewer from '@/pdfComponents/Termination/ContractTermination/ContractTermination-PdfViewer';
// import PagareTerminationPDFViewer from '@/pdfComponents/Termination/PagareTermination/PagareTermination-PdfViewer';
// import RecessionTerminationPDFViewer from '@/pdfComponents/Termination/RecessionTermination/RecessionTermination-PdfViewer';
import React, { useEffect, useState } from 'react';

export default function TestPage() {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <>
      <div className="flex flex-col items-center justify-center h-screen">
        {/* <h1>Contract Termination PDF Viewer</h1> */}
        {/* <AgreementTerminationPDFViewer />
        <PagareTerminationPDFViewer /> */}
        {/* <ContractTerminationPDFViewer /> */}
        {/* <RecessionTerminationPDFViewer /> */}
      </div>
    </>
  );
}
