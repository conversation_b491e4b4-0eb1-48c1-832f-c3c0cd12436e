'use client';
import ZoomImage from '@/app/dashboard/flotilla/components/others/ZoomImage';
import { useToast } from '@chakra-ui/react';
import { ChangeEvent, useRef } from 'react';
import { TbCameraPlus } from 'react-icons/tb';

interface EditPictureProps {
  picture: {
    url: string;
    originalName: string;
    docId: string;
  };
  onSubmit: (file: File) => Promise<void>;
  canEdit?: boolean;
}

export default function EditPicture({ picture, onSubmit, canEdit = true }: EditPictureProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const onChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      try {
        await onSubmit(file);
        toast({
          title: 'Imagen actualizada',
          status: 'success',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
        setTimeout(() => {
          window.location.reload();
          // router.refresh();
        }, 3000);
      } catch (error) {
        toast({
          title: 'Algo fallo',
          status: 'error',
          duration: 3000,
          isClosable: true,
          position: 'top',
        });
      }
      // console.log(res.data);
    }
  };

  return (
    <div className="w-full h-full relative">
      <ZoomImage
        w="100%"
        h="100%"
        imageUrl={
          picture
            ? picture.url
            : 'https://img.freepik.com/vector-premium/imagen-dibujos-animados-hongo-palabra-hongo_587001-200.jpg?w=2000'
        }
      />
      {canEdit && (
        <div
          className="
          w-[40px] h-[40px] 
          flex justify-center 
          items-center bg-[#5800F7]
          rounded-full 
          cursor-pointer
          absolute bottom-[-20px] right-[-20px]
        "
          onClick={handleButtonClick}
        >
          <TbCameraPlus color="white" size={24} />
        </div>
      )}
      <input type="file" className="hidden" ref={fileInputRef} onChange={onChange} accept="image/*" />
    </div>
  );
}
