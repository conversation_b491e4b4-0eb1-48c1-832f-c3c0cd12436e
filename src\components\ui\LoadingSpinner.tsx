import React from 'react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
};

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = '#5800F7',
  className = '',
}) => {
  const sizeClass = sizeClasses[size];

  return (
    <div className={`relative ${sizeClass} ${className}`}>
      {/* Background circle */}
      <div className={`${sizeClass} border-4 border-solid border-gray-200 rounded-full`}></div>
      {/* Spinning circle */}
      <div
        className={`absolute top-0 left-0 ${sizeClass} border-4 border-solid border-transparent rounded-full animate-spin`}
        style={{ borderTopColor: color }}
      ></div>
    </div>
  );
};

export default LoadingSpinner;
