import { useEffect, useState } from 'react';
import {
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react';
import { DateTime } from 'luxon';
import SelectInput from '@/components/Inputs/SelectInput';
import CustomInput from '@/components/Inputs/CustomInput';
import useDebounce from '@/hooks/useDebounce';
import axios from 'axios';
import { ApprovalTypes, URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import PrimaryButton from '@/components/PrimaryButton';
import FormikContainer from '../Formik/FormikContainer';
import { useFetchWeeklyPaymentMexico } from './ContractModal';
import { useVehicleDetailData } from '@/app/dashboard/flotilla/components/Providers/VehicleDetailDataProvider';
import { useFormikContext } from 'formik';
import Link from 'next/link';

interface ShowCalculatedWeeksProps {
  stockId: string;
  approvedStatus?: string;
}

export function ShowCalculatedWeeks({ stockId, approvedStatus }: ShowCalculatedWeeksProps) {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { user } = useCurrentUser();
  const [hasInvoice, setHasInvoice] = useState<boolean | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [invoiceAmount, setInvoiceAmount] = useState<number>();
  const [isOldVersion, setIsOldVersion] = useState(false);
  const [weeksDetail, setWeeksDetail] = useState<{
    totalWeeks: number;
    weeksWithoutOverhauling: number | undefined;
    lastPaymentDate: string;
    paymentsCompleted: number;
    previousTotalPayments: number;
    invoiceAmount: number | undefined;
  }>();
  const [clientId, setClientId] = useState('');

  const { vehicleData: vehicleDetail } = useVehicleDetailData();

  const { rentingProduct, assistanceProduct } = useFetchWeeklyPaymentMexico({
    model: vehicleDetail.model,
    region: vehicleDetail.state?.toUpperCase(),
    approvedStatus: approvedStatus || ApprovalTypes.PREAPPROVED,
    isNew: vehicleDetail.newCar,
    downPaymentModified: vehicleDetail.downPaymentModified,
  });

  const onChange = useDebounce((value: string) => {
    const newValue = Number(value);
    if (newValue > 0) {
      setInvoiceAmount(newValue);
    }
  }, 500);

  useEffect(() => {
    const calculateWeeks = async () => {
      try {
        setIsLoading(true);
        const sendData = {
          stockId,
          weeklyPayment: 3450,
          // Solo enviar hasInvoice si está definido
          ...(hasInvoice !== undefined && { hasInvoice }),
          invoiceAmount,
          removeInvoice: hasInvoice === false,
        };
        const res = await axios.post(`${URL_API}/contract/semi-new/calculate-weeks`, sendData, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        });
        const data = res.data.data;

        setIsOldVersion(data.detail.overhauling.isOldVersion);
        setClientId(data.clientId);
        setWeeksDetail({
          totalWeeks: data.weeks,
          weeksWithoutOverhauling: data.detail.weeksWithoutOverhauling,
          lastPaymentDate: data.lastPaymentDate,
          paymentsCompleted: data.paymentsCompleted,
          previousTotalPayments: data.detail.previousTotalPayments,
          invoiceAmount: data.detail.overhauling.invoiceAmount,
        });

        setHasInvoice(data.detail.overhauling.hasInvoice);

        if (!data.detail.overhauling.isOldVersion && data.detail.overhauling.hasInvoice) {
          setInvoiceAmount(data.detail.overhauling.invoiceAmount);
        }
      } catch (error: any) {
        console.log('error response data: ', error.response?.data);
      } finally {
        setIsLoading(false);
      }
    };
    calculateWeeks();
  }, [hasInvoice, invoiceAmount, assistanceProduct, rentingProduct, stockId, user.accessToken, isOpen]);

  return (
    <>
      <PrimaryButton
        className="text-xs md:text-sm py-1 px-2"
        onClick={() => {
          onOpen();
        }}
      >
        Ver calculo de semanas
      </PrimaryButton>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent className="pb-5">
          <ModalHeader>Cálculo de Semanas</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <div className="flex flex-col gap-3">
              {isOldVersion && (
                <h2 className="text-sm text-red-500">
                  Esta revisión no se corroboró si tiene factura o no, favor de confirmar
                </h2>
              )}

              <FormikContainer
                hideFooter
                initialValues={{
                  hasInvoice: hasInvoice ? { value: 'Si', label: 'Si' } : { value: 'No', label: 'No' },
                  invoiceAmount: invoiceAmount,
                }}
                onSubmit={async () => {
                  // calculateWeeks();
                  // console.log('submitting');
                }}
                onClose={() => {
                  // onClose();
                }}
              >
                <div className="space-y-4">
                  <SelectInput
                    label="¿Tiene factura?"
                    name="hasInvoice"
                    options={[
                      { value: 'Si', label: 'Si' },
                      { value: 'No', label: 'No' },
                    ]}
                    defaultOption={{ value: hasInvoice ? 'Si' : 'No', label: hasInvoice ? 'Si' : 'No' }}
                    onChange={(option) => {
                      setHasInvoice(option.value === 'Si');
                    }}
                  />

                  {hasInvoice && (
                    <>
                      <CustomInput
                        name="invoiceAmount"
                        label="Monto de la factura"
                        type="number"
                        onChange={onChange}
                        disabled={isOldVersion && hasInvoice === undefined}
                      />
                    </>
                  )}
                  {isLoading && (
                    <div className="flex flex-col gap-3 text-purple-800">
                      <p>Calculando semanas...</p>
                    </div>
                  )}
                  {weeksDetail && (
                    <div className="flex flex-col gap-2 text-sm">
                      <p>
                        Semanas del contrato anterior:{' '}
                        <span className="font-semibold">{weeksDetail.previousTotalPayments}</span>
                      </p>
                      <p>
                        Pagos completados del contrato anterior:{' '}
                        <span className="font-semibold">{weeksDetail.paymentsCompleted}</span>
                      </p>
                      <p>
                        Fecha de último pago:{' '}
                        <span className="font-semibold">
                          {DateTime.fromISO(weeksDetail.lastPaymentDate).toFormat("d 'de' MMMM 'de' yyyy", {
                            locale: 'es',
                          })}
                        </span>
                      </p>
                      <Link
                        href={`/dashboard/pagos/clientDetails?id=${clientId}`}
                        target="_blank"
                        prefetch={false}
                      >
                        <p className="text-blue-500 underline">Ver detalle de pagos</p>
                      </Link>
                      {weeksDetail.weeksWithoutOverhauling &&
                        weeksDetail.weeksWithoutOverhauling !== weeksDetail.totalWeeks && (
                          <>
                            <p>
                              Monto de revisión:{' '}
                              <span className="font-semibold">
                                $
                                {weeksDetail.invoiceAmount?.toLocaleString('es-MX', {
                                  minimumFractionDigits: 2,
                                  maximumFractionDigits: 2,
                                }) || 0}
                              </span>
                            </p>
                            <p>
                              Semanas sin considerar monto de revisión:{' '}
                              <span className="font-semibold">{weeksDetail.weeksWithoutOverhauling}</span>
                            </p>
                            <div>
                              <p>
                                Suma adicional de semanas por monto de revisión:{' '}
                                <span className="font-semibold">
                                  {weeksDetail.totalWeeks - weeksDetail.weeksWithoutOverhauling}
                                </span>
                              </p>
                              <p className="text-xs mt-1">
                                Calculo realizado: (Monto de revisión / Pago semanal) redondeando al entero
                                más cercano
                              </p>
                            </div>
                          </>
                        )}
                      <p>
                        Total de semanas calculadas:{' '}
                        <span className="font-semibold">{weeksDetail.totalWeeks}</span>
                      </p>
                    </div>
                  )}
                  <ModifyForm setInvoiceAmount={setInvoiceAmount} setHasInvoice={setHasInvoice} />
                </div>
              </FormikContainer>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}

function ModifyForm({ setInvoiceAmount, setHasInvoice }: any) {
  const formik = useFormikContext();
  const values = formik.values as any;
  useEffect(() => {
    if (values.hasInvoice.value === 'No') {
      formik.setFieldValue('invoiceAmount', '');
      formik.setFieldValue('invoiceFile', '');
      setInvoiceAmount(undefined);
      setHasInvoice(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [values.hasInvoice]);

  return null;
}
