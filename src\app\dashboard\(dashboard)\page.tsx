import getUserById from '@/actions/getUserById';
import { citiesWithMoka, Countries, Paths } from '@/constants';
import { getWeeklyRecordDatesServer, getWeeklyRecords } from '@/actions/getWeeklyRecords';
import WeeklyRecordsChart from './components/WeeklyRecordsChart';
import { redirect } from 'next/navigation';
import TotalStatusSection from './components/CategorySection';
import { Car } from 'lucide-react';
import CountrySelector from '../clientes/_components/CountrySelector';
import { CountryProvider } from '../providers/CountryProvider';
import {
  StatusTranslations,
  StatusTranslationsMX,
  VehicleCategoryTranslations,
  VehicleCategoryTranslationsMX,
  VehicleSubCategoryTranslations,
  VehicleSubCategoryTranslationsMX,
} from '../flotilla/components/translations/statusTranslations';
import { getDashboardData } from '@/actions/getDashboardData';
import ActivesChart from './components/ActiveChart';

export const metadata = {
  title: 'Dashboard',
  description: 'Esto es el dashboard',
};

interface SearchComponentProps {
  searchParams: {
    country?: string;
  };
}

export default async function Dashboard({ searchParams: { country } }: SearchComponentProps) {
  const currentUser = await getUserById();
  if (currentUser?.role === 'auditor') {
    redirect(Paths.fleet_active);
  }

  if (!currentUser) return null;

  const role = currentUser.role;
  const allowedRegions = currentUser.settings.allowedRegions;
  const lastItem = allowedRegions.length - 1;

  const isSuperAdmin = role === 'superadmin' || role === 'admin';
  const auditorRole = role === 'auditor';

  const isUSA = country === Countries['United States'];
  const statusTranslations = isUSA ? StatusTranslations : StatusTranslationsMX;
  const categoryTranslations = isUSA ? VehicleCategoryTranslations : VehicleCategoryTranslationsMX;
  const subCategoryTranslations = isUSA ? VehicleSubCategoryTranslations : VehicleSubCategoryTranslationsMX;

  const dashData = await getDashboardData();

  const chartData = dashData?.chartData ?? {};
  const categoryData = dashData?.categoryData ?? {};

  const statusCounts = categoryData?.vehicleStatusCounts ?? {};
  const categoryCounts = categoryData?.categoryCounts ?? [];

  const weeklyDates = await getWeeklyRecordDatesServer();
  const weeklyRecords = await getWeeklyRecords();

  return (
    <CountryProvider>
      <div className="flex flex-col ">
        <div className="mb-4 flex flex-row justify-between items-center">
          <div className="flex gap-2 text-[32px] font-bold text-[#262D33]">
            <h1 className="">
              Dashboard {isSuperAdmin && 'General'}
              {auditorRole && ''}
              {!isSuperAdmin &&
                !auditorRole &&
                allowedRegions.map((region, i) => (
                  <span key={i}>{`${citiesWithMoka[region].label}${lastItem !== i ? ', ' : ''}`}</span>
                ))}
            </h1>
          </div>
          <CountrySelector />
        </div>
        <div className="flex gap-[30px]">
          <div className="min-w-[330px] h-[130px] bg-white rounded shadow p-[15px] flex flex-col relative">
            <div className="flex items-center gap-2 bottom-[15px]">
              <Car color="#5800F7" size={20} />
              <p>Total vehículos OCN</p>
              <span className="text-[32px] text-[#262D33] font-bold">{statusCounts?.Total || 0}</span>
            </div>
            <div className="mt-10 flex gap-6 justify-center">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-[#5800F7]"></div>
                <span className="text-sm text-gray-600">
                  {statusTranslations.active}{' '}
                  <span className="text-black font-bold">{statusCounts?.Active || 0}</span>
                </span>
              </div>
              <div className="ml-6 flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-gray-300"></div>
                <span className="text-sm text-gray-600">
                  {statusTranslations.inactive}{' '}
                  <span className="text-black font-bold">{statusCounts?.Inactive || 0}</span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex gap-[10px] flex-wrap">
            {Object?.entries(categoryCounts)?.map(([category, details]: [string, any], index) => (
              <TotalStatusSection
                key={index}
                categoryName={category}
                total={details.count}
                subcategories={details.subcategories}
                categoryTranslations={categoryTranslations}
                subCategoryTranslations={subCategoryTranslations}
              />
            ))}
          </div>
        </div>
        {currentUser.role === 'superadmin' && <ActivesChart activeData={chartData} />}

        {currentUser.role === 'superadmin' && weeklyRecords && weeklyDates && (
          <WeeklyRecordsChart data={weeklyRecords} weeksAvailable={weeklyDates} />
        )}
      </div>
    </CountryProvider>
  );
}
