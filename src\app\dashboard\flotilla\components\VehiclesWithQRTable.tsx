'use client';
import {
  <PERSON>,
  TableContainer,
  Tbody,
  Td,
  Th,
  The<PERSON>,
  Tr,
  <PERSON>u,
  MenuButton,
  MenuList,
  MenuItem,
  IconButton,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  useDisclosure,
  Image,
  VStack,
  Text,
  Spinner,
  Box,
  Flex,
} from '@chakra-ui/react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { FiPrinter, FiEye } from 'react-icons/fi';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';
import { useState, useMemo } from 'react';
import PrintQRButton from './PrintQRButton';
import { PaginationStock } from '@/components/PaginationStock';
import { generateQRCodePDF } from '@/utils/pdfGenerator';

interface VehiclesWithQRTableProps {
  vehicles: VehicleWithQR[];
  showPrintButton?: boolean;
}

export default function VehiclesWithQRTable({ vehicles, showPrintButton = true }: VehiclesWithQRTableProps) {
  const toast = useToast();
  const { isOpen: isQRModalOpen, onOpen: onQRModalOpen, onClose: onQRModalClose } = useDisclosure();
  const [currentPage, setCurrentPage] = useState(1);
  const [generatingPDF, setGeneratingPDF] = useState<string | null>(null);
  const [selectedVehicleForView, setSelectedVehicleForView] = useState<VehicleWithQR | null>(null);
  const [qrImageLoading, setQrImageLoading] = useState(false);
  const itemsPerPage = 10;

  // Calculate pagination
  const totalPages = Math.ceil(vehicles.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentVehicles = useMemo(
    () => vehicles.slice(startIndex, endIndex),
    [vehicles, startIndex, endIndex]
  );

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handlePrintQR = async (vehicle: VehicleWithQR) => {
    try {
      setGeneratingPDF(vehicle._id);

      toast({
        title: 'Generando PDF...',
        description: 'Por favor espere mientras se genera el PDF del código QR.',
        status: 'info',
        duration: 2000,
        position: 'top',
      });

      await generateQRCodePDF(vehicle);

      toast({
        title: 'PDF generado exitosamente',
        description: 'El código QR se ha abierto en una nueva pestaña.',
        status: 'success',
        duration: 3000,
        position: 'top',
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'Error al generar PDF',
        description: 'No se pudo generar el PDF del código QR. Inténtelo nuevamente.',
        status: 'error',
        duration: 5000,
        position: 'top',
      });
    } finally {
      setGeneratingPDF(null);
    }
  };

  const handleView = (vehicle: VehicleWithQR) => {
    setSelectedVehicleForView(vehicle);
    setQrImageLoading(true);
    onQRModalOpen();
  };

  const handleQRImageLoad = () => {
    setQrImageLoading(false);
  };

  const handleQRImageError = () => {
    setQrImageLoading(false);
    toast({
      title: 'Error al cargar QR',
      description: 'No se pudo cargar la imagen del código QR.',
      status: 'error',
      duration: 3000,
      position: 'top',
    });
  };

  const handleCloseQRModal = () => {
    onQRModalClose();
    setSelectedVehicleForView(null);
    setQrImageLoading(false);
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        {/* Print QR Button - conditionally rendered */}
        {showPrintButton && (
          <div className="flex justify-end">
            <PrintQRButton />
          </div>
        )}

        {/* Table */}
        <div
          style={{
            border: '1px solid white',
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '1.5%',
            overflow: 'hidden',
          }}
        >
          <TableContainer>
            <Table colorScheme="gray" border="1px solid #e2e8f0">
              <Thead bgColor={'#FAFAFA'}>
                <Tr>
                  <Th>#</Th>
                  <Th>Nombre del Vehículo</Th>
                  <Th>VIN #</Th>
                  <Th>Año</Th>
                  <Th textAlign="center">Acciones</Th>
                </Tr>
              </Thead>
              <Tbody>
                {currentVehicles && currentVehicles.length > 0 ? (
                  currentVehicles.map((vehicle, index) => (
                    <Tr key={vehicle._id}>
                      <Td>{startIndex + index + 1}</Td>
                      <Td>
                        {vehicle.brand} {vehicle.model}
                      </Td>
                      <Td>{vehicle.vin || 'N/A'}</Td>
                      <Td>{vehicle.year || 'N/A'}</Td>
                      <Td textAlign="center">
                        <Menu>
                          <MenuButton
                            as={IconButton}
                            icon={<BsThreeDotsVertical />}
                            variant="ghost"
                            size="sm"
                          />
                          <MenuList>
                            <MenuItem
                              icon={<FiPrinter />}
                              onClick={() => handlePrintQR(vehicle)}
                              isDisabled={generatingPDF === vehicle._id}
                            >
                              {generatingPDF === vehicle._id ? 'Generando...' : 'Imprimir QR'}
                            </MenuItem>
                            <MenuItem icon={<FiEye />} onClick={() => handleView(vehicle)}>
                              Ver QR
                            </MenuItem>
                          </MenuList>
                        </Menu>
                      </Td>
                    </Tr>
                  ))
                ) : (
                  <Tr>
                    <Td colSpan={5} textAlign="center" py={8}>
                      No se encontraron vehículos con códigos QR
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          {vehicles.length > itemsPerPage && (
            <div style={{ marginTop: '16px', display: 'flex', justifyContent: 'end' }}>
              <PaginationStock
                currentPage={currentPage}
                hasMorePages={currentPage < totalPages}
                pagesCount={totalPages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      </div>

      {/* QR Code Viewing Modal */}
      <Modal isOpen={isQRModalOpen} onClose={handleCloseQRModal} size="lg" isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader bg="#5800F7" color="white" borderTopRadius="md">
            <Flex align="center" gap={3}>
              <FiEye size={20} />
              <Text>Código QR del Vehículo</Text>
            </Flex>
          </ModalHeader>
          <ModalCloseButton color="white" />
          <ModalBody p={6}>
            {selectedVehicleForView && (
              <VStack spacing={6} align="center">
                {/* Vehicle Information */}
                <Box textAlign="center">
                  <Text fontSize="xl" fontWeight="bold" color="#5800F7">
                    {selectedVehicleForView.brand} {selectedVehicleForView.model}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    VIN: {selectedVehicleForView.vin}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Vehículo #: {selectedVehicleForView.carNumber}
                  </Text>
                  <Text fontSize="sm" color="gray.600">
                    Año: {selectedVehicleForView.year}
                  </Text>
                </Box>

                {/* QR Code Display */}
                <Box
                  position="relative"
                  bg="white"
                  p={4}
                  borderRadius="lg"
                  border="2px"
                  borderColor="#E2E8F0"
                  minH="300px"
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                >
                  {qrImageLoading && (
                    <VStack spacing={3}>
                      <Spinner size="xl" color="#5800F7" thickness="4px" />
                      <Text color="gray.500">Cargando código QR...</Text>
                    </VStack>
                  )}

                  <Image
                    src={selectedVehicleForView.qrCode.url}
                    alt={`QR Code for ${selectedVehicleForView.brand} ${selectedVehicleForView.model}`}
                    maxW="300px"
                    maxH="300px"
                    objectFit="contain"
                    onLoad={handleQRImageLoad}
                    onError={handleQRImageError}
                    display={qrImageLoading ? 'none' : 'block'}
                  />
                </Box>

                <Text fontSize="sm" color="gray.500" textAlign="center">
                  Escanea este código QR para acceder a la información del vehículo
                </Text>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter bg="gray.50" borderBottomRadius="md">
            <button
              onClick={handleCloseQRModal}
              className="border-2 border-[#5800F7] text-[#5800F7] px-4 py-2 rounded hover:bg-[#5800F7] hover:text-white transition-colors"
            >
              Cerrar
            </button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
