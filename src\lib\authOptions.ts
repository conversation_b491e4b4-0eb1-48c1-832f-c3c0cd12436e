import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import CredentialsProvider from 'next-auth/providers/credentials';
import axios from 'axios';
import { ONE_CAR_NOW_EMAIL_REGEX, URL_API } from '@/constants';
import { AuthErrors } from './utils';
import { deleteCookie, setCookie } from 'cookies-next';
import { cookies } from 'next/headers';

export function getBullBoardCookieOptions() {
  const envUrl = process.env.NEXT_PUBLIC_PAYMENTS_API_URL ?? '';
  try {
    const url = new URL(envUrl);
    const isHttps = url.protocol === 'https:';
    const urlSegmentsToUse = 2;
    let domain = url.hostname;
    // If not localhost and has at least 2 segments, use ".domain.tld" format
    if (!domain.includes('localhost') && domain.split('.').length >= urlSegmentsToUse) {
      domain = '.' + domain.split('.').slice(-2).join('.');
    }

    // Remove port for localhost HTTP
    if (domain === 'localhost' && !isHttps) {
      domain = domain.split(':')[0];
    }

    const baseOptions = {
      cookies,
      domain,
      httpOnly: true,
    };

    // Add secure and sameSite options only for HTTPS
    if (isHttps) {
      return {
        ...baseOptions,
        secure: true,
        sameSite: 'none',
      };
    }

    return baseOptions;
  } catch (error) {
    // Fallback to localhost if URL parsing fails
    return {
      cookies,
      domain: 'localhost',
      httpOnly: true,
    };
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      wellKnown: 'https://accounts.google.com/.well-known/openid-configuration',
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
          scope:
            'https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/calendar http://www.google.com/calendar/feeds/default/allcalendars/full http://www.google.com/calendar/feeds/ http://www.google.com/calendar/feeds https://www.google.com/calendar/feeds/default/private/full http://www.google.com/calendar/feeds/default/private/full http://www.google.com/calendar/feeds/default/owncalendars/full/ https://www.google.com/calendar/feeds/default https://www.google.com/calendar/freebusy https://www.googleapis.com/auth/calendar.app.created https://www.googleapis.com/auth/calendar.events https://www.googleapis.com/auth/calendar.events.freebusy https://www.googleapis.com/auth/calendar.events.owned https://www.googleapis.com/auth/calendar.events.owned.readonly https://www.googleapis.com/auth/calendar.events.public.readonly https://www.googleapis.com/auth/calendar.events.readonly https://www.googleapis.com/auth/calendar.readonly',
        },
      },
    }),

    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'email', type: 'string' },
        password: { label: 'password', type: 'password' },
      },
      async authorize(credentials) {
        let user;

        try {
          // Realiza la autenticación con tus propias lógicas
          const res = await axios.post(URL_API + '/auth/login', credentials);
          // const user = res.data.user

          user = res.data.user;
          // console.log(user, 'soy el user');

          // Si la autenticación es exitosa, devuelve el objeto de usuario
          if (user) {
            user.accessToken = res.data.accessToken;
            const expires = res.headers.expiration;
            user.expiration = expires;

            console.log('user: ', user);

            return user;
            // eslint-disable-next-line brace-style
          }
          // Si la autenticación falla, devuelve null
          else throw new Error('Algo salio mal');
        } catch (error: any) {
          console.log(error.response.data);
          throw new Error(error.response.data.message);
        }
      },
    }),
  ],
  callbacks: {
    async signIn(props: any) {
      const { profile, account } = props;
      if (
        account &&
        account.provider === 'google' &&
        profile &&
        ONE_CAR_NOW_EMAIL_REGEX.test(profile.email) &&
        profile.email_verified
      ) {
        return true;
      }
      if (account && account.provider === 'credentials') {
        return true;
      }
      return false;
    },

    async session(props: any) {
      const { session, token } = props;
      session.user.accessToken = token.accessToken;
      session.user.id = token.id;
      session.user.name = token.name;
      session.user.role = token.role;
      session.user.image = token.image;
      session.user.email = token.email;
      session.user.city = token.city;
      session.user.error = token.error;
      session.user.expiration = token.expiration;
      session.user.exp = token.expiration;
      session.user.googleSignInfailureCount = token.googleSignInfailureCount;
      session.user.area = token.area;
      session.user.permissions = token.permissions;
      return session;
    },
    async jwt(props: any) {
      const { token, account, profile, user } = props;
      if (account && account.provider === 'google' && profile) {
        try {
          const res = await axios.post(URL_API + '/auth/google-login', {
            email: profile.email,
            googleProvidedAccessToken: account.access_token,
            googleProvidedRefreshToken: account.refresh_token,
            googleProvidedIdToken: account.id_token,
          });
          const logedInUser = res?.data?.user;

          if (logedInUser) {
            logedInUser.accessToken = res.data.accessToken;
            const expires = res.headers.expiration;
            logedInUser.expiration = expires;
            token.exp = expires;
            setCookie('bull-board-token', logedInUser.accessToken, getBullBoardCookieOptions());
            return { ...token, ...logedInUser };
          }
          return undefined;
        } catch (error: any) {
          /**
           * for logging purpose on Vercel
           */
          console.error('[google-login] api call, Error in jwt callback: ', error);
          console.log(
            'google-login] api call, error?.response?.data?.message: ',
            error?.response?.data?.message
          );
          try {
            const res = await axios.post(URL_API + '/auth/google-login-failure-count', {
              email: profile.email,
            });
            const logedInUserFailure = res?.data?.user;

            token.googleSignInfailureCount = logedInUserFailure.googleSignInfailureCount;
          } catch (err: any) {
            console.error('[google-login-failure-count] api call, Error in jwt callback: ', err);
            console.log(
              '[google-login-failure-count] api call, error?.response?.data?.message: ',
              err?.response?.data?.message
            );
          }
          token.exp = 0;
          token.expiration = 0;
          if (error?.response?.data?.message === AuthErrors.Id_Token_Invalid) {
            return { ...token, error: AuthErrors.Id_Token_Invalid };
          }
          if (error?.response?.data?.message === AuthErrors.Google_Registered_User_Not_Found) {
            return { ...token, error: AuthErrors.Google_Registered_User_Not_Found };
          }
          if (error?.code === 'ECONNREFUSED') {
            return {
              ...token,
              error: AuthErrors.Server_Is_Down,
            };
          }
          if (error?.response?.status === AuthErrors.Bad_Gateway_Status_Code) {
            return {
              ...token,
              error: AuthErrors.Server_Is_Down,
            };
          }

          return {
            ...token,
            error: AuthErrors.Internal_Server_Error,
          };
        }
      }
      if (account && account.provider === 'credentials') {
        token.exp = token.expiration;
        return { ...token, ...user };
      }

      return token;
    },
    async redirect({ url, baseUrl }) {
      // Check if the URL is absolute (starts with http:// or https://)
      if (url.startsWith('http')) {
        // If the URL is from the same site, allow it
        if (url.startsWith(baseUrl)) {
          return url;
        }
        // Parse URL to get the callbackUrl parameter
        try {
          const urlObj = new URL(url);
          const callbackUrl = urlObj.searchParams.get('callbackUrl');
          // If there's a callbackUrl parameter and it's a relative URL
          if (callbackUrl && !callbackUrl.startsWith('http')) {
            return `${baseUrl}${callbackUrl.startsWith('/') ? callbackUrl : `/${callbackUrl}`}`;
          }
        } catch (error) {
          console.error('Error parsing URL:', error);
        }
        // Default to baseUrl if we can't extract a valid callbackUrl
        return baseUrl;
      }

      // Handle relative URLs (for internal redirects)
      return url.startsWith('/') ? `${baseUrl}${url}` : `${baseUrl}/${url}`;
    },
  },
  debug: process.env.NODE_ENV === 'development',
  // sesion de 1 hora momentaneamente, ya que el token del back dura 30 minutos
  // session: {
  //   maxAge: 60 * 60 * 10,
  // },
  events: {
    signOut: async () => {
      deleteCookie('bull-board-token', getBullBoardCookieOptions());
    },
  },
  pages: {
    error: '/',
    signIn: '/',
  },
};
