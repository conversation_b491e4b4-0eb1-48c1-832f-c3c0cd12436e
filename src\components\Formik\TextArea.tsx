import { useField } from 'formik';
import { Textarea as ChakraTextArea, TextareaProps as ChakraTextAreaProps } from '@chakra-ui/react';
import * as React from 'react';

type Props = ChakraTextAreaProps;

const TextArea = ({ name = '', children, ...props }: Props) => {
  const [field, , { setValue }] = useField({ name: name, value: props.value });

  const namedChildren = React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) return null;

    return React.cloneElement(child as React.ReactElement<any>, {
      name,
    });
  });

  return (
    <ChakraTextArea {...field} {...props} onChange={(e) => setValue(e.target.value)}>
      {namedChildren}
    </ChakraTextArea>
  );
};

export default TextArea;
