import { PropsWithChildren } from 'react';
import { RequestsWrapper } from '../../_components/RequestsWrapper';
// import RequestsFilters from './RequestsFilters';
// import { CreateRequestDialog } from './CreateRequestDialog';

export default function RequestsLayout(props: PropsWithChildren<{}>) {
  const { children } = props;

  return (
    <div className="w-full">
      <RequestsWrapper>{children}</RequestsWrapper>
    </div>
  );
}
