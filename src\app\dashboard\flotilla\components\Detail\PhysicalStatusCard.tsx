'use client';

import React, { useState, useMemo, useCallback } from 'react';
import {
  Box,
  Text,
  Heading,
  Icon,
  useColorModeValue,
  VStack,
  HStack,
  Divider,
  Button,
  Collapse,
  Avatar,
  Flex,
  useBreakpointValue,
} from '@chakra-ui/react';
import { MapPin, Clock, ChevronDown, ChevronUp, AlertCircle, Edit3 } from 'lucide-react';
import { getStatusLabel, getModalOptionLabel } from '../Modals/PhysicalStatusUpdateModal';
import { format } from 'date-fns';
import { es, enUS } from 'date-fns/locale';

import { Countries, URL_API, PhysicalVehicleStatus } from '@/constants';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { physicalStatusTranslations } from '../translations/physicalStatusTranslations';
import StatusHistoryItem from './StatusHistoryItem';
import PhotoModal from './PhotoModal';
import SuperadminPhysicalStatusModal from '../Modals/SuperadminPhysicalStatusModal';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useRouter } from 'next/navigation';

enum ActionType {
  SCAN = 'SCAN',
  STATUS_CHANGE = 'STATUS_CHANGE',
}

interface QRScanHistoryItem {
  _id: string;
  vehicleId: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  scanTime: string;
  statusChangedFrom: string;
  statusChangedTo: string;
  deviceInfo?: string;
  actionType: ActionType;
  notes?: string;
  location?: string;
  photo?: {
    _id: string;
    path: string;
    originalName: string;
    createdAt: string;
    updatedAt: string;
    vehicleId: string;
    url?: string;
  };
  vendorWorkshopName?: string;
  vendorRegion?: string;
  isAdminCorrection?: boolean;
  vendorUserName?: string;
}

interface PhysicalStatusCardProps {
  physicalStatus: string | undefined | null;
  qrScanHistory?: QRScanHistoryItem[];
  vehicleId: string;
}

const repairStatusesWithVendorInfo: string[] = [
  PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP,
  PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP,
  PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP,
  PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR,
  PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT,
];

const PhysicalStatusCard: React.FC<PhysicalStatusCardProps> = ({
  physicalStatus,
  qrScanHistory = [],
  vehicleId,
}) => {
  const [showHistory, setShowHistory] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState<string | null>(null);
  const [isPhotoModalOpen, setIsPhotoModalOpen] = useState(false);
  const [isSuperadminModalOpen, setIsSuperadminModalOpen] = useState(false);
  const { country } = useCountry();
  const { user: currentUser } = useCurrentUser();
  const isLargerScreen = useBreakpointValue({ base: false, md: true });
  const router = useRouter();

  const translatedText =
    physicalStatusTranslations[country.value as keyof typeof physicalStatusTranslations] ||
    physicalStatusTranslations[Countries.Mexico];

  const viewPhotoText = translatedText.viewPhoto;
  const correctionButtonText = translatedText.correctStatus;

  const statusChangeHistory = useMemo(() => {
    return qrScanHistory.filter((entry) => entry.actionType === ActionType.STATUS_CHANGE);
  }, [qrScanHistory]);

  const latestStatusChange = useMemo(() => {
    return statusChangeHistory.length > 0 ? statusChangeHistory[0] : null;
  }, [statusChangeHistory]);

  const historyItems = useMemo(() => {
    return statusChangeHistory.slice(1); // Get all items except the first one (current status)
  }, [statusChangeHistory]);

  const historyAvailable = historyItems.length > 0;

  // Determine displayStatus with vendor information if applicable
  const baseDisplayStatus = getStatusLabel(physicalStatus ?? null, country.value);

  let displayStatus = baseDisplayStatus;

  // if repair statuses, set vendor region and workshopname to displayStatus
  if (physicalStatus && repairStatusesWithVendorInfo.includes(physicalStatus as PhysicalVehicleStatus)) {
    const relevantHistoryEntry = [...statusChangeHistory]
      .sort((a, b) => new Date(b.scanTime).getTime() - new Date(a.scanTime).getTime())
      .find((entry) => entry.statusChangedTo === physicalStatus);

    if (
      relevantHistoryEntry &&
      relevantHistoryEntry.vendorWorkshopName &&
      relevantHistoryEntry.vendorRegion
    ) {
      displayStatus = `${baseDisplayStatus} - [${relevantHistoryEntry.vendorWorkshopName} - ${relevantHistoryEntry.vendorRegion}]`;
    }
  }

  // Compute the main display status label, handling the special case for post-repair return
  let mainDisplayStatus = displayStatus;
  if (
    (physicalStatus === PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER ||
      physicalStatus === PhysicalVehicleStatus.COLLECTED_FROM_STOCK) &&
    statusChangeHistory.length > 0 &&
    statusChangeHistory[0].statusChangedFrom === PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT
  ) {
    mainDisplayStatus = getModalOptionLabel(
      physicalStatus,
      country.value,
      PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT
    );
  }

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');
  const headingColor = useColorModeValue('gray.600', 'gray.300');
  const mutedTextColor = useColorModeValue('gray.500', 'gray.400');
  const timelineColor = useColorModeValue('gray.100', 'gray.700');
  const timelineItemBg = useColorModeValue('white', 'gray.800');
  const timelineBorder = useColorModeValue('purple.100', 'purple.700');
  const primaryColor = '#5800F7';

  // New formatDate function
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      if (country.value === Countries['United States']) {
        return format(date, 'MMM dd yyyy, HH:mm', { locale: enUS });
      }
      return format(date, 'dd MMM yyyy, HH:mm', { locale: es });
    } catch (e) {
      return dateString;
    }
  };

  const toggleHistory = useCallback(() => setShowHistory((prev) => !prev), []);

  const handleViewPhoto = useCallback((photoPathOrUrl: string) => {
    const fullPhotoUrl = photoPathOrUrl.startsWith('http')
      ? photoPathOrUrl
      : `${URL_API}${photoPathOrUrl.startsWith('/') ? '' : '/'}${photoPathOrUrl}`;

    setSelectedPhoto(fullPhotoUrl);
    setIsPhotoModalOpen(true);
  }, []);

  const closePhotoModal = useCallback(() => {
    setIsPhotoModalOpen(false);
    setSelectedPhoto(null);
  }, []);
  const handleStatusUpdated = () => {
    setIsSuperadminModalOpen(false);
    // Refresh the page to show the updated status and history
    router.refresh();
  };
  return (
    <Box
      p={4}
      borderWidth="1px"
      borderRadius="lg"
      borderColor={borderColor}
      bg={bgColor}
      boxShadow="sm"
      w="full"
      h="max-content"
      transition="box-shadow 0.2s"
      _hover={{ boxShadow: 'md' }}
    >
      <Flex justifyContent="space-between" alignItems="center" mb={3}>
        <Heading size="sm" display="flex" alignItems="center" color={headingColor}>
          <Icon as={MapPin} mr={2} color={primaryColor} />
          {translatedText.physicalStatus}
        </Heading>
        {/* Superadmin correction button */}
        {currentUser.role === 'superadmin' && (
          <Button
            size="xs"
            variant="ghost"
            colorScheme="purple"
            onClick={() => setIsSuperadminModalOpen(true)}
            leftIcon={<Edit3 size={12} />}
            fontSize="xs"
            px={2}
            h="24px"
            _hover={{ bg: 'rgba(88, 0, 247, 0.1)' }}
          >
            {correctionButtonText}
          </Button>
        )}
      </Flex>

      <Box display="flex" flexDirection="column" mb={4}>
        <Text fontSize="md" fontWeight="medium" color={primaryColor}>
          {mainDisplayStatus}
        </Text>
        {latestStatusChange && (
          <Flex
            mt={2}
            direction={isLargerScreen ? 'row' : 'column'}
            align={isLargerScreen ? 'center' : 'start'}
            gap={isLargerScreen ? 3 : 1}
          >
            <HStack spacing={1} align="center">
              <Avatar
                size="2xs"
                name={latestStatusChange.userId?.name || latestStatusChange.vendorUserName || 'U'}
                bg={primaryColor}
                color="white"
              />
              <Text fontSize="xs" color={mutedTextColor}>
                {latestStatusChange.userId?.name || latestStatusChange.vendorUserName || translatedText.user}
              </Text>
            </HStack>
            <HStack spacing={1} align="center">
              <Icon as={Clock} size={14} color={mutedTextColor} />
              <Text fontSize="xs" color={mutedTextColor}>
                {formatDate(latestStatusChange.scanTime)}
              </Text>
            </HStack>
          </Flex>
        )}
      </Box>

      <Divider my={3} />

      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Text fontSize="sm" fontWeight="medium" color={headingColor}>
          {translatedText.changeHistory}
        </Text>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleHistory}
          rightIcon={showHistory ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          color={primaryColor}
        >
          {showHistory ? translatedText.hide : translatedText.show}
        </Button>
      </Box>

      <Collapse in={showHistory} animateOpacity>
        {historyAvailable ? (
          <VStack
            spacing={2}
            align="stretch"
            mt={3}
            maxH="300px"
            overflowY="auto"
            px={1}
            css={{
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: timelineColor,
                borderRadius: '4px',
              },
              '&::-webkit-scrollbar-thumb': {
                background: primaryColor,
                borderRadius: '4px',
              },
            }}
          >
            {historyItems.map((entry, index) => (
              <StatusHistoryItem
                key={entry._id || index}
                entry={entry}
                countryValue={country.value}
                primaryColor={primaryColor}
                timelineItemBg={timelineItemBg}
                timelineBorder={timelineBorder}
                repairStatusesWithVendorInfo={repairStatusesWithVendorInfo}
                translatedText={translatedText}
                handleViewPhoto={handleViewPhoto}
                viewPhotoText={viewPhotoText}
                formatDate={formatDate}
                isAdminCorrection={entry.isAdminCorrection}
              />
            ))}
          </VStack>
        ) : (
          <Box
            p={3}
            borderWidth="1px"
            borderRadius="md"
            bg={timelineItemBg}
            borderColor={timelineBorder}
            display="flex"
            alignItems="center"
            gap={2}
          >
            <Icon as={AlertCircle} color="gray.400" />
            <Text color="gray.500" fontSize="sm">
              {translatedText.noHistory}
            </Text>
          </Box>
        )}
      </Collapse>
      <PhotoModal
        isOpen={isPhotoModalOpen}
        onClose={closePhotoModal}
        photoUrl={selectedPhoto}
        translatedText={translatedText}
        primaryColor={primaryColor}
      />

      {currentUser.role === 'superadmin' && currentUser.accessToken && (
        <SuperadminPhysicalStatusModal
          isOpen={isSuperadminModalOpen}
          onClose={() => setIsSuperadminModalOpen(false)}
          vehicleId={vehicleId}
          currentStatus={physicalStatus as PhysicalVehicleStatus}
          accessToken={currentUser.accessToken}
          onStatusUpdated={handleStatusUpdated}
        />
      )}
    </Box>
  );
};

export default PhysicalStatusCard;
