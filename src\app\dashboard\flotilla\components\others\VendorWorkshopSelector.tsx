import React from 'react';
import { Box, Text, Select, VStack } from '@chakra-ui/react';

interface Workshop {
  name: string;
  address: string;
  phone: string;
}

interface Region {
  region: string;
  label: string;
  workshops: Workshop[];
}

interface VendorWorkshopSelectorProps {
  regions: Region[];
  selectedRegion: string | null;
  onRegionChange: (region: string) => void;
  availableWorkshops: Workshop[];
  selectedWorkshop: string | null;
  onWorkshopChange: (workshop: string) => void;
  translations: {
    selectRegion: string;
    selectWorkshop: string;
    noWorkshopsAvailable: string;
  };
  borderColor?: string;
  textColor?: string;
  primaryButtonColorScheme?: string;
}

const VendorWorkshopSelector: React.FC<VendorWorkshopSelectorProps> = ({
  regions,
  selectedRegion,
  onRegionChange,
  availableWorkshops,
  selectedWorkshop,
  onWorkshopChange,
  translations,
  borderColor = 'gray.200',
  textColor = 'gray.600',
  primaryButtonColorScheme = 'purple',
}) => (
  <VStack spacing={4} mt={2} p={4} borderRadius="md" borderWidth="1px" borderColor={borderColor}>
    <Box w="full">
      <Text color={textColor} mb={1} fontWeight="medium">
        {translations.selectRegion}
      </Text>
      <Select
        placeholder={translations.selectRegion}
        value={selectedRegion || ''}
        onChange={(e) => onRegionChange(e.target.value)}
        colorScheme={primaryButtonColorScheme}
      >
        {regions.map((region) => (
          <option key={region.region} value={region.region}>
            {region.label}
          </option>
        ))}
      </Select>
    </Box>

    {selectedRegion && (
      <Box w="full">
        <Text color={textColor} mb={1} fontWeight="medium">
          {translations.selectWorkshop}
        </Text>
        {availableWorkshops.length > 0 ? (
          <Select
            placeholder={translations.selectWorkshop}
            value={selectedWorkshop || ''}
            onChange={(e) => onWorkshopChange(e.target.value)}
            colorScheme={primaryButtonColorScheme}
          >
            {availableWorkshops.map((workshop) => (
              <option key={workshop.name} value={workshop.name}>
                {workshop.name}
              </option>
            ))}
          </Select>
        ) : (
          <Text fontSize="sm" color="gray.500" mt={1}>
            {translations.noWorkshopsAvailable}
          </Text>
        )}
      </Box>
    )}
  </VStack>
);

export default VendorWorkshopSelector;
