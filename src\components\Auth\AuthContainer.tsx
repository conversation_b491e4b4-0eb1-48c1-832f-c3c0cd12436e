import React, { ReactNode } from 'react';

type Props = {
  children: ReactNode;
};

const AuthContainer = ({ children }: Props) => {
  return (
    <div
      // w-1/4 min-w-[600px]
      className="
                w-full
                sm:w-3/4 
                md:w-1/4 md:min-w-[600px]
                bg-white 
                z-20
                relative
                min-h-[200px]
                h-[max-content]
                rounded 
                flex flex-col 
                items-center
                px-8
                pt-8
                pb-6
                text-black 
                gap-4
                text-[14px]
                border-[#EAECEE]
                border-[1px] 
                "
    >
      {children}
    </div>
  );
};

export default AuthContainer;
