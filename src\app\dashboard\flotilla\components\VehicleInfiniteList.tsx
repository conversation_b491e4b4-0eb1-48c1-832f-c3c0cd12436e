'use client';
import Link from 'next/link';
import Bar from './PageBar/Bar';
import { VirtuosoGrid } from 'react-virtuoso';
import Card from '@/components/stockCard/Card';
import { AiOutlineClose } from 'react-icons/ai';
import { getCookie, setCookie } from 'cookies-next';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { getStockVehicles, GetVehicleProps, VehicleCard } from '@/actions/getAllVehicles';
import {
  citiesSelect,
  platformOptions,
  statusListByPage,
  statusSelected,
  stepsSelect,
  US_CITIES,
} from '@/constants';
import { CountryProvider, useCountry } from '../../providers/CountryProvider';

interface FlotillaCardsProps {
  page: string;
  data: VehicleCard[];
  totalCount: number;
  route: string;
}

const getValues = (params: Record<string, string>, isCountryUSA: boolean) => {
  const filters = [];
  if (params.city) {
    let cityFound;
    if (isCountryUSA) {
      cityFound = US_CITIES.find((city) => city.value === params.city.toUpperCase());
    } else {
      cityFound = citiesSelect.find((city) => city.value === params.city);
    }
    if (cityFound) {
      filters.push({ key: 'city', value: cityFound.label });
    }
  }
  if (params.stepNumber) {
    const stepFound = stepsSelect.find((step) => step.value === params.stepNumber);
    if (stepFound) {
      filters.push({ key: 'stepNumber', value: stepFound.label });
    }
  }

  if (params.isNew) {
    const boolIsNew = params.isNew === 'true' ? 'Nuevos' : 'Seminuevos';
    filters.push({ key: 'isNew', value: boolIsNew });
  }

  if (params.status) {
    const statusFound = statusSelected.find((status) => status.value === params.status);
    if (statusFound) {
      filters.push({ key: 'status', value: statusFound.label });
    }
  }
  if (params.isElectric) {
    filters.push({ key: 'isElectric', value: params.isElectric });
  }

  if (params.platform) {
    const platformFound = platformOptions.find((platform) => platform.value === params.platform);
    if (platformFound) {
      filters.push({ key: 'platform', value: platformFound.label });
    }
  }

  if (params.reason && window.location.pathname.includes('withdrawn')) {
    const reasonValue = isCountryUSA
      ? params.reason === 'Theft'
        ? 'Theft'
        : 'Accident'
      : params.reason === 'Robo'
      ? 'Robo'
      : 'Accidente';
    filters.push({ key: 'reason', value: reasonValue });
  }

  return filters;
};

const getSearchParamsObj = (filterCookie: any) => {
  const filter = filterCookie ? JSON.parse(filterCookie) : {};
  const params: Record<string, string> = {};
  for (const [key, value] of Object.entries(filter)) {
    if (key === 'reason' && !window.location.pathname.includes('withdrawn')) {
      continue;
    }
    params[key] = value as string;
  }
  return params;
};

export default function VehicleInfiniteList({ page, data, totalCount, route }: FlotillaCardsProps) {
  const [numberPage, setNumberPage] = useState(0);
  const [vehicles, setVehicle] = useState(data);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setVehicle(data);
  }, [data]);

  const pathname = usePathname();
  const filterCookie = getCookie('filters');

  const search = useSearchParams();

  const country = search ? search.get('country') : '';

  const currentPage = pathname.split('/').pop();

  const fetchData = async () => {
    const newPageNumber = numberPage + 1;
    setNumberPage(newPageNumber);
    setLoading(true);

    const res = getSearchParamsObj(filterCookie);
    const listStatus = statusListByPage[currentPage as string];
    const isActivePage = page === 'Activos' || page === 'Active';

    const fetchObj: GetVehicleProps = {
      page: newPageNumber,
      limit: 50,
      listStatus: currentPage === 'active' ? [] : listStatus,
      searchParams: {
        vehicleStatus: currentPage === 'active' ? (currentPage as string) : '',
        country: country || 'Mexico',
        ...res,
      },
    };

    if (!isActivePage) {
      fetchObj.excludeStatus = statusListByPage.activos;
    }

    const result = await getStockVehicles(fetchObj);

    if (!result) {
      setLoading(false);
      return null;
    }
    if (result.stock.length === 0) {
      setLoading(false);
      return false;
    } else {
      setVehicle([...vehicles, ...result.stock]);
    }

    if (vehicles.length >= totalCount) {
      setLoading(false);
      return false;
    }
    return false;
  };

  const [bottomPosition, setBottomPosition] = useState(0);

  return (
    <>
      <CountryProvider>
        <div
          className="
        flex flex-col h-full  
        relative 
        overflow-hidden
        w-[calc(100vw-35px)]
        md:w-full
        lg:w-[calc(100vw-335px)] 
        md:ml-[35px]
        lg:ml-0
      "
        >
          <Bar page={page} />
          <AppliedFilters setBottomPosition={setBottomPosition} totalCount={totalCount} />
          <div id="vehicle-list">
            <style>
              {`
              .virtuso-grid-list {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 320px));
                gap: 1.25rem;
              }

              .virtuso-grid-item {
                display: flex;
                flex-direction: column;
              }
            `}
            </style>

            <VirtuosoGrid
              // set the height of the grid to the 100vh - the offset
              style={{ height: `calc(100vh - ${bottomPosition + 10}px)` }}
              data={vehicles?.filter((vehicle: VehicleCard) => vehicle?._id)}
              endReached={fetchData}
              listClassName="virtuso-grid-list"
              // overscan={150}
              // make overscan increment when having more vehicles to avoid flickering
              // so multiple the overscan 1.3 times the number of vehicles
              // overscan={
              //   vehicles.length > 300 ? Math.round(vehicles.length * 1.2) : vehicles.length > 0 ? 50 : 0
              // }
              overscan={100}
              className="mt-[10px] pb-[30px]"
              itemContent={(index: number, vehicle: VehicleCard) => (
                <VehicleCardComponent
                  car={vehicle}
                  key={index}
                  index={index}
                  route={route}
                  total={vehicles.filter((v: VehicleCard) => v?._id).length}
                  paramCountry={country}
                />
              )}
              components={{
                Footer: () => <Footer loading={loading} />,
              }}
              itemClassName="virtuso-grid-item"
              computeItemKey={(index, item) => item._id}
            />
          </div>
        </div>
      </CountryProvider>
    </>
  );
}

const dynamicValues = {
  platform: 'Plataforma',
  isElectric: 'Eléctricos',
};

function Footer({ loading }: { loading: boolean }) {
  return (
    <>
      {loading && (
        <div
          style={{
            padding: '2rem',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          Loading...
        </div>
      )}
    </>
  );
}

function VehicleCardComponent({
  car,
  route,
  index,
  total,
  paramCountry,
}: {
  car: VehicleCard;
  index: number;
  route: string;
  total: number;
  paramCountry: string | null;
}) {
  return (
    <Link
      href={`/dashboard/flotilla/${route}/${car._id}${
        paramCountry ? `?country=${encodeURI(paramCountry)}` : ''
      }`}
      prefetch={false}
    >
      <Card
        index={index}
        total={total}
        color={car.color}
        contract={car.carNumber}
        brand={car.brand}
        model={car.model}
        status={car.status}
        isElectric={car.isElectric}
        newCar={car.newCar}
        step={car.step}
        extensionCarNumber={car.extensionCarNumber}
        // key={key}
        dischargedReason={car.dischargedData?.reason}
        isBlocked={car.isBlocked}
        vehicleStatus={car?.vehicleStatus}
      />
    </Link>
  );
}

const AppliedFilters = (props: any) => {
  const { setBottomPosition, totalCount } = props;
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isCountryUSA } = useCountry();
  const filterCookie = getCookie('filters');
  const previousElement = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    function getOffsetBottom() {
      if (previousElement.current) {
        const btmPosition = previousElement.current.getBoundingClientRect().bottom;
        setBottomPosition(btmPosition);
      }
      return 0;
    }
    getOffsetBottom();
    window.addEventListener('resize', getOffsetBottom);
    return () => {
      window.removeEventListener('resize', getOffsetBottom);
    };
  }, [setBottomPosition]);

  const removeFilterByName = (name: string) => {
    const params = new URLSearchParams(searchParams);
    params.delete(name);
    const filterCookies = getCookie('filters');
    const filt = filterCookies ? JSON.parse(filterCookies) : {};
    delete filt[name];

    setCookie('filters', JSON.stringify(filt));
    router.refresh();
  };

  const getFilters = () => {
    const searchParamsObj = getSearchParamsObj(filterCookie);
    console.log('searchParamsObj', searchParamsObj);
    const values = getValues(searchParamsObj, isCountryUSA);
    console.log('values', values);
    return values;
  };

  const filters = getFilters();

  return (
    <>
      {filters.length > 0 ? (
        <div className="flex flex-col lg:flex-row gap-2 mt-[10px] flex-wrap " ref={previousElement}>
          <p> {isCountryUSA ? 'Filters applied:' : 'Filtros aplicados:'} </p>
          <div className="flex flex-wrap gap-2">
            {filters.map((filter, i) => {
              if (filter.key === 'platform' || filter.key === 'isElectric') {
                if (filter.key === 'isElectric') {
                  console.log(
                    'filter',
                    filter,
                    filter.value === 'true' && 'Sí',
                    filter.value === 'false' && 'No'
                  );
                }
                return (
                  <button
                    key={i}
                    className="h-[30px] w-[max-content] py-2 px-3 bg-[#5800F7] rounded-[15px] text-white flex gap-3 items-center "
                    onClick={() => {
                      removeFilterByName(filter.key);
                    }}
                  >
                    {/* <p>{filter.value}</p> */}

                    <p>
                      {/* {filter.key}: {filter.value} */}
                      {dynamicValues[filter.key]}: {filter.value === 'true' && 'Sí'}{' '}
                      {filter.value === 'false' && 'No'}{' '}
                      {filter.value !== 'true' && filter.value !== 'false' && filter.value}
                    </p>

                    <AiOutlineClose color="#FFFFFF" />
                  </button>
                );
              }

              return (
                <>
                  <button
                    key={i}
                    className="h-[30px] w-[max-content] py-2 px-3 bg-[#5800F7] rounded-[15px] text-white flex gap-3 items-center "
                    onClick={() => {
                      removeFilterByName(filter.key);
                    }}
                  >
                    <p>{filter.value}</p>
                    <AiOutlineClose color="#FFFFFF" />
                  </button>
                </>
              );
            })}
          </div>
          <p> {isCountryUSA ? `Total results: ${totalCount}` : `Total de resultados: ${totalCount}`} </p>
        </div>
      ) : (
        <div id="prev" ref={previousElement}>
          <p className="mt-[20px]">
            {' '}
            {isCountryUSA ? `Results: ${totalCount}` : `Resultados: ${totalCount}`}{' '}
          </p>
        </div>
      )}
    </>
  );
};
