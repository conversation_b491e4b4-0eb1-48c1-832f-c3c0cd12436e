import jsPDF from 'jspdf';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';

export const generateQRCodePDF = async (vehicle: VehicleWithQR): Promise<void> => {
  try {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    pdf.setProperties({
      title: `QR Code - ${vehicle.brand} ${vehicle.model}`,
      subject: 'Vehicle QR Code',
      author: 'OCN Admin',
      creator: 'OCN Admin System',
    });

    const loadImageAsDataURL = (url: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = img.width;
          canvas.height = img.height;
          if (ctx) {
            ctx.drawImage(img, 0, 0);
            resolve(canvas.toDataURL('image/png'));
          } else {
            reject(new Error('Could not get canvas context'));
          }
        };
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    };

    let qrDataUrl = '';
    try {
      qrDataUrl = await loadImageAsDataURL(vehicle.qrCode.url);
    } catch (error) {
      console.error(`Error loading QR for vehicle ${vehicle.carNumber}:`, error);
    }

    let logoDataUrl = '';
    try {
      logoDataUrl = await loadImageAsDataURL('/images/Logo.png');
    } catch (error) {
      console.error('Error loading company logo. Make sure /images/Logo.png is in the public folder:', error);
    }

    const pageWidth = 210;
    const pageHeight = 297;

    const qrSize = 25.4;
    const logoScaleFactor = 0.45;
    const logoHeight = qrSize * logoScaleFactor;
    const logoWidth = logoHeight * (170 / 61);
    const carNumberFontSize = 10;
    const carNumberMarginTop = 3;
    const textHeightApproximation = carNumberFontSize * 0.352778;

    const blockContentWidth = logoWidth + 5 + qrSize;
    const startX = (pageWidth - blockContentWidth) / 2;
    const startY = 50;

    const logoAreaTotalHeight = logoHeight + carNumberMarginTop + textHeightApproximation;
    let logoBlockOffsetY = 0;
    if (qrSize > logoAreaTotalHeight) {
      logoBlockOffsetY = (qrSize - logoAreaTotalHeight) / 2;
    }

    if (logoDataUrl) {
      pdf.addImage(logoDataUrl, 'PNG', startX, startY + logoBlockOffsetY, logoWidth, logoHeight);
    } else {
      pdf.setDrawColor(220, 220, 220);
      pdf.rect(startX, startY + logoBlockOffsetY, logoWidth, logoHeight);
      pdf.setFontSize(6);
      pdf.text('Logo N/A', startX + logoWidth / 2, startY + logoBlockOffsetY + logoHeight / 2, {
        align: 'center',
      });
    }

    pdf.setFontSize(carNumberFontSize);
    pdf.setFont('helvetica', 'normal');
    const carNumberTitleY =
      startY + logoBlockOffsetY + logoHeight + carNumberMarginTop + textHeightApproximation / 2;
    pdf.text(`CAR# ${vehicle.carNumber}`, startX + logoWidth / 2, carNumberTitleY, { align: 'center' });

    pdf.setFontSize(8);
    pdf.setFont('helvetica', 'normal');
    const vinY = carNumberTitleY + textHeightApproximation + 1.5;
    pdf.text(`VIN# ${vehicle.vin}`, startX + logoWidth / 2, vinY, { align: 'center' });

    const qrXPosition = startX + logoWidth + 5;
    if (qrDataUrl) {
      pdf.addImage(qrDataUrl, 'PNG', qrXPosition, startY, qrSize, qrSize);
    } else {
      pdf.setDrawColor(200, 200, 200);
      pdf.rect(qrXPosition, startY, qrSize, qrSize);
      pdf.setFontSize(10);
      pdf.text('QR N/A', qrXPosition + qrSize / 2, startY + qrSize / 2, { align: 'center' });
    }

    pdf.setFontSize(16);
    pdf.setFont('helvetica', 'bold');
    const titleY = startY + Math.max(qrSize, logoAreaTotalHeight) + 15;
    pdf.text(`${vehicle.brand} ${vehicle.model}`, pageWidth / 2, titleY, {
      align: 'center',
    });

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'italic');
    pdf.text('Generado por OCN Admin System', pageWidth / 2, pageHeight - 25, { align: 'center' });
    pdf.text(`Fecha: ${new Date().toLocaleDateString()}`, pageWidth / 2, pageHeight - 20, {
      align: 'center',
    });

    const pdfBlob = pdf.output('blob');
    const url = URL.createObjectURL(pdfBlob);
    window.open(url, '_blank');
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};

export const generateMultipleQRCodesPDF = async (vehicles: VehicleWithQR[]): Promise<void> => {
  try {
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    pdf.setProperties({
      title: `Códigos QR - ${vehicles.length} Vehículos`,
      subject: 'Vehicle QR Codes',
      author: 'OCN Admin',
      creator: 'OCN Admin System',
    });

    const loadImageAsDataURL = (url: string): Promise<string> => {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          canvas.width = img.width;
          canvas.height = img.height;
          if (ctx) {
            ctx.drawImage(img, 0, 0);
            resolve(canvas.toDataURL('image/png'));
          } else {
            reject(new Error('Could not get canvas context'));
          }
        };
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    };

    const qrDataUrls: string[] = [];
    for (const vehicle of vehicles) {
      try {
        const dataUrl = await loadImageAsDataURL(vehicle.qrCode.url);
        qrDataUrls.push(dataUrl);
      } catch (error) {
        console.error(`Error loading QR for vehicle ${vehicle.carNumber}:`, error);
        qrDataUrls.push('');
      }
    }

    let logoDataUrl = '';
    try {
      logoDataUrl = await loadImageAsDataURL('/images/Logo.png');
    } catch (error) {
      console.error('Error loading company logo. Make sure /images/Logo.png is in the public folder:', error);
    }

    const pageWidth = 210;
    const pageHeight = 297;
    const pageMargin = 15;
    const itemSpacing = 10;

    const qrSize = 25.4;
    const logoScaleFactor = 0.45;
    const logoHeight = qrSize * logoScaleFactor;
    const logoWidth = logoHeight * (170 / 61);
    const carNumberFontSize = 8;
    const carNumberMarginTop = 2;
    const textHeightApproximation = carNumberFontSize * 0.352778;

    const itemContentWidth = logoWidth + 5 + qrSize;
    const logoAreaHeight = logoHeight + carNumberMarginTop + textHeightApproximation;
    const itemContentHeight = Math.max(qrSize, logoAreaHeight) + 5;

    const availableWidth = pageWidth - 2 * pageMargin;
    const availableHeight = pageHeight - 2 * pageMargin;

    const itemsPerRow = Math.floor((availableWidth + itemSpacing) / (itemContentWidth + itemSpacing));
    const actualRowsNeeded = Math.ceil(vehicles.length / itemsPerRow);
    const finalGridHeight =
      actualRowsNeeded * itemContentHeight +
      (actualRowsNeeded > 1 ? (actualRowsNeeded - 1) * itemSpacing : 0);

    const actualGridWidth =
      itemsPerRow * itemContentWidth + (itemsPerRow > 1 ? (itemsPerRow - 1) * itemSpacing : 0);

    const gridStartX = (pageWidth - actualGridWidth) / 2;
    let gridStartY = (pageHeight - finalGridHeight) / 2;
    if (gridStartY < pageMargin) {
      gridStartY = pageMargin;
    }

    let currentPage = 0;
    let itemsProcessed = 0;

    for (let i = 0; i < vehicles.length; i++) {
      const vehicle = vehicles[i];
      const qrDataUrl = qrDataUrls[i];

      let itemIndexOnPage = itemsProcessed % (itemsPerRow * actualRowsNeeded); // Reset index for new page logic
      let rowIndex = Math.floor(itemIndexOnPage / itemsPerRow);
      let colIndex = itemIndexOnPage % itemsPerRow;

      const maxItemsEffectivelyPerPage =
        Math.floor((availableHeight + itemSpacing) / (itemContentHeight + itemSpacing)) * itemsPerRow;
      if (itemsProcessed > 0 && itemsProcessed % maxItemsEffectivelyPerPage === 0 && i < vehicles.length) {
        pdf.addPage();
        currentPage++;
        itemsProcessed = 0;
        itemIndexOnPage = 0;
        rowIndex = 0;
        colIndex = 0;
      } else if (itemsProcessed === 0 && currentPage === 0) {
        currentPage = 1;
      }

      const currentXBase = gridStartX + colIndex * (itemContentWidth + itemSpacing);
      const currentYBaseOnPage = gridStartY + rowIndex * (itemContentHeight + itemSpacing);

      let logoBlockOffsetY = 0;
      if (qrSize > logoAreaHeight) {
        logoBlockOffsetY = (qrSize - logoAreaHeight) / 2;
      }

      if (logoDataUrl) {
        pdf.addImage(
          logoDataUrl,
          'PNG',
          currentXBase,
          currentYBaseOnPage + logoBlockOffsetY,
          logoWidth,
          logoHeight
        );
      } else {
        pdf.setDrawColor(220, 220, 220);
        pdf.rect(currentXBase, currentYBaseOnPage + logoBlockOffsetY, logoWidth, logoHeight);
        pdf.setFontSize(6);
        pdf.text(
          'Logo',
          currentXBase + logoWidth / 2,
          currentYBaseOnPage + logoBlockOffsetY + logoHeight / 2,
          { align: 'center' }
        );
      }

      pdf.setFontSize(carNumberFontSize);
      pdf.setFont('helvetica', 'normal');
      const carNumberTitleDrawY =
        currentYBaseOnPage + logoBlockOffsetY + logoHeight + carNumberMarginTop + textHeightApproximation / 2;
      pdf.text(`CAR# ${vehicle.carNumber}`, currentXBase + logoWidth / 2, carNumberTitleDrawY, {
        align: 'center',
      });

      pdf.setFontSize(7);
      pdf.setFont('helvetica', 'normal');
      const vinDrawY = carNumberTitleDrawY + textHeightApproximation + 1.5;
      pdf.text(`VIN# ${vehicle.vin}`, currentXBase + logoWidth / 2, vinDrawY, { align: 'center' });

      const qrXDrawPosition = currentXBase + logoWidth + 5;
      if (qrDataUrl) {
        pdf.addImage(qrDataUrl, 'PNG', qrXDrawPosition, currentYBaseOnPage, qrSize, qrSize);
      } else {
        pdf.setDrawColor(200, 200, 200);
        pdf.rect(qrXDrawPosition, currentYBaseOnPage, qrSize, qrSize);
        pdf.setFontSize(8);
        pdf.text('QR N/A', qrXDrawPosition + qrSize / 2, currentYBaseOnPage + qrSize / 2, {
          align: 'center',
        });
      }

      itemsProcessed++;
    }

    // Add footer to each page if pagination occurred, or once if not.
    for (let pageNum = 0; pageNum < currentPage; pageNum++) {
      pdf.setPage(pageNum + 1);
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'italic');
      pdf.text('Generado por OCN Admin System', pageWidth / 2, pageHeight - 10, { align: 'center' });
      pdf.text(`Fecha: ${new Date().toLocaleDateString()}`, pageWidth / 2, pageHeight - 5, {
        align: 'center',
      });
    }

    const pdfBlob = pdf.output('blob');
    const url = URL.createObjectURL(pdfBlob);
    window.open(url, '_blank');
  } catch (error) {
    console.error('Error generating multiple QR codes PDF:', error);
    throw error;
  }
};
