import React from 'react';

interface ProgressBarProps {
  /** Optional upload progress percentage (0-100) */
  progress?: number;
  /** Animation duration in ms when no progress prop is provided */
  duration?: number;
  color?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ progress, duration = 3000, color = '#5800F7' }) => {
  return (
    <>
      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
        <div
          style={{
            width: progress !== undefined ? `${progress}%` : '100%',
            backgroundColor: color,
            height: '100%',
            animation: progress === undefined ? `loader ${duration / 1000}s ease-out forwards` : undefined,
          }}
        ></div>
      </div>
      <style jsx>{`
        @keyframes loader {
          0% {
            width: 0%;
          }
          100% {
            width: 100%;
          }
        }
      `}</style>
    </>
  );
};

export default ProgressBar;
