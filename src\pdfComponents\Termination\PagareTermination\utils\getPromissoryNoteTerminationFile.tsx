import { pdf } from '@react-pdf/renderer';
import PagareTerminationDocumentPDF from '../PagareTermination-DocumentPDF';

type PagareTerminationDocumentPDFProps = React.ComponentProps<typeof PagareTerminationDocumentPDF>;

/**
 * Function to get the pagare termination file
 * @param data - The props of the component PagareTerminationDocumentPDF
 * @returns The file instance
 */

export const getPromissoryNoteTerminationFile = async (data: PagareTerminationDocumentPDFProps) => {
  const blob = await pdf(<PagareTerminationDocumentPDF {...data} />).toBlob();

  const buffer = await blob.arrayBuffer();
  const upperCaseFirstName = data.firstName.split('')[0].toUpperCase() + data.firstName.slice(1);
  const upperCaseLastName = data.lastName.split('')[0].toUpperCase() + data.lastName.slice(1);

  // create random id with 6 characters including numbers and letters with upperCase and lowerCase
  const randomId = Math.random().toString(36).slice(2, 8);

  const fileInstance = new File(
    [buffer],
    `Pagaré-${upperCaseFirstName}${upperCaseLastName}-${randomId}.pdf`,
    {
      type: 'application/pdf',
    }
  );

  return fileInstance;
};
