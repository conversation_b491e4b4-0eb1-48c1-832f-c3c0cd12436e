/* eslint-disable jsx-a11y/alt-text */
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import PagareGris from '../assets/images/logoGris.png';
import { getYear, parseISO } from 'date-fns';
// import es from 'date-fns/locale/es';
import moment from 'moment';
import 'moment/locale/es';

const rowGap = '3vh' as unknown as number;

const styles = StyleSheet.create({
  body: {
    flexDirection: 'column',
    rowGap: rowGap,
    marginBottom: '20px',
  },

  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    marginTop: '70px',
    opacity: 0.2,
    height: '500px',
    width: '100%',
  },

  anexoSubTitle: {
    color: '#6210FF',
    textAlign: 'center',
    fontStyle: 'Helvetica-Bold',
    fontSize: 12,
    marginBottom: 10,
  },

  date: {
    alignItems: 'flex-end',
    textAlign: 'right',
    fontSize: 11,
  },

  read: {
    color: 'black',
    textAlign: 'center',
    fontSize: 8,
    marginTop: 15,
    fontFamily: 'Helvetica-Oblique',
  },

  text: {
    fontSize: '10px',
    marginBottom: '10px',
    lineHeight: '2px',
  },

  content: {
    alignItems: 'center',
    marginTop: '20px',
  },
});

interface UsoFacturaProps {
  city: string;
  date: string;
  firstName: string;
  lastName: string;
  brand: string;
  model: string;
  version: string;
  vin: string;
  policyNumber: string;
  plates: string;
  km: string;
}

export default function UsoFactura({ city, date, ...rest }: UsoFacturaProps) {
  const year = getYear(parseISO(date));

  const day = moment(date).format('DD');
  const month = moment(date).format('MMMM');
  const fullName = `${rest.firstName} ${rest.lastName}`;
  return (
    <View style={styles.body} break>
      <Image src={PagareGris.src} style={styles.viewBackground} />

      <Text style={styles.anexoSubTitle}>Recepción y uso de factura</Text>

      <Text style={styles.date}>
        {city}, a {day} de {month} del {year}
      </Text>

      <Text style={styles.text}>
        Yo,{fullName}, recibo copia de la factura original del auto de la marca {rest.brand}, modelo{' '}
        {rest.model} y con número de serie {rest.vin}, recibiendo esta únicamente para realizar el trámite de
        la primera verificación.
      </Text>

      <Text style={styles.text}>
        La copia de la factura puede ser solicitada por el verificentro, debe entregarla. Al recibir esta
        factura estoy consciente de que será únicamente con fines de trámites, y que no se me está brindando
        la propiedad del auto en ningún momento.
      </Text>

      <Text style={styles.text}>
        Al recibir esta copia de la factura me hago responsable de cualquier mal uso que se le pudiese dar,
        así como de cualquier fotografía o copia que se le pudiese hacer.
      </Text>

      <View style={styles.content}>
        <Text style={styles.text}>________________________</Text>
        <Text style={styles.text}>
          {rest.firstName} {rest.lastName}
        </Text>
      </View>
    </View>
  );
}
