'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON>dal<PERSON><PERSON>er,
  ModalOverlay,
  ModalContent,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  ModalFooter,
  SimpleGrid,
  Box,
  Select,
  useToast,
  InputGroup,
  InputLeftAddon,
} from '@chakra-ui/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Formik, FormikValues, Form, Field } from 'formik';
import { statesSelect } from '@/constants';
import { useEffect, useState, useTransition } from 'react';
import { StateOption, URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { editPersonalDataSchema } from '@/validatorSchemas/editPersonalDataSchema';

interface PersonalData {
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  birthdate?: string | null;
  taxId?: string | null;
  nationalId?: string | null;
  postalCode?: string | null;
  city?: string | null;
  state?: string | null;
  neighborhood?: string | null;
  street?: string | null;
  streetNumber?: string | null;
  department?: string | null;
}

export function PersonalDataDialog({ data, requestId }: { data: PersonalData; requestId: string }) {
  const router = useRouter();
  const toast = useToast();
  const searchParams = useSearchParams();
  const [isTransitioning, startTransition] = useTransition();
  const [isSubmitting, setIsSubmitting] = useState(false);
  // const pathname = usePathname();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [isOpen, setIsOpen] = useState(searchParams.get('dialog') === 'edit-personal-data');
  const hardCodedCountryCode = '+52';
  function onClose() {
    setIsOpen(false);
    router.back();
  }

  useEffect(() => {
    if (searchParams.get('dialog') === 'edit-personal-data') {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  }, [searchParams]);

  // If phone alread hast the country code, remove it
  const phone = data.phone?.startsWith(hardCodedCountryCode)
    ? data.phone.replace(hardCodedCountryCode, '')
    : data.phone;

  const initialValues: PersonalData = {
    firstName: data.firstName,
    lastName: data.lastName,
    email: data.email,
    phone: phone,
    birthdate: data.birthdate,
    taxId: data.taxId,
    nationalId: data.nationalId,
    postalCode: data.postalCode,
    city: data.city,
    state: data.state,
    neighborhood: data.neighborhood,
    street: data.street,
    streetNumber: data.streetNumber,
    department: data.department,
  };

  async function handleSubmit(values: FormikValues) {
    if (!user) return null;
    setIsSubmitting(true);
    // Create a payload object containing only the fields that have changed
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      phone: values.phone,
      birthdate: values.birthdate,
      taxId: values.taxId,
      nationalId: values.nationalId,
      postalCode: values.postalCode,
      city: values.city,
      state: values.state,
      neighborhood: values.neighborhood,
      street: values.street,
      streetNumber: values.streetNumber,
      department: values.department,
    };

    if (payload.phone) {
      // Trim and set null if empty string
      const formattedPhone = payload.phone ? `${hardCodedCountryCode}${payload.phone.trim()}` : null;
      payload.phone = formattedPhone;
    }

    const res = await fetch(`${URL_API}/admission/requests/${requestId}/personal-data`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
      body: JSON.stringify(payload),
    });

    const response = await res.json();
    if (response && response.success) {
      startTransition(() => {
        setTimeout(() => {
          window.location.reload();
        }, 2500);

        toast({
          title: 'Datos personales actualizados',
          status: 'success',
        });
      });
    }

    setIsSubmitting(false);
    setIsOpen(false);
    router.back();
    return response;
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent minWidth="fit-content">
        <ModalHeader>Editar</ModalHeader>
        <ModalCloseButton />
        <Formik
          initialValues={initialValues}
          validationSchema={editPersonalDataSchema}
          onSubmit={handleSubmit}
        >
          {({ errors, touched, isValid, setFieldValue }) => {
            const validate = isValid && touched && !isTransitioning;
            return (
              <Form>
                <ModalBody>
                  <SimpleGrid columns={2} spacing={4}>
                    <Box gridColumn="span 1">
                      <FormControl isInvalid={!!(touched.firstName && errors.firstName)}>
                        <FormLabel>Nombre(s)</FormLabel>
                        <Field name="firstName" as={Input} />
                        <FormErrorMessage>{errors.firstName}</FormErrorMessage>
                      </FormControl>
                    </Box>
                    <Box gridColumn="span 1">
                      <FormControl isInvalid={!!(touched.lastName && errors.lastName)}>
                        <FormLabel>Apellido(s)</FormLabel>
                        <Field name="lastName" as={Input} />
                        <FormErrorMessage>{errors.lastName}</FormErrorMessage>
                      </FormControl>
                    </Box>
                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.email && errors.email)}>
                        <FormLabel>Correo electrónico</FormLabel>
                        <Field name="email" as={Input} />
                        <FormErrorMessage>{errors.email}</FormErrorMessage>
                      </FormControl>
                    </Box>
                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.phone && errors.phone)}>
                        <FormLabel>Teléfono</FormLabel>
                        <InputGroup>
                          <InputLeftAddon>{hardCodedCountryCode}</InputLeftAddon>
                          <Field name="phone" as={Input} />
                        </InputGroup>
                        <FormErrorMessage>{errors.phone}</FormErrorMessage>
                      </FormControl>
                    </Box>
                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.birthdate && errors.birthdate)}>
                        <FormLabel>Fecha de Nacimiento</FormLabel>
                        <Field name="birthdate" as={Input} type="date" />
                        <FormErrorMessage>{errors.birthdate}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.taxId && errors.taxId)}>
                        <FormLabel>RFC</FormLabel>
                        <Field
                          name="taxId"
                          as={Input}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            // Convert the value to uppercase
                            const upperCaseValue = e.target.value.toUpperCase();
                            // Set the value in Formik
                            setFieldValue('taxId', upperCaseValue);
                          }}
                        />
                        <FormErrorMessage>{errors.taxId}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.nationalId && errors.nationalId)}>
                        <FormLabel>CURP</FormLabel>
                        <Field
                          name="nationalId"
                          as={Input}
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            // Convert the value to uppercase
                            const upperCaseValue = e.target.value.toUpperCase();
                            // Set the value in Formik
                            setFieldValue('nationalId', upperCaseValue);
                          }}
                        />
                        <FormErrorMessage>{errors.nationalId}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.postalCode && errors.postalCode)}>
                        <FormLabel>Código Postal</FormLabel>
                        <Field name="postalCode" as={Input} />
                        <FormErrorMessage>{errors.postalCode}</FormErrorMessage>
                      </FormControl>
                    </Box>
                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.state && errors.state)}>
                        <FormLabel>Estado</FormLabel>
                        <Field name="state" as={Select} placeholder="Estado">
                          {statesSelect.map((state: StateOption) => (
                            <option key={state.value} value={state.value}>
                              {state.label}
                            </option>
                          ))}
                        </Field>
                        <FormErrorMessage>{errors.state}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.city && errors.city)}>
                        <FormLabel>Ciudad</FormLabel>
                        <Field name="city" as={Input} />
                        <FormErrorMessage>{errors.city}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.neighborhood && errors.neighborhood)}>
                        <FormLabel>Colonia</FormLabel>
                        <Field name="neighborhood" as={Input} />
                        <FormErrorMessage>{errors.neighborhood}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 2">
                      <FormControl isInvalid={!!(touched.street && errors.street)}>
                        <FormLabel>Calle</FormLabel>
                        <Field name="street" as={Input} />
                        <FormErrorMessage>{errors.street}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 1">
                      <FormControl isInvalid={!!(touched.streetNumber && errors.streetNumber)}>
                        <FormLabel>Número Exterior</FormLabel>
                        <Field name="streetNumber" as={Input} />
                        <FormErrorMessage>{errors.streetNumber}</FormErrorMessage>
                      </FormControl>
                    </Box>

                    <Box gridColumn="span 1">
                      <FormControl isInvalid={!!(touched.department && errors.department)}>
                        <FormLabel>Número Interior (Opcional)</FormLabel>
                        <Field name="department" as={Input} />
                        <FormErrorMessage>{errors.department}</FormErrorMessage>
                      </FormControl>
                    </Box>
                  </SimpleGrid>
                </ModalBody>
                <ModalFooter gap={3}>
                  <Button
                    sx={{
                      color: '#5800F7',
                      borderColor: '#5800F7 !important',
                      border: '1px',
                      h: '40px',
                    }}
                    onClick={onClose}
                  >
                    Cancelar
                  </Button>
                  <Button
                    sx={{
                      // bg: '#5800F7 !important',
                      color: 'white',
                      h: '40px',
                    }}
                    className={
                      validate
                        ? 'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'
                        : 'bg-gray-400 cursor-not-allowed'
                    }
                    type="submit"
                    disabled={validate}
                    isLoading={isSubmitting}
                  >
                    Guardar
                  </Button>
                </ModalFooter>
              </Form>
            );
          }}
        </Formik>
      </ModalContent>
    </Modal>
  );
}
