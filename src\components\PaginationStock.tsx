'use client';

import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

import { cn } from '@/utils/styling';

const PAGES_COUNT = 5;

interface PaginationProps {
  currentPage: number;
  hasMorePages: boolean;
  pagesCount: number;
  onPageChange?: (page: number) => void; // New prop for page change handler
}

export function PaginationStock(props: PaginationProps) {
  const { currentPage = 1, hasMorePages = true, pagesCount, onPageChange } = props;

  const hasPrevious = currentPage > 1;
  const numbers = getPageNumbers(currentPage, hasMorePages, pagesCount);

  // Handle page change
  const handlePageChange = (page: number) => {
    if (onPageChange && page >= 1 && page <= pagesCount) {
      onPageChange(page);
    }
  };

  if (pagesCount <= 1) {
    return null;
  }

  if (!hasMorePages && currentPage === 1) {
    return null;
  }

  return (
    <div className="flex justify-center lg:justify-end">
      <nav className="flex justify-center -space-x-px rounded-md ">
        {/* Previous Page Button */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={!hasPrevious}
          className={cn(
            'relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0',
            'disabled:pointer-events-none disabled:opacity-30'
          )}
        >
          <span className="sr-only">Anterior</span>
          <FaChevronLeft className="h-3 w-3 text-gray-600 mx-1" aria-hidden="true" />
        </button>

        {/* First page and ellipsis */}
        {currentPage > (PAGES_COUNT - 1) / 2 + 1 && (
          <>
            <button
              onClick={() => handlePageChange(1)}
              className="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0 sm:inline-flex"
            >
              1
            </button>
            <div className="relative hidden items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0 sm:inline-flex">
              ...
            </div>
          </>
        )}

        {/* Page Numbers */}
        {numbers.map((number) => (
          <button
            key={`page_${number}`}
            onClick={() => handlePageChange(number)}
            aria-current={currentPage === number ? 'page' : undefined}
            className={cn(
              'relative inline-flex w-12 items-center justify-center px-4 py-2 text-sm font-semibold tabular-nums text-gray-900 ring-1 ring-inset ring-gray-300 focus:z-20 focus:outline-offset-0',
              currentPage === number ? 'bg-gray-100 text-[#5800F7]' : 'hover:bg-gray-100 hover:text-gray-600'
            )}
          >
            {number}
          </button>
        ))}

        {/* Next Page Button */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={!hasMorePages}
          className={cn(
            'relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 hover:text-gray-600 focus:z-20 focus:outline-offset-0',
            'disabled:pointer-events-none disabled:opacity-30'
          )}
        >
          <span className="sr-only">Siguiente</span>
          <FaChevronRight className="h-3 w-3 text-gray-600 mx-1" aria-hidden="true" />
        </button>
      </nav>
    </div>
  );
}

function getPageNumbers(currentPage: number, hasMorePages: boolean, pagesCount: number) {
  const numbers: number[] = [];
  const hasPrevious = currentPage > 1;

  if (pagesCount <= 7) {
    return Array.from({ length: pagesCount }, (_, i) => i + 1);
  }

  if (hasPrevious) {
    numbers.push(currentPage - 1);
  }

  numbers.push(currentPage);

  if (hasMorePages) {
    numbers.push(currentPage + 1);
  }

  if (pagesCount && pagesCount > PAGES_COUNT) {
    const lastPage = pagesCount;
    const firstPage = 1;

    if (currentPage > (PAGES_COUNT - 1) / 2 + 1) {
      numbers.unshift(firstPage);
      numbers.unshift(firstPage + 1);
    }

    if (currentPage < lastPage - (PAGES_COUNT - 1) / 2 - 1) {
      numbers.push(lastPage - 1);
      numbers.push(lastPage);
    }
  }

  return numbers;
}
