import { getCookie, set<PERSON><PERSON><PERSON> } from 'cookies-next';

/**
 * Remove a key from a cookie object, and set the new cookie object
 * @param key Key to remove from the cookie object
 * @param cookieName Name of the cookie
 */

export const removeCookieObjKey = (key: string, cookieName: string) => {
  const cookieObj = JSON.parse(getCookie(cookieName) || '{}');

  if (cookieObj[key]) {
    delete cookieObj[key];
  }

  setCookie(cookieName, JSON.stringify(cookieObj));
};
