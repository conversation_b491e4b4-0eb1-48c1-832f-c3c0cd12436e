import { URL_API } from '@/constants';
import { Media } from './FileUpload';
import { useState } from 'react';
import { MediaType } from '@/app/dashboard/clientes/solicitudes/enums';

export const useFileUpload = (mediaType: MediaType, totalFiles = 5) => {
  const [uploadedFiles, setUploadedFiles] = useState<Media[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const uploadFile = async (file: File) => {
    setIsLoading(true);
    const formData = new FormData();
    formData.append('file', file);
    formData.append('mediaType', mediaType);

    const res = await fetch(`${URL_API}/media/upload`, {
      method: 'POST',
      body: formData,
    });

    const response = await res.json();
    if (response && response.success) {
      setUploadedFiles([...uploadedFiles, ...response.data]);
    }
    setIsLoading(false);
  };

  const deleteFile = (index: number) => {
    const newUploadedFiles = uploadedFiles.filter((_, idx) => idx !== index);
    setUploadedFiles(newUploadedFiles);
  };

  return {
    uploadedFiles,
    uploadFile,
    deleteFile,
    isLoading,
    istotalFilesReached: uploadedFiles.length >= totalFiles,
  };
};

export default useFileUpload;
