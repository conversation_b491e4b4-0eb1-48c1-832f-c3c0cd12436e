import { columns, columnsUS } from './columns';
import { DataTable } from '@/components/DataTable';
import { User } from '../types';
import axios from 'axios';
import { Countries, PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { ClientSectionHeader } from './_components/ClientsHeader';
import { CountryProvider } from '../../providers/CountryProvider';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';

async function getClients(url: string): Promise<User[]> {
  let res = null;
  try {
    res = await axios(url, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

interface ClientesProps {
  searchParams: Record<string, string>;
}

export default async function Clientes({ searchParams }: ClientesProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const url = new URL(`${PAYMENTS_API_URL}/clients`);
  for (const [key, value] of Object.entries(searchParams)) {
    url.searchParams.append(key, value);
  }

  let data: User[] = [];
  const response = await getClients(url.toString());
  if (response) {
    data = response;
  }

  const country = searchParams.country;
  const isCountryUSA = country === Countries['United States'];
  let tableColumns = columns;
  if (isCountryUSA) {
    tableColumns = columnsUS;
  }

  return (
    <CountryProvider>
      <section className="py-8">
        <div className="container">
          <ClientSectionHeader />
          <DataTable columns={tableColumns} data={data} />
        </div>
      </section>
    </CountryProvider>
  );
}
