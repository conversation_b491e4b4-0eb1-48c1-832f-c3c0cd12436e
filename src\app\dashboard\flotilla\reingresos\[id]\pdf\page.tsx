'use client';
import { useParams } from 'next/navigation';
import React from 'react';

export default function PreviewPDFGeneratedPage() {
  const id = useParams().id;

  const readmissionData = JSON.parse(localStorage.getItem('readmissionData' + id) || 'null');

  if (!readmissionData)
    return (
      <div className="fixed top-0 left-0 w-full h-full bg-gray-900 text-white z-50">
        <div className="flex flex-col p-14 h-full">
          <h1 className="text-3xl font-bold">No se encontrarón datos de este vehiculo</h1>
          <p className="text-lg">No se ha generado el PDF</p>
        </div>
      </div>
    );

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-gray-900 text-white z-50">
      <div className="flex flex-col p-14 h-full">
        <h1 className="text-3xl font-bold">PDF generado</h1>
        <p className="text-lg">El PDF se ha generado correctamente</p>

        <a href="/dashboard/flotilla/reingresos" className="mt-4 text-blue-500">
          Regresar
        </a>
      </div>
    </div>
  );
}
