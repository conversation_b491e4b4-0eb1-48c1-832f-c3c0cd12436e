'use client';

import { MyUser } from '@/actions/getCurrentUser';
import {
  StatusTranslations,
  StatusTranslationsMX,
  VehicleCategoryTranslations,
  VehicleCategoryTranslationsMX,
} from '@/app/dashboard/flotilla/components/translations/statusTranslations';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Paths, Sections, Subsections, URL_API } from '@/constants';
import useSidebarStore from '@/zustand/sidebarStore';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { FaClock, FaChevronDown } from 'react-icons/fa';

const InactivoNavItems = () => {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const search = useSearchParams();

  const country = search ? search.get('country') : '';

  const { data: session } = useSession();
  //console.log("session",session?.user)
  const user = session?.user as unknown as MyUser;
  //console.log("session2",session?.user)
  //console.log("user",user)
  const toggleOpen = () => setIsOpen(!isOpen);

  const vehicleStatusCounts = useSidebarStore((state) => state.vehicleStatusCounts);
  const categoryCounts = useSidebarStore((state) => state.categoryCounts);

  const { setVehicleStatusCounts, setCategoryCounts } = useSidebarStore((state) => ({
    setVehicleStatusCounts: state.setVehicleStatusCounts,
    setCategoryCounts: state.setCategoryCounts,
  }));

  useEffect(() => {
    if (pathname.includes('inactive')) {
      setIsOpen(true);
    }
  }, [pathname]);

  useEffect(() => {
    if (user?.accessToken) {
      const fetchSidebarData = async () => {
        try {
          const response = await axios.get(
            `${URL_API}/user/sideData/${country ? `?country=${encodeURI(country)}` : ''}`,
            {
              headers: {
                Authorization: `Bearer ${user?.accessToken}`,
              },
            }
          );

          if (response.data?.sideBarData) {
            // console.log(vehicleStatusCounts, categoryCounts);
            setVehicleStatusCounts(response?.data?.sideBarData?.vehicleStatusCounts);
            setCategoryCounts(response?.data?.sideBarData?.categoryCounts);
          }
        } catch (error) {
          console.error('Error fetching sidebar data:', error);
        }
      };
      fetchSidebarData();
    }
  }, [user]);

  const isMexico = country === 'Mexico';
  const statusTranslations = isMexico ? StatusTranslationsMX : StatusTranslations;
  const categoryTranslations = isMexico ? VehicleCategoryTranslationsMX : VehicleCategoryTranslations;

  const navButton = {
    icon: <FaClock size={18} />,
    name: `${statusTranslations.inactive} (${vehicleStatusCounts?.inactive})`,
  };
  const ability = usePermissions();

  const subNavLinks = [
    {
      link: Paths.fleet_inactive + '/in-preparation',
      key: 'in-preparation',
    },
    { link: Paths.fleet_inactive + '/stock', key: 'stock' },
    { link: Paths.fleet_inactive + '/assigned', key: 'assigned' },
    { link: Paths.fleet_inactive + '/delivered', key: 'delivered' },
    { link: Paths.fleet_inactive + '/insurance', key: 'insurance' },
    { link: Paths.fleet_inactive + '/workshop', key: 'workshop' },
    { link: Paths.fleet_inactive + '/revision', key: 'revision' },
    { link: Paths.fleet_inactive + '/legal', key: 'legal' },
    { link: Paths.fleet_inactive + '/collection', key: 'collection' },
    { link: Paths.fleet_inactive + '/withdrawn', key: 'withdrawn' },
    { link: Paths.fleet_inactive + '/sold', key: 'sold' },
    { link: Paths.fleet_inactive + '/adendum', key: 'adendum' },
    { link: Paths.fleet_inactive + '/utilitary', key: 'utilitary' },
  ]
    .map((item) => ({
      ...item,
      name: `${categoryTranslations[item.key as keyof typeof categoryTranslations]} (${
        categoryCounts?.[item.key as keyof typeof categoryTranslations] || 0
      })`,
      link: country ? `${item.link}?country=${encodeURI(country)}` : item.link,
    }))
    .filter((item) => canPerform(ability, item.key, Sections.Fleet, Subsections.Inactive));

  const isAnySubLinkActive = subNavLinks.some((item) => pathname.includes(item.link.split('?')[0]));
  //const isAnySubLinkActive = subNavLinks.some((item) => pathname?.includes(item.link));

  return (
    <div className="group transition-all duration-150 content-center h-auto">
      <div
        onClick={toggleOpen}
        className={`transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 mr-2 ${
          isAnySubLinkActive ? 'bg-primaryPurple text-white' : ''
        }`}
      >
        {navButton.icon}
        <span
          className={`text-sm font-semibold leading-tight ml-2.5 transition duration-300 ${
            isAnySubLinkActive ? 'text-white' : 'text-gray-600'
          }`}
        >
          {navButton.name}
        </span>
        <span className={`ml-auto shrink-0 transition duration-300 ${isOpen ? 'rotate-180' : ''}`}>
          <FaChevronDown color={isAnySubLinkActive ? 'white' : 'black'} />
        </span>
      </div>
      {isOpen && (
        <nav className="mt-1.5 ml-5 flex-col transition-all duration-500">
          {subNavLinks.map((item, key) => (
            <Link
              href={pathname?.includes(item.link.split('?')[0]) ? '#' : item.link}
              key={key}
              prefetch={false}
            >
              <button
                className={`ml-3 flex items-center rounded-lg px-4 py-2 ${
                  pathname?.includes(item.link.split('?')[0])
                    ? 'text-white bg-primaryPurple'
                    : 'hover:bg-gray-100 hover:text-gray-700'
                }`}
                style={{ width: '90%' }}
              >
                <span className="text-sm font-medium">{item.name}</span>
              </button>
            </Link>
          ))}
        </nav>
      )}
    </div>
  );
};

export default InactivoNavItems;
