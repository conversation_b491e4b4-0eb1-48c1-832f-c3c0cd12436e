'use client';
import { Fragment } from 'react';
import { FaCheck } from 'react-icons/fa';

import {
  RequestDocumentTypeTranslationsUS,
  RequestStatusTranslations,
  RequestStatusTranslationsUS,
} from '../../data';
import { AdmissionRequestDocumentTypeUS, AdmissionRequestStatus } from '../../enums';
import { useCountry } from '../detail';

const formatStatus = (status: AdmissionRequestStatus, isCountryUSA: boolean) => {
  if (isCountryUSA) {
    return RequestStatusTranslationsUS[status as keyof typeof RequestStatusTranslationsUS];
  }
  return RequestStatusTranslations[status as keyof typeof RequestStatusTranslations];
};
const formatDocumentType = (type: AdmissionRequestDocumentTypeUS) => {
  return RequestDocumentTypeTranslationsUS[type as keyof typeof RequestDocumentTypeTranslationsUS];
};

export default function StepperCardUS({
  status,
  documentsAnalysis,
}: {
  status: AdmissionRequestStatus;
  documentsAnalysis: any;
}) {
  // Define the base step order
  const baseStepOrder = [
    AdmissionRequestStatus.created,
    AdmissionRequestStatus.earnings_analysis,
    AdmissionRequestStatus.documents_analysis,
    AdmissionRequestStatus.risk_analysis,
    AdmissionRequestStatus.social_analysis,
  ];

  // Add approved and rejected steps conditionally
  const stepOrder = [
    ...baseStepOrder,
    ...(status === AdmissionRequestStatus.approved ? [AdmissionRequestStatus.approved] : []),
    ...(status === AdmissionRequestStatus.rejected ? [AdmissionRequestStatus.rejected] : []),
  ];

  // Get the index of the current step
  const currentStepIndex = stepOrder.indexOf(status);
  const { isCountryUSA } = useCountry();

  return (
    <div className="py-12">
      <div className="p-4 mx-4">
        <div className="flex items-center">
          {status === AdmissionRequestStatus.documents_analysis &&
            documentsAnalysis.documents.map(
              (
                document: {
                  type: AdmissionRequestDocumentTypeUS;
                  status: string;
                },
                index: number
              ) => (
                <Fragment key={index}>
                  {index > 0 && (
                    <div
                      className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
                        document.status === 'approved' ? 'border-[#29CC97]' : 'border-gray-300'
                      }`}
                    ></div>
                  )}
                  <div className="relative flex items-center text-gray-500">
                    <div
                      className={`flex  justify-center items-center rounded-full transition duration-500 ease-in-out h-8 w-8 py-3 border-2 text-center ${
                        document.status === 'approved' ? 'bg-[#29CC97] border-[#29CC97]' : 'border-gray-300'
                      }`}
                    >
                      {document.status === 'approved' ? (
                        <FaCheck className="w-3 h-3 text-white" />
                      ) : (
                        <div className="w-2 h-2 bg-gray-300 rounded-full "></div>
                      )}
                    </div>
                    <div
                      className={`absolute top-0 -ml-11 text-center mt-14 w-32 text-xs font-medium uppercase ${
                        document.status === 'approved' ? 'text-[#29CC97]' : 'text-gray-500'
                      }`}
                    >
                      {formatDocumentType(document.type)}
                    </div>
                  </div>
                </Fragment>
              )
            )}
          {status !== AdmissionRequestStatus.documents_analysis &&
            stepOrder.map((step, index) => (
              <Fragment key={step}>
                {index > 0 && (
                  <div
                    className={`flex-auto border-t-2 transition duration-500 ease-in-out ${
                      index <= currentStepIndex ? 'border-[#29CC97]' : 'border-gray-300'
                    }`}
                  ></div>
                )}
                <div className="relative flex items-center text-gray-500">
                  <div
                    className={`flex  justify-center items-center rounded-full transition duration-500 ease-in-out h-8 w-8 py-3 border-2 text-center ${
                      index <= currentStepIndex ? 'bg-[#29CC97] border-[#29CC97]' : 'border-gray-300'
                    }`}
                  >
                    {index < currentStepIndex ? (
                      <FaCheck className="w-3 h-3 text-white" />
                    ) : (
                      index === currentStepIndex && <div className="w-2 h-2 bg-white rounded-full "></div>
                    )}
                    {index > currentStepIndex && <div className="w-2 h-2 bg-gray-300 rounded-full "></div>}
                  </div>
                  <div
                    className={`absolute top-0 -ml-11 text-center mt-14 w-32 text-xs font-medium uppercase ${
                      index <= currentStepIndex ? 'text-[#29CC97]' : 'text-gray-500'
                    }`}
                  >
                    {formatStatus(step, isCountryUSA)}
                  </div>
                </div>
              </Fragment>
            ))}
        </div>
      </div>
    </div>
  );
}
