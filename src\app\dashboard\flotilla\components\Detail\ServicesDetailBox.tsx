'use client';

import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import CustomInput from '@/components/Inputs/CustomInput';
import InputDate from '@/components/Inputs/InputDate';
import CustomModal from '@/components/Modals/CustomModal';
import { URL_API } from '@/constants';
import { sendServiceSchema } from '@/validatorSchemas/changeStatusSchema';
import { useToast } from '@chakra-ui/react';
import axios from 'axios';
import { useParams, useRouter } from 'next/navigation';
import { useCallback } from 'react';

interface ServicesDetailProps {
  services: {
    dateIn: string;
    dateOut: string;
    comments: string;
  }[];
}

const initialValues = {
  dateIn: '',
  dateOut: '',
  comments: '',
};

export default function ServicesDetailBox({ services }: ServicesDetailProps) {
  const toast = useToast();
  const { user } = useCurrentUser();
  const router = useRouter();
  const { id } = useParams();

  const onSubmit = useCallback(
    async (values: typeof initialValues) => {
      try {
        const response = await axios.patch(`${URL_API}/stock/sendService/${id}`, values, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        toast({
          title: response.data.message,
          description: 'Actualizando pagina...',
          duration: 3000,
          status: 'success',
          position: 'top',
        });
        router.refresh();
        router.push(`/dashboard/flotilla/bajas/${id}`);
      } catch (error: any) {
        toast({
          title: error.response.data.message,
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      }
    },
    [toast, router, id, user.accessToken]
  );

  return (
    <div
      className="
        w-full
        bg-white 
        border-[#EAECEE] 
        font-bold 
        rounded 
        min-h-[300px] 
        py-[25px]
        pl-[20px]
        pr-[15px]
        border-[1px]
        flex
        flex-col
        overflow-y-auto
      "
    >
      <div className="flex justify-between">
        <p className="text-[24px] mb-[5px]">Mantenimientos</p>
        {services.length > 0 && <button>HOLA</button>}
      </div>

      {services.length === 0 && (
        <div className="flex justify-center items-center h-full ">
          <CustomModal
            header="Agregar Mantenimiento"
            openButtonText="Agregar Mantenimiento"
            confirmButtonText="Agregar"
            initialValues={initialValues}
            validatorSchema={sendServiceSchema}
            isPrimaryButton
            onCloseModal={() => console.log('close')}
            onSubmit={onSubmit}
            body={
              <div className="flex flex-col gap-[20px]">
                <InputDate name="dateIn" label="Fecha de ingreso" />
                <InputDate name="dateOut" label="Fecha tentativa de salida" />
                <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
              </div>
            }
          />
        </div>
      )}
    </div>
  );
}
