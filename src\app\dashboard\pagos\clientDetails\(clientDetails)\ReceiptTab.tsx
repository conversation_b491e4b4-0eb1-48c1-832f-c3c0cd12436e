import React from 'react';
import { Receipt } from '../../types';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { DataTable } from '@/components/DataTable';
import { receiptColumns } from './receiptColumns';

async function getReceipts(id: string): Promise<Receipt[]> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/payments/receipts?clientId=${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}

type Props = {
  id?: string;
};

async function ReceiptTab({ id }: Props) {
  let receiptData: Receipt[] = [];
  if (id) {
    const invoiceResponse = await getReceipts(id as string);
    if (invoiceResponse) {
      receiptData = invoiceResponse;
    }
  }

  return <DataTable columns={receiptColumns} data={receiptData}></DataTable>;
}

export default ReceiptTab;
