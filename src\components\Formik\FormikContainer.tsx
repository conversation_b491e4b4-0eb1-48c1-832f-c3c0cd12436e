'use client';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Spinner } from '@chakra-ui/react';
import { Form, Formik, FormikHelpers, FormikValues } from 'formik';
import { ReactNode } from 'react';
import * as Yup from 'yup';

interface FormikContainerProps<T extends FormikValues> {
  children: ReactNode;
  initialValues: T;
  onSubmit: (values: T, formikHelpers: FormikHelpers<T>) => Promise<any>;
  validatorSchema?: Yup.ObjectSchema<any>;
  onClose?: () => void;
  footerClassName?: string;
  confirmBtnText?: string;
  cancelBtnText?: string;
  validateOnMount?: boolean;
  validateChanges?: boolean;
  addFooterBtns?: ReactNode;
  showSubmitButton?: boolean;
  hideFooter?: boolean;
  // This prop is used to invalidate the form validation schema on the button click
  isInvalidated?: boolean;
}

interface WithModal {
  isModal?: true;
  onClose: () => void;
}

interface WithoutModal {
  isModal?: false;
  onClose?: () => void;
}

type TypeValidation<T extends FormikValues> = FormikContainerProps<T> & (WithModal | WithoutModal);

export default function FormikContainer<T extends FormikValues>({
  onSubmit,
  children,
  initialValues,
  onClose,
  confirmBtnText,
  cancelBtnText,
  validatorSchema,
  footerClassName,
  validateOnMount = false,
  addFooterBtns,
  showSubmitButton = true,
  hideFooter = false,
  validateChanges = false,
  isInvalidated,
}: TypeValidation<T>) {
  const { user } = useCurrentUser();
  if (user.role === 'auditor') return null;
  return (
    <Formik
      initialValues={initialValues}
      onSubmit={async (values, formikHelpers) => {
        await onSubmit(values, formikHelpers);
      }}
      validationSchema={isInvalidated ? undefined : validatorSchema}
      validateOnMount={validateOnMount}
    >
      {({ isValid, dirty, isSubmitting, values }) => {
        const validate = dirty && isValid;
        // Special handling for categories without subcategories
        const isSimpleCategory =
          values.category?.value &&
          ['sold', 'utilitary', 'delivered', 'adendum'].includes(values.category.value);
        const shouldValidate = isSimpleCategory ? dirty : validate;

        return (
          <Form className="font-normal">
            {children}
            {hideFooter ? null : (
              <div className={footerClassName ? footerClassName : 'flex gap-3 pt-[20px] justify-end'}>
                <button
                  className="w-[100px] h-[40px] bg-white border-2 font-[600] text-primaryPurple border-primaryBtn rounded-[5px] text-[14px] hover:bg-[#EAEAEA] transition-colors duration-200"
                  onClick={onClose}
                  type="button"
                >
                  {cancelBtnText ? cancelBtnText : 'Cancelar'}
                </button>
                {addFooterBtns && addFooterBtns}
                {showSubmitButton && (
                  <button
                    disabled={!validateChanges && (isSubmitting || (!shouldValidate && !isInvalidated))}
                    type="submit"
                    className={`
                    w-[max-content] min-w-[100px] 
                    h-[40px] 
                    text-white rounded-[5px] 
                    cursor-pointer
                    ${
                      !validateChanges && (isSubmitting || (!shouldValidate && !isInvalidated))
                        ? 'bg-[#9CA3AF]'
                        : 'bg-[#5800F7]'
                    }
                    text-[14px] font-[500] 
                    transition-colors duration-200 px-2
                    `}
                  >
                    {isSubmitting ? (
                      <p className="flex items-center justify-center">
                        <Spinner />
                      </p>
                    ) : (
                      confirmBtnText ?? 'Confirmar'
                    )}
                  </button>
                )}
              </div>
            )}
          </Form>
        );
      }}
    </Formik>
  );
}
