'use client';

import { ReactNode } from 'react';
import { CreateRequestDialog } from '../CreateRequestDialog';
import RequestsFilters from '../RequestsFilters';
import { RequestsWrapper } from '../../../_components/RequestsWrapper';

export const RequestsContainer = ({ children }: { children: ReactNode }) => {
  return (
    <>
      <RequestsWrapper>
        <div className="mb-4 flex flex-row  justify-end items-center px-4">
          <div className="flex gap-4">
            <RequestsFilters />
          </div>
        </div>
        <div className="mt-6">{children}</div>
        <CreateRequestDialog />
      </RequestsWrapper>
    </>
  );
};
