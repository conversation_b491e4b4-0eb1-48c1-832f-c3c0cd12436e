/* eslint-disable prettier/prettier */
'use client';
// import { DataTable } from '@/components/DataTable';
import React from 'react';
import { paymentColumns } from './columns';
import { Payment } from '../types';
import { DataTableV2 } from '@/components/DataTableV2';
// import axios from 'axios';
// import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
// import { ColumnFiltersState, PaginationState, SortingState } from '@tanstack/react-table';
import { PaginationState } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { getCookie, setCookie } from 'cookies-next';
import { X } from 'lucide-react';
import { removeCookieObjKey } from './lib/handle-cookies';
import { usePaymentTransactionState } from '@/hooks/usePaymentTransaction';
import PaymentTransactionModal from './_components/PaymentTransactionModal';

interface PaymentsClientPageProps {
  data: Payment[];
  allRecords: number;
  total: number;
}

export default function PaymentsClientPage({
  data: paymentsData,
  allRecords,
  total,
}: PaymentsClientPageProps) {
  const filters = JSON.parse(getCookie('payments-page-filters') || '{}');
  const { isOpen } = usePaymentTransactionState();
  const router = useRouter();

  async function onPageChange(pagination: PaginationState) {
    setCookie('payments-pagination', JSON.stringify(pagination));
    router.refresh();
  }

  const getAllFiltersOnArray = Object.keys(filters).map((key) => {
    return {
      key,
      value: filters[key],
    };
  });

  return (
    <>
      {/* Text to describe that below are the filters applied */}

      {getAllFiltersOnArray.length > 0 &&
        getAllFiltersOnArray.find((filter) => filter.key === 'region' && filter.value !== 'none') ? (
        <div className="text-sm mt-2">
          <span>Filtros aplicados:</span>
        </div>
      ) : null}

      {getAllFiltersOnArray.map((filter) => {
        if (filter.key === 'region' && filter.value === 'none') return null;
        return (
          <div key={filter.key} className="text-sm mt-2 flex items-center gap-2">
            <span>{filter.key.toUpperCase()}:</span>
            <span>{filter.value}</span>

            <button
              className=" text-red-500"
              onClick={() => {
                removeCookieObjKey(filter.key, 'payments-page-filters');
                router.refresh();
              }}
            >
              <X />
            </button>
          </div>
        );
      })}
      {isOpen && <PaymentTransactionModal />}
      <DataTableV2
        columns={paymentColumns}
        data={paymentsData}
        allRecords={allRecords}
        // allRecords={hasFilters ? total : allRecords}
        total={total}
        onPageChange={onPageChange}
      />
    </>
  );
}
