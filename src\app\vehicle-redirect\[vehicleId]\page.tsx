'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { URL_API } from '@/constants';
import { Loader2 } from 'lucide-react';
import ModalContainer from '@/app/dashboard/flotilla/components/Modals/ModalContainer';
import { PhysicalVehicleStatus } from '@/constants';
import { physicalStatusTranslations } from '@/app/dashboard/flotilla/components/translations/physicalStatusTranslations';
import { Countries } from '@/constants';

interface RedirectInfo {
  redirectUrl: string;
  physicalStatus?: string;
  category?: string;
  vehicleId?: string;
  vehicleCountry?: string;
}

type Params = {
  params: {
    vehicleId: string;
  };
};

export default function VehicleRedirectPage({ params }: Params) {
  const { vehicleId } = params;
  const router = useRouter();
  const { data: session, status } = useSession();
  const [error, setError] = useState<string | null>(null);
  const [showHandoverModal, setShowHandoverModal] = useState(false);
  const [handoverLoading, setHandoverLoading] = useState(false);
  const [redirectInfo, setRedirectInfo] = useState<RedirectInfo | null>(null);

  useEffect(() => {
    const fetchRedirectInfo = async () => {
      try {
        const headers: HeadersInit = {};
        if (status === 'authenticated' && session?.user?.accessToken) {
          headers.Authorization = `Bearer ${session.user.accessToken}`;
        }
        const response = await fetch(`${URL_API}/stock-vehicle/${vehicleId}/redirect-info`, {
          cache: 'no-store',
          headers,
        });
        if (!response.ok) {
          console.error(`API error: ${response.status} ${response.statusText}`);
          setError('Could not fetch redirect information');
          return;
        }
        const data: RedirectInfo = await response.json();
        setRedirectInfo(data);
        // If status is REPAIR_COMPLETE_BY_VENDOR, show modal instead of redirect
        if (data.physicalStatus === PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR) {
          setShowHandoverModal(true);
          return;
        }
        if (!data.redirectUrl) {
          setError('No redirect URL provided');
          return;
        }
        if (data.redirectUrl.startsWith('http')) {
          window.location.href = data.redirectUrl;
        } else {
          router.push(data.redirectUrl);
        }
      } catch (err) {
        setError('Error during redirection');
      }
    };
    fetchRedirectInfo();
  }, [vehicleId, router, session, status]);

  // Handler for handover selection
  const handleHandover = async (handover: 'customer' | 'agent') => {
    if (!redirectInfo) return;
    setHandoverLoading(true);
    try {
      const headers: HeadersInit = {};
      if (status === 'authenticated' && session?.user?.accessToken) {
        headers.Authorization = `Bearer ${session.user.accessToken}`;
      }
      const response = await fetch(
        `${URL_API}/stock-vehicle/${vehicleId}/redirect-info?handover=${handover}`,
        {
          cache: 'no-store',
          headers,
        }
      );
      if (!response.ok) {
        setError('Could not fetch redirect information for handover');
        setHandoverLoading(false);
        return;
      }
      const data: RedirectInfo = await response.json();
      if (!data.redirectUrl) {
        setError('No redirect URL provided for handover');
        setHandoverLoading(false);
        return;
      }
      if (data.redirectUrl.startsWith('http')) {
        window.location.href = data.redirectUrl;
      } else {
        router.push(data.redirectUrl);
      }
    } catch (err) {
      setError('Error during handover redirection');
    } finally {
      setHandoverLoading(false);
    }
  };

  // Helper to get translation object based on vehicleCountry
  const getTranslations = () => {
    const country = redirectInfo?.vehicleCountry as keyof typeof Countries | undefined;
    if (country === Countries.Mexico) {
      return physicalStatusTranslations[Countries.Mexico];
    }
    return physicalStatusTranslations[Countries['United States']];
  };
  const translations = getTranslations();

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-xl font-semibold mb-4">{translations.redirectFailed}</h1>
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  // Show handover modal if needed
  if (showHandoverModal) {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
          <p>{translations.handoverWaiting}</p>
        </div>
        <ModalContainer title={translations.handoverModalTitle} onClose={() => setShowHandoverModal(false)}>
          <div className="flex flex-col gap-6 items-center justify-center p-4">
            <p className="text-lg font-medium mb-2">{translations.handoverQuestion}</p>
            <div className="flex gap-4">
              <button
                className="bg-[#5800F7] text-white px-6 py-2 rounded font-semibold disabled:opacity-60"
                onClick={() => handleHandover('customer')}
                disabled={handoverLoading}
              >
                {translations.handoverCustomer}
              </button>
              <button
                className="bg-[#5800F7] text-white px-6 py-2 rounded font-semibold disabled:opacity-60"
                onClick={() => handleHandover('agent')}
                disabled={handoverLoading}
              >
                {translations.handoverAgent}
              </button>
            </div>
            {handoverLoading && <Loader2 className="h-6 w-6 animate-spin text-primary mt-4" />}
          </div>
        </ModalContainer>
      </>
    );
  }

  // Default loading state while fetching redirect info
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p>{translations.redirecting}</p>
    </div>
  );
}
