import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';
import CalendarPreview from './_components/Calendar';
import { Suspense } from 'react';
import { getAllEvents } from '@/actions/getAdmissionRequest';
import { UserSelectBar } from './_components/UserSelectBar';

interface SearchComponentProps {
  searchParams: {
    selectedUsers: string;
  };
}

const Page = async ({ searchParams: { selectedUsers } }: SearchComponentProps) => {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const events = await getAllEvents(selectedUsers);
  return (
    <>
      <div>
        <Suspense fallback={<div>Cargando...</div>}>
          <CalendarPreview events={events}>{<UserSelectBar />}</CalendarPreview>
        </Suspense>
      </div>
    </>
  );
};

export default Page;
