'use client';

import FileDownloadList from './FileDownloadList';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { useCountry } from './detail';

interface FileData {
  name: string;
  platform: string;
  path: string;
}

interface Props {
  fileList: FileData[] | null;
}

const DownloadPopover = ({ fileList }: Props) => {
  const { isCountryUSA } = useCountry();
  const downloadText = isCountryUSA ? 'Download' : 'Descargar';

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" color="purple">
          {downloadText}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-90 h-60 overflow-auto">
        <FileDownloadList fileList={fileList} />
      </PopoverContent>
    </Popover>
  );
};

export default DownloadPopover;
