import getAdmissionRequest from '@/actions/getAdmissionRequest';
import HomeVisitStepStepper from './_components/HomeVisitStepsStepper';

interface PageProps {
  params: {
    id: string;
  };
}

export default async function HomeVisit(props: PageProps) {
  const { params } = props;
  const { id } = params;
  const { data } = await getAdmissionRequest({ id });
  return (
    <div className="bg-gray-50">
      <h2 className="font-bold text-xl py-4">Visit Information</h2>
      <HomeVisitStepStepper admissionRequest={data} />
    </div>
  );
}
