import { URL_API } from '@/constants';
import axios from 'axios';
import { UserResponse } from './getUserById';

export interface StockCarProps {
  carNumber: string;
  brand: string;
  model: string;
  status: string;
  newCar: string;
}

interface UpdateStatusParams {
  id: string;
  message: string;
  user: UserResponse;
  receptionDate?: string;
}

export default async function updateStatus({ id, message, user, receptionDate }: UpdateStatusParams) {
  try {
    const data: { stepName: string; receptionDate?: string } = {
      stepName: message,
    };
    if (receptionDate) {
      data.receptionDate = receptionDate;
    }
    const res = await axios.patch(`${URL_API}/stock/update/${id}`, data, {
      headers: {
        Authorization: `bearer ${user.accessToken}`,
      },
    });
    return res;
  } catch (error) {
    return console.error(error);
  }
}
