'use client';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CiCalendarDate } from 'react-icons/ci';
import { DateTime } from 'luxon';

interface IDatePickerFormProps {
  form: any;
  name: string;
  label: string;
  placeholder?: string;
  isDisabled?: boolean;
}

export function DatePickerForm(props: IDatePickerFormProps) {
  const { form, name, label, placeholder, isDisabled = false } = props;

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex-1">
          <div className="flex flex-col gap-2">
            <FormLabel className={'text-primaryBlueGray text-sm'}>{label}</FormLabel>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  className={cn(
                    'justify-start text-left font-normal w-full h-8 px-2',
                    !field.value && 'text-muted-foreground'
                  )}
                  disabled={isDisabled}
                >
                  <CiCalendarDate size={25} />
                  {field.value ? (
                    format(new Date(field.value), 'PPP')
                  ) : (
                    <span>{placeholder ? placeholder : 'Pick a date'}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={field.value}
                  onSelect={(date) => {
                    form.setValue(name, date);
                    if (name === 'blockSlotStartDate') {
                      form.setValue('blockSlotEndDate', date);
                    } else if (name === 'addSlotStartDate') {
                      form.setValue('addSlotEndDate', date);
                    }
                  }}
                  disabled={(date) => date < DateTime.now().startOf('day').toJSDate()}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
