/* eslint-disable jsx-a11y/alt-text */
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import logoGris from '../assets/images/logoGris.png';

const rowGap = '3vh' as unknown as number;

const styles = StyleSheet.create({
  body: {
    flexDirection: 'column',
    marginBottom: '20px',
  },
  viewBackground: {
    position: 'absolute',
    // zIndex: 0,
    marginTop: '70px',
    opacity: 0.2,
    height: '500px',
    width: '100%',
  },
  anexoTitle: {
    textAlign: 'center',
    color: '#6210FF',
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    lineHeight: '0px',
    // zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    textAlign: 'right',
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    // zIndex: 1,
  },
  anexoText: {
    color: 'black',
    fontSize: 11,
    lineHeight: '2px',
    // zIndex: 1,
    marginBottom: '10px',
  },

  text: {
    fontSize: '11px',
    marginBottom: '20px',
  },

  viewMain: {
    fontSize: 11,
    rowGap: rowGap,
    textAlign: 'justify',
    zIndex: 1,
    marginTop: '30px',
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    // zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  content: {
    width: '40%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    rowGap: 10,
  },
  names: {
    fontSize: 10,
    textAlign: 'center',
    fontFamily: 'Helvetica-Bold',
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 10,
    textAlign: 'center',
  },
});

interface ActaEntregaProps {
  contract: string;
  extensionCarNumber: string;
  // city: string;
  firstName: string;
  lastName: string;
  brand: string;
  model: string;
  version: string;
  vin: string;
  policyNumber: string;
  plates: string;
  km: string;
}

export default function ActaEntrega({ contract, extensionCarNumber, ...rest }: ActaEntregaProps) {
  const contractNumber = `${contract}${extensionCarNumber ? ` - ${extensionCarNumber}` : ''}`;
  return (
    <View style={styles.body} break>
      <Image src={logoGris.src} style={styles.viewBackground} />
      <Text style={styles.anexoTitle}>Acta de entrega voluntaria</Text>

      <View style={styles.viewMain}>
        <Text>Número de contrato: {contractNumber}</Text>

        <Text>
          {`ACTA DE ENTREGA VOLUNTARIA
            DE GARANTÍA PRENDARIA SOBRE VEHÍCULO AUTOMOTOR`}
        </Text>

        <Text>
          Derivado de las diversas pláticas siempre cordiales y respetuosas entre las partes y sus respectivos
          empleados y representantes, en cumplimiento a lo dispuesto y en el CONTRATO DE ARRENDAMIENTO N.{' '}
          {contractNumber} celebrado entre el/la suscrito(a) {rest.firstName} {rest.lastName}, entrego a valor
          de compra correspondiente a la fecha de entrega, en este acto de forma voluntaria y pacífica la
          posesión del vehiculo marca {rest.brand}, modelo {rest.model}, Placas {rest.plates} Número de serie{' '}
          {rest.vin}, a quien dice llamarse ______________________________________ en su calidad de apoderado
          legal de ___________________________________________, firmado de conformidad la presente acta de
          entrega ambas partes, en presencia de dos testigos, entregandoló en el siguiente estado:
          _______________________________________________________________________________
          _______________________________________________________________________________
        </Text>

        <Text>
          Asimismo, en mi calidad de cliente de _______________________ manifiesto bajo protesta de decir
          verdad que el vehículo descrito no ha sido sujeto de multas o infracciones, así como no ha sido
          relacionado con delito alguno, por lo que hasta este momento soy el único responsable del uso del
          mismo y en su caso responderé ante las autoridades competentes por cualquier situación que hubiere
          sucedido con antelación a esta fecha y hora de entrega.
        </Text>
        <Text>
          La presente acta de entrega se firma siendo las _________________ horas del día _____________ del
          mes _______________ del año ____________.
        </Text>
      </View>

      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          textAlign: 'center',
          flexDirection: 'column',
          fontSize: 11,
          marginTop: '30px',
        }}
      >
        <Text>________________________</Text>
        <Text>Cliente</Text>
        <Text>(Suscrito)</Text>
      </View>

      <View style={styles.containerS}>
        <View style={styles.content}>
          <Text style={styles.dateAndWho}>________________________</Text>
          <Text style={styles.dateAndWho}>Testigo</Text>
        </View>

        <View style={styles.content}>
          <Text style={styles.dateAndWho}>________________________</Text>
          <Text style={styles.dateAndWho}>Testigo</Text>
        </View>
      </View>
    </View>
  );
}
