import { DocumentCategory } from '@/constants';
import { documentUploadTranslationsMX, documentUploadTranslationsUS } from '@/constants/translations';

// Required fields mapping for each document category
export const requiredFieldsMapping: Record<string, string[]> = {
  [DocumentCategory.INSURANCE_POLICY]: ['vin', 'policyNumber', 'insurer', 'validity', 'broker'],
  [DocumentCategory.TENENCIA]: ['vin', 'payment', 'validity'],
  [DocumentCategory.CIRCULATION_CARD_FRONT]: ['vin', 'number', 'validity'],
  [DocumentCategory.CIRCULATION_CARD_BACK]: ['serialNumber'],
  [DocumentCategory.PLATES_ALTA_PLACAS]: ['vin', 'plates'],
  [DocumentCategory.PLATES_FRONT]: ['plates'],
  [DocumentCategory.PLATES_BACK]: ['plates'],
  [DocumentCategory.FACTURE]: ['vin', 'billNumber', 'billDate', 'billAmount'],
};

/**
 * Get document info message for a specific document category
 * @param documentCategory - The document category to get info for
 * @param useUSTranslations - Whether to use US translations (default: false for MX)
 * @returns The formatted info message string
 */
export function getDocumentInfoMessage(
  documentCategory: DocumentCategory,
  useUSTranslations: boolean = false
): string {
  const translations = useUSTranslations ? documentUploadTranslationsUS : documentUploadTranslationsMX;
  const fields = requiredFieldsMapping[documentCategory];

  if (!fields) {
    return translations.defaultDocumentInfo;
  }

  return `${translations.requiredFields}: ${fields
    .map((f) => translations[f as keyof typeof translations])
    .join(', ')}`;
}

/**
 * Get all document info messages for multiple categories
 * @param useUSTranslations - Whether to use US translations (default: false for MX)
 * @returns Object with all document categories as keys and their info messages as values
 */
export function getAllDocumentInfoMessages(useUSTranslations: boolean = false): Record<string, string> {
  const translations = useUSTranslations ? documentUploadTranslationsUS : documentUploadTranslationsMX;

  return Object.fromEntries(
    Object.entries(requiredFieldsMapping).map(([key, fields]) => [
      key,
      `${translations.requiredFields}: ${fields
        .map((f) => translations[f as keyof typeof translations])
        .join(', ')}`,
    ])
  );
}

/**
 * Get the default document info message
 * @param useUSTranslations - Whether to use US translations (default: false for MX)
 * @returns The default info message string
 */
export function getDefaultDocumentInfoMessage(useUSTranslations: boolean = false): string {
  const translations = useUSTranslations ? documentUploadTranslationsUS : documentUploadTranslationsMX;
  return translations.defaultDocumentInfo;
}

/**
 * Get the base document quality message (general instructions about document quality and visibility)
 * @param useUSTranslations - Whether to use US translations (default: false for MX)
 * @returns The base quality message string
 */
export function getBaseDocumentQualityMessage(useUSTranslations: boolean = false): string {
  const translations = useUSTranslations ? documentUploadTranslationsUS : documentUploadTranslationsMX;
  return translations.requiredFields;
}

/**
 * Get only the required fields for a specific document category (without base message)
 * @param documentCategory - The document category to get fields for
 * @param useUSTranslations - Whether to use US translations (default: false for MX)
 * @returns Comma-separated list of required fields
 */
export function getDocumentRequiredFieldsOnly(
  documentCategory: DocumentCategory,
  useUSTranslations: boolean = false
): string {
  const translations = useUSTranslations ? documentUploadTranslationsUS : documentUploadTranslationsMX;
  const fields = requiredFieldsMapping[documentCategory];

  if (!fields) {
    return '';
  }

  return fields.map((f) => translations[f as keyof typeof translations]).join(', ');
}
