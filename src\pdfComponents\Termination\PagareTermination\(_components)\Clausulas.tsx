import { cities, stateDependingCity } from '@/constants';
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { NumerosALetras } from 'numero-a-letras';
const clauslasList = (data: {
  contractTerminationDate: string;
  contractNumber: string;
  pagareAmount: number;
  signDate: string;
  city: string;
}) => {
  const pagareAmountText = NumerosALetras(data.pagareAmount);

  // from iso string to format like '14 de febrero de 2025'
  const parsedDate = new Date(data.signDate).toLocaleDateString('es-MX', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });

  const state = stateDependingCity[data.city] || data.city;

  const cityName = cities[data.city].label || data.city;

  const parsedSignDate = new Date(data.signDate).toLocaleDateString('es-MX', {
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });

  const pagareAmount = data.pagareAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');

  return [
    {
      title: 'PRIMERA.- ',
      description: `Acuerdan Las Partes dar por terminado el contrato del día ${parsedDate}, al cual se le asignó el número ${data.contractNumber} respecto del vehículo, por así convenir a sus intereses.`,
    },
    {
      title: 'SEGUNDA.- ',
      description: `Las partes acuerdan que no se reserva ninguna acción ni derecho presente o futuro respecto del contrato que dan por terminado de manera anticipada.`,
    },
    {
      title: 'TERCERA.- ',
      description: `Que el Conductor hace entrega física y material al arrendador del vehículo materia del contrato de marras y que en este acto signa un pagaré por la cantidad de $${pagareAmount} M.N. (${pagareAmountText}), a efecto de garantizar los daños que pudiera presentar el vehículo que entrega.`,
    },
    {
      title: 'CUARTA.- ',
      description: `El arrendador, recibe el vehículo objeto del contrato de mérito, así como el documento mercantil exhibido, para en su caso hacerlo valer por la vía legal conducente. `,
    },
    {
      title: 'QUINTA.- ',
      description: `Las Partes se someten a la jurisdicción de los Tribunales competentes en el Estado de ${state} sean locales y/o federales, renunciando expresamente a cualquier otra jurisdicción que pudiera corresponderles, por razón de sus domicilios presentes o futuros o por cualquier otra razón. `,
    },
    {
      title: 'SEXTA.- ACUERDO TOTAL.',
      description: `El presente convenio contiene la totalidad de los acuerdos entre Las Partes con relación al presente acto contractual; ningún cambio, renuncia o modificación será exigible a menos que conste por escrito y firmado por Las Partes. 
      Leído que fue el presente contrato por las partes y enteradas del alcance legal de todo el contenido de este, lo firman al calce y al margen para constancia, en el Estado de ${state}, ${cityName} a ${parsedSignDate}.
`,
    },
  ];
};

interface ClauListProps {
  contractTerminationDate: string;
  contractNumber: string;
  city: string;
  pagareAmount: number;
  // signDate: string;
}

export function ClauList({ contractTerminationDate, contractNumber, city, pagareAmount }: ClauListProps) {
  return (
    <View break>
      <Text style={styles.title}> C L A U S U L A S </Text>

      <View style={styles.clausulasContainer}>
        {clauslasList({
          contractTerminationDate: contractTerminationDate,
          contractNumber: contractNumber,
          city: city,
          pagareAmount,
          signDate: contractTerminationDate,
        }).map(({ title, description }, i) => {
          if (!title && !description) {
            return null;
          }

          return (
            <View style={styles.item} key={i}>
              <Text style={styles.itemContent} break>
                {' '}
                {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
              </Text>
            </View>
          );
        })}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  clausulasContainer: {
    flexDirection: 'column',
    rowGap: 10,
  },
  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },
  item: {
    flexDirection: 'row',
    alignContent: 'center',
  },

  itemContent: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  clasulasBolds: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
  },
});
