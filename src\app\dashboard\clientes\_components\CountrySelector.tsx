'use client';
import { Countries, CountriesOptions, URL_API, countries } from '@/constants';
import { Select } from '@chakra-ui/react';
import { useCountry } from '../../providers/CountryProvider';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import useSidebarStore from '@/zustand/sidebarStore';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

export default function CountrySelector() {
  const { setVehicleStatusCounts, setCategoryCounts } = useSidebarStore((state) => ({
    setVehicleStatusCounts: state.setVehicleStatusCounts,
    setCategoryCounts: state.setCategoryCounts,
  }));

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const countryParam = searchParams.get('country');

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const { handleCountryChange, country } = useCountry();

  const defaultCountry = countryParam
    ? countries.find((c) => c.value.toLowerCase() === countryParam.toLowerCase()) || countries[0]
    : country || CountriesOptions[Countries.Mexico];

  const fetchSidebarData = async (selectedCountry: string) => {
    try {
      const response = await axios.get(`${URL_API}/user/sideData/?country=${selectedCountry}`, {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
        },
      });

      if (response.data?.sideBarData) {
        setVehicleStatusCounts(response?.data?.sideBarData?.vehicleStatusCounts);
        setCategoryCounts(response?.data?.sideBarData?.categoryCounts);
      }
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
    }
  };

  function handleSelectChange(value: string) {
    const selectedCountry = CountriesOptions[value as Countries];
    handleCountryChange(selectedCountry);
    fetchSidebarData(selectedCountry.value);
    if (pathname === '/dashboard') {
      router.push(`?country=${selectedCountry.value}`);
    }
  }

  return (
    <div className="relative">
      <div className="flex flex-row items-center justify-between gap-4">
        <Select onChange={(e) => handleSelectChange(e.target.value)} defaultValue={defaultCountry.value}>
          {countries.map((_country) => {
            return <option key={_country.value} value={_country.value} label={_country.label} />;
          })}
        </Select>
      </div>
    </div>
  );
}
