import { create } from 'zustand';

interface PaymentTransactionStateProps {
  isOpen: boolean;
  updatePayments: boolean;
  setUpdatePayments: (value: boolean) => void;
  onOpen: () => void;
  onClose: () => void;
  paymentId: string;
  setPaymentId: (value: string) => void;
  paymentAmount: number;
  setPaymentAmount: (value: number) => void;
}

export const usePaymentTransactionState = create<PaymentTransactionStateProps>((set) => ({
  isOpen: false,
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
  paymentId: '',
  setPaymentId: (value: string) => set({ paymentId: value }),
  paymentAmount: 0,
  setPaymentAmount: (value: number) => set({ paymentAmount: value }),
  updatePayments: false,
  setUpdatePayments: (value: boolean) => set({ updatePayments: value }),
}));
