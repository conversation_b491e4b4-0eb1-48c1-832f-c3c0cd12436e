'use client';
import { teamLeads, URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useToast, IconButton } from '@chakra-ui/react';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import React, { useState } from 'react';
import { CiTrash } from 'react-icons/ci';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import Spinner from '@/components/Loading/Spinner';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import { VehicleResponse } from '@/actions/getVehicleData';

interface DeleteVehicleDocsProps {
  documentToDelete: string;
  vehicle: VehicleResponse;
}

export default function DeleteVehicleDocs({ documentToDelete, vehicle }: DeleteVehicleDocsProps) {
  const updateSideData = useUpdateSideData();
  const { data: session } = useSession();
  const { user } = useCurrentUser();
  const toast = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const handleDelete = () => {
    Swal.fire({
      title: '¿Estás segura?',
      text: `¿Desea eliminar el documento más reciente? Esto moverá el vehículo a "En preparación"`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#5800F7',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Sí, eliminar',
      cancelButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        setIsLoading(true);
        try {
          const data = {
            vehicleId: vehicle._id,
            documentToDelete: documentToDelete,
          };
          await axios.patch(`${URL_API}/stock/removeVehicleDocs`, data, {
            headers: {
              Authorization: `bearer ${session ? user.accessToken : null}`,
            },
          });

          toast({
            title: 'Documento eliminado exitosamente',
            description: 'Actualizando pagina...',
            duration: 3000,
            status: 'success',
            position: 'top',
          });
          setIsLoading(false);
          await updateSideData(user);
          router.refresh();
        } catch (error: any) {
          setIsLoading(false);
          toast({
            title: error.response.data.message,
            duration: 5000,
            status: 'error',
            position: 'top',
          });
        }
      }
    });
  };

  if (isLoading) return <Spinner />;

  if (user && teamLeads.includes(user.email) && vehicle.step.stepNumber < 3) {
    return (
      <IconButton
        icon={<CiTrash size={30} strokeWidth="1px" />}
        p={0}
        data-cy="delete-doc"
        bgColor="transparent"
        aria-label="delete document"
        onClick={handleDelete}
      />
    );
  }
  return null;
}
