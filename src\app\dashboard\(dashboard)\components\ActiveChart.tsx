'use client';
import { ChartData } from '@/actions/getDashboardData';
import BarChart from '@/components/Charts/BarChart';
import { useEffect, useRef, useState } from 'react';

interface ActivesChartProps {
  activeData?: ChartData | null;
}

export default function ActivesChart({ activeData }: ActivesChartProps) {
  const { containerRef, containerWidth } = useContainerWidth();

  const chartData = activeData;

  const minWidthPerBar = 80;

  const calculatedMinWidth = chartData && chartData?.labels?.length * minWidthPerBar;
  const needsScroll = calculatedMinWidth && calculatedMinWidth > containerWidth && containerWidth > 0;
  const chartWidth = needsScroll ? calculatedMinWidth : '100%';

  return (
    chartData?.labels?.length && (
      <div
        ref={containerRef}
        className=" my-[50px] bg-white px-3 pt-[25px] pb-[50px] rounded shadow flex flex-col gap-1 overflow-x-auto overflow-y-hidden "
      >
        <p className="pl-[15px] text-[24px] text-textGray2 font-[600] ">Activos</p>
        <div style={{ height: '450px', width: chartWidth }}>
          <BarChart data={chartData} barThickness={50} />
        </div>
      </div>
    )
  );
}

const useContainerWidth = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    // Medimos el ancho inicial
    updateWidth();

    // Observamos cambios en el tamaño
    const resizeObserver = new ResizeObserver(updateWidth);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return { containerRef, containerWidth };
};
