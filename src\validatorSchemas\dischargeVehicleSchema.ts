import * as Yup from 'yup';
import createSelectInputValidator from './selectInputValidator';
import { fileValidator } from './assignAssociateSchema';

export const dischargeVehicleSchema = Yup.object().shape({
  category: createSelectInputValidator('Debe seleccionar una categoría').required('Categoría requerida'),
  reason: Yup.mixed().when('category', {
    is: (category: any) => category.value === 'total-loss',
    then: createSelectInputValidator('Debe seleccionar un motivo'),
    otherwise: Yup.mixed().notRequired(),
  }),
  comments: Yup.string(),
  date: Yup.string().required('Fecha requerida'),
  platesDischargedDoc: Yup.mixed().when('category', {
    is: (category: any) => category.value === 'total-loss' || category.value === 'operational-loss',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.mixed().notRequired(),
  }),
  dictamenDoc: Yup.mixed().when(['category', 'reason'], {
    is: (category: any, reason: any) => category.value === 'total-loss' && reason.value === 'Accidente',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.mixed().notRequired(),
  }),
  reportDoc: Yup.mixed().when(['category', 'reason'], {
    is: (category: any, reason: any) => category.value === 'total-loss' && reason.value === 'Robo',
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.mixed().notRequired(),
  }),
});
