import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { Capabilities, Paths, Sections, Subsections } from '@/constants';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import {
  FaChevronDown,
  FaUserFriends,
  FaFileAlt,
  FaClock,
  FaHandsHelping,
  FaPeopleArrows,
} from 'react-icons/fa';

const ClientesNavItems = () => {
  const ability = usePermissions();
  const pathname = usePathname();
  const navButton = {
    icon: <FaUserFriends size={18} />,
    name: 'Client<PERSON>',
  };
  let subNavLinks = [
    {
      link: Paths.clients_admissions,
      icon: <FaFileAlt size={16} />,
      name: 'Solicitudes',
      key: Subsections.Admissions,
    },
    {
      link: Paths.clients_waitlist,
      icon: <FaClock size={16} />,
      name: 'Lista de espera',
      key: Subsections.Waitlist,
    },
    {
      link: Paths.clients_leads,
      icon: <FaPeopleArrows size={16} />,
      name: 'Clientes potenciales',
      key: Subsections.Leads,
    },
    {
      link: Paths.clients_assignedLeads,
      icon: <FaHandsHelping size={16} />,
      name: 'Clientes asignados',
      key: Subsections.AssignedLeads,
    },
  ].filter((item) => canPerform(ability, Capabilities.View, Sections.Clients, item.key));

  return (
    <details className="group transition-all duration-150 ml-4 content-center h-auto open:h-auto overflow-visible ">
      {pathname.includes('/dashboard/clientes') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2">
          <FaUserFriends size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Clientes{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname.includes('/dashboard/clientes') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500">
        {subNavLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={false}>
              <button
                className={
                  pathname === item.link
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                style={{ width: '96%' }}
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
      </nav>
    </details>
  );
};

export default ClientesNavItems;
