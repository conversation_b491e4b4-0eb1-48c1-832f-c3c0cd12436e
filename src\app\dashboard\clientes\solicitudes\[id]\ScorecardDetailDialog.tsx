'use client';
import {
  <PERSON><PERSON>,
  ModalBody,
  Modal<PERSON>lose<PERSON>utton,
  ModalHeader,
  ModalOverlay,
  ModalContent,
  Table,
  TableContainer,
  Thead,
  Tr,
  Th,
  Tbody,
  Text,
} from '@chakra-ui/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { RiskCategory, ScorecardVariableName } from '../enums';
import { RiskCategoryTranslations, ScorecardVariableNameTranslations } from '../data';

interface ScorecardVariableCategory {
  threshold: { min: number; max: number } | string;

  riskCategory: RiskCategory;

  riskScore: number;

  weight: number;

  result: number;
}

interface ScorecardDetail {
  variable: ScorecardVariableName;

  category: ScorecardVariableCategory;
}

interface RiskAnalysis {
  scorecard: {
    details: ScorecardDetail[];
  };
}

function formatVariableName(variableName: ScorecardVariableName) {
  return ScorecardVariableNameTranslations[variableName as keyof typeof ScorecardVariableName];
}

function formatRiskCategory(riskCategory: RiskCategory) {
  const translation = RiskCategoryTranslations.find((t) => t.category === riskCategory);
  return translation ? <Text color={translation.color}>{translation?.name}</Text> : riskCategory;
}

function formatThreshold(threshold: { min: number; max: number } | string) {
  if (typeof threshold === 'string') return threshold;
  return `${threshold.min} - ${threshold.max}`;
}

/*
 *** DEPRECATED ***
 */
export function ScorecardDetailDialog({ riskAnalysis }: { riskAnalysis: RiskAnalysis }) {
  const { scorecard } = riskAnalysis;
  const router = useRouter();
  const searchParams = useSearchParams();
  const isOpen = searchParams.get('dialog') === 'view-detail-scorecard';

  function onClose() {
    router.back();
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent minWidth="fit-content">
        <ModalHeader>Detalle del Score</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <TableContainer fontSize="sm">
            <Table variant="striped">
              <Thead>
                <Tr>
                  <Th>Variable</Th>
                  <Th>Categoría</Th>
                  <Th>Riesgo</Th>
                </Tr>
              </Thead>
              <Tbody>
                {scorecard &&
                  scorecard.details.map((detail) => (
                    <Tr key={detail.variable}>
                      <Th>{formatVariableName(detail.variable)}</Th>
                      <Th>{formatThreshold(detail.category.threshold)}</Th>
                      <Th>{formatRiskCategory(detail.category.riskCategory)}</Th>
                    </Tr>
                  ))}
              </Tbody>
            </Table>
          </TableContainer>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
