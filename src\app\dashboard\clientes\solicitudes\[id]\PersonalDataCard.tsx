import { QueryLink } from '@/components/QueryLink';
import { Card, CardBody, CardHeader, Flex, Heading, Stack, Text, useToast } from '@chakra-ui/react';
import { FaPencilAlt, FaPhone, FaEnvelope } from 'react-icons/fa';
import { useCountry } from './detail';
import { format, parseISO } from 'date-fns';
import { PersonalDataDialogUS } from './_components/PersonalDataDialogUS';
import { Capabilities, CountriesShortNames, Sections, Subsections } from '@/constants';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';

function formatDate(date: string | Date, isCountryUSA: boolean) {
  if (!date) return '--/--/--';
  return isCountryUSA ? format(new Date(parseISO(date as string)), 'MM/dd/yyyy') : date.toString();
}

interface Referrer {
  name: string;
  email: string;
  phone: string;
}

interface PersonalData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  birthdate: string;
  postalCode: string;
  city: string;
  state: string;
  neighborhood: string;
  street: string;
  streetNumber: string;
  department: string;
  nationalId: string;
  taxId: string;
  country: string;
  vehicleSelected?: string;
  ssn?: string;
  referrer?: Referrer;
}

interface Hubspot {
  id: string;
}

export function formatAddress({
  postalCode = '',
  city = '',
  state = '',
  neighborhood = '',
  street = '',
  streetNumber = '',
  department = '',
  country = '',
} = {}) {
  if (country === CountriesShortNames['United States']) {
    return formatAddressUS({
      postalCode,
      city,
      state,
      street,
      department,
    });
  }
  const isNonEmpty = (value: null | undefined | string) =>
    value !== null && value !== undefined && value.trim() !== '';
  const addressParts = [
    isNonEmpty(street) && isNonEmpty(streetNumber) ? `${street} ${streetNumber}` : null,
    department,
    isNonEmpty(neighborhood) && isNonEmpty(postalCode) ? `${neighborhood} ${postalCode}` : null,
    isNonEmpty(city) && isNonEmpty(state) ? `${city}, ${state}` : null,
  ];

  return addressParts.filter((part) => part !== null).join(', ');
}

function formatAddressUS({ postalCode = '', city = '', state = '', street = '', department = '' } = {}) {
  let fullAddress = ``;
  if (street) {
    fullAddress = `${street}`;
  }
  if (department) {
    fullAddress = `${fullAddress} ${department}`;
  }
  if (city) {
    fullAddress = `${fullAddress}, ${city}`;
  }
  if (state) {
    fullAddress = `${fullAddress}, ${state}`;
  }
  if (postalCode) {
    fullAddress = `${fullAddress}, ${postalCode}`;
  }
  return fullAddress;
}

export default function PersonalDataCard({
  data,
  source,
  clientIpAddress,
  hubspot,
  hilos,
}: {
  data: PersonalData;
  source?: string;
  clientIpAddress?: string;
  hubspot?: Hubspot;
  hilos?: string;
}) {
  const {
    firstName,
    lastName,
    email,
    phone,
    birthdate,
    taxId,
    nationalId,
    postalCode,
    city,
    state,
    neighborhood,
    street,
    streetNumber,
    department,
    country,
    vehicleSelected,
    ssn,
  } = data;

  const { isCountryUSA } = useCountry();
  console.log('referrer data', data.referrer);
  const countryText = isCountryUSA ? 'Country' : 'País';
  const vehicleSelectionText = isCountryUSA ? 'Selected vehicle' : 'Vehículo seleccionado';
  const dateOfBirthText = isCountryUSA ? 'Date of birth' : 'Fecha de nacimento';
  const AddressText = isCountryUSA ? 'Address' : 'Dirección';
  const sourceText = isCountryUSA ? 'Source' : 'Fuente';
  const clientIpAddressText = isCountryUSA ? 'Ip Address' : 'Dirección IP';
  const referrer = isCountryUSA ? 'Referrer' : 'Referente';
  const toast = useToast();
  const ability = usePermissions();
  const canEdit = canPerform(ability, Capabilities.EditProfile, Sections.Clients, Subsections.Admissions);
  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {firstName} {lastName}
          </Heading>
          {canEdit &&
            (isCountryUSA ? (
              <PersonalDataDialogUS data={data} />
            ) : (
              <QueryLink query={{ dialog: 'edit-personal-data' }}>
                <FaPencilAlt color="#5800F7" size={18} />
              </QueryLink>
            ))}
        </Flex>
      </CardHeader>
      <CardBody>
        <Stack spacing={2}>
          <Text fontSize="sm" color="gray.900">
            <Text as="span" fontWeight="bold">
              {countryText}:{' '}
            </Text>
            {country?.toUpperCase()}
          </Text>
          {vehicleSelected && (
            <Text fontSize="sm" color="gray.900">
              <Text as="span" fontWeight="bold">
                {vehicleSelectionText}:{' '}
              </Text>
              {vehicleSelected}
            </Text>
          )}

          <Text fontSize="sm" color="gray.900">
            <Text as="span" fontWeight="bold">
              {dateOfBirthText}:{' '}
            </Text>
            {formatDate(birthdate, isCountryUSA)}
          </Text>

          {!isCountryUSA && <Curp nationalId={nationalId} />}
          {!isCountryUSA && <RFC taxId={taxId} />}
          {isCountryUSA && <SSN ssn={ssn} />}
          <Text fontSize="sm" color="gray.900">
            <Text as="span" fontWeight="bold">
              {AddressText}:{' '}
            </Text>
            {formatAddress({
              postalCode,
              city,
              state,
              neighborhood,
              street,
              streetNumber,
              department,
              country,
            })}
          </Text>
        </Stack>
        {/* with icons */}
        <Stack direction={'row'} spacing={8} mt={2}>
          <Flex align="center">
            <Text fontSize="sm" color="gray.900">
              <FaPhone size={14} />
            </Text>
            <Text ml={2} fontSize="sm" color="gray.900">
              {phone ? phone : '--'}
            </Text>
          </Flex>
          <Flex align="center">
            <Text fontSize="sm" color="gray.900">
              <FaEnvelope size={14} />
            </Text>
            <Text ml={2} fontSize="sm" color="gray.900">
              {email ? email : '--'}
            </Text>
          </Flex>
        </Stack>
        <Text fontSize="sm" color="gray.900" marginTop={2}>
          <Text as="span" fontWeight="bold">
            {sourceText}:{' '}
          </Text>
          {source ? source : '--'}
        </Text>
        <Text fontSize="sm" color="gray.900" marginTop={2}>
          <Text as="span" fontWeight="bold">
            {clientIpAddressText}:{' '}
          </Text>
          {clientIpAddress ? clientIpAddress : '--'}
        </Text>
        {data.referrer && (
          <Text fontSize="sm" color="gray.900" marginTop={2}>
            <Text as="span" fontWeight="bold">
              {referrer}:{' '}
            </Text>
            {data.referrer.name}
          </Text>
        )}
        <Text fontSize="sm" color="gray.900" marginTop={2}>
          <Text as="span" fontWeight="bold">
            Hubspot:{' '}
          </Text>
          {hubspot ? (
            <Text
              as="span"
              color="blue.500"
              cursor="pointer"
              onClick={() => {
                navigator.clipboard.writeText(
                  `https://app.hubspot.com/contacts/22771322/record/0-1/${hubspot.id}/`
                );
                toast({
                  title: 'ID copiado',
                  description: 'El ID de Hubspot ha sido copiado al portapapeles',
                  status: 'success',
                  position: 'top',
                  duration: 2000,
                  isClosable: true,
                });
              }}
            >
              Copiar link
            </Text>
          ) : (
            '--'
          )}
        </Text>
        <Text fontSize="sm" color="gray.900" marginTop={2}>
          <Text as="span" fontWeight="bold">
            Hilos:{' '}
          </Text>
          {hubspot ? (
            <Text
              as="span"
              color="blue.500"
              cursor="pointer"
              onClick={() => {
                navigator.clipboard.writeText(`${hilos}`);
                toast({
                  title: 'Link copiado',
                  description: 'El Link de Hilos ha sido copiado al portapapeles',
                  status: 'success',
                  position: 'top',
                  duration: 2000,
                  isClosable: true,
                });
              }}
            >
              Copiar link
            </Text>
          ) : (
            '--'
          )}
        </Text>
      </CardBody>
    </Card>
  );
}

const Curp = ({ nationalId }: any) => {
  return (
    <Text fontSize="sm" color="gray.900">
      <Text as="span" fontWeight="bold">
        CURP:{' '}
      </Text>
      {nationalId ? nationalId : '--'}
    </Text>
  );
};

const RFC = ({ taxId }: any) => {
  return (
    <Text fontSize="sm" color="gray.900">
      <Text as="span" fontWeight="bold">
        RFC:{' '}
      </Text>
      {taxId ? taxId : '--'}
    </Text>
  );
};

const SSN = ({ ssn }: any) => {
  return (
    <Text fontSize="sm" color="gray.900">
      <Text as="span" fontWeight="bold">
        SSN:{' '}
      </Text>
      {ssn}
    </Text>
  );
};
