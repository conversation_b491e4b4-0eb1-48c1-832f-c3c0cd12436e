export enum AppointmentStatus {
  scheduled = 'scheduled',
  completed = 'completed',
  canceled = 'canceled',
  noshow = 'noshow',
  pending = 'pending',
  /**
   * the below two status are for the appointment status history tracking,
   * these status will not be set as appointment status in database
   */
  rescheduled = 'rescheduled',
  reassigned = 'reassigned',
}
export enum AppointmentStatusTranslationMX {
  pending = 'Pendiente',
  scheduled = 'Reservada',
  completed = 'Completada',
  canceled = 'Cancelada',
  noshow = 'No se presentó',
  rescheduled = 'Reprogramada',
  reassigned = 'Reasignada',
}

export type AppointmentEvent = {
  id: string;
  title: string;
  start: Date;
  end: Date;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  admissionRequestId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: AppointmentStatus;
  meetingLink: string;
  slot: string;
  user: any;
};
