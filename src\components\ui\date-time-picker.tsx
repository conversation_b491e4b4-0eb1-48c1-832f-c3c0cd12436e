'use client';

import * as React from 'react';
import { CalendarSearchIcon } from 'lucide-react';
import { format, isSameDay } from 'date-fns';

import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea, ScrollBar } from './scroll-area';

export function DateTimePicker({
  selectedDate,
  onDateChange,
  disabledHoursBefore,
  className,
  btnDisabled = false,
}: {
  selectedDate: Date | null;
  onDateChange: (date: Date | null) => void;
  disabledHoursBefore?: Date | null;
  className?: string;
  btnDisabled?: boolean;
}) {
  const [isOpen, setIsOpen] = React.useState(false);

  const hours = Array.from({ length: 12 }, (_, i) => i + 1);
  const minutes = Array.from({ length: 12 }, (_, i) => i * 5);

  const handleSelect = (date: Date | undefined) => {
    if (!date) return;

    const newDate = new Date(date);

    // Only apply logic if disabledHoursBefore is provided and same day
    if (disabledHoursBefore && isSameDay(newDate, disabledHoursBefore)) {
      const disabledHour = disabledHoursBefore.getHours();
      const disabledMinute = disabledHoursBefore.getMinutes();

      // If this is a fresh date selection (midnight), use time from disabledHoursBefore
      if (newDate.getHours() === 0 && newDate.getMinutes() === 0 && newDate.getSeconds() === 0) {
        newDate.setHours(disabledHour);
        newDate.setMinutes(disabledMinute);
      }
    }

    onDateChange(newDate);
  };

  const handleTimeChange = (type: 'hour' | 'minute' | 'ampm', value: string) => {
    if (!selectedDate) return;

    const newDate = new Date(selectedDate);

    if (type === 'hour') {
      const hour = parseInt(value);
      const adjustedHour = (hour % 12) + (newDate.getHours() >= 12 ? 12 : 0);
      newDate.setHours(adjustedHour);
    } else if (type === 'minute') {
      newDate.setMinutes(parseInt(value));
    } else if (type === 'ampm') {
      const currentHours = newDate.getHours();
      const baseHour = currentHours % 12;
      newDate.setHours(value === 'PM' ? baseHour + 12 : baseHour);
    }

    onDateChange(newDate);
  };

  let validHours = hours;
  let validMinutes = minutes;

  if (disabledHoursBefore && selectedDate && isSameDay(selectedDate, disabledHoursBefore)) {
    const disabledHour = disabledHoursBefore.getHours();
    const disabledMinute = disabledHoursBefore.getMinutes();

    // First filter: remove hours before disabledHour or allow partial minutes
    validHours = hours.filter((h) => {
      const hourIn24 = (h % 12) + (disabledHour >= 12 ? 12 : 0);
      return hourIn24 > disabledHour || (hourIn24 === disabledHour && disabledMinute < 55);
    });

    // Second filter: if disabledHour is in PM, disallow AM options
    const isDisabledHourInPM = disabledHour >= 12;
    if (isDisabledHourInPM) {
      validHours = validHours.filter((h) => {
        const hourIn24 = (h % 12) + (disabledHour >= 12 ? 12 : 0);
        return hourIn24 >= 12; // Only PM times allowed
      });
    }

    // If currently selected hour matches disabledHour, restrict minutes
    if (selectedDate.getHours() === disabledHour) {
      validMinutes = minutes.filter((m) => m > disabledMinute);
    }
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-[300px] justify-start text-left font-normal',
            !selectedDate && 'text-muted-foreground',
            className
          )}
          disabled={btnDisabled}
        >
          <CalendarSearchIcon className="mr-2 h-4 w-4" />
          {selectedDate ? format(selectedDate, 'MM/dd/yyyy hh:mm aa') : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <div className="sm:flex">
          <Calendar
            mode="single"
            selected={selectedDate || undefined}
            onSelect={handleSelect}
            disabled={(date) => {
              if (!disabledHoursBefore) return false;

              const disabledDateOnly = new Date(
                disabledHoursBefore.getFullYear(),
                disabledHoursBefore.getMonth(),
                disabledHoursBefore.getDate()
              );

              const currentDateOnly = new Date(date.getFullYear(), date.getMonth(), date.getDate());

              return currentDateOnly < disabledDateOnly;
            }}
            initialFocus
          />

          <div className="flex flex-col sm:flex-row sm:h-[300px] divide-y sm:divide-y-0 sm:divide-x">
            <ScrollArea className="w-64 sm:w-auto">
              <div className="flex sm:flex-col p-2">
                {validHours.map((hour) => (
                  <Button
                    key={hour}
                    size="icon"
                    variant={selectedDate && selectedDate.getHours() % 12 === hour % 12 ? 'default' : 'ghost'}
                    className="sm:w-full shrink-0 aspect-square"
                    onClick={() => handleTimeChange('hour', hour.toString())}
                  >
                    {hour}
                  </Button>
                ))}
              </div>
              <ScrollBar orientation="horizontal" className="sm:hidden" />
            </ScrollArea>

            <ScrollArea className="w-64 sm:w-auto">
              <div className="flex sm:flex-col p-2">
                {validMinutes.map((minute) => (
                  <Button
                    key={minute}
                    size="icon"
                    variant={selectedDate && selectedDate.getMinutes() === minute ? 'default' : 'ghost'}
                    className="sm:w-full shrink-0 aspect-square"
                    onClick={() => handleTimeChange('minute', minute.toString())}
                  >
                    {minute}
                  </Button>
                ))}
              </div>
              <ScrollBar orientation="horizontal" className="sm:hidden" />
            </ScrollArea>

            <ScrollArea>
              <div className="flex sm:flex-col p-2">
                {['AM', 'PM'].map((ampm) => {
                  const isAmpmDisabled =
                    disabledHoursBefore &&
                    selectedDate &&
                    isSameDay(selectedDate, disabledHoursBefore) &&
                    disabledHoursBefore.getHours() >= 12 &&
                    ampm === 'AM';

                  return (
                    <Button
                      key={ampm}
                      size="icon"
                      variant={
                        selectedDate &&
                        ((ampm === 'AM' && selectedDate.getHours() < 12) ||
                          (ampm === 'PM' && selectedDate.getHours() >= 12))
                          ? 'default'
                          : 'ghost'
                      }
                      className="sm:w-full shrink-0 aspect-square"
                      disabled={!!isAmpmDisabled}
                      onClick={() => handleTimeChange('ampm', ampm)}
                    >
                      {ampm}
                    </Button>
                  );
                })}
              </div>
            </ScrollArea>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
