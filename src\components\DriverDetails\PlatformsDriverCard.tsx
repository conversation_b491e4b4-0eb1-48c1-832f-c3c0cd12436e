import {
  <PERSON>,
  CardBody,
  CardHeader,
  <PERSON>lex,
  <PERSON>ing,
  TableContainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Box,
} from '@chakra-ui/react';
import {
  EarningsAnalysisStatus,
  GigPlatform,
  PalencaAccountStatus,
} from '@/app/dashboard/clientes/solicitudes/enums';
import { findPlatform, PalencaAccountStatusTranslations } from '@/app/dashboard/clientes/solicitudes/data';
import { associateTranslationsMX, associateTranslationsUS } from '@/constants/translations';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';

interface FileData {
  name: string;
  platform: string;
  path: string;
}

interface Props {
  fileList: FileData[] | null;
  translations: any;
}

const FileDownloadList = ({ fileList, translations }: Props) => {
  const handleDownload = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="p-4">
      <ul className="bg-white rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
        {fileList ? (
          fileList?.map((file, index) => (
            <li
              key={index}
              className="border-b last:border-none p-4 cursor-pointer hover:bg-gray-100 flex items-center"
              onClick={() => handleDownload(file?.path)}
            >
              <span className="text-purple-600 hover:underline">{file?.name}</span>
            </li>
          ))
        ) : (
          <li className="border-b last:border-none p-4 cursor-pointer hover:bg-gray-100 flex items-center">
            <span>{translations.noFiles}</span>
          </li>
        )}
      </ul>
    </div>
  );
};

const DownloadPopover = ({ fileList, translations }: Props) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" color="purple">
          {translations.download}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" style={{ zIndex: 9999 }}>
        <FileDownloadList fileList={fileList} translations={translations} />
      </PopoverContent>
    </Popover>
  );
};

interface Account {
  accountId: string;
  platform: GigPlatform;
  status: PalencaAccountStatus;
}

interface RequestPalenca {
  accounts: Account[];
}

interface EarningsAnalysis {
  totalEarnings?: number | null;
  status: EarningsAnalysisStatus;
}

interface Screenshot {
  name: string;
  platform: string;
  path: string;
}

const statusColorScheme = {
  [PalencaAccountStatus.success]: { text: '#067F20', bg: '#E8FAEB' },
  [PalencaAccountStatus.incomplete]: { text: '#946200', bg: '#FFF5CC' },
  [PalencaAccountStatus.pending]: { text: '#946200', bg: '#FFF5CC' },
  [PalencaAccountStatus.retry]: { text: '#946200', bg: '#FFF5CC' },
  [PalencaAccountStatus.error]: { text: '#D32F2F', bg: '#FDECEA' },
  [PalencaAccountStatus.failure]: { text: '#D32F2F', bg: '#FDECEA' },
};

function formatTotalEarnings(totalEarnings?: number | null) {
  if (totalEarnings === null || totalEarnings === undefined) {
    return '';
  }
  return `(${new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(totalEarnings)})`;
}

export default function PlatformsDriverCard({
  palenca,
  earningsAnalysis,
  screenshots,
  countryCondition = false,
}: {
  palenca: RequestPalenca | null;
  earningsAnalysis: EarningsAnalysis | null;
  screenshots: Screenshot[];
  countryCondition: boolean;
}) {
  const translations = countryCondition ? associateTranslationsUS : associateTranslationsMX;
  const accounts = palenca?.accounts || [];
  const hasAccounts = accounts?.length > 0;
  const isApproved = earningsAnalysis?.status === EarningsAnalysisStatus?.approved;
  const isApprovedWithConditions =
    earningsAnalysis?.status === EarningsAnalysisStatus?.approved_with_conditions;
  const isRejected = earningsAnalysis?.status === EarningsAnalysisStatus?.rejected;

  let statusText = '';
  let statusColor = '';

  if (isApproved) {
    statusText = translations.preApproved;
    statusColor = '#067F20';
  } else if (isApprovedWithConditions) {
    statusText = translations.preApprovedConditions;
    statusColor = '#946200';
  } else if (isRejected) {
    statusText = translations.rejected;
    statusColor = '#D32F2F';
  } else {
    statusText = '';
    statusColor = '#333';
  }

  const screenshotsByPlatform = screenshots?.reduce((acc: { [key: string]: Screenshot[] }, screenshot) => {
    if (!acc[screenshot?.platform]) {
      acc[screenshot?.platform] = [];
    }
    acc[screenshot?.platform]?.push(screenshot);
    return acc;
  }, {});

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {translations.revenueAnalysis}{' '}
            <Text as="span" color="gray.500" fontSize="md" fontWeight="medium">
              {formatTotalEarnings(earningsAnalysis?.totalEarnings)}
            </Text>
          </Heading>
          {statusText && (
            <Text fontSize="sm" color={statusColor}>
              {statusText}
            </Text>
          )}
        </Flex>
      </CardHeader>
      <CardBody>
        <TableContainer fontSize="sm" border="1px solid #e2e8f0" borderRadius="md" overflow="hidden">
          <Table>
            <Thead bg="#FAFAFA">
              <Tr>
                <Th width="30%" textAlign="start" pl={4}>
                  {translations.platform}
                </Th>
                <Th width="40%" textAlign="start">
                  {translations.status}
                </Th>
                <Th width="30%" textAlign="start">
                  {translations.screenshots}
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {hasAccounts ? (
                accounts.map((account: Account) => (
                  <Tr key={account?.accountId} _hover={{ bg: 'gray.50' }}>
                    <Td pl={4} textAlign="start">
                      {findPlatform(account?.platform)?.name}
                    </Td>
                    <Td textAlign="start">
                      <Box
                        display="inline-block"
                        px={2}
                        py={1}
                        borderWidth="1px"
                        borderRadius="md"
                        fontSize="xs"
                        fontWeight={600}
                        bg={statusColorScheme[account?.status]?.bg || '#EEE'}
                        color={statusColorScheme[account?.status]?.text || '#333'}
                        borderColor={statusColorScheme[account?.status]?.bg || '#EEE'}
                      >
                        {PalencaAccountStatusTranslations[account?.status] || 'N/A'}
                      </Box>
                    </Td>
                    <Td textAlign="start">
                      <DownloadPopover
                        fileList={screenshotsByPlatform?.[account?.platform]}
                        translations={translations}
                      />
                    </Td>
                  </Tr>
                ))
              ) : (
                <Tr>
                  <Td colSpan={3} textAlign="center">
                    {translations.noAccounts}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>
      </CardBody>
    </Card>
  );
}
