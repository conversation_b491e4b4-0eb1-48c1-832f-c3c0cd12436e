'use server';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';

export interface VehicleWithQR {
  _id: string;
  carNumber: string;
  model: string;
  brand: string;
  year: number | string;
  color: string;
  vin: string;
  vehicleState: string;
  qrCode: {
    url: string;
    docId: string;
    originalName: string;
  };
  status: string;
  vehicleStatus: string;
  category: string;
  subCategory: string;
}

export default async function getVehiclesWithQR(): Promise<VehicleWithQR[]> {
  try {
    const user = await getCurrentUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    const response = await axios.get(`${URL_API}/stock/with-qr-codes`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response.data.data || [];
  } catch (error) {
    return [];
  }
}
