import { Field, FieldProps, FormikProps, useField } from 'formik';
import Select, { StylesConfig } from 'react-select';

interface SelectInputProps {
  options: {
    value: string;
    label: string;
  }[];
  label: string;
  name: string;
  onChange?: (option: SelectInputProps['options'][number], form: FormikProps<any>) => void;
  inputId?: string;
  dataCy?: string;
  defaultOption?: any;
  placeholder?: string;
  isMulti?: boolean;
  disabled?: boolean;
}

const customStyles: StylesConfig = {
  option: (provided: any, state: any) => ({
    ...provided,
    backgroundColor: state.isSelected ? '#5800F7' : state.isFocused ? 'rgba(88, 0, 247, 0.1)' : 'white',
    color: state.isSelected ? 'white' : 'black',
    border: '0 !important',
    boxShadow: '0 !important',
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: state.isSelected ? '#5800F7' : 'rgba(88, 0, 247, 0.1)',
      border: '0 !important',
    },
    '&:active': {
      backgroundColor: '#5800F7',
      color: 'white',
    },
  }),
};

export default function SelectInput({
  label,
  options,
  name,
  onChange,
  inputId = '431',
  dataCy,
  defaultOption,
  placeholder = 'Selecciona',
  isMulti = false,
  disabled = false,
}: SelectInputProps) {
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;
  const objMessage = meta.error as unknown as {
    value: string;
  };
  const errorMessage = objMessage;

  return (
    <>
      <div className="w-full" data-cy={dataCy || 'cy-select'}>
        <label className="block mb-2 text-gray-700" htmlFor="selectOption">
          {label}
        </label>
        <div
          className={`
            w-full
            border-2
            ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            rounded
          `}
        >
          <Field
            id={name}
            name={name}
            component={({ form, ...props }: FieldProps) => (
              <Select
                isMulti={isMulti}
                defaultValue={defaultOption}
                styles={customStyles}
                isDisabled={disabled}
                {...field}
                {...props}
                value={field.value}
                options={options}
                classNamePrefix={`${name} react-select`}
                placeholder={placeholder}
                onChange={(option: SelectInputProps['options'][number]) => {
                  // console.log('soy el on change', option);
                  form.setFieldValue(field.name, option);
                  if (onChange) {
                    onChange(option, form);
                  }
                }}
                // onInputChange={() => form.setFieldTouched(field.name, true)}
                onBlur={() => form.setFieldTouched(field.name, true)}
                inputId={inputId}
                className={`
                  ${name}
                  alfksfff
                  border-0
                  outline-none
              `}
              />
            )}
          />
        </div>
        {hasError && <div className="mt-1 text-sm text-red-500">{errorMessage?.value || meta.error}</div>}
      </div>
    </>
  );
}
