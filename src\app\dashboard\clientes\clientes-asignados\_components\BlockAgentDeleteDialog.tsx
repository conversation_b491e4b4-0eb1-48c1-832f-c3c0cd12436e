'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alog<PERSON>escription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';

interface BlockAgentDeleteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  blockId: string | null;
  onSuccess?: () => void;
}

export function BlockAgentDeleteDialog({
  open,
  onOpenChange,
  blockId,
  onSuccess,
}: BlockAgentDeleteDialogProps) {
  const { data: session } = useSession();
  const user = session?.user as any;

  const handleDelete = async () => {
    if (!blockId) return;

    try {
      const res = await fetch(`${URL_API}/leadAssignment/block-leads/${blockId}/${user.id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      if (!res.ok) throw new Error('Failed to delete record');

      toast.success('El registro del bloque ha sido borrado exitosamente.');

      onOpenChange(false);
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error deleting block:', error);
      toast.error('No se pudo eliminar el registro del bloque.');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>¿Estás seguro?</DialogTitle>
          <DialogDescription>
            Esta acción no se puede deshacer. Esto eliminará permanentemente el registro del bloque.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancelar
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            Eliminar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
