import getPermissionMatrix from '@/actions/getPermissionMatrix';
import AssignPermissionForm from './assignPermissionForm';
import getUserAssignedPermissionsById from '@/actions/getUserAssignedPermissionsById';

interface EditPermisoPageProps {
  params: {
    userId: string;
  };
}

export default async function AssignPermisoPage({ params: { userId } }: EditPermisoPageProps) {
  const permissionMatrix = await getPermissionMatrix();
  if (!permissionMatrix) return null;

  const assignedpermissions = await getUserAssignedPermissionsById(userId);
  if (!assignedpermissions) return null;

  return (
    <>
      <h1 className="text-[28px] font-bold text-[#262D33]">Asignar Permisos Adicionales</h1>
      <div className="py-2 bg-gray-50">
        <AssignPermissionForm permissionMatrix={permissionMatrix} assignedpermissions={assignedpermissions} />
      </div>
    </>
  );
}
