/* eslint-disable react-hooks/rules-of-hooks */
import { cn } from '@/lib/utils';
import { usePDF } from '@react-pdf/renderer';
import React from 'react';

interface ViewPDFLoaderProps {
  documentComponent: React.ReactElement;
  conditional?: boolean;
  conditionalComponent?: React.ReactElement;
  documentComponent2?: React.ReactElement;
  conditional2?: boolean;
  conditionalComponent2?: React.ReactElement;
  removeDefaultBackground?: boolean;
  additionalClasses?: string;
  frameHeight?: string;
  frameWidth?: string;
}

export default function ViewPDFLoader({
  documentComponent,
  conditional = false,
  conditionalComponent,
  removeDefaultBackground,
  additionalClasses,
  frameHeight,
  frameWidth,
}: ViewPDFLoaderProps) {
  const [instance] = usePDF({
    document: conditional ? conditionalComponent : documentComponent,
  });

  if (instance.loading)
    return (
      <div /* className="fixed top-0 left-0 w-full h-[100vh] z-[100] bg-gray-500 " */
        className={cn(!removeDefaultBackground && 'fixed top-0 left-0 w-full h-full z-[100] bg-gray-500')}
      >
        <div className="flex justify-center items-center h-screen">
          <div className="bg-white p-5 rounded">
            <p>Generando PDF...</p>
          </div>
        </div>
      </div>
    );

  if (instance.error) return <div>Algo salió mal: {instance.error}</div>;

  return (
    <>
      <div /* className="fixed top-0 left-0 w-full h-[100vh] z-[100] bg-gray-500 " */
        className={cn(
          !removeDefaultBackground && 'fixed top-0 left-0 w-full z-[100] bg-gray-500',
          '!h-[90vh]',
          additionalClasses
        )}
      >
        <iframe
          src={instance.url!}
          style={{ width: frameWidth || '100%', height: frameHeight || '80vh' }}
          title="pdf"
        ></iframe>
      </div>
    </>
  );
}
