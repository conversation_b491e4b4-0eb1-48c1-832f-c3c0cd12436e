import { Font, StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';

const declarationList = (data: { deliveryDate: string; fullName: string }) => {
  return [
    {
      letter: 'a',
      description: `Es una persona moral mexicana debidamente constituida conforme la legislación nacional aplicable, según consta en la escritura pública número 62,229, de fecha 16 de febrero del 2021, pasada ante la fe del Lic. <PERSON>, Notario Público número 75, en la Ciudad de México, e inscrita en el Registro Público de la Propiedad y de Comercio, con fecha 24 de febrero del mismo año, bajo el folio mercantil número N-2021011614. Modificando su denominación a Sociedad Anónima Promotora de Inversión a través de la escritura pública 28,831 de fecha 10 de febrero de 2022, pasada ante la fé del Lic. <PERSON>, notario 238 de la Ciudad de México, e inscrita en el Registro Público de la Propiedad y de Comercio, con fecha 01 de marzo del mismo año, bajo el mismo folio mercantil. `,
    },
    {
      letter: 'b',
      description: `Su representante legal cuenta con todas las facultades suficientes para formalizar el presente convenio, personalidad que se acredita en términos de los testimonios señalados en el inciso inmediato anterior, facultades que a la fecha no le han sido revocadas ni modificadas en forma alguna. `,
    },
    {
      letter: 'c',
      description: `Su Registro Federal de Contribuyentes corresponde al número: EGM210216UV7.`,
    },
    {
      letter: 'd',
      description: `Dentro de su objeto social se encuentra la de otorgar en arrendamiento vehículos.`,
    },
    {
      letter: 'e',
      description: `Cuenta con la estructura material y jurídica suficiente para prestar el servicio correspondiente, de conformidad con la legislación civil vigente y aplicable al presente contrato. `,
    },
    {
      letter: 'f',
      description: `Informó a “EL CONDUCTOR” los alcances y efectos jurídicos del presente convenio.`,
    },
    {
      letter: 'g',
      description: `Que es su deseo firmar el presente acuerdo de voluntades, en los términos y condiciones que se establecen en este documento.`,
    },
    {
      letter: 'h',
      description: `Que en fecha de ${data.deliveryDate}, celebró el contrato de arrendamiento número 3703 con el conductor de nombre ${data.fullName}.`,
    },
    {
      letter: 'i',
      description: `Que señala como domicilio para oír y recibir notificaciones el ubicado en prolongación paseo de la reforma número 1015 piso sexto, interior 140 Santa Fe Cuajimalpa de Morelos, Ciudad de México. `,
    },
  ];
};

export function DeclarationList(/* { data }: DeclarationListProps */) {
  return (
    <>
      <Text style={styles.definicionesText}>
        {' '}
        PRIMERA.- DECLARA “EL ARRENDADOR”, A TRAVÉS DE SU REPRESENTANTE LEGAL QUE:{' '}
      </Text>
      <View style={styles.definicionContainer}>
        {declarationList({
          deliveryDate: '12 de marzo de 2022',
          fullName: 'Juan Pérez Sánchez Ejemplo',
        }).map(({ letter, /* title, */ description }, i) => {
          return (
            <View style={styles.item} key={i}>
              <View>
                <Text style={styles.letterPoint}> {letter}) </Text>
              </View>

              <Text style={styles.itemContent}>
                {' '}
                {/* <Text style={styles.defTitle}> {title} </Text> */}
                {description}{' '}
              </Text>
            </View>
          );
        })}
      </View>
    </>
  );
}

const fontConfig = {
  family: 'Helvetica',
  fonts: [],
};

Font.register(fontConfig);

const styles = StyleSheet.create({
  definicionContainer: {
    flexDirection: 'column',
    rowGap: 15,
  },

  definicionesText: {
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },

  item: {
    flexDirection: 'row',
    alignContent: 'center',
    marginLeft: '30px',
  },
  letterPoint: {
    fontSize: 8,
    marginRight: 17,
  },

  letterPointD: {
    fontSize: 8,
    marginRight: 10,
  },
  itemContent: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  defTitle: {
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
    fontWeight: 'bold',
  },
  clausulasContainer: {
    flexDirection: 'column',
    rowGap: '10px' as unknown as number,
  },

  clasulasBolds: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
  },
});

const declarationList2 = (data: { fullAddress: string }) => {
  return [
    {
      letter: 'a',
      description: `Llamarse según lo anotado en el proemio de este convenio, así como contar con la capacidad legal para 
cumplir con las obligaciones contenidas en este instrumento. `,
    },
    {
      letter: 'b',
      description: `Que cuenta con la capacidad legal, en términos de las leyes aplicables, para obligarse bajo los términos y condiciones contenidos en este convenio.`,
    },
    {
      letter: 'c',
      description: `Que es su deseo firmar el presente acuerdo de voluntades, en los términos y condiciones que se establecen en este documento.`,
    },
    {
      letter: 'd',
      description: `Que señala como domicilio para oír y recibir notificaciones el ubicado en: ${data.fullAddress}.`,
    },
  ];
};
export function DeclarationList2() {
  return (
    <>
      <Text style={styles.definicionesText}> SEGUNDA.- DECLARA “EL CONDUCTOR”:</Text>
      <View style={styles.definicionContainer}>
        {declarationList2({
          fullAddress: '15 DE MAYO 313 S/N VALLE HERMOSO Nuevo León GUADALUPE 67160. ',
        }).map(({ letter, /* title, */ description }, i) => {
          return (
            <View style={styles.item} key={i}>
              <View>
                <Text style={styles.letterPoint}> {letter}) </Text>
              </View>

              <Text style={styles.itemContent}>
                {' '}
                {/* <Text style={styles.defTitle}> {title} </Text> */}
                {description}{' '}
              </Text>
            </View>
          );
        })}
      </View>
    </>
  );
}
