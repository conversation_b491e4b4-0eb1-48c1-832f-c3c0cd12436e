import { Button, ButtonProps } from '@chakra-ui/react';
import { HomeVisitStepsStatus } from '@/constants';
import { anyStepInCompletedExceptOutcome, steps } from './steps';
import { cn } from '@/lib/utils';
import { translations } from './translations';

export interface IStepperButtonsProps {
  goToPrevious: () => void;
  goToNext: () => void;
  isLoading?: boolean;
  activeStep?: number;
  homeVisit?: Record<string, any>;
  showFinishWithoutCompleting?: boolean;
  finishWithoutCompleting?: () => void;
}

export const StepperButtons = (props: IStepperButtonsProps) => {
  const {
    goToPrevious,
    goToNext,
    isLoading,
    activeStep,
    homeVisit,
    showFinishWithoutCompleting,
    finishWithoutCompleting,
  } = props;

  const isActiveStepEqulsToFirstStep = activeStep === 0;
  const isActiveStepEqulsToLastStep = activeStep === steps.length - 1;

  let isAnyStepInCompleted = homeVisit?.homeVisitStepsStatus
    ? anyStepInCompletedExceptOutcome(homeVisit.homeVisitStepsStatus)
    : false;
  if (
    !isAnyStepInCompleted &&
    (homeVisit?.homeVisitStepsStatus.outcome === HomeVisitStepsStatus.incomplete ||
      homeVisit?.homeVisitStepsStatus.outcome === '')
  ) {
    isAnyStepInCompleted = false;
  }

  return (
    <section className={`flex py-2 ${isActiveStepEqulsToFirstStep ? 'justify-end' : 'justify-between'}`}>
      {!isActiveStepEqulsToFirstStep ? (
        <StepperButton
          text={translations.es.Previous}
          variant={'outline'}
          onClick={goToPrevious}
          className={'border border-primaryPurple text-primaryPurple'}
          isDisabled={isLoading}
        />
      ) : null}
      <div className="flex items-center gap-4">
        <button
          onClick={finishWithoutCompleting}
          className=" text-primaryNavyBlue hover:underline  disabled:opacity-50 "
          disabled={isLoading || isActiveStepEqulsToLastStep ? !showFinishWithoutCompleting : false}
        >
          {translations.es.FinishWithoutCompleting}
        </button>

        {isActiveStepEqulsToLastStep ? (
          <StepperButton
            text={translations.es.Finish}
            variant={'solid'}
            onClick={goToNext}
            className={'text-white bg-primaryPurple'}
            isDisabled={isLoading || showFinishWithoutCompleting}
            isLoading={isLoading}
          />
        ) : (
          <StepperButton
            text={translations.es.Next}
            variant={'solid'}
            onClick={goToNext}
            className={'text-white bg-primaryPurple'}
            isDisabled={isLoading}
            isLoading={isLoading}
          />
        )}
      </div>
    </section>
  );
};

interface IDefaultButton extends ButtonProps {
  text: string;
  className?: string;
}

export const StepperButton = (props: IDefaultButton) => {
  const { className, text, ...args } = props;
  return (
    <Button {...args} variant="primary" size="md" className={cn('px-6 rounded-full text-sm', className)}>
      {text}
    </Button>
  );
};
