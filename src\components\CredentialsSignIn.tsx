import { Formik, FormikState, FormikValues } from 'formik';
import { useEffect, useState } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { URL_API } from '@/constants';
import { loginValidation } from './Form/YupValidator';
import LoginForm from './Form/LoginForm';

type HandleSubmitType = (
  values: FormikValues,
  {
    resetForm,
  }: {
    resetForm: (nextState?: Partial<FormikState<FormikValues>> | undefined) => void;
  }
) => Promise<void>;

const defaultValues = {
  email: '',
  password: '',
};

interface CredentialsSignInProps {
  handleSetLoading: (loading: boolean) => void;
  callbackUrl?: string;
}

export const CredentialsSignIn = ({
  handleSetLoading,
  callbackUrl = '/dashboard',
}: CredentialsSignInProps) => {
  const router = useRouter();
  const [isInitialValid, setIsInitialValid] = useState(false);

  useEffect(() => {
    loginValidation
      .validate(defaultValues)
      .then(() => setIsInitialValid(true))
      .catch(() => setIsInitialValid(false));
  }, [isInitialValid]);

  const handleSubmit: HandleSubmitType = async (values, { resetForm }) => {
    handleSetLoading(true);
    const result = await signIn('credentials', {
      email: values.email,
      password: values.password,
      redirect: false,
      callbackUrl,
    })
      .catch(() => {})
      .finally(() => handleSetLoading(false));
    if (result?.error) {
      handleSetLoading(false);
      resetForm();
      return alert(result.error);
    } else {
      resetForm();
      if (!callbackUrl.startsWith('/dashboard') && !callbackUrl.startsWith('/adminpanel')) {
        const referrer = document.referrer;
        if (referrer && referrer !== '' && referrer.includes(URL_API)) {
          router.back();
        } else {
          router.push(callbackUrl);
        }
      } else {
        router.push(callbackUrl);
      }
    }
  };

  return (
    <Formik
      initialValues={defaultValues}
      validationSchema={loginValidation}
      onSubmit={handleSubmit}
      validateOnMount={true}
    >
      {({ isValid, dirty }) => <LoginForm valid={isValid} dirty={dirty} />}
    </Formik>
  );
};
