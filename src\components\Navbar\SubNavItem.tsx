'use client';
import { Flex, FlexProps, Icon, Link } from '@chakra-ui/react';
import { usePathname, useRouter } from 'next/navigation';
import { IconType } from 'react-icons';

interface NavItemProps extends FlexProps {
  icon: IconType;
  children: string;
  link: string;
}
const SubNavItem = ({ icon, children, link, ...rest }: NavItemProps) => {
  // const params = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  // console.log(pathname);

  return (
    <Link
      onClick={() => {
        router.push(link);
      }}
      style={{ textDecoration: 'none' }}
      _focus={{ boxShadow: 'none' }}
    >
      <Flex
        align="center"
        p="4"
        ml="8"
        mr="4"
        borderRadius="lg"
        role="group"
        cursor="pointer"
        color={link === pathname ? 'white' : 'black'}
        bg={link === pathname ? '#5800F7' : ''}
        _hover={{
          bg: '#5800F7',
          color: 'white',
        }}
        {...rest}
      >
        {icon && (
          <Icon
            mr="4"
            fontSize="16"
            _groupHover={{
              color: 'white',
            }}
            as={icon}
          />
        )}
        {children}
      </Flex>
    </Link>
  );
};

export default SubNavItem;
