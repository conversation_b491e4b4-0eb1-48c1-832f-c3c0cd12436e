import { HookFormRadixUI<PERSON>ield, HookFormRadixUISelect } from '../HookFormField';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormItem, FormLabel } from '@/components/ui/form';
import {
  calculateAverageEarningsOfLastTwelveWeeks,
  capitalizeFirstLetter,
  CountriesShortNames,
  getNationalities,
  HomeVisitStepsStatus,
} from '@/constants';
import { FormSectionHeader } from '../FormHeaderSection';
import { FormSection } from '../FormSection';
import { LuClock5, LuCalendar } from 'react-icons/lu';
import { format, parse, isValid } from 'date-fns';
import { IStepperButtonsProps } from '../StepperButtons';
import { useDisclosure, useToast } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { useState } from 'react';
import { Steps, toastConfigs } from '.';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlatformDetailDialog } from '../../../PlatformDetailDialog';
import { CountryContext } from '../../../detail';
import { DigiLogo } from '@/svgsComponents/DidiLogo';
import { UberLogo } from '@/svgsComponents/UberLogo';
import { InDriveLogo } from '@/svgsComponents/InDriveLogo';

const PersonalInformationSchema = z.object({
  visitDate: z.string({
    required_error: translations.es.DateRequired,
  }),
  visitTime: z.string({
    required_error: translations.es.TimeRequired,
  }),
  firstName: z.string({
    required_error: translations.es.FirstNameRequired,
  }),
  lastName: z.string({
    required_error: translations.es.LastNameRequired,
  }),
  nationality: z.string({
    required_error: translations.es.NationalityRequired,
  }),
  dateOfBirth: z.string({ required_error: translations.es.BirthDateRequired }).refine(
    (arg) => {
      const parsedDate = parse(arg, 'dd-MM-yyyy', new Date());
      return isValid(parsedDate);
    },
    { message: translations.es.BirthDateFormatErrorMsg }
  ),
  age: z
    .number()
    .lte(60, { message: translations.es.AgeLessThan60 })
    .gte(18, { message: translations.es.AgeGreaterThan18 }),
  occupation: z.string(),
  weeklyEarnings: z.number(),
});

interface IPersonalInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function PersonalInformation(props: IPersonalInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;

  const { id: requestId, personalData, homeVisit, earningsAnalysis, palenca } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof PersonalInformationSchema>>({
    resolver: zodResolver(PersonalInformationSchema),
    defaultValues: {
      visitDate: homeVisit
        ? homeVisit.visitDate
          ? format(new Date(homeVisit.visitDate), 'yyyy-MM-dd')
          : format(new Date(), 'yyyy-MM-dd')
        : format(new Date(), 'yyyy-MM-dd'),
      visitTime: homeVisit?.visitTime ?? '',
      firstName: personalData.firstName || '',
      lastName: personalData.lastName || '',
      nationality: personalData.nationality || 'Mexico',
      dateOfBirth: personalData.birthdate ? format(new Date(personalData.birthdate), 'dd-MM-yyyy') : '',
      age: personalData.age
        ? personalData.age
        : personalData.birthdate
        ? new Date().getFullYear() - new Date(personalData.birthdate).getFullYear()
        : 0,
      occupation: personalData.occupation || '',
      weeklyEarnings: personalData.avgEarningPerWeek
        ? personalData.avgEarningPerWeek
        : earningsAnalysis
        ? calculateAverageEarningsOfLastTwelveWeeks(earningsAnalysis.totalEarnings)
        : 0,
    },
  });

  async function onSubmit(data: z.infer<typeof PersonalInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);
      const isDataCompleted = Object.values(data).every((value) => value !== '');
      const dateOfBirth = parse(data.dateOfBirth, 'dd-MM-yyyy', new Date());
      const payload = {
        personalData: {
          firstName: data.firstName,
          lastName: data.lastName,
          nationality: data.nationality,
          birthdate: format(dateOfBirth, 'yyyy-MM-dd'),
          age: data.age,
          occupation: data.occupation,
          avgEarningPerWeek: data.weeklyEarnings,
        },
        homeVisitData: {
          visitDate: data.visitDate,
          visitTime: data.visitTime,
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            personal: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Personal, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Personal, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }
  const isCountryUSA = personalData?.country === CountriesShortNames['United States'];

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.DateAndTimeOfVisit} />
      <Form {...form}>
        <form>
          <div className="flex py-2 gap-4 w-3/6  ">
            <HookFormRadixUIField
              form={form}
              fieldName="visitDate"
              formLabel={translations.es.Date}
              Icon={LuCalendar}
              isDisabled={true}
            />
            <HookFormRadixUIField
              form={form}
              fieldName="visitTime"
              formLabel={translations.es.Time}
              Icon={LuClock5}
            />
          </div>
          <FormSectionHeader title={translations.es.PersonalInformation} />
          <div className="flex py-2 gap-6">
            <HookFormRadixUIField form={form} fieldName="firstName" formLabel={translations.es.FirstName} />
            <HookFormRadixUIField form={form} fieldName="lastName" formLabel={translations.es.LastName} />
          </div>

          <HookFormRadixUISelect
            control={form.control}
            fieldName="nationality"
            selectOptions={getNationalities()}
            formLabel={translations.es.Nationality}
            className="w-3/6"
          />

          <HookFormRadixUIField
            form={form}
            fieldName="dateOfBirth"
            formLabel={translations.es.DateOfBirth}
            placeholder={'dd-mm-yyyy'}
            className="w-3/6 py-2"
          />

          <HookFormRadixUIField
            form={form}
            fieldName="age"
            formLabel={translations.es.Age}
            className="w-3/6 py-2"
            type="number"
          />

          <HookFormRadixUIField
            form={form}
            fieldName="occupation"
            formLabel={translations.es.Occupation}
            className="w-3/6 py-2"
          />
          <div className="flex py-2 gap-4 ">
            <HookFormRadixUIField
              form={form}
              fieldName="weeklyEarnings"
              formLabel={translations.es.WeeklyEarnings}
              type="number"
            />
            <CountryContext.Provider
              value={{
                isCountryUSA: isCountryUSA,
              }}
            >
              <ViewEarnings requestId={requestId} palenca={palenca} />
            </CountryContext.Provider>
          </div>
        </form>
      </Form>
    </FormSection>
  );
}

const ViewEarnings = ({ requestId, palenca }: { requestId: string; palenca: Record<string, any> }) => {
  const { onClose, onOpen, isOpen } = useDisclosure();
  const [selectedPlatform, setSelectedPlatform] = useState('');

  const platformsLogos = {
    didi: DigiLogo,
    uber: UberLogo,
    indriver: InDriveLogo,
  };

  return (
    <>
      <FormItem className={'flex-1 flex flex-col h-6'}>
        <FormLabel className="basis-1/3 text-primaryBlueGray text-sm">
          {translations.es.ViewEarnings}
        </FormLabel>
        <Select
          onValueChange={(value) => {
            setSelectedPlatform(value);
            onOpen();
          }}
          value={selectedPlatform}
        >
          <FormControl className="basis-2/3">
            <SelectTrigger>
              <SelectValue placeholder={translations.es.SelectOne} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {palenca.accounts.map((account: any) => {
              const Logo = platformsLogos[account.platform as keyof typeof platformsLogos];
              return (
                <SelectItem
                  key={account.accountId}
                  className="flex items-center px-1"
                  value={account?.platform}
                >
                  <span className="flex items-center gap-1">
                    <Logo />
                    <span className=" text-primaryBlueGray text-sm font-normal">
                      {account?.platform ? capitalizeFirstLetter(account?.platform) : ''}
                    </span>
                  </span>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>

        {isOpen ? (
          <PlatformDetailDialog
            requestId={requestId}
            isOpen={isOpen}
            onClose={() => {
              setSelectedPlatform('');
              onClose();
            }}
            platform={selectedPlatform}
            isPlatformMetricsTable={false}
          />
        ) : null}
      </FormItem>
    </>
  );
};
