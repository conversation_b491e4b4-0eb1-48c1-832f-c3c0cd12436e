import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontainer, <PERSON>body, Th, Thead, Tr, useToast } from '@chakra-ui/react';
import { useCountry } from './detail';
import { z } from 'zod';
import { Control, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Metric, updateAdmissionRequestAccountInfo } from '@/actions/postAdmissionRequest';
import { useState } from 'react';
import { useRouter } from 'next/navigation';

function numberFormat(value: number) {
  if (!value) return 0;
  return new Intl.NumberFormat('es-MX').format(value);
}

export default function PlatformMetricsTable({
  metric,
  requestId,
  onClose,
}: {
  metric: Metric;
  requestId: string;
  onClose: () => void;
}) {
  const { isCountryUSA } = useCountry();
  const { isSuperAdminOrAdmin } = useCurrentUser();

  const totalTrips = isCountryUSA ? 'Total trips' : 'Viajes totales';
  const acceptanceRate = isCountryUSA ? 'Acceptance rate' : 'Tasa de aceptación';
  const cancellationRate = isCountryUSA ? 'Cancellation rate' : 'Tasa de cancelación';
  const rating = isCountryUSA ? 'Rating' : 'Calificación';
  const timeSinceFirstTrip = isCountryUSA ? 'Time since first trip' : 'Tiempo desde primer viaje';

  return (
    <TableContainer fontSize="sm">
      {!isSuperAdminOrAdmin ? (
        <TableView metric={metric}>
          <TableHead
            totalTrips={totalTrips}
            acceptanceRate={acceptanceRate}
            cancellationRate={cancellationRate}
            rating={rating}
            timeSinceFirstTrip={timeSinceFirstTrip}
          />
        </TableView>
      ) : (
        <TableEdit metric={metric} requestId={requestId} onClose={onClose}>
          <TableHead
            totalTrips={totalTrips}
            acceptanceRate={acceptanceRate}
            cancellationRate={cancellationRate}
            rating={rating}
            timeSinceFirstTrip={timeSinceFirstTrip}
          />
        </TableEdit>
      )}
    </TableContainer>
  );
}

interface ITableHead {
  totalTrips: string;
  acceptanceRate: string;
  cancellationRate: string;
  rating: string;
  timeSinceFirstTrip: string;
}
const TableHead = (props: ITableHead) => {
  const { totalTrips, acceptanceRate, cancellationRate, rating, timeSinceFirstTrip } = props;
  return (
    <Thead>
      <Tr>
        <Th>{totalTrips}</Th>
        <Th>{acceptanceRate}</Th>
        <Th>{cancellationRate}</Th>
        <Th>{rating}</Th>
        <Th>{timeSinceFirstTrip}</Th>
      </Tr>
    </Thead>
  );
};
interface ITableView {
  children: React.ReactNode;
  metric: Metric;
}
const TableView = (props: ITableView) => {
  const { metric, children } = props;
  return (
    <Table variant="striped">
      {children}
      <Tbody>
        <Tr bg="gray.50">
          <Th>{numberFormat(metric.lifetimeTrips)}</Th>
          <Th>{metric.acceptanceRate}%</Th>
          <Th>{metric.cancellationRate}%</Th>
          <Th>{metric.rating}</Th>
          <Th>{numberFormat(metric.timeSinceFirstTrip)} días</Th>
        </Tr>
      </Tbody>
    </Table>
  );
};

const InputFormField = (props: { control: Control<any>; name: string }) => {
  const { control, name } = props;
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col items-center w-full">
          <FormControl>
            <Input
              type="number"
              className="basis-1/3"
              {...field}
              onChange={(e) => field.onChange(e.target.valueAsNumber)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

const FormSchema = z.object({
  lifetimeTrips: z.number(),
  acceptanceRate: z.number(),
  cancellationRate: z.number(),
  rating: z.number(),
  timeSinceFirstTrip: z.number(),
});

interface ITableEdit extends ITableView {
  requestId: string;
  onClose: () => void;
}
const TableEdit = (props: ITableEdit) => {
  const { metric, children, requestId, onClose } = props;

  const [isLoading, setLoading] = useState(false);
  const { isCountryUSA } = useCountry();

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      lifetimeTrips: metric.lifetimeTrips || 0,
      acceptanceRate: metric.acceptanceRate || 0,
      cancellationRate: metric.cancellationRate || 0,
      rating: metric.rating || 0,
      timeSinceFirstTrip: metric.timeSinceFirstTrip || 0,
    },
    mode: 'onBlur',
    reValidateMode: 'onBlur',
  });
  const toast = useToast();
  const router = useRouter();

  const handleSubmit = async (data: Metric) => {
    try {
      setLoading(true);
      await updateAdmissionRequestAccountInfo({ requestId: requestId, requestPayload: data });
      toast({
        title: 'Client profile updated',
        status: 'success',
      });
      onClose();
      router.refresh();
    } catch (err) {
      toast({
        title: 'Error occured while updating platform into.',
        status: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.reset();
  };

  const cancelText = isCountryUSA ? 'Cancel' : 'Cancelar';
  const saveText = isCountryUSA ? 'Save' : 'Guardar';

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <Table variant="striped">
          {children}
          <Tbody>
            <Tr bg="gray.50">
              <Th>{<InputFormField control={form.control} name="lifetimeTrips" />}</Th>
              <Th>{<InputFormField control={form.control} name="acceptanceRate" />}</Th>
              <Th>{<InputFormField control={form.control} name="cancellationRate" />}</Th>
              <Th>{<InputFormField control={form.control} name="rating" />}</Th>
              <Th className="flex  items-center">
                {<InputFormField control={form.control} name="timeSinceFirstTrip" />}
                {<p>year</p>}
              </Th>
            </Tr>
          </Tbody>
        </Table>
        <div className="flex justify-end gap-2 py-4 w-full">
          <Button
            sx={{
              color: '#5800F7',
              borderColor: '#5800F7 !important',
              border: '1px',
              h: '40px',
            }}
            onClick={handleCancel}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            sx={{
              color: 'white',
              h: '40px',
            }}
            className={'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'}
            type="submit"
            disabled={isLoading}
            isLoading={isLoading}
          >
            {saveText}
          </Button>
        </div>
      </form>
    </Form>
  );
};
