import React from 'react';
import { Invoice } from '../../types';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { DataTable } from '@/components/DataTable';
import { invoiceColumns } from './invoiceColumns';

async function getRecurringInvoice(id: string): Promise<Invoice[]> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/payments/invoices?clientId=${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}

type Props = {
  id?: string;
};

export default async function InvoiceTab({ id }: Props) {
  let invoiceData: Invoice[] = [];
  if (id) {
    const invoiceResponse = await getRecurringInvoice(id as string);
    if (invoiceResponse) {
      invoiceData = invoiceResponse;
    }
  }

  return <DataTable columns={invoiceColumns} data={invoiceData}></DataTable>;
}
