/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: '/dashboard/flotilla/vehicle-redirect/:vehicleId',
        destination: '/vehicle-redirect/:vehicleId',
      },
    ];
  },
  images: {
    //this is just for testing purposes, will be eliminated soon
    domains: [
      'ionicframework.com',
      'w7.pngwing.com',
      'onecarnow-demo.s3.us-east-1.amazonaws.com',
      'onecarnow.s3.us-east-1.amazonaws.com',
      'img.freepik.com',
      'onecarnow-demo.s3.us-east-1.amazonaws.com',
    ],
  },
  reactStrictMode: false,
};

module.exports = nextConfig;
