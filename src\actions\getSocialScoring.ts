import { URL_API } from '@/constants';

interface GetSocialScoring {
  requestId: string;
}

export default async function getSocialScoring({ requestId }: GetSocialScoring) {
  console.log({ requestId });
  try {
    const res = await fetch(`${URL_API}/mlservice/homevisit/stacking-clf/${requestId}`);

    const response = await res.json();

    return response;
  } catch (error) {
    console.error(error);
    return null;
  }
}
