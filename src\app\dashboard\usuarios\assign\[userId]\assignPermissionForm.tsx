'use client';
import React, { useState } from 'react';
import Swal from 'sweetalert2';
import axios from 'axios';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { MyUser } from '@/actions/getCurrentUser';
import { useSession } from 'next-auth/react';
import { Section } from '@/actions/getPermissionMatrix';
import { useRouter } from 'next/navigation';
import PermissionMatrix from '../../../permisos/PermissionMatrix';
import { UserAssignedPermissionsResponse } from '@/actions/getUserAssignedPermissionsById';
interface Props {
  permissionMatrix: Section[];
  assignedpermissions: UserAssignedPermissionsResponse;
}

const AssignPermissionForm: React.FC<Props> = ({ permissionMatrix, assignedpermissions }) => {
  const user = assignedpermissions.user;
  const permissionSet = user.permissions ?? [];

  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    permissionSet.forEach((perm) => {
      const key = `${perm.section}.${perm.subSection}.${perm.capability}`;

      const section = permissionMatrix.find((s) => s.section === perm.section);
      const subSection = section?.subSections.find((ss) => ss.subSection === perm.subSection);
      const hasCapability = subSection?.capabilities.includes(perm.capability);

      initial[key] = !!hasCapability;
    });

    return initial;
  });

  const disabledPermissionSet = assignedpermissions?.permissions ?? [];

  const [disabledPermissions] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    disabledPermissionSet.forEach((perm) => {
      const key = `${perm.section}.${perm.subSection}.${perm.capability}`;

      const section = permissionMatrix.find((s) => s.section === perm.section);
      const subSection = section?.subSections.find((ss) => ss.subSection === perm.subSection);
      const hasCapability = subSection?.capabilities.includes(perm.capability);

      initial[key] = !!hasCapability;
    });

    return initial;
  });
  const { data: session } = useSession();
  const url = useCurrentUrl();
  const myUser = session?.user as unknown as MyUser;
  const router = useRouter();

  const assignPermissionSetAPI = async (body: any) => {
    const response = await axios.patch(`${url}/permissionSet/assginPermissionSet/${user._id}`, body, {
      headers: {
        Authorization: `Bearer ${myUser.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const permissionData = {
      permissions: [] as Array<{ section: string; subSection: string; capability: string }>,
    };

    Object.keys(selectedPermissions).forEach((key) => {
      if (selectedPermissions[key]) {
        const [section, subSection, capability] = key.split('.');
        permissionData.permissions.push({ section, subSection, capability });
      }
    });

    try {
      const response = await assignPermissionSetAPI(permissionData);

      if (response.status === 200) {
        Swal.fire({
          title: 'Actualización exitosa',
          text: response.data.message,
          icon: 'success',
          confirmButtonText: 'Cerrar',
        }).then(() => {
          router.push('/dashboard/usuarios');
        });
      } else {
        Swal.fire({
          title: 'Algo salió mal',
          text: 'Respuesta inesperada del servidor',
          icon: 'warning',
          confirmButtonText: 'Cerrar',
        });
      }
    } catch (error: any) {
      Swal.fire({
        title: 'Algo salió mal',
        text: error?.response?.data?.message || 'Ocurrió un error inesperado',
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    }
  };

  if (!permissionSet) return <div>Loading...</div>;

  return (
    <form onSubmit={handleSubmit}>
      <div className="flex flex-wrap items-center gap-x-8 gap-y-2 text-sm font-medium">
        <div className="flex items-center gap-1">
          <span className="text-gray-500">Nombre:</span>
          <span className="font-semibold">{user.name}</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="text-gray-500">Correo:</span>
          <span className="font-semibold">{user.email}</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="text-gray-500">Área:</span>
          <span className="font-semibold">{user.area}</span>
        </div>
        <div className="flex items-center gap-1">
          <span className="text-gray-500">Rol:</span>
          <span className="font-semibold">{user.role}</span>
        </div>
      </div>
      <div className="mt-6">
        <PermissionMatrix
          permissionSets={permissionMatrix}
          selected={selectedPermissions}
          setSelected={setSelectedPermissions}
          disabledPermissions={disabledPermissions}
        />
      </div>

      <div className="flex justify-end mt-6">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded"
        >
          Asignar Permisos Adicionales
        </button>
      </div>
    </form>
  );
};

export default AssignPermissionForm;
