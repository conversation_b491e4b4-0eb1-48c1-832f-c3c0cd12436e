import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import { redirect } from 'next/navigation';

interface AdminPanelPageProps {
  params: {
    slug: string;
  };
}

export default async function AdminPanelPage({ params }: AdminPanelPageProps) {
  // Check if user is authenticated
  const session = await getServerSession(authOptions);

  // If not authenticated, redirect to home (the middleware will handle adding callbackUrl)
  if (!session?.user) {
    redirect('/');
  }

  // If authenticated, display the protected content
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Admin Panel: {params.slug}</h1>
      <p className="mb-4">Welcome, {session.user.name}!</p>
      <p className="mb-4">You are viewing the {params.slug} section.</p>
      <p>This page is protected and only accessible to authenticated users.</p>
    </div>
  );
}
