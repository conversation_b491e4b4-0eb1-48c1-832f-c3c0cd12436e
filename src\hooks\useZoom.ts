import { DocumentData } from '@/actions/getUserById';
import { create } from 'zustand';

interface DriverData {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  image: DocumentData;
  setImage: (imageUrl: DocumentData) => void;
}

export const useZoom = create<DriverData>((set) => ({
  isOpen: false,
  image: {
    docId: '',
    originalName: '',
    url: '',
  },
  setImage: (image: DocumentData) => set(() => ({ image })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));
