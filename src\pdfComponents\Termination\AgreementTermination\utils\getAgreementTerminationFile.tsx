import { pdf } from '@react-pdf/renderer';
import AgreementTerminationDocumentPDF from '../AgreementTermination-DocumentPDF';

type AgreementTerminationDocumentPDFProps = React.ComponentProps<typeof AgreementTerminationDocumentPDF>;

/**
 * Function to get the Agreement termination file
 * @param data - The props of the component AgreementTerminationDocumentPDF
 * @returns The file instance
 */

export const getAgreementTerminationFile = async (data: AgreementTerminationDocumentPDFProps) => {
  const blob = await pdf(<AgreementTerminationDocumentPDF {...data} />).toBlob();

  const buffer = await blob.arrayBuffer();
  const upperCaseFirstName = data.firstName.split('')[0].toUpperCase() + data.firstName.slice(1);
  const upperCaseLastName = data.lastName.split('')[0].toUpperCase() + data.lastName.slice(1);

  // create random id with 6 characters including numbers and letters with upperCase and lowerCase
  const randomId = Math.random().toString(36).slice(2, 8);

  const fileInstance = new File(
    [buffer],
    `Convenio-${upperCaseFirstName}${upperCaseLastName}-${randomId}.pdf`,
    {
      type: 'application/pdf',
    }
  );

  return fileInstance;
};
