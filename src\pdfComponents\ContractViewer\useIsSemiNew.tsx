import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { URL_API } from '@/constants';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { useSemiNewContractInfo } from '../contract/useSemiNewContractInfo';
import { differenceInWeeks, parse } from 'date-fns';
import { paymentsArray, siguienteLunes } from '../contract/data/Lunes';
import moment from 'moment';

export default function useIsSemiNewCar({
  newCar,
  deliveredDate,
  paymentsDone,
  form,
}: {
  newCar: boolean;
  deliveredDate: string;
  paymentsDone: string;
  form: any;
}) {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>({});
  const { user } = useCurrentUser();
  // console.log('loading', loading);
  const { setIsNew, setTotalWeeks } = useSemiNewContractInfo();

  const params = useParams();

  const getData = useCallback(async () => {
    try {
      setLoading(true);
      // const start = performance.now();
      const response = await axios(`${URL_API}/contract/get/vehiclePayments/last/${params.id}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      // const end = performance.now();
      // console.log('Tiempo de ejecución en ms', end - start);
      setLoading(false);

      // console.log(response.data.lastPayment);
      const paymentNumber = paymentsDone ? Number(paymentsDone) : response.data.lastPayment.paymentNumber;
      // const lastPayment = response.data.mainContract.allPayments[paymentNumber - 1].day;
      const lastPayment =
        response.data.mainContract.allPayments[paymentNumber == '0' ? 0 : paymentNumber - 1].day;

      const date = moment(deliveredDate);

      const nextMondey = siguienteLunes(date);
      const allPayments = paymentsArray(nextMondey, form.totalPays);
      const entryFormat = 'dd-MM-yyyy';
      const firstPayment = parse(allPayments[0].day, entryFormat, new Date());
      const lastPaymentMade = parse(lastPayment, entryFormat, new Date());
      const weeksDiff = differenceInWeeks(firstPayment, lastPaymentMade);
      const total = allPayments.length - paymentNumber + weeksDiff;
      setTotalWeeks(total);
      setData(response.data);
    } catch (error) {
      console.log(error);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.id, user, deliveredDate]);

  useEffect(() => {
    if (!newCar) {
      setIsNew(newCar);
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [newCar]);
  return {
    loading,
    data,
  };
}
