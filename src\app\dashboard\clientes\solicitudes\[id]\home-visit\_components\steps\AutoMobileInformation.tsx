import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField, HookFormRadixUISelect } from '../HookFormField';
import { FormSection } from '../FormSection';
import { FormSectionHeader } from '../FormHeaderSection';
import { HomeVisitStepsStatus, YesOrNoOptions, yesOrNoOptions } from '@/constants';
import { useState } from 'react';
import { useToast } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { IStepperButtonsProps } from '../StepperButtons';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { NoSelected, Steps, toastConfigs } from '.';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

const AutoMobileInformationSchema = z.object({
  ownACar: z.string(),
  leasingtime: z.string(),
  make: z.string(),
  model: z.string(),
  doesItApplyToElectricCars: z.string(),
});

interface IAutoMobileInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function AutoMobileInformation(props: IAutoMobileInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, personalData, homeVisit } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof AutoMobileInformationSchema>>({
    resolver: zodResolver(AutoMobileInformationSchema),
    defaultValues: {
      ownACar: personalData.ownACar || NoSelected,
      leasingtime: personalData.carLeasingtime || '',
      make: personalData.carMake || '',
      model: personalData.carModel || '',
      doesItApplyToElectricCars: personalData.doesItApplyToElectricCars || NoSelected,
    },
  });

  const ownACar = form.watch('ownACar');

  async function onSubmit(data: z.infer<typeof AutoMobileInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);
      const isDataCompleted = Object.entries(data).every(([key, value]) => {
        if (key === 'leasingtime') {
          return data.ownACar === YesOrNoOptions.No ? value !== '' : true;
        }
        return value !== '' && value !== NoSelected;
      });

      const payload = {
        personalData: {
          ownACar: data.ownACar,
          carLeasingtime: data.leasingtime,
          carMake: data.make,
          carModel: data.model,
          doesItApplyToElectricCars: data.doesItApplyToElectricCars,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            automobile: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Automobile, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Automobile, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.DebtInformation} />
      <Form {...form}>
        <form>
          <div className="w-3/6 py-2">
            <HookFormRadixUISelect
              control={form.control}
              fieldName="ownACar"
              selectOptions={yesOrNoOptions}
              formLabel={translations.es.ownACar}
            />

            {ownACar === YesOrNoOptions.No && (
              <HookFormRadixUIField
                form={form}
                fieldName="leasingtime"
                formLabel={translations.es.HowLongHaveYouBeenLeasingIt}
                className="py-2"
              />
            )}

            <div className="py-2">
              <HookFormRadixUIField form={form} fieldName="make" formLabel={translations.es.Make} />
            </div>

            <div className="py-2">
              <HookFormRadixUIField form={form} fieldName="model" formLabel={translations.es.Model} />
            </div>
            <div className="py-2">
              <HookFormRadixUISelect
                control={form.control}
                fieldName="doesItApplyToElectricCars"
                selectOptions={yesOrNoOptions}
                formLabel={translations.es.doesItApplyToElectricCars}
              />
            </div>
          </div>
        </form>
      </Form>
    </FormSection>
  );
}
