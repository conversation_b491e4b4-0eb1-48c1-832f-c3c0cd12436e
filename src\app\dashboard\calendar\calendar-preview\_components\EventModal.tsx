import { Stepper<PERSON>utton } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/StepperButtons';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Capabilities, Sections, Subsections, URL_API } from '@/constants';
import { ApplicationLogo } from '@/svgsComponents/ApplicationLogo';
import { CalendarLogo } from '@/svgsComponents/CalendarLogo';
import { ClockLogo } from '@/svgsComponents/Clock';
import { HomeLogo } from '@/svgsComponents/HomeLogo';
import { MailLogo } from '@/svgsComponents/MailLogo';
import { MeetingLinkLogo } from '@/svgsComponents/MeetingLinkLogo';
import { PersonLogo } from '@/svgsComponents/PersonLogo';
import { PhoneLogo } from '@/svgsComponents/PhoneLogo';
import { QuoteLogo } from '@/svgsComponents/QuoteLogo';
import { Box, Link, Text, useToast } from '@chakra-ui/react';
import { DateTime } from 'luxon';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { ClientPath } from '@/constants/api.endpoints';
import { statusChangeAppointment } from '@/actions/calendar';
import { toastConfigs } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/steps';
import { MdOutlineMeetingRoom } from 'react-icons/md';
import { MyUser } from '@/actions/getCurrentUser';
import { translations } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/translations';
import { AppointmentEvent, AppointmentStatus, AppointmentStatusTranslationMX } from '../../types';
import { IoIosArrowBack } from 'react-icons/io';
import { HomeVisitorsWithFreeSlots } from './HomeVisitorsSelect';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: AppointmentEvent;
}

interface IAppointmentDetailsLayoutProps {
  event: AppointmentEvent;
}

export const AppointmentDetailsLayout = (props: IAppointmentDetailsLayoutProps) => {
  const {
    event: {
      date,
      startTime: startTimeInISO,
      firstName,
      email,
      phone,
      admissionRequestId,
      status,
      meetingLink,
    },
  } = props;

  const router = useRouter();
  const navivateToRequest = (id: string) => {
    router.push(`${ClientPath.DASHBOARD}${ClientPath.CLIENTES}${ClientPath.SOLICITUDES}/${id}`);
  };

  const eventDate = DateTime.fromISO(date, { zone: 'utc' }).toLocaleString({
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const startTime = DateTime.fromISO(startTimeInISO).toFormat('hh:mm a');

  const clientDetails = [
    {
      Icon: HomeLogo,
      text: 'Home Visit',
    },
    {
      Icon: PersonLogo,
      text: firstName,
    },
    {
      Icon: MailLogo,
      text: email,
    },
    {
      Icon: PhoneLogo,
      text: phone,
    },
    {
      Icon: CalendarLogo,
      text: eventDate,
    },
    {
      Icon: ClockLogo,
      text: startTime,
    },
  ];

  return (
    <Box py={1}>
      {clientDetails.map((clientDetail) => {
        return (
          <Box
            key={clientDetail.text}
            py={2}
            display={'flex'}
            gap={3}
            alignItems={'center'}
            color={'#464E5F'}
          >
            <clientDetail.Icon /> <Text fontWeight={'medium'}>{clientDetail.text}</Text>
          </Box>
        );
      })}
      <Box display={'flex'} gap={2} color={'#464E5F'} py={2}>
        <QuoteLogo />
        <Text fontWeight={'medium'}>
          {translations.es.ID} {': '} {admissionRequestId}{' '}
        </Text>
      </Box>

      <Box display={'flex'} gap={2} color={'#464E5F'} py={2}>
        <MdOutlineMeetingRoom size={23} />
        <Text fontWeight={'medium'}>
          {translations.es.MeetingStatus} {': '} {AppointmentStatusTranslationMX[status]}{' '}
        </Text>
      </Box>
      <Box display={'flex'} justifyContent={'space-even'} gap={12}>
        <Box py={2} display={'flex'} gap={4} alignItems={'center'} color={'#464E5F'}>
          <ApplicationLogo />
          <Text
            fontWeight={'medium'}
            className=" text-primaryVibrantPurpleGradient underline cursor-pointer"
            onClick={() => navivateToRequest(admissionRequestId)}
          >
            {translations.es.ApplicationLink}
          </Text>
        </Box>
        <Box py={2} display={'flex'} gap={2} alignItems={'center'} color={'#464E5F'}>
          <MeetingLinkLogo />
          <Link
            fontWeight={'medium'}
            className=" text-primaryVibrantPurpleGradient underline "
            href={meetingLink}
            target="_blank"
          >
            {translations.es.MeetingLink}
          </Link>
        </Box>
      </Box>
    </Box>
  );
};

const Steps = {
  Appointment: 0,
  ReAssign: 1,
};

export function EventModal({ isOpen, onClose, event }: EventModalProps) {
  const [admissionRequest, setAdmissionRequest] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const toast = useToast();
  const router = useRouter();
  const { data: session } = useSession();

  const user = session?.user as unknown as MyUser;
  useEffect(() => {
    async function getAdmissionRequest({ id, accessToken }: { id: string; accessToken: string }) {
      try {
        const res = await fetch(`${URL_API}/admission/requests/${id}`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        const response = await res.json();
        if (response.success) {
          setAdmissionRequest(response.data);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Error occured while fetching admission request',
          status: 'error',
          duration: 2000,
        });
      }
    }

    if (!admissionRequest) {
      getAdmissionRequest({
        id: event.admissionRequestId,
        accessToken: user.accessToken,
      });
    }
  }, [admissionRequest, event, toast, user.accessToken]);

  const handleMeetingStatusChange = async (status: keyof typeof AppointmentStatus) => {
    try {
      setIsLoading(true);
      const payload = {
        id: event?.id,
        status: status,
      };
      const response = await statusChangeAppointment(payload);
      if (response?.success) {
        toast(toastConfigs('Appointment status', 'success'));
        onClose();
        router.refresh();
      }
    } catch (err) {
      toast(toastConfigs('Appointment status', 'error'));
    } finally {
      setIsLoading(false);
    }
  };
  const isAppointmentCompleted = event.status === AppointmentStatus.completed;
  const isAppointmentNoShow = event.status === AppointmentStatus.noshow;

  const [step, setStep] = useState(Steps.Appointment);

  const handleSetStep = (stepNumber: number) => {
    setStep(stepNumber);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {
        <DialogContent className="sm:max-w-[450px] ">
          {step === Steps.Appointment && (
            <Appointment
              event={event}
              admissionRequest={admissionRequest}
              handleMeetingStatusChange={handleMeetingStatusChange}
              isLoading={isLoading}
              isAppointmentCompleted={isAppointmentCompleted}
              isAppointmentNoShow={isAppointmentNoShow}
              handleSetStep={handleSetStep}
            />
          )}
          {step === Steps.ReAssign && (
            <AppointmentReAssign handleSetStep={handleSetStep}>
              <HomeVisitorsWithFreeSlots event={event} onClose={onClose} />
            </AppointmentReAssign>
          )}
        </DialogContent>
      }
    </Dialog>
  );
}

const Appointment = (props: any) => {
  const {
    event,
    admissionRequest,
    handleMeetingStatusChange,
    isLoading,
    isAppointmentCompleted,
    isAppointmentNoShow,
    handleSetStep,
  } = props;

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const isMyEvent = event.user._id === user.id;

  const ability = usePermissions();

  const canEdit = canPerform(
    ability,
    Capabilities.EditMyCalendar,
    Sections.Calendar,
    Subsections.CalendarPreview
  );

  const canEditAll = canPerform(
    ability,
    Capabilities.EditAllCalendar,
    Sections.Calendar,
    Subsections.CalendarPreview
  );

  return (
    <>
      <DialogHeader>
        <DialogTitle className=" text-2xl">{translations.es.VirtualHomeVisit}</DialogTitle>
      </DialogHeader>
      <div>
        <AppointmentDetailsLayout
          event={{
            ...event,
            firstName: admissionRequest?.personalData?.firstName,
            email: admissionRequest?.personalData?.email,
            phone: admissionRequest?.personalData?.phone,
          }}
        />
      </div>
      {(canEditAll || (canEdit && isMyEvent)) && (
        <div className="flex flex-col gap-1 justify-center">
          <div className="flex gap-2 justify-evenly">
            <StepperButton
              text={translations.es.NoShow}
              variant={'outline'}
              onClick={() => {
                handleMeetingStatusChange(AppointmentStatus.noshow);
              }}
              className="flex-1 text-md font-semibold bg-white text-[#5800F7] p-2 rounded border-[#5800F7] border hover:bg-gray-100"
              isDisabled={isLoading || isAppointmentCompleted || isAppointmentNoShow}
            />
            <StepperButton
              text={translations.es.Reassign}
              variant={'outline'}
              onClick={() => handleSetStep(Steps.ReAssign)}
              className="flex-1 text-md font-semibold bg-white text-[#5800F7] p-2 rounded border-[#5800F7] border hover:bg-gray-100"
              isDisabled={isLoading || isAppointmentCompleted || isAppointmentNoShow}
            />
          </div>
          <StepperButton
            text={translations.es.FinishMeeting}
            variant={'solid'}
            onClick={() => {
              handleMeetingStatusChange(AppointmentStatus.completed);
            }}
            className={'text-white bg-primaryPurple py-2 my-2 rounded-md  hover:!bg-primaryPurple'}
            isDisabled={isLoading || isAppointmentCompleted || isAppointmentNoShow}
          />
        </div>
      )}
    </>
  );
};

const AppointmentReAssign = (props: any) => {
  const { handleSetStep, children } = props;

  return (
    <>
      <div className="pt-4 flex flex-row items-center gap-1">
        <IoIosArrowBack size={25} onClick={() => handleSetStep(Steps.Appointment)} />
        <DialogTitle className="text-2xl">{translations.es.SelectHomeVisitor}</DialogTitle>
      </div>
      <div className="px-2">
        <h4>{translations.es.AppointmentCalendar}</h4>
        {children}
      </div>
    </>
  );
};
