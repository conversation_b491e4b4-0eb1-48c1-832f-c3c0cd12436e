'use client';
import Link from 'next/link';
import Card from '@/components/stockCard/Card';
// import { useState /* , useEffect, useReducer */ } from 'react';
import FilterBar from './FilterBar/FilterBar';
import { useVehiclesContext } from './vehiclesContext';
// import { GrClose } from 'react-icons/gr';
import { AiOutlineClose } from 'react-icons/ai';
import { CONTRACT_REGIONS, statusSelected, stepsSelect } from '@/constants';

function returnValueFilter(filterArray: any[]) {
  return filterArray.map((item) => {
    if (item.filterName === 'region') {
      return CONTRACT_REGIONS.find((r) => r.value === item.filterValue);
    }
    if (item.filterName === 'step') {
      return stepsSelect.find((r) => r.value === item.filterValue);
    }
    if (item.filterName === 'new') {
      return JSON.parse(item.filterValue || 'null') === true
        ? { label: 'Nuevo', value: '3' }
        : { label: 'Seminuevo', value: '3' };
    }
    if (item.filterName === 'status') {
      return statusSelected.find((r) => r.value === item.filterValue);
    }
    // if(item.filterName === 'R')
    return undefined;
  });
}

export default function FlotillaCards({ route, page }: { route: string; page: string }) {
  const { filterSelectedState, removeFilterByName, state } = useVehiclesContext();
  const results = returnValueFilter(filterSelectedState);
  return (
    <div className="flex flex-col h-full ">
      <FilterBar page={page} />
      {filterSelectedState.length > 0 && (
        <div className="flex items-center gap-3 mt-[10px] h-[max-content] ">
          <p className="font-semibold ">Filtrado por</p>
          {filterSelectedState.map((filter, i) => {
            return (
              <button
                key={i}
                className="h-[30px] py-2 px-3 bg-[#5800F7] rounded-[15px] text-white flex gap-3 items-center "
                onClick={() => {
                  removeFilterByName(filter.filterName);
                }}
              >
                <p>{results[i]?.label}</p>
                <AiOutlineClose color="#FFFFFF" />
              </button>
            );
          })}
          <p> Total de resultados: {state.filteredData.length} </p>
        </div>
      )}

      {state.filteredData.length >= 0 && filterSelectedState.length < 1 && (
        <p> Resultados: {state.filteredData.length} </p>
      )}
      <div className="flex flex-row flex-wrap h-full mt-8 gap-x-5 gap-y-5 ">
        {state.filteredData.length > 0 ? (
          state.filteredData.map((car, key) => {
            return (
              <Link href={`/dashboard/flotilla/${route}/${car._id}`} key={car._id} prefetch={false}>
                <Card
                  // color={car.color}
                  // brand={car.brand}
                  // model={car.model}
                  // status={car.status}
                  // newCar={car.newCar}
                  // step={car.step}
                  {...car}
                  contract={car.carNumber}
                  extensionCarNumber={car.extensionCarNumber}
                  key={key}
                  dischargedReason={car.dischargedData?.reason}
                  isBlocked={car.isBlocked}
                  // vehiclePhoto={car.vehiclePhotoUrl.url}
                />
              </Link>
            );
          })
        ) : (
          <div className="flex flex-col items-center justify-center gap-5 w-full h-[100%] min-h-[calc(100vh-400px)] ">
            <p className="text-3xl font-semibold text-[#464E5F]">¡Sin resultados!</p>
            <p className="text-md font-semibold text-[#9CA3AF] ">
              {filterSelectedState.length < 1 && 'No hay resultados'}
              {results.length > 0 && (
                <>
                  No hay resultados para los filtros:{' '}
                  {results.map((st, i) => (
                    <span key={i}>
                      {st?.label}
                      {results.length - 1 !== i && ', '}
                    </span>
                  ))}
                </>
              )}
            </p>
            {/* <img src="/images/empty.svg" alt="empty" className="w-[300px]" /> */}
          </div>
        )}
      </div>
    </div>
  );
}
