'use client';

import { ColumnDef } from '@tanstack/react-table';

// eslint-disable-next-line import/no-extraneous-dependencies
import { CirclePlus, Download } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Invoice } from '../../types';
import { Copy, Mail, Link, FileText } from 'lucide-react';
import { PAGOS_PAYMENT_URL, PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { toast } from 'sonner';
import axios from 'axios';

export const invoiceColumns: ColumnDef<Invoice>[] = [
  {
    accessorKey: 'id',
    header: 'Invoice ID',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-start">
          <div className="font-medium">{row?.original?.id}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'customer.legal_name',
    header: 'EMITIDA A',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row?.original?.customer?.legal_name}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total',
    header: 'TOTAL',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row?.original?.total}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'ESTADO',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.status}</div>;
    },
  },
  {
    accessorKey: 'folio_number',
    header: 'FOLIO',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.folio_number}</div>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'FECHA',
    cell: ({ row }) => {
      const date = new Date(row?.original?.created_at);
      const formatted = date.toLocaleDateString();
      return <div className="font-medium">{formatted}</div>;
    },
  },
  {
    accessorKey: 'download',
    header: 'FUENTE',
    cell: ({ row }) => {
      const downloadFileHandler = () => {
        const downloadUrl = `${PAYMENTS_API_URL}/payments/invoice/download/${row?.original?.id}`;
        axios({
          url: downloadUrl, //your url
          method: 'GET',
          responseType: 'blob', // important

          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
            'Cache-control': 'no-cache',
            Pragma: 'no-cache',
            Expires: '0',
          },
        }).then((response) => {
          // create file link in browser's memory
          const href = URL.createObjectURL(response.data);

          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', `${row?.original?.id}.pdf`); //or any other extension
          document.body.appendChild(link);
          link.click();

          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        });
      };
      return (
        <div className="font-medium">
          <Download className="hover:cursor-pointer" onClick={downloadFileHandler} />
        </div>
      );
    },
  },
  {
    accessorKey: 'payment_method',
    header: 'FORMA DE PAGO',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.payment_method}</div>;
    },
  },
  {
    accessorKey: 'actions',
    header: 'ACCIONES',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <CirclePlus />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <FileText className="h-4 w-4 pr-1" />
              Emitir factura
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link className="h-4 w-4 pr-1" />
              Asociar Recurso
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                navigator.clipboard.writeText(`${PAGOS_PAYMENT_URL}` + row.original.id);
                toast('URL de pago copiada al portapapeles');
              }}
            >
              <Copy className="h-4 w-4 pr-1" />
              Copiar al portapapeles
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Mail className="h-4 w-4 pr-1" />
              Enviar por correo
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
