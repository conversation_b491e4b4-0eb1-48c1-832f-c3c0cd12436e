import { Payment } from '@/app/dashboard/pagos/types';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { PaginationState } from '@tanstack/react-table';
import axios from 'axios';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';

type PaymentsResponse = {
  allRecords: number;
  payments: Payment[];
  total: number;
  totalQuery: number;
};

export async function getPayments(
  //   pageIndex: number = 0,
  //   pageSize: number = 10
  // ): Promise<{ allRecords: number; payments: Payment[] } | null> {

  pagination: PaginationState,
  searchParams: Record<string, string>
  // tableFilters?: ColumnFiltersState,
  // sorting?: SortingState
) {
  try {
    // console.log('pagination', pagination);
    // console.log('tableFilters', tableFilters);
    // console.log('sorting', sorting);

    const url = new URL(`${PAYMENTS_API_URL}/payments`);
    for (const [key, value] of Object.entries(searchParams)) {
      url.searchParams.append(key, value);
    }

    const paymentsPageCookies = getCookie('payments-page-filters', { cookies });

    const filters = paymentsPageCookies ? JSON.parse(paymentsPageCookies) : {};
    // console.log('filters', filters, '-------------------------------');
    url.searchParams.append('limit', pagination.pageSize.toString());

    if (pagination.pageIndex > 0) url.searchParams.append('page', (pagination.pageIndex + 1).toString());
    // if (region && region !== 'none') url.searchParams.append('region', region);
    if (filters.clientId) url.searchParams.append('clientId', filters.clientId);

    if (filters.region && filters.region !== 'none') url.searchParams.append('region', filters.region);

    console.log('url string', url.toString(), '------------------------------------------------');

    const { data } = await axios.get(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    return {
      allRecords: data.allRecords,
      payments: data.data,
      total: data.total,
      totalQuery: data.totalQuery,
    } as PaymentsResponse;
  } catch (error) {
    return null;
  }
}
