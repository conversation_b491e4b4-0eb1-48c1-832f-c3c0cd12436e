'use client';

import React, { useEffect, useState } from 'react';
import { Invoice } from '../../../app/dashboard/pagos/types';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { DataTable } from '@/components/DataTable';
import { invoiceColumns } from '../../../app/dashboard/pagos/clientDetails/(clientDetails)/invoiceColumns';
import { Skeleton } from '@/components/ui/skeleton';

async function getInvoice(id: string): Promise<Invoice> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/payments/invoices/${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}

type Props = {
  id?: string;
};

export default function InvoiceTable({ id }: Props) {
  const [invoiceData, setInvoiceData] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    try {
      if (id) {
        setLoading(true);
        getInvoice(id as string).then((res) => {
          const invoiceArr: Invoice[] = [res];
          setInvoiceData(invoiceArr);
          setLoading(false);
        });
      }
    } catch (error) {
      console.log(error);
    }
  }, [id]);

  if (loading) {
    return <Skeleton className="w-full h-28"></Skeleton>;
  } else {
    return (
      <div className="flex flex-col">
        <h2 className="text-3xl">Detalles de la factura</h2>
        <DataTable columns={invoiceColumns} data={invoiceData} showPagination={false}></DataTable>
      </div>
    );
  }
}
