import { URL_API } from '@/constants';
import { cache } from 'react';
import getCurrentUser from './getCurrentUser';

export interface StockCarProps {
  carNumber: string;
  brand: string;
  model: string;
  status: string;
  newCar: string;
}

const getCars = cache(async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const res = await fetch(`${URL_API}/stock/get`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return await res.json();
  } catch (error) {
    return console.error(error);
  }
});

export default getCars;
