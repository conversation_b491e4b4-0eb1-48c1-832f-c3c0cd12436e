'use client';

import React from 'react';
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from '@/components/ui/sheet';
import InvoiceTable from './InvoiceTable';
import ReceiptTable from './ReceiptTable';
import { Badge } from '@/components/ui/badge';

type Props = {
  invoiceId?: string;
  receiptId?: string;
};

const InvoiceReceiptSheet = ({ invoiceId, receiptId }: Props) => {
  return (
    <div>
      <Sheet>
        <SheetTrigger asChild>
          <div>
            {invoiceId ? <Badge className="bg-orange-500">Factura</Badge> : <></>}
            <br />
            {receiptId ? <Badge className="bg-gray-500">Recibo</Badge> : <></>}
          </div>
        </SheetTrigger>
        <SheetContent side={'bottom'}>
          <SheetHeader>
            {invoiceId ? <InvoiceTable id={invoiceId}></InvoiceTable> : <></>}
            <br />
            {receiptId ? <ReceiptTable id={receiptId}></ReceiptTable> : <></>}
          </SheetHeader>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default InvoiceReceiptSheet;
