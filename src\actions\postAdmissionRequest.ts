'use server';
import axios from 'axios';
import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';
import { ApiPath } from '@/constants/api.endpoints';

export interface Metric {
  acceptanceRate: number;
  cancellationRate: number;
  rating: number;
  lifetimeTrips: number;
  timeSinceFirstTrip: number;
}

interface IUpdateAdmissionRequestAccountInfoProps {
  requestId: string;
  requestPayload: Metric;
}

export async function updateAdmissionRequestAccountInfo({
  requestId,
  requestPayload,
}: IUpdateAdmissionRequestAccountInfoProps) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.patch(
      `${URL_API}/admission/requests/${requestId}/update-platform-metric`,
      requestPayload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    throw new Error('Error occured while updating platform into.');
  }
}

export async function updateAdmissionRequestHomeVisit({ requestId, requestPayload }: any) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.patch(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}/${ApiPath.HOME_VISIT}`,
      requestPayload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    throw new Error('Error occured while updating platform into.');
  }
}

export async function sendLocationGatheringMessage({ requestId }: { requestId: string }) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.post(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}/${ApiPath.LOCATION_GATHERING_MESSAGE}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    return null;
  }
}

export async function resetHomeVisitSchedulingLinkSendDate({ requestId }: { requestId: string }) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.patch(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}${ApiPath.RESET_HOME_VISIT_SCHEDULE_LINK_SEND_DATE}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    throw new Error('Error occured while updating home visit appointment scheduling Link send date');
  }
}

export async function updateAdmissionRequestPersonalData({
  requestId,
  payload,
}: {
  requestId: string;
  payload: Record<string, any>;
}) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.patch(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}${ApiPath.PERSONAL_DATA}`,
      payload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    throw new Error('Error occured while updating personal data');
  }
}

export async function sendHomeImageUploadMessage({ requestId }: { requestId: string }) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.post(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}${ApiPath.HOME_IMAGE_UPLOAD_MESSAGE}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    return null;
  }
}
