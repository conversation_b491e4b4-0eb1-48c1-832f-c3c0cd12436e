'use client';

import CustomInput from '../Inputs/CustomInput';
import SelectInput from '../Inputs/SelectInput';
import InputFile from '../Inputs/InputFile';
import {
  countries,
  CountriesOptions,
  US_CITIES_OPTIONS,
  US_COUNTRY_CODE,
  US_CURRENCY,
  US_STATES_OPTIONS,
  USCITIESNAMES,
} from '@/constants';
import { Checkbox as ChakraCheckbox } from '@chakra-ui/react';
import { cfdiUseOptions, taxSystemOptions } from '@/constants/sat';
import InputPhone from '../Inputs/InputPhone';
import { ErrorMessage, useFormikContext } from 'formik';
import { forwardRef, useImperativeHandle } from 'react';
// import SignatureCanvas from 'react-signature-canvas';
import InputPrice from '../Inputs/InputPrice';
import { CalenderComp } from '../CustomCalender';
import { format } from 'date-fns';

export const AssoicateDataUS = () => {
  const form = useFormikContext<any>();

  return (
    <>
      <p className="font-bold">Customer Data</p>
      <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
        <CustomInput label="First Name" name="firstName" type="text" />
        <CustomInput label="Last Name" name="lastName" type="text" />
        <InputPhone label="Cellular phone" name="phone" placeholder="" countryCode={US_COUNTRY_CODE} />
        <CustomInput label="Email" name="email" type="text" />

        <span className="ml-2">
          <label className="block text-gray-700 text-[16px] mb-2">{'Birthdate'}</label>
          <CalenderComp
            form={form}
            fieldName="birthDay"
            onChange={(date) => {
              const formattedDate = format(new Date(date as Date), 'yyyy-MM-dd');
              form.setFieldTouched('birthDay', true);
              form.setFieldValue('birthDay', formattedDate);
            }}
          />
          {!!(form.touched?.birthDay && form.errors?.birthDay) && (
            <ErrorMessage name="birthDay" component="div" className="text-red-500 text-sm mt-1" />
          )}
        </span>

        <CustomInput label="SSN" name="ssn" type="text" />
      </div>
    </>
  );
};

export const AssociateAddressUS = (props: any) => {
  const { selectedState, setSelectedState } = props;

  return (
    <>
      <p className="font-bold">Driver direction</p>
      <div className="flex flex-col gap-2">
        <CustomInput label="Street address" name="addressStreet" type="text" />
        <CustomInput label="Street address line 2" name="interior" type="text" />
        <div className="grid w-full grid-cols-2 gap-2">
          <SelectInput
            label="State"
            options={US_STATES_OPTIONS}
            name="state"
            onChange={(option) => setSelectedState(option)}
          />
          <SelectInput
            label="City"
            options={selectedState ? [US_CITIES_OPTIONS[USCITIESNAMES.Dallas]] : [{ label: '', value: '' }]}
            name="city"
          />
          <CustomInput label="Zip code" name="postalCode" type="text" />
        </div>
      </div>
    </>
  );
};

export const TaxDataUS = (props: any) => {
  const { firstName, lastName } = props;

  return (
    <>
      <p className="font-bold">Tax data (Optional) </p>
      <div className="grid grid-cols-1 gap-4">
        <CustomInput
          label="Legal Name"
          name="legal_name"
          type="text"
          defaultValue={(firstName + ' ' + lastName).toUpperCase()}
          disabled
        />
        <SelectInput label="Tax Regime" name="tax_system" options={taxSystemOptions} />
        <SelectInput label="Use of CFDI" name="use_cfdi" options={cfdiUseOptions} />
      </div>
    </>
  );
};

export const AvalUS = (props: any) => {
  const { nameFiles, handleSetNames } = props;

  return (
    <>
      <p className="font-bold">Aval</p>
      <div className="grid grid-cols-2 gap-4">
        <CustomInput label="Name" name="avalName" type="text" />
        <CustomInput label="Phone" name="avalPhone" type="text" />
        <CustomInput label="Email" name="avalEmail" type="text" />
        <CustomInput label="Address" name="avalAddress" type="text" />

        <InputFile
          name="avalINE"
          label="INE"
          accept="all-images"
          nameFile={nameFiles.avalINE}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
      </div>
    </>
  );
};

// export const ContactsUS = (props: any) => {
//   const { initialValues } = props;
//   return (
//     <>
//       <p className="font-bold">Contacts</p>
//       <div className="grid gap-4 transition-all transform duration-300">
//         <InputArray
//           name="contacts"
//           label="Contactos"
//           fields={[
//             { name: "contactName", type: "text", label: "Name" },
//             { name: "contactKinship", type: "text", label: "Relationship" },
//             { name: "contactPhone", type: "text", label: "Cellular" },
//             { name: "contactAddress", type: "textarea", label: "Address" },
//           ]}
//           initialItem={initialValues.contacts[0]}
//         />
//       </div>
//     </>
//   );
// };

export const ContactsUS = () => {
  return (
    <>
      <p className="font-bold">{'Emergency Contact'}</p>
      <div className="grid gap-4 transition-all transform duration-300">
        <CustomInput label="Name" name="emergencyContactName" type="text" />
        <InputPhone
          label="Cellular phone"
          name="emergencyContactPhone"
          placeholder=""
          countryCode={US_COUNTRY_CODE}
        />
        <CustomInput label="Relationship" name="emergencyContactRelation" type="text" />
      </div>
    </>
  );
};

interface CountrySelectorProps {
  defaultCountry?: string;
}

export const CountrySelector = (props: CountrySelectorProps) => {
  const { defaultCountry } = props;
  const options = defaultCountry
    ? [CountriesOptions[defaultCountry as keyof typeof CountriesOptions]]
    : countries;
  return (
    <div>
      <SelectInput
        name="country"
        label="País"
        options={options}
        onChange={(option, form) => {
          form.resetForm();
          form.setFieldValue('country', option);
        }}
        dataCy="country-selector"
      />
    </div>
  );
};

export const FilesUS = (props: any) => {
  const { nameFiles, handleSetNames } = props;

  return (
    <>
      <p className="font-bold">Documents</p>

      <div className="grid grid-cols-3 overflow-hidden gap-y-4 gap-x-3 ">
        <InputFile
          name="picture"
          label="Driver photo"
          accept="all-images"
          nameFile={nameFiles.picture}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="addressVerification"
          label="Proof Of Address"
          nameFile={nameFiles.addressVerification}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />

        <InputFile
          name="driverLicenseFront"
          label="Driver License Front"
          accept="all-images"
          nameFile={nameFiles.driverLicenseFront}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />

        <InputFile
          name="driverLicenseBack"
          label="Driver License Back"
          accept="all-images"
          nameFile={nameFiles.driverLicenseBack}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />

        <InputFile
          name="garage"
          label="Garage Photo"
          accept="all-images"
          nameFile={nameFiles.garage}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />

        <InputFile
          name="bankStatementsOne"
          label="Account Statement 1"
          nameFile={nameFiles.bankStatementsOne}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="bankStatementsTwo"
          label="Account Statement 2"
          nameFile={nameFiles.bankStatementsTwo}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="bankStatementsThree"
          label="Account Statement 3"
          nameFile={nameFiles.bankStatementsThree}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="bankStatementsFour"
          label="Account Statement 4"
          nameFile={nameFiles.bankStatementsFour}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="bankStatementsFive"
          label="Account Statement 5"
          nameFile={nameFiles.bankStatementsFive}
          accept="pdf"
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />
        <InputFile
          name="bankStatementsSix"
          label="Account Statement 6"
          accept="pdf"
          nameFile={nameFiles.bankStatementsSix}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        />

        {/* <InputFile
          name="proofOfCompletionOfAnyRequiredSafetyCourses"
          label="Proof of Completion of Safety Courses"
          accept="all-images"
          nameFile={nameFiles.proofOfCompletionOfAnyRequiredSafetyCourses}
          handleSetName={handleSetNames}
          buttonText="Upload file"
          placeholder="Images no larger than 2 MB"
          placeHolderDown
        /> */}
      </div>
    </>
  );
};

const ConsentCheckBox = (props: any) => {
  const { colorScheme = 'blue', name, onChange, text, belowText, defaultCheck } = props;

  return (
    <div>
      <ChakraCheckbox name={name} colorScheme={colorScheme} onChange={onChange} defaultChecked={defaultCheck}>
        {text}
      </ChakraCheckbox>
      <p className="px-4 text-gray-600 ">{belowText}</p>
    </div>
  );
};

const Consent = (props: any, signRef: any) => {
  // const localSignRef = useRef<any>();

  useImperativeHandle(signRef, () => {
    return {
      getSignatureImage: async () => {
        return new File([new Blob()], 'signature.png', { type: 'image/png' });
      },
    };
  });

  // const [data, setData] = useState<any>();
  const formik = useFormikContext();

  // const getSignatureImage = async () => {
  //   const base64Data = data.getTrimmedCanvas().toDataURL('image/png');
  //   const base64 = await fetch(base64Data);
  //   const blob = await base64.blob();
  //   const file = new File([blob], 'signature.png', { type: 'image/png' });
  //   return file;
  // };

  // const clearSign = async () => {
  //   data.clear();
  // };

  return (
    <>
      <p className="font-bold">Consents</p>
      <div className="grid grid-cols-1 gap-4">
        {/* <div>
          <p className="text-xl">Signature</p>
          <div className="border">
            <SignatureCanvas
              penColor="blue"
              canvasProps={{ width: 500, height: 150, className: 'sigCanvas' }}
              ref={(ref) => {
                setData(ref);
              }}
            />
          </div>

          <div className="py-2">
            <button
              onClick={clearSign}
              type="button"
              className="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800"
            >
              Clear
            </button>
          </div>
        </div> */}

        <ConsentCheckBox
          name="termsAndConditions"
          text={'Accept terms and conditions'}
          belowText={'You agree to our Terms of Service and Privacy Policy.'}
          onChange={(e: any) => {
            formik.setFieldValue('termsAndConditions', e.target.checked);
          }}
        />

        <ConsentCheckBox
          name="dataPrivacyConsentForm"
          text={'Data Privacy Consent Form'}
          onChange={(e: any) => {
            formik.setFieldValue('dataPrivacyConsentForm', e.target.checked);
          }}
        />
      </div>
    </>
  );
};
export const Consents = forwardRef(Consent);

interface IDriverRecord {
  nameFiles: Record<string, any>;
  handleSetNames: (name: string, value: string, size: number) => void;
}

export const DriverRecord = (props: IDriverRecord) => {
  const {} = props;

  return (
    <>
      <p className="font-bold">Driving Record</p>
      <div className="grid grid-cols-1 gap-4">
        <CustomInput label="Ride Share Total Rides" name="rideShareTotalRides" type="text" />

        <InputPrice
          label="Avg earning per week"
          name="avgEarningPerWeek"
          placeholder="0.00"
          currencySymbol={US_CURRENCY}
        />

        <SelectInput
          label="Mobility Platforms"
          name="mobilityPlatforms"
          options={[
            {
              label: 'Uber',
              value: 'Uber',
            },
          ]}
          isMulti={true}
        />

        {/* <div className="grid grid-cols-2 gap-y-4 gap-x-3 p-2">
          <InputFile
            name="drivingRecord"
            label="Driving Record"
            accept="pdf"
            nameFile={nameFiles.drivingRecord}
            handleSetName={handleSetNames}
            buttonText="Upload file"
            placeholder="Images no larger than 2 MB"
            placeHolderDown
          />

          <InputFile
            name="rideShareDates"
            label="Ride Share Dates"
            accept="all-images"
            nameFile={nameFiles.rideShareDates}
            handleSetName={handleSetNames}
            buttonText="Upload file"
            placeholder="Images no larger than 2 MB"
            placeHolderDown
          />

          <InputFile
            name="rideShareRideHistory"
            label="Ride Share Ride History"
            accept="all-images"
            nameFile={nameFiles.rideShareRideHistory}
            handleSetName={handleSetNames}
            buttonText="Upload file"
            placeholder="Images no larger than 2 MB"
            placeHolderDown
          />

          <InputFile
            name="avgWeeklyIncomeOfLastTwelveWeeks"
            label="Weekly income of last 12 weeks"
            accept="all-images"
            nameFile={nameFiles.avgWeeklyIncomeOfLastTwelveWeeks}
            handleSetName={handleSetNames}
            buttonText="Upload file"
            placeholder="Images no larger than 2 MB"
            placeHolderDown
            multiple={true}
          />
        </div> */}
      </div>
    </>
  );
};
