import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  Modal<PERSON>eader,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ooter,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Checkbox,
  Input,
  InputGroup,
  InputLeftElement,
  VStack,
  HStack,
  Text,
  Box,
} from '@chakra-ui/react';
import { AiOutlineSearch } from 'react-icons/ai';
import { FiEye, FiPrinter } from 'react-icons/fi';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';

interface VehicleSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  vehicles: VehicleWithQR[];
  loading: boolean;
  searchQuery: string;
  setSearchQuery: (q: string) => void;
  filteredVehicles: VehicleWithQR[];
  selectedVehicles: Set<string>;
  setSelectedVehicles: (s: Set<string>) => void;
  isAllSelected: boolean;
  isIndeterminate: boolean;
  handleSelectAll: (checked: boolean) => void;
  handleSelectVehicle: (vehicleId: string, checked: boolean) => void;
  handlePreview: () => void;
  handlePrint: () => void;
  generatingPDF: boolean;
  VehicleTableRow: React.ComponentType<any>;
}

const VehicleSelectionModal: React.FC<VehicleSelectionModalProps> = ({
  isOpen,
  onClose,
  loading,
  searchQuery,
  setSearchQuery,
  filteredVehicles,
  selectedVehicles,
  isAllSelected,
  isIndeterminate,
  handleSelectAll,
  handleSelectVehicle,
  handlePreview,
  handlePrint,
  generatingPDF,
  VehicleTableRow,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="6xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader bg="#5800F7" color="white" borderTopRadius="md">
          Seleccionar vehículos para imprimir QR
        </ModalHeader>
        <ModalCloseButton color="white" onClick={onClose} />
        <ModalBody p={6}>
          {/* Search Bar */}
          <VStack spacing={6} align="stretch">
            <InputGroup size="lg">
              <InputLeftElement pointerEvents="none">
                <AiOutlineSearch color="#9CA3AF" size={20} />
              </InputLeftElement>
              <Input
                placeholder="Buscar por nombre del vehículo, VIN o número..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                bg="white"
                border="2px"
                borderColor="#E2E8F0"
                _focus={{
                  borderColor: '#5800F7',
                  boxShadow: '0 0 0 1px #5800F7',
                }}
              />
            </InputGroup>

            {/* Selection Summary */}
            <Box bg="#F8F9FA" p={4} borderRadius="md" border="1px" borderColor="#E2E8F0">
              <HStack justify="space-between">
                <Text fontSize="sm" color="gray.600">
                  {filteredVehicles.length} vehículos encontrados
                </Text>
                <Text fontSize="sm" fontWeight="bold" color="#5800F7">
                  {selectedVehicles.size} seleccionados
                </Text>
              </HStack>
            </Box>

            {/* Vehicle Table */}
            <Box borderWidth={1} borderRadius="md" borderColor="#E2E8F0" overflow="hidden">
              <Table size="sm" variant="simple">
                <Thead bg="#FAFAFA">
                  <Tr>
                    <Th width="50px">
                      <Checkbox
                        isChecked={isAllSelected}
                        isIndeterminate={isIndeterminate}
                        onChange={(e) => handleSelectAll(e.target.checked)}
                        colorScheme="purple"
                      />
                    </Th>
                    <Th color="gray.700" fontWeight="bold">
                      Vehículo
                    </Th>
                    <Th color="gray.700" fontWeight="bold">
                      VIN
                    </Th>
                    <Th color="gray.700" fontWeight="bold">
                      Número
                    </Th>
                    <Th color="gray.700" fontWeight="bold">
                      Año
                    </Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {loading ? (
                    <Tr>
                      <Td colSpan={5} textAlign="center" py={8}>
                        <Text color="gray.500">Cargando vehículos...</Text>
                      </Td>
                    </Tr>
                  ) : filteredVehicles.length === 0 ? (
                    <Tr>
                      <Td colSpan={5} textAlign="center" py={8}>
                        <Text color="gray.500">No se encontraron vehículos</Text>
                      </Td>
                    </Tr>
                  ) : (
                    filteredVehicles.map((vehicle) => (
                      <VehicleTableRow
                        key={vehicle._id}
                        vehicle={vehicle}
                        isSelected={selectedVehicles.has(vehicle._id)}
                        onSelect={(checked: boolean) => handleSelectVehicle(vehicle._id, checked)}
                      />
                    ))
                  )}
                </Tbody>
              </Table>
            </Box>
          </VStack>
        </ModalBody>
        <ModalFooter bg="gray.50" borderBottomRadius="md">
          <HStack spacing={3}>
            <button
              onClick={onClose}
              className="border-2 border-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-100 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handlePreview}
              disabled={selectedVehicles.size === 0}
              className={`
                border-2 border-[#5800F7] text-[#5800F7] px-4 py-2 rounded transition-colors flex items-center gap-2
                ${
                  selectedVehicles.size === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:bg-[#5800F7] hover:text-white cursor-pointer'
                }
              `}
            >
              <FiEye size={16} />
              Previsualizar ({selectedVehicles.size})
            </button>
            <button
              onClick={handlePrint}
              disabled={selectedVehicles.size === 0 || generatingPDF}
              className={`
                px-4 py-2 rounded transition-colors flex items-center gap-2 text-white
                ${
                  selectedVehicles.size === 0 || generatingPDF
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-[#5800F7] hover:bg-purple-800 cursor-pointer'
                }
              `}
            >
              <FiPrinter size={16} />
              {generatingPDF ? 'Generando...' : `Imprimir ${selectedVehicles.size} QR`}
            </button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default VehicleSelectionModal;
