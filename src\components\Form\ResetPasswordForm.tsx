import { Form } from 'formik';
import InputEmail from '../Inputs/InputEmail';

interface ResetPasswordFormProps {
  valid?: boolean;
  dirty?: boolean;
}

export default function ResetPasswordForm({ valid, dirty }: ResetPasswordFormProps) {
  const validate = dirty && valid;

  return (
    <Form className="grid w-full gap-3">
      <p className="flex justify-center text-2xl text-black">Recuperar contraseña</p>
      <InputEmail name="email" placeholder="Ingresa tu correo" label="Correo electronico" />
      <button
        type="submit"
        disabled={!validate}
        className={`
          ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
          text-white rounded-md  min-h-[40px] py-2 cursor-pointer`}
      >
        Enviar enlace de recuperación al email
      </button>
      <a className="text-[14px] text-[#5800F7] font-bold flex justify-center mt-6" href="/">
        Atrás para iniciar sesión
      </a>
    </Form>
  );
}
