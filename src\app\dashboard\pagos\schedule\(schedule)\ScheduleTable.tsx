/* eslint-disable prettier/prettier */
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

import PaymentsTableRow from './PaymentsTableRow';
import { PaymentSchedule } from '../../types';
import { Check, X } from 'lucide-react';

export default function ScheduleTable({ paymentSchedule }: { paymentSchedule: PaymentSchedule[] }) {
  console.log('ScheduleTable->paymentSchedule', paymentSchedule);
  return (
    <div className="w-full sm:p-4">
      <div className="rounded-md sm:border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-medium">Term No</TableHead>
              <TableHead className="font-medium">Stream No</TableHead>
              <TableHead className="font-medium">Contract No</TableHead>
              <TableHead className="font-medium">Associate ID</TableHead>
              <TableHead className="font-medium">Due Date</TableHead>
              <TableHead className="font-medium">Late Fee Applied</TableHead>
              <TableHead className="font-medium">Late Fee Paid</TableHead>
              <TableHead className="font-medium">Amount</TableHead>
              <TableHead className="font-medium">Fee</TableHead>
              <TableHead className="font-medium">Payment Status</TableHead>
              <TableHead className="font-medium">Total Contract</TableHead>
              <TableHead className="font-medium">Total Outstanding</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paymentSchedule
              ? paymentSchedule.map((schedule) => (
                  <Collapsible key={schedule.termNo} asChild>
                    <>
                      <TableRow>
                        <CollapsibleTrigger asChild>
                          <TableCell>{schedule.termNo}</TableCell>
                        </CollapsibleTrigger>
                        <TableCell>{schedule.streamNo}</TableCell>
                        <TableCell>{schedule.contractNumber}</TableCell>
                        <TableCell>{schedule.associateId}</TableCell>
                        <TableCell>{new Date(schedule.dueDate).toLocaleDateString()}</TableCell>
                        <TableCell>{schedule.lateFeeApplied ? <Check /> : <X />}</TableCell>
                        <TableCell>{schedule.lateFeePaid ? <Check /> : <X />}</TableCell>
                        <TableCell>{schedule.amount}</TableCell>
                      <TableCell>{schedule.fee}</TableCell>
                        <TableCell>{schedule.paymentStatus}</TableCell>
                        <TableCell>{schedule.totalContactValue}</TableCell>
                        <TableCell>{schedule.amountOutstanding}</TableCell>
                      </TableRow>
                      <CollapsibleContent asChild>
                        <PaymentsTableRow payments={schedule.payments} />
                      </CollapsibleContent>
                    </>
                  </Collapsible>
                ))
              : null}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
