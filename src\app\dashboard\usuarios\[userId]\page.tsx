import getCurrentUser from '@/actions/getCurrentUser';
import getUserById from '@/actions/getUserById';
import getUserRestrictions from '@/actions/getUserRestrictions';
import Image from 'next/image';
import { redirect } from 'next/navigation';
import UserVehicleRestrictions from './restrictions';

interface UserDetailPageProps {
  params: {
    userId: string;
  };
}

export const metadata = {
  title: 'Detalles de usuario',
};

export default async function UserDetailPage({ params: { userId } }: UserDetailPageProps) {
  const currentUser = await getCurrentUser();

  if (!currentUser || currentUser.role !== 'superadmin') return redirect('/dashboard');

  const user = await getUserById(userId);

  if (!user) return <h1>Usuario no encontrado</h1>;

  const restrictions = await getUserRestrictions(userId);

  return (
    <div className="flex flex-col gap-4">
      <h1 className="font-bold text-[32px] ">Detalles de usuario</h1>
      <div className="flex justify-between ">
        <div className="flex gap-3">
          {/* < /> */}
          <Image
            width={60}
            height={60}
            src={user.image?.url || '/images/avatar.jpg'}
            alt={user.image?.originalName || 'default user image'}
            className="w-[60px] h-[60px] rounded-full"
          />
          <div className="flex flex-col gap-2  ">
            <h2>{user.name}</h2>
            <h3>{user.email}</h3>
          </div>
        </div>
      </div>
      <UserVehicleRestrictions restrictions={restrictions} />
    </div>
  );
}
