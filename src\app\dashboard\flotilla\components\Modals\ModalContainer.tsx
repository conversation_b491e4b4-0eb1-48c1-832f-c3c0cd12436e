'use client';
import { ReactNode, useEffect } from 'react';
import { IoClose } from 'react-icons/io5';

export default function ModalContainer({
  children,
  title,
  onClose,
  className,
  classAnimation,
  removeScrollBar = true,
  width,
}: {
  children: ReactNode;
  title: string;
  onClose: () => void;
  className?: string;
  classAnimation?: string;
  removeScrollBar?: boolean;
  width?: string;
}) {
  useEffect(() => {
    if (removeScrollBar) {
      document.body.classList.add('overflow-hidden');
    }
    return () => {
      document.body.classList.remove('overflow-hidden');
    };
  }, [removeScrollBar]);

  return (
    <div
      id="modal-container"
      className={`
        w-full h-[100%]
        fixed top-0 left-0 z-[100]
        bg-black bg-opacity-50
        flex justify-center items-start
        overflow-auto
        transition-opacity duration-500 ease-in-out
      `}
    >
      <div
        className={`
          ${width ? width : 'w-[540px]'}
          rounded-[10px]
          mt-[70px]
          bg-white
          p-[20px]
          flex flex-col
          gap-[30px]
          relative
          ${className ? className : ''}
          animate__animated animate__faster
          ${classAnimation ? classAnimation : 'animate__fadeInDown'}
        `}
      >
        <header className="flex gap-3 justify-between items-center">
          <p className="text-[18px] font-[600] text-modalText">{title}</p>
          <IoClose className="cursor-pointer" size={24} onClick={onClose} />
        </header>
        {children}
      </div>
    </div>
  );
}
