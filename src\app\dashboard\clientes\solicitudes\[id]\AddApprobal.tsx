/* eslint-disable prettier/prettier */
'use client';
import { useEffect, useState } from 'react';
import {
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  Button,
  Grid,
  Select,
} from '@chakra-ui/react';
import { Formik, Form } from 'formik';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import * as Yup from 'yup';
import { ApprovalTypes, URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';

export const AddApproval = ({
  requestId,
}: // accounts,
{
  requestId: string;
  accounts: Array<{ accountId: string; platform: string; status: string }>;
}) => {
  const [isLoading, setIsLoading] = useState(true);

  const router = useRouter();
  const toast = useToast();
  const pathname = usePathname();
  const { user } = useCurrentUser();

  const [platform, setPlatform] = useState('');
  // const existingPlatforms = accounts.map((account) => account.platform.toLowerCase());

  function onClose() {
    router.back();
  }

  const searchParams = useSearchParams();
  const isOpen = searchParams.get('dialog') === 'type-of-approbation';

  const [suggestedPlatform, setSuggestedPlatform] = useState('');
  const [showReasonBox, setShowReasonBox] = useState(false);

  const defaultValues = {
    platform: platform,
    reason: '',
  };

  const validationSchema = Yup.object().shape({
    platform: Yup.string().required('La plataforma es requerida'),
    reason: Yup.string().when('platform', {
      is: (val: string) => suggestedPlatform && val.toLowerCase() !== suggestedPlatform.toLowerCase(),
      then: () => Yup.string().required('Razón es requerida cuando se selecciona un valor diferente'),
      otherwise: () => Yup.string(),
    }),
  });

  async function handleSubmit(values: any, actions: { setSubmitting: (arg0: boolean) => void }) {
    try {
      if (!user) return null;
      const payload = {
        requestId,
        preapproval: values.platform,
        ...(values.reason && { reason: values.reason }),
        suggestedPreapproval: suggestedPlatform,
      };

      const response = await fetch(`${URL_API}/admissionRequest/type`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify(payload),
      });
      if (response.ok) {
        toast({
          title: 'Tipo de preaprobación guardado',
          description: 'El tipo de preaprobación se ha guardado correctamente',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        setTimeout(() => {
          window.location.reload();
        }, 2500);
      } else {
        throw new Error('Error al guardar');
      }
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error',
        description: 'Hubo un problema al guardar',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      actions.setSubmitting(false);
      router.refresh();
      router.push(`${pathname}`);
      onClose();
    }
  }
  useEffect(() => {
    async function fetchPlatform() {
      try {
        if (!user) return;
        const response = await fetch(`${URL_API}/mlservice/pre-approval/${requestId}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        const data = await response.json();
        const platformValue = data.message || '';
        if (Object.values(ApprovalTypes).includes(platformValue as ApprovalTypes)) {
          setPlatform(platformValue);
          setSuggestedPlatform(platformValue);
        } else {
          setPlatform('');
          setSuggestedPlatform('');
        }
      } catch (error) {
        console.error('Error fetching platform:', error);
      } finally {
        setIsLoading(false);
      }
    }
    if (!platform && isOpen) {
      fetchPlatform();
    }
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        {isLoading ? (
          <ModalBody>Loading...</ModalBody>
        ) : (
          <>
            <ModalHeader>Tipo de preaprobación</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Formik
                initialValues={defaultValues}
                validationSchema={validationSchema}
                onSubmit={handleSubmit}
              >
                {({ handleChange, handleBlur, isValid, isSubmitting }) => (
                  <Form>
                    <Grid gap={4}>
                      <Select
                        name="platform"
                        value={platform}
                        onChange={(e) => {
                          setPlatform(e.target.value.toLowerCase());
                          if (suggestedPlatform) {
                            setShowReasonBox(
                              e.target.value.toLowerCase() !== suggestedPlatform.toLowerCase()
                            );
                          }
                          handleChange(e);
                        }}
                        onBlur={handleBlur}
                      >
                        <option value="">Selecciona una opción</option>
                        <option value={ApprovalTypes.PREAPPROVED}>Preaprobado</option>
                        <option value={ApprovalTypes.PRE_OWNED}>Preaprobado seminuevo</option>
                          {/* <option value={ApprovalTypes.MG3}>Preaprobado MG3</option> */}
                        <option value={ApprovalTypes.DOWN_PAYMENT}>Preaprobado con Enganche</option>
                        <option value={ApprovalTypes.DEPOSIT}>Preaprobado con Depósito</option>
                      </Select>
                      {showReasonBox && (
                        <textarea
                          name="reason"
                          placeholder="Razón para seleccionar un valor diferente"
                          className="w-full p-2 border rounded"
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      )}
                    </Grid>
                    <ModalFooter>
                      <Button type="submit" isLoading={isSubmitting} disabled={!isValid || isSubmitting}>
                        Guardar
                      </Button>
                    </ModalFooter>
                  </Form>
                )}
              </Formik>
            </ModalBody>
          </>
        )}
      </ModalContent>
    </Modal>
  );
};
