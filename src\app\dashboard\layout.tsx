import '../globals.css';
import { redirect } from 'next/navigation';
import SideNavbar from '@/components/Navbar/SideNavbar';
import CheckSession from '../checkSession';
import { getSession } from '@/actions/getCurrentUser';
import getUserById from '@/actions/getUserById';
import LogoutButton from '@/components/LogoutButton';
import CurrentUserProvider from './providers/CurrentUserProvider';
import { PermissionsProvider } from '@/casl/PermissionsContext';

// const inter = Inter({ subsets: ['latin'] });

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const session = await getSession();
  const user = session?.user;

  if (!user) return redirect('/');
  const currentUser = await getUserById();
  if (!currentUser)
    return (
      <CheckSession user={user}>
        <div className="w-[100vw] h-[100vh] flex  ">
          <div className="m-auto flex flex-col items-center gap-3">
            <p className="text-[24px] font-bold ">Tu sesión ha expirado, por favor inicia sesión de nuevo</p>
            <p>Si no se redirige automáticamente, haz click </p>
            <LogoutButton />
          </div>
        </div>
      </CheckSession>
    );

  return (
    <CheckSession user={user}>
      <CurrentUserProvider currentUser={currentUser}>
        <PermissionsProvider permissions={currentUser?.permissions || []}>
          <SideNavbar>
            <div className={'min-h-[calc(100vh-120px)] h-full '}>{children}</div>
          </SideNavbar>
        </PermissionsProvider>
      </CurrentUserProvider>
    </CheckSession>
  );
}
