import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { listStatus } from '../activos/[id]/lib';
import { cookies } from 'next/headers';
import { getCookie } from 'cookies-next';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { combineSearchParamsAndFilters } from '@/constants';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Flotilla  | Vendidos',
  description: 'Esto es la flotilla',
};

interface SoldPageProps {
  searchParams: Record<string, string>;
}

export default async function Sold({ searchParams }: SoldPageProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });

  const result = await getStockVehicles({
    limit: 50,
    // listStatus: ['stock', 'overhauling'],
    status: 'sold',
    searchParams: definitiveFilters,
    excludeStatus: listStatus,
  });

  if (!result) return null;

  return (
    <VehicleInfiniteList route="sold" page="Vendidos" data={result.stock} totalCount={result.totalCount} />
  );
}
