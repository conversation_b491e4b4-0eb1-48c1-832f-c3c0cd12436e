import React from 'react';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { User } from '../../types';
import BarActions from './Bar/BarActions';
import { taxSystemOptions } from '@/constants/sat';
import EditClient from './Bar/Parts/EditClient';

export default async function ClientDetailCard({ clientData }: { clientData: User }) {
  const regimenFound =
    taxSystemOptions.find((item) => item.value === clientData.tax_system)?.label || 'No encontrado';

  // console.log('clientData', clientData);
  return (
    <div>
      <section className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <h4 className=" text-3xl font-bold">{clientData?.name}</h4>
          <EditClient clientData={clientData} />
        </div>
        {/* <button>something here</button> */}

        <BarActions clientData={clientData} />
      </section>
      <section>
        <Card className="w-full] bg-gradient-to-r from-violet-200 to-violet-400">
          <CardHeader>
            <CardTitle>{`Información general de ${clientData?.name}`}</CardTitle>
            <p className="">
              Datos fiscales:{' '}
              {clientData.is_valid_tax_info ? (
                <span className="text-green-600 font-bold">✅ Correctos </span>
              ) : (
                <span className="text-red-600 font-bold"> ❌ Incorrectos</span>
              )}
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid w-full grid-cols-3 gap-4">
              <div className="flex flex-col space-y-2">
                <Label>
                  <span className="font-bold">Client ID: </span> {`${clientData?.id}`}
                </Label>

                <Label>
                  <span className="font-bold">Nombre Legal: </span>
                  {clientData?.legal_name}
                </Label>
                <Label>
                  <span className="font-bold">RFC / Identificador Tributario: </span>
                  {clientData?.rfc}
                </Label>
                <Label>
                  <span className="font-bold">Regimen Fiscal: </span>
                  {regimenFound}
                </Label>
              </div>
              <div className="flex flex-col space-y-2 pl-3">
                {/* <Label>{`Contract Number: ${clientData?.contractNumber}`}</Label>
                <Label>{`País: ${clientData?.country}`}</Label>
                <Label>{`Uso del CFDI:`}</Label> */}

                <Label>
                  <span className="font-bold">Contract Number: </span>
                  {clientData?.contractNumber}
                </Label>
                <Label>
                  <span className="font-bold">País: </span>
                  {clientData?.country}
                </Label>
                <Label>
                  <span className="font-bold">Uso del CFDI: </span>
                  {/* {clientData. */}
                </Label>
              </div>
              <div className="flex flex-col space-y-2 pl-3">
                {/* <Label>{`Código Postal: ${clientData?.zip}`}</Label>
                <Label>{`Creado: ${
                  clientData.createdAt ? new Date(clientData?.createdAt).toLocaleDateString() : ''
                }`}</Label> */}

                <Label>
                  <span className="font-bold">Código Postal: </span>
                  {clientData?.zip}
                </Label>
                <Label>
                  <span className="font-bold">Creado: </span>
                  {clientData.createdAt ? new Date(clientData?.createdAt).toLocaleDateString() : ''}
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    </div>
  );
}
