'use client';
import { WeeklyDate, WeeklyRecord } from '@/actions/getWeeklyRecords';
import FormikContainer from '@/components/Formik/FormikContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';
import { es } from 'date-fns/locale';
import { useCurrentUser } from '../../providers/CurrentUserProvider';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useRecordsModal } from '../stores/useRecordsModal';
import VehicleRecordsModal from './VehicleRecordsModal';
import RecordsBarChart from './Charts/RecordsBarChart';

interface WeeklyRecordsChartProps {
  data: WeeklyRecord;
  weeksAvailable: WeeklyDate[];
}

const reference: { [key: string]: string } = {
  activo: 'Activos',
  active: 'Activos',
  stock: 'En stock',
  'in-service': 'Taller',
  readmissions: 'Reingresos',
  discharged: 'Bajas',
  'legal-process': 'Legal',
  overhauling: 'Reparación',
  'awaiting-insurance': 'Seguro',
  bloqueo: 'Bloqueados',
};
function mapStatus(array: string[]) {
  // Objeto de referencia para mapear los valores

  // Mapeamos los valores del array según el objeto de referencia
  const newArray = array.map((status) => reference[status] || status);

  return newArray;
}

export default function WeeklyRecords({ data, weeksAvailable }: WeeklyRecordsChartProps) {
  const [dataState, setDataState] = useState(data);
  const [startDate, setStartDate] = useState<string | null>(null);
  const [filter, setFilter] = useState<string>('');

  const valuesIndicators = Object.values(dataState.statusStatistics);
  const labelsKeys = Object.keys(dataState.statusStatistics);

  const labels = mapStatus(labelsKeys);
  const { user } = useCurrentUser();

  const recordsModal = useRecordsModal();

  useEffect(() => {
    async function fetchWeeklyRecords() {
      const baseUrl = new URL(`${URL_API}/weekly-records`);
      if (startDate) {
        baseUrl.searchParams.append('startDate', startDate);
      }

      const url = baseUrl.toString();
      const res = await axios.get(url, {
        headers: {
          Authorization: 'Bearer ' + user.accessToken,
        },
      });
      setDataState(res.data.weeklyRecords);
    }
    if (startDate) {
      fetchWeeklyRecords();
    }
  }, [startDate, user]);

  const chartData = {
    // label: `Suma total: ${activesPerRegion.reduce((acc, curr) => acc + curr, 0)} `,
    label:
      'Registros semanales para ' +
      format(new Date(startDate || dataState.startDate), "dd 'de' MMMM 'de' yyyy", { locale: es }),
    labels,
    indicators: valuesIndicators,
    filters: labelsKeys,
  };

  return (
    <>
      {recordsModal.isOpen && filter && (
        <VehicleRecordsModal data={dataState.stockVehicles} filter={filter} filterName={reference[filter]} />
      )}
      <div
        className=" my-[50px] bg-white px-3 pt-[25px] pb-[50px] rounded shadow flex flex-col gap-1 relative "
        style={{ height: '450px', width: '100%' }}
      >
        <div className=" absolute right-[25px] top-[25px] w-[300px]  ">
          <FormikContainer
            hideFooter
            initialValues={{
              week: { value: '', label: 'Selecciona' },
            }}
            onSubmit={async () => {}}
            onClose={() => {}}
          >
            <SelectInput
              label=""
              name="week"
              onChange={(option) => {
                setStartDate(option.value);
              }}
              options={weeksAvailable.map((week) => ({
                value: week.startDate,
                label: format(new Date(week.startDate), "dd 'de' MMMM 'de' yyyy", { locale: es }),
              }))}
            />
          </FormikContainer>
        </div>

        <p className="pl-[15px] text-[24px] text-textGray2 font-[600] ">Registro semanal</p>

        {/* <BarChart data={chartData} barThickness={80} /> */}
        <RecordsBarChart data={chartData} barThickness={80} setFilter={setFilter} />
      </div>
    </>
  );
}
