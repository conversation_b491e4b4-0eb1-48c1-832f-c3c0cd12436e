export const translations = {
  en: {
    Personal: 'Personal',
    Contact: 'Contact',
    Address: 'Address',
    Family: 'Family',
    Property: 'Property',
    Automobile: 'Automobile',
    Debt: 'Debt',
    References: 'References',
    Outcome: 'Outcome',
    Previous: 'Previous',
    FinishWithoutCompleting: 'Finish Without Completing',
    Next: 'Next',
    Finish: 'Finish',
    DateAndTimeOfVisit: 'Date and Time of Visit',
    Date: 'Date',
    Time: 'Time',
    PersonalInformation: 'Personal Information',
    FirstName: 'First Name',
    LastName: 'Last Name',
    Nationality: 'Nationality',
    DateOfBirth: 'Date of Birth',
    Age: 'Age',
    Occupation: 'Occupation',
    WeeklyEarnings: 'Weekly Earnings',
    ContactInformation: 'Contact Information',
    Phone: 'Phone',
    HomePhone: 'Home Phone',
    Email: 'Email',
    ExteriorNo: 'Exterior Number',
    InteriorNo: 'Interior Number',
    Street: 'Street',
    PostalCode: 'Postal Code',
    TimeInResidence: 'Time In Residence',
    Colony: 'Colony',
    Municipality: 'Municipality',
    State: 'State',
    FamilyInformation: 'Family Information',
    MaritalStatus: 'Marital Status',
    DoesYourPartnerGenerateAnyKindOfIncome: 'Does your partner generate any kind of income?',
    WhatIsYourPartnerSourceOfIncome: `What is your partner's source of income?`,
    PartnerName: 'Partner Name',
    PartnerPhone: 'Partner Phone',
    DoYouHaveOtherDependents: 'Do you have any other dependents?',
    HowManyDependents: 'How many dependents?',
    DependentName: 'Dependent Name',
    Relationship: 'Relationship',
    PropertyInformation: 'Property Information',
    DoYouOwnThisProperty: 'Do you own this property?',
    NameOfOwner: 'Name of Owner',
    IsTheOwnerARelativeOrFamilyMember: 'Is the owner a relative or family member?',
    RelationToOwner: 'Relation to Owner',
    OwnerPhone: 'Owner Phone',
    ContactNumberOfOwner: 'Contact Number of Owner',
    TypeOfProperty: 'Type of Property',
    NumberOfBedrooms: 'Number of Bedrooms',
    LivingRoom: 'Living Room',
    DinningRoom: 'Dinning Room',
    Kitchen: 'Kitchen',
    Television: 'Television',
    AudioSystem: 'Audio System',
    Stove: 'Stove',
    Refrigerator: 'Refrigerator',
    WashingMachine: 'Washing Machine',
    DebtInformation: 'Debt Information',
    DoYouHaveAnyDebt: 'Do you have any debt?',
    WhereDoYouHaveOutstandingDebt: 'Where do you have outstanding debt?',
    DoesYourDebtSignificantlyAffectYourPersonalFinance:
      'Does your debt significantly affect your personal finance?',
    AutomobileInformation: 'Automobile Information',
    ownACar: 'Do you own a car?',
    HowLongHaveYouBeenLeasingIt: 'How long have you been leasing it?',
    Make: 'Make',
    Model: 'Model',
    ReferencesInformation: 'References Information',
    Name: 'Name',
    HowDidYouLearnAboutOneCarNow: 'How did you learn about One Car Now?',
    Reference: 'Reference',
    VisitInformationOutcome: 'Visit Information Outcome',
    HomeVisitorInformationEmailAddress: 'Home visitor information - email address',
    DoesTheProofOfAddressMatchTheLocation: 'Does the proof of address match the location?',
    WhatAreTheCharacteristicsOfTheGarage: 'What are the characteristics of the garage?',
    WhatWasTheBehaviourOfTheCustomerDuringTheCall: 'What was the behaviour of the customer during the call?',
    IsThePropertyOwnedOrRented: 'Is the property owned or rented?',
    VisitOutcome: 'Visit Outcome',
    PleaseWriteTheVisitInformationOutcomeInBelow: 'Please write the visit information outcome in below',
    BirthDateRequired: 'Birth Date is required',
    BirthDateFormatErrorMsg: 'Birthdate format should be dd-mm-yyyy',
    AgeLessThan60: 'Age must be less than 60',
    AgeGreaterThan18: 'Age must be greater than or equal to 18',
    DateRequired: 'Date is required',
    TimeRequired: 'Time is required',
    FirstNameRequired: 'First Name is required',
    LastNameRequired: 'Last Name is required',
    NationalityRequired: 'Nationality is required',
    PhoneRequired: 'Phone is required',
    PhoneLengthErrorMsg: 'Phone number must be 10 characters',
    PhoneFormatErrorMsg: 'Phone number should only contain numbers',
    HomePhoneRequired: 'Home phone is required',
    HomePhoneLengthErrorMsg: 'Home phone number must be 10 characters',
    EmailRequired: 'Email is required',
    EmailFormatErrorMsg: 'Invalid email address',
    ExteriorNoRequired: 'Exterior Number is required',
    InteriorNoRequired: 'Interior Number is required',
    StreetRequired: 'Street is required',
    StreetMinLengthErrorMsg: 'Street must be at least 3 characters',
    PostalCodeRequired: 'Postal Code is required',
    PostalCodeLengthErrorMsg: 'Postal Code must be at least 5 characters',
    TimeInResidenceRequired: 'Time in residency is required',
    ColonyRequired: 'Colony is required',
    MunicipalityRequired: 'Municipality is required',
    StateRequired: 'State is required',
    MaritalStatusRequired: 'Marital Status is required',
    DependentsRequired: 'Dependents is required',
    SpouseOrPartnerIncomeRequired: 'Spouse or Partner Income is required',
    PartnerSourceOfIncomeRequired: 'Partner Source of Income is required',
    NoOfDependentsRequired: 'No of Dependents is required',
    MaxNoOfDependentsErrorMsg: 'Max dependents is 5',
    DependentsNameRequired: 'Dependents Name is required',
    DependentsPhoneRequired: 'Dependents Phone is required',
    DependentsRelationshipRequired: 'Dependents Relationship is required',
    PartnerPhoneLengthErrorMsg: 'Partner phone number must be 10 characters',
    DoYouOwnThisPropertyRequired: 'Do you own this property is required',
    NameOfOwnerRequired: 'Name of Owner is required',
    IsTheOwnerARelativeOrFamilyMemberRequired: 'Owner a relative or family member is required',
    RelationToOwnerRequired: 'Relation to Owner is required',
    OwnerPhoneRequired: 'Owner Phone is required',
    TypeOfPropertyRequired: 'Type of Property is required',
    NumberOfBedroomsRequired: 'Number of Bedrooms is required',
    LivingRoomRequired: 'Living Room is required',
    DinningRoomRequired: 'Dinning Room is required',
    KitchenRequired: 'Kitchen is required',
    TelevisionRequired: 'Television is required',
    AudioSystemRequired: 'Audio System is required',
    StoveRequired: 'Stove is required',
    RefrigeratorRequired: 'Refrigerator is required',
    WashingMachineRequired: 'Washing Machine is required',
    ProofOfPropertyOwnership: 'Upload proof of Ownership (IMG or PDF) Select from menu',
    PropertyPictures: 'Upload pictures of the property (IMG or PDF) Select from menu',
    InvalidFileFormat: 'Invalid file format',
    FileUploadErrorMsg: 'Error occured while uploading file',
    FileDeleteErrorMsg: 'Error occured while deleting file',
    Images: 'Images',
    ReasonOfRejection: 'Reason of Rejection',
    VirtualHomeVisit: 'Virtual Home Visit',
    MeetingStatus: 'Meeting Status',
    ApplicationLink: 'Application Link',
    MeetingLink: 'Meeting Link',
    NoShow: 'No Show',
    FinishMeeting: 'Finish Meeting',
    Appointments: 'Appointments',
    AppointmentScheduler: 'Appointment Scheduler',
    AppointmentDuration: 'Appointment Duration',
    GeneralAvailability: 'General Availability',
    BreakTime: 'Break Time',
    SchedulingWindow: 'Scheduling Window',
    Timezone: 'Timezone',
    StartTime: 'Start Time',
    EndTime: 'End Time',
    HowLongShouldEachAppointmentLast: 'How long should each appointment last?',
    MaximumTimeInAdvanceThatAnAppointmentCanBeBooked:
      'Maximum time in advance that an appointment can be booked',
    MininumTimeBeforeTheAppointmentStartThatItCanBeBooked:
      'Mininum time before the appointment start that it can be booked',
    minutes: 'minutes',
    ShouldBeAValidNumber: 'Should be a valid number',
    applyToPreOwned: 'Does it apply to pre-owned?',
  },
  es: {
    Personal: 'Personal',
    Contact: 'Contacto',
    Address: 'Dirección',
    Family: 'Familia',
    Property: 'Propiedad',
    Automobile: 'Automóvil',
    Debt: 'Deuda',
    References: 'Referencias',
    Outcome: 'Resultado',
    Previous: 'Anterior',
    FinishWithoutCompleting: 'Finalizar sin completar',
    Next: 'Siguiente',
    Finish: 'Finalizar',
    DateAndTimeOfVisit: 'Fecha y Hora de Visita',
    Date: 'Fecha',
    Time: 'Hora',
    PersonalInformation: 'Información Personal',
    FirstName: 'Nombre',
    LastName: 'Apellido',
    Nationality: 'Nacionalidad',
    DateOfBirth: 'Fecha de Nacimiento',
    Age: 'Edad',
    Occupation: 'Ocupación',
    WeeklyEarnings: 'Ganancias Semanales',
    ContactInformation: 'Información de Contacto',
    Phone: 'Teléfono',
    HomePhone: 'Teléfono de Casa',
    Email: 'Correo Electrónico',
    ExteriorNo: 'Número Exterior',
    InteriorNo: 'Número Interior',
    Street: 'Calle',
    PostalCode: 'Código Postal',
    TimeInResidence: 'Tiempo en Residencia',
    Colony: 'Colonia',
    Municipality: 'Municipio',
    State: 'Estado',
    FamilyInformation: 'Información Familiar',
    MaritalStatus: 'Estado Civil',
    DoesYourPartnerGenerateAnyKindOfIncome: '¿Tu pareja genera algún tipo de ingreso?',
    WhatIsYourPartnerSourceOfIncome: '¿Cual es la fuente de ingresos de tu pareja?',
    PartnerName: 'Nombre de Pareja',
    PartnerPhone: 'Teléfono de Pareja',
    DoYouHaveOtherDependents: '¿Tiene algun otro dependiente?',
    HowManyDependents: '¿Cuantos dependientes?',
    DependentName: 'Nombre del Dependiente',
    Relationship: 'Relación',
    PropertyInformation: 'Información de la Propiedad',
    DoYouOwnThisProperty: '¿Eres dueño de esta propiedad?',
    NameOfOwner: 'Nombre del Dueño',
    IsTheOwnerARelativeOrFamilyMember: 'El propietario es un conocido o familiar?',
    RelationToOwner: 'Relación con el Dueño',
    OwnerPhone: 'Teléfono del Dueño',
    ContactNumberOfOwner: 'Numero de contacto del propietario',
    TypeOfProperty: 'Tipo de vivienda',
    NumberOfBedrooms: 'Número de dormitorios',
    LivingRoom: 'Sala',
    DinningRoom: 'Comedor',
    Kitchen: 'Cocina',
    Television: 'Televisión',
    AudioSystem: 'Equipo de Audio',
    Stove: 'Estufa',
    Refrigerator: 'Refrigerador',
    WashingMachine: 'Lavadora',
    DebtInformation: 'Información de deuda',
    DoYouHaveAnyDebt: '¿Tienes alguna deuda?',
    WhereDoYouHaveOutstandingDebt: '¿En Donde tienes una deuda pendiente?',
    DoesYourDebtSignificantlyAffectYourPersonalFinance: '¿Tu deuda afecta significativamente tus finanzas?',
    AutomobileInformation: 'Información de Automóvil',
    ownACar: '¿Eres Dueño de un auto?',
    HowLongHaveYouBeenLeasingIt: '¿Cuanto tiempo tienes rentando el auto?',
    Make: 'Marca',
    Model: 'Modelo',
    ReferencesInformation: 'Información de Referencias',
    Name: 'Nombre',
    HowDidYouLearnAboutOneCarNow: '¿Como supiste de OneCarNow?',
    Reference: 'Referencia',
    VisitInformationOutcome: 'Resultado de la Visita',
    HomeVisitorInformationEmailAddress: 'Correo de visitador',
    DoesTheProofOfAddressMatchTheLocation: '¿El comprobante de domicilio coincide con la ubicación?',
    WhatAreTheCharacteristicsOfTheGarage: '¿Cuales son las características del garage?',
    WhatWasTheBehaviourOfTheCustomerDuringTheCall:
      '¿Cual fue el comportamiento del cliente durante la videollamada?',
    IsThePropertyOwnedOrRented: '¿La vivienda es propia o rentada?',
    VisitOutcome: 'Resultado de la Visita',
    PleaseWriteTheVisitInformationOutcomeInBelow:
      'Por favor escribe la informacion del resultado de la visita abajo',
    VisitInformationOutcomeErrorMessage: 'Se requiere información sobre los resultados de la visita',
    ReasonOfRejectionErrorMessage: 'Se requiere el motivo del rechazo',
    ReasonForSelectingDifferentStatus: 'Razón por seleccionar un estado diferente al sugerido',
    BirthDateRequired: 'Fecha de nacimiento es requerida',
    BirthDateFormatErrorMsg: 'El formato de la fecha de nacimiento debe ser dd-mm-yyyy',
    AgeLessThan60: 'La edad debe ser menor a 60',
    AgeGreaterThan18: 'La edad debe ser mayor o igual a 18',
    DateRequired: 'Fecha es requerida',
    TimeRequired: 'Hora es requerida',
    FirstNameRequired: 'Nombre es requerido',
    LastNameRequired: 'Apellido es requerido',
    NationalityRequired: 'Nacionalidad es requerido',
    PhoneRequired: 'Teléfono es requerido',
    PhoneLengthErrorMsg: 'El número de teléfono debe tener 10 dígitos',
    PhoneFormatErrorMsg: 'El número de teléfono debe contener solo números',
    HomePhoneRequired: 'Teléfono de casa es requerido',
    HomePhoneLengthErrorMsg: 'El número de teléfono de casa debe tener 10 caracteres',
    EmailRequired: 'Correo electrónico es requerido',
    EmailFormatErrorMsg: 'Correo electrónico inválido',
    ExteriorNoRequired: 'Número exterior es requerido',
    InteriorNoRequired: 'Número interior es requerido',
    StreetRequired: 'Calle es requerida',
    StreetMinLengthErrorMsg: 'La calle debe tener al menos 3 caracteres',
    PostalCodeRequired: 'Código postal es requerido',
    PostalCodeLengthErrorMsg: 'El código postal debe tener al menos 5 caracteres',
    TimeInResidenceRequired: 'Tiempo en residencia es requerido',
    ColonyRequired: 'Colonia es requerida',
    MunicipalityRequired: 'Municipio es requerido',
    StateRequired: 'Estado es requerido',
    MaritalStatusRequired: 'Estado civil es requerido',
    DependentsRequired: 'Dependientes es requerido',
    SpouseOrPartnerIncomeRequired: 'Ingreso de pareja es requerido',
    PartnerSourceOfIncomeRequired: 'Fuente de ingreso de pareja es requerido',
    NoOfDependentsRequired: 'Número de dependientes es requerido',
    NoOfDependentsPositiveErrorMsg: 'El número de dependientes debe ser un número positivo',
    MaxNoOfDependentsErrorMsg: 'Máximo de dependientes es 5',
    DependentsNameRequired: 'Nombre de dependiente es requerido',
    DependentsPhoneRequired: 'Teléfono de dependiente es requerido',
    DependentsRelationshipRequired: 'Relación de dependiente es requerido',
    PartnerPhoneLengthErrorMsg: 'El número de teléfono de pareja debe tener 10 caracteres',
    DoYouOwnThisPropertyRequired: 'Eres dueño de esta propiedad es requerido',
    NameOfOwnerRequired: 'Nombre de dueño es requerido',
    IsTheOwnerARelativeOrFamilyMemberRequired: 'El dueño es un familiar es requerido',
    RelationToOwnerRequired: 'Relación con el dueño es requerido',
    OwnerPhoneRequired: 'Teléfono de dueño es requerido',
    TypeOfPropertyRequired: 'Tipo de propiedad es requerido',
    NumberOfBedroomsRequired: 'Número de dormitorios es requerido',
    LivingRoomRequired: 'Sala es requerida',
    DinningRoomRequired: 'Comedor es requerido',
    KitchenRequired: 'Cocina es requerida',
    TelevisionRequired: 'Televisión es requerida',
    AudioSystemRequired: 'Equipo de audio es requerido',
    StoveRequired: 'Estufa es requerida',
    RefrigeratorRequired: 'Refrigerador es requerido',
    WashingMachineRequired: 'Lavadora es requerida',
    ProofOfPropertyOwnership: 'Subir comprobante de propiedad (IMG o PDF) Cargar desde ordenador',
    PropertyPictures: 'Subir 5 fotos de la propiedad (IMG o PDF) Cargar desde ordenador',
    InvalidFileFormat: 'Formato de archivo inválido',
    FileUploadErrorMsg: 'Error ocurrió al subir archivo',
    FileDeleteErrorMsg: 'Error ocurrió al borrar archivo',
    Images: 'Imágenes',
    ReasonOfRejection: 'Razón de Rechazo',
    VirtualHomeVisit: 'Visita Virtual',
    MeetingStatus: 'Estado de la Reunión',
    ApplicationLink: 'Enlace de Aplicación',
    MeetingLink: 'Enlace de Reunión',
    NoShow: 'No se presentó',
    FinishMeeting: 'Finalizar Reunión',
    Appointments: 'Appointments',
    AppointmentScheduler: 'Programador de citas',
    AppointmentDuration: 'Duración de la cita',
    GeneralAvailability: 'Disponibilidad general',
    BreakTime: 'Tiempo de descanso',
    SchedulingWindow: 'Ventana de programación',
    Timezone: 'Zona horaria',
    StartTime: 'Hora de inicio',
    EndTime: 'Hora de finalización',
    HowLongShouldEachAppointmentLast: '¿Cuánto tiempo debe durar cada cita?',
    MaximumTimeInAdvanceThatAnAppointmentCanBeBooked:
      'Tiempo máximo con anticipación que se puede reservar una cita',
    MininumTimeBeforeTheAppointmentStartThatItCanBeBooked:
      'Tiempo mínimo antes de que comience la cita que se puede reservar',
    minutes: 'minutos',
    ShouldBeAValidNumber: 'Debe ser un número válido',
    ID: 'ID',
    MinImgesErrorMessage:
      'Se requieren un mínimo de 5 fotografías para completar el formulario de visita domiciliaria.',
    doesItApplyToElectricCars: '¿Aplica para auto eléctrico?',
    SelectHomeVisitor: 'Seleccionar visitante domiciliario',
    AppointmentCalendar: 'Calendario de citas',
    Reassign: 'Reasignar',
    AllHomeVisitors: 'Todos los visitantes del hogar',
    NoVisitorAvailable: 'Ningún visitante disponible',
    Success: 'Éxito',
    AppointmentHomeVisitorUpdatedSuccessfully: 'Visitante domiciliario de cita actualizado con éxito',
    Error: 'Error',
    ErrorUpdatingAppointmentHomeVisitor:
      'Se produjo un error al actualizar la cita del visitante a domicilio',
    success: 'Éxito',
    error: 'Error',
    AppointmentSchedulingSuccessMsg: 'Programador de citas actualizado con éxito.',
    AppointmentSchedulingErrorMsg:
      'Se produjo un error al actualizar la información del programador de citas',
    BlockSlots: 'Bloquear ranuras',
    FieldIsRequired: 'Campo requerido',
    InvalidTimeFormat: 'Ingrese la hora en formato HH:MM, como 09:00am',
    EndTimeGreaterThanStartTime: 'La hora de finalización debe ser mayor que la hora de inicio.',
    StartDate: 'Fecha de inicio',
    EndDate: 'Fecha de finalización',
    Save: 'Guardar',
    BlockSlotRepeat: 'The slot you are trying to add already exist.',
    AddSlots: 'Agregar ranuras',
    days: 'días',
    Repeat: 'Repetir',
    SLOT_NOT_FOUND: 'Ranura no encontrada',
    SLOT_NOT_FOUND_WITH_GIVEN_DATE_TIME: 'No se encontró la ranura con la fecha y hora dadas.',
    SLOT_IS_NOT_AVAILABLE_TO_BLOCK: 'La ranura no está disponible para bloquear.',
    SLOT_DETAILS_ARE_NOT_AVAILABLE: 'Los detalles de la ranura no están disponibles.',
    SLOT_ALREADY_EXISTS: 'La ranura que estás intentando agregar ya existe.',
    SendLocationVerificationLink: 'Enviar enlace de verificación de ubicación',
    LocationInformation: 'Información de ubicación',
    VerifiedAddress: 'Dirección Verificada',
    FetchedIpAddress: 'Dirección IP obtenida',
    AppointmentHistory: 'Historial de citas',
    Status: 'Estado',
    ActionBy: 'Acción por',
    InformationAboutAppointmentForHomeVisit: 'Información sobre la cita para la visita domiciliaria',
    DateOfReservationLink: 'Fecha del enlace de reserva',
    HomeVisitAppointmentStatus: 'Estado de la cita de visita domiciliaria',
    DateOfHomeVisit: 'Fecha de visita domiciliaria',
    HomeVisitorEmailID: 'Visitante del hogar (ID de correo electrónico)',
    ScheduleAnAppointmentWithTheClient: 'Agendar Cita con el Cliente',
    Customer: 'Cliente',
    TotalTrips: 'Viajes totales',
    AcceptanceRate: 'Tasa de aceptación',
    CancellationRate: 'Tasa de cancelación',
    Rating: 'Calificación',
    TimeSinceFirstTrip: 'Tiempo desde primer viaje',
    ViewEarnings: 'Ver ganancias',
    SelectOne: 'Seleccione',
    Year: 'año',
    SendHomeImageUploadLink: 'Enviar enlace de carga de imagen a casa',
    ViewDocument: 'Ver documento',
    LocationPageRefreshInstruction:
      'Por favor, actualiza la página una vez que el cliente haya verificado la ubicación desde su lado.',
  },
};
