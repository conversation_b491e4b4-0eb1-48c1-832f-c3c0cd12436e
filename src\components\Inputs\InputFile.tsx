import { Input, Button } from '@chakra-ui/react';
import { ErrorMessage, Field, FieldInputProps, FormikValues, useField } from 'formik';
import { useRef, useState } from 'react';
import { IconType } from 'react-icons';
import { GrDocumentPdf } from 'react-icons/gr';
import { ImImages } from 'react-icons/im';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface FieldProps {
  name: string;
  label: string;
  nameFile: string;
  buttonText: string;
  accept: 'pdf' | 'all-images' | 'jpg' | 'png' | 'xml' | 'all';
  // the function below has to remove the state or states of the name file
  handleSetName?: (name: string, value: string, size: number) => void;
  handleSingleSetName?: (value: string) => void;
  placeholder: string;
  placeHolderDown?: boolean;
  multiple?: boolean;
  onChange?: (event: FileList | null) => void;
  isLoading?: boolean;
  loadingText?: string;
  infoMessage?: string; // New prop for showing document requirements
}

const acceptTypes: { [key: string]: string } = {
  all: '.jpg, .jpeg, .png, .webp, .pdf',
  'all-images': '.jpg, .jpeg, .png, .webp',
  jpg: 'image/jpg',
  png: 'image/png',
  pdf: 'application/pdf',
};

interface IconData {
  icon: IconType;
  size: number;
}

const icons: { [key: string]: IconData } = {
  pdf: {
    icon: GrDocumentPdf,
    size: 20,
  },
  'all-images': {
    icon: ImImages,
    size: 20,
  },
  png: {
    icon: ImImages,
    size: 20,
  },
  jpg: {
    icon: ImImages,
    size: 20,
  },
};

export default function InputFile({
  name,
  label,
  nameFile,
  accept,
  placeholder,
  placeHolderDown,
  buttonText,
  handleSetName,
  handleSingleSetName,
  multiple = false,
  onChange,
  isLoading = false,
  loadingText = 'Processing...',
  infoMessage, // New prop for showing document requirements
}: FieldProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [, meta] = useField(name);
  const [isDragging, setIsDragging] = useState(false);
  const hasError = meta.touched && meta.error;

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleDragEnter = () => {
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (event: React.DragEvent<HTMLButtonElement>, form: any) => {
    event.preventDefault();
    setIsDragging(false);

    const droppedFiles = event.dataTransfer.files;

    if (!droppedFiles) return;
    if (multiple) {
      if (onChange) onChange(droppedFiles);
      form.setFieldValue(name, droppedFiles);
      return;
    }

    const droppedFile = event.dataTransfer.files[0];
    if (!droppedFile) return;

    const fileName = droppedFile.name;
    const fileSize = droppedFile.size;
    if (handleSetName) handleSetName(name, fileName, fileSize);
    form.setFieldValue(name, droppedFile);
  };

  function IconRenderer() {
    const iconData = icons[accept];
    if (!iconData) return null;

    const { icon: IconComponent, size } = iconData;

    return <IconComponent size={size} className="mr-[6px]" />;
  }

  return (
    <Field name={name} id={name}>
      {({ form }: { field: FieldInputProps<any>; form: FormikValues }) => (
        <div className="relative font-normal ">
          <label htmlFor={name}>{label}</label>
          {infoMessage && (
            <div className="mt-1 mb-1">
              <div className="bg-[#F4EEFF] border border-[#5800F7] rounded-md p-2 flex items-center gap-2">
                <p className="text-sm text-[#5800F7]">{infoMessage}</p>
              </div>
            </div>
          )}
          <div
            className={`flex ${
              placeHolderDown ? 'flex-col gap-1' : 'flex-row items-center gap-3'
            } mt-2 w-full overflow-hidden `}
            style={{ gridTemplateColumns: 'max-content 1fr' }}
          >
            <Button
              h="40px"
              minW="138px"
              w="max-content"
              borderColor={isDragging ? '#9CA3AF !important' : '#5800F7 !important'}
              color="#5800F7 !important"
              fontWeight={600}
              border={isDragging ? '2px dashed' : '2px solid'}
              sx={{
                '&::placeholder': {
                  color: '#5800F7',
                },
                _hover: {
                  borderColor: '#5800F7',
                },
              }}
              cursor={isLoading ? 'not-allowed' : 'pointer'}
              onClick={isLoading ? undefined : handleButtonClick}
              onDragEnter={isLoading ? undefined : handleDragEnter} // Manejadores de eventos de arrastre
              onDragLeave={isLoading ? undefined : handleDragLeave} // Manejadores de eventos de arrastre
              onDragOver={isLoading ? undefined : (event) => event.preventDefault()} // Evita comportamiento por defecto
              onDrop={isLoading ? undefined : (e) => handleFileDrop(e, form)}
              disabled={isLoading}
              opacity={isLoading ? 0.6 : 1}
            >
              {isLoading ? (
                <div className="flex items-center gap-3 py-1">
                  <LoadingSpinner size="lg" color="#5800F7" />
                  <span className="text-[#5800F7] font-semibold text-sm">{loadingText}</span>
                </div>
              ) : (
                buttonText
              )}
            </Button>
            <div className={`text-[14px]`}>
              {Array.isArray(nameFile) && nameFile.length > 0 ? (
                <div>{`file count: ${nameFile.length}`}</div>
              ) : nameFile?.length > 0 ? (
                <div className="flex items-center max-w-screen-sm ">
                  <IconRenderer />
                  <p></p>
                  {nameFile.slice(0, 25)}
                </div>
              ) : (
                <p>{placeholder}</p>
              )}
            </div>
          </div>

          <Input
            h="45px"
            display="none"
            name={name}
            ref={fileInputRef}
            cursor="pointer"
            accept={acceptTypes[accept]}
            type="file"
            multiple={multiple}
            disabled={isLoading}
            onChange={(event) => {
              if (isLoading) return;
              if (!multiple) {
                const file = event.currentTarget.files ? event.currentTarget.files[0] : undefined;
                if (!file) return;
                form.setFieldValue(name, file);
                if (handleSetName) handleSetName(name, file.name, file.size);
                if (handleSingleSetName) handleSingleSetName(file.name);
                if (onChange) {
                  onChange(event.currentTarget.files);
                }
              } else {
                const files = event.currentTarget.files;
                const arrayArray = Array.from(files || []);
                if (handleSetName) handleSetName(name, arrayArray as unknown as string, arrayArray[0].size);
                form.setFieldValue(name, arrayArray);
                if (onChange) {
                  onChange(files);
                }
              }
            }}
          />

          {hasError && <ErrorMessage name={name} component="div" className="mt-1 text-sm text-red-500" />}
        </div>
      )}
    </Field>
  );
}
