/* eslint-disable prettier/prettier */
import { stateDependingCity } from '@/constants';
import { Font, StyleSheet, Text, View } from '@react-pdf/renderer';
import { DateTime } from 'luxon';
import React from 'react';

type DeclarationListFunctionProps = {
  deliveryDate: string;
  fullName: string;
  contractNumber: string;
  city: string;
};

const declarationList = (data: DeclarationListFunctionProps) => {

  const parsedDate = DateTime.fromISO(data.deliveryDate)
    .setLocale('es')
    .toLocaleString(DateTime.DATE_FULL);

  const city = stateDependingCity[data.city] || data.city;

  return [
    {
      letter: 'a',
      description: `Es una persona moral mexicana debidamente constituida conforme la legislación nacional aplicable, según consta en la escritura pública número 62,229, de fecha 16 de febrero del 2021, pasada ante la fe del Lic. <PERSON>, Notario Público número 75, en la Ciudad de México, e inscrita en el Registro Público de la Propiedad y de Comercio, con fecha 24 de febrero del mismo año, bajo el folio mercantil número N-2021011614. Modificando su denominación a Sociedad Anónima Promotora de Inversión a través de la escritura pública 28,831 de fecha 10 de febrero de 2022, pasada ante la fé del Lic. Alfonso Martin León Orantes, notario 238 de la Ciudad de México, e inscrita en el Registro Público de la Propiedad y de Comercio, con fecha 01 de marzo del mismo año, bajo el mismo folio mercantil.`,
    },
    {
      letter: 'b',
      description: `Su apoderado legal cuenta con todas las facultades suficientes para formalizar el presente acto jurídico, personalidad que se acredita en términos del instrumento notarial que se adjunta a este documento.`,
    },
    {
      letter: 'c',
      description: `Que en fecha ${parsedDate} su poderdante celebró el contrato de arrendamiento de número ${data.contractNumber} con el conductor de nombre ${data.fullName.toUpperCase()} `,
    },
    {
      letter: 'd',
      description: `Que señala como domicilio para oír y recibir notificaciones el ubicado en Prolongación Paseo de la Reforma 1015 PISO 5 INT 140, Santa Fe Cuajimalpa, Cuajimalpa de Morelos, Ciudad de México, 05348, México.`,
    },
    {
      letter: 'e',
      description: `Que  la cláusula Décima Primera del citado contrato, otorga al ARRENDADOR el derecho para declarar rescindido  o exigir su clumplimiento anticipado y forzoso, en diversos supuesto como el siguiente:  `,
      nested: {
        letter: 'a',
        description: `La falta de pago puntual de alguno de los pagos parciales o de cualquier otra cantidad a cargo de “EL ARRENDATARIO” pasados 5 (cinco) días naturales de su incumplimiento;`,
      },
    },
    {
      letter: 'f',
      description: `Supuesto que se actualiza derivado que el conductor dejó de pagar las semanalidades que le corresponden, motivo por el cual se rescinde este acuerdo de voluntades.`,
    },
    {
      letter: 'g',
      description: `No debe pasar inadvertido lo dispuesto en  el Código Civil para el Estado de ${city}, en su artículo  2383, que  dispone las causas de rescisión, entre otras: `,
    },
  ];
};

interface DeclarationListProps {
  deliveryDate: string;
  fullName: string;
  contractNumber: string;
  city: string;
}

export function DeclarationList({ deliveryDate, fullName, contractNumber, city }: DeclarationListProps) {
  return (
    <>
      <Text style={styles.title}> D E C L A R A C I O N E S </Text>

      <Text style={styles.definicionesText}>
        {' '}
        PRIMERA.- DECLARA “EL ARRENDADOR”, A TRAVÉS DE SU APODERADO LEGAL QUE:  {' '}
      </Text>
      <View style={styles.definicionContainer}>
        {declarationList({
          deliveryDate,
          fullName,
          contractNumber,
          city,
        }).map(({ letter, description }, i) => {
          return (
            <View style={styles.item} key={i}>
              <View>
                <Text style={styles.letterPoint}> {letter}) </Text>
              </View>

              <Text style={styles.itemContent}>
                {' '}
                {description}{' '}
              </Text>
            </View>
          );
        })}
      </View>
    </>
  );
}

const fontConfig = {
  family: 'Helvetica',
  fonts: [],
};

Font.register(fontConfig);

const styles = StyleSheet.create({
  definicionContainer: {
    flexDirection: 'column',
    rowGap: 15,
  },

  definicionesText: {
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },
  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },
  item: {
    flexDirection: 'row',
    alignContent: 'center',
    marginLeft: '30px',
  },
  letterPoint: {
    fontSize: 8,
    marginRight: 17,
  },

  letterPointD: {
    fontSize: 8,
    marginRight: 10,
  },
  itemContent: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  defTitle: {
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
    fontWeight: 'bold',
  },
  clausulasContainer: {
    flexDirection: 'column',
    rowGap: '10px' as unknown as number,
  },

  clasulasBolds: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
  },
});

const declarationList2 = (data: { fullAddress: string }) => {
  return [
    {
      letter: 'a',
      description: `Llamarse según lo anotado en el proemio de este convenio, así como contar con la capacidad legal para 
cumplir con las obligaciones contenidas en este instrumento. `,
    },
    {
      letter: 'b',
      description: `Que cuenta con la capacidad legal, en términos de las leyes aplicables, para obligarse bajo los términos y condiciones contenidos en este convenio.`,
    },
    {
      letter: 'c',
      description: `Que es su deseo firmar el presente acuerdo de voluntades, en los términos y condiciones que se establecen en este documento.`,
    },
    {
      letter: 'd',
      description: `Que señala como domicilio para oír y recibir notificaciones 
el ubicado en: ${data.fullAddress}.`,
    },
  ];
};

interface DeclarationList2Props {
  fullAddress: string;
}

export function DeclarationList2({ fullAddress }: DeclarationList2Props) {
  return (
    <>
      <Text style={styles.definicionesText}> SEGUNDA.- DECLARA “EL CONDUCTOR”:</Text>
      <View style={styles.definicionContainer}>
        {declarationList2({
          fullAddress,
        }).map(({ letter, /* title, */ description }, i) => {
          return (
            <View style={styles.item} key={i}>
              <View>
                <Text style={styles.letterPoint}> {letter}) </Text>
              </View>

              <Text style={styles.itemContent}>
                {' '}
                {/* <Text style={styles.defTitle}> {title} </Text> */}
                {description}{' '}
              </Text>
            </View>
          );
        })}
      </View>
    </>
  );
}
