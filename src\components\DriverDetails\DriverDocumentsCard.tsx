import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Table<PERSON>ontainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Box,
  Stack,
} from '@chakra-ui/react';
import {
  AdmissionRequestDocumentType,
  AdmissionRequestAdditionalDocumentType,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
} from '@/app/dashboard/clientes/solicitudes/enums';
import {
  RequestDocumentStatusTranslations,
  RequestDocumentStatusTranslationsUS,
  RequestDocumentTypeTranslations,
  RequestDocumentTypeTranslationsUS,
} from '@/app/dashboard/clientes/solicitudes/data';
import { truncateFileName } from '@/utils/text';
import { associateTranslationsMX, associateTranslationsUS } from '@/constants/translations';

export interface RequestDocument {
  mediaId: string | null;
  status: RequestDocumentStatus;
  type: AdmissionRequestDocumentType | AdmissionRequestAdditionalDocumentType;
  media?: {
    url: string;
    fileName: string;
    mimeType: string;
  };
}

interface DocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus;
  documents: RequestDocument[];
  additionalDocuments?: RequestDocument[];
}

function formatDocumentType(
  type: AdmissionRequestDocumentType | AdmissionRequestAdditionalDocumentType,
  countryCondition: boolean
) {
  if (countryCondition) {
    return (RequestDocumentTypeTranslationsUS as Record<string, string>)[type] || type;
  }
  return (RequestDocumentTypeTranslations as Record<string, string>)[type] || type;
}

const DocumentBadge = ({ fileName }: { fileName: string }) => {
  const truncated = truncateFileName(fileName, 25);
  return (
    <Box
      display="inline-block"
      px={2}
      py={1}
      borderWidth="1px"
      bg="#EFF1F2"
      borderColor="#EFF1F2"
      color="#0A293B"
      borderRadius="md"
      fontSize="xs"
      fontWeight="normal"
      _hover={{ bg: 'gray.300' }}
    >
      {truncated}
    </Box>
  );
};

const StatusBadge = ({
  status,
  countryCondition,
}: {
  status: RequestDocumentStatus;
  countryCondition: boolean;
}) => {
  const colorScheme = {
    [RequestDocumentStatus.approved]: { text: '#067F20', bg: '#E8FAEB' },
    [RequestDocumentStatus.pending]: { text: '#946200', bg: '#FFF5CC' },
    [RequestDocumentStatus.pending_review]: { text: '#6B46C1', bg: '#EDE9FE' },
    [RequestDocumentStatus.rejected]: { text: '#D32F2F', bg: '#FDECEA' },
  }[status] || { text: '#333', bg: '#EEE' };

  return (
    <Box
      display="inline-block"
      px={2}
      py={1}
      borderWidth="1px"
      borderRadius="md"
      fontSize="xs"
      fontWeight={600}
      bg={colorScheme.bg}
      color={colorScheme.text}
      borderColor={colorScheme.bg}
    >
      {(countryCondition
        ? RequestDocumentStatusTranslationsUS[status]
        : RequestDocumentStatusTranslations[status]) || status}
    </Box>
  );
};

const DocumentsTable = ({
  documents,
  countryCondition,
  translations,
}: {
  documents: RequestDocument[];
  countryCondition: boolean;
  translations: any;
}) => {
  return (
    <TableContainer fontSize="sm" border="1px solid #e2e8f0" borderRadius="md" overflow="hidden">
      <Table>
        <Thead bg="#FAFAFA">
          <Tr>
            <Th width="40%" textAlign="start" pl={4}>
              {translations.name}
            </Th>
            <Th width="35%" textAlign="start">
              {translations.document}
            </Th>
            <Th width="25%" textAlign="start">
              {translations.status}
            </Th>
          </Tr>
        </Thead>
        <Tbody>
          {documents?.map((document: RequestDocument) => (
            <Tr key={document?.type} _hover={{ bg: 'gray.50' }}>
              <Td pl={4} textAlign="start">
                {formatDocumentType(document?.type, countryCondition)}
              </Td>
              <Td textAlign="start">
                {document.media ? (
                  <a href={document.media.url} target="_blank" rel="noopener noreferrer">
                    <DocumentBadge fileName={document.media?.fileName} />
                  </a>
                ) : (
                  'N/A'
                )}
              </Td>
              <Td textAlign="start">
                <StatusBadge status={document.status} countryCondition={countryCondition} />
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </TableContainer>
  );
};

export default function DriverDocumentsCard({
  documentsAnalysis,
  countryCondition = false,
}: {
  documentsAnalysis: DocumentsAnalysis;
  countryCondition: boolean;
}) {
  const translations = countryCondition ? associateTranslationsUS : associateTranslationsMX;

  const isDocumentsAnalysisApproved = documentsAnalysis?.status === RequestDocumentsAnalysisStatus.approved;
  const isDocumentsAnalysisRejected = documentsAnalysis?.status === RequestDocumentsAnalysisStatus.rejected;

  const mandatoryDocuments =
    documentsAnalysis?.documents?.filter((doc) => doc.type in AdmissionRequestDocumentType) || [];

  const additionalDocuments =
    documentsAnalysis?.documents?.filter((doc) => doc.type in AdmissionRequestAdditionalDocumentType) || [];

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {translations.docAnalysis}
          </Heading>
          {isDocumentsAnalysisApproved && (
            <Text color="green.500" fontSize="sm">
              {translations.preApproved}
            </Text>
          )}
          {isDocumentsAnalysisRejected && (
            <Text color="red.500" fontSize="sm">
              {translations.rejected}
            </Text>
          )}
        </Flex>
      </CardHeader>
      {documentsAnalysis && (
        <CardBody>
          <Stack spacing={6}>
            {mandatoryDocuments?.length > 0 && (
              <Box>
                <Heading size="sm" mb={4} color="gray.600">
                  {translations.mandatoryDocs}
                </Heading>
                <DocumentsTable
                  documents={mandatoryDocuments}
                  countryCondition={countryCondition}
                  translations={translations}
                />
              </Box>
            )}

            {additionalDocuments?.length > 0 && (
              <Box>
                <Heading size="sm" mb={4} color="gray.600">
                  {translations.additionalDocs}
                </Heading>
                <DocumentsTable
                  documents={additionalDocuments}
                  countryCondition={countryCondition}
                  translations={translations}
                />
              </Box>
            )}
          </Stack>
        </CardBody>
      )}
    </Card>
  );
}
