'use client';

import { Input, Tooltip } from '@chakra-ui/react';
// import SelectInput from '@/components/Inputs/SelectInput';
import Select from 'react-select';
import { Avatar } from '@chakra-ui/react';
import { VehicleResponse } from '@/actions/getVehicleData';

const showOptions = [{ value: 'last-5', label: 'Ultimos 5' }];
const userOptions = [{ value: 'id', label: '<PERSON>' }];

interface HistoryArray {
  updateHistory: VehicleResponse['updateHistory'];
}

interface HistoryCard {
  props: VehicleResponse['updateHistory'][number];
}

function areDatesEqualWithoutTime(date1: string, date2: string) {
  // Convertir las fechas a cadenas en el formato 'YYYY-MM-DD'
  const dateString1 = date1.split('T')[0];
  const dateString2 = date2.split('T')[0];

  // Comparar las cadenas de fecha sin tener en cuenta la parte del tiempo
  return dateString1 === dateString2;
}

export default function History({ updateHistory }: HistoryArray) {
  // console.log('is same', areDatesEqualWithoutTime(updateHistory[0].time, updateHistory[1].time));

  const updateHistoryValidation = updateHistory.map((history, i) => {
    if (i === 0) return { ...history, displayDate: true };
    if (areDatesEqualWithoutTime(history.time, updateHistory[i - 1].time)) {
      return { ...history, displayDate: false };
    }
    return { ...history, displayDate: true };
  });

  return (
    <div
      className="
        w-full
        min-h-[400px]
        h-[max-content]
        bg-[white] 
        flex flex-col 
        gap-3
        py-[25px]
        px-[20px]
        border-[1px] 
        border-[#EAECEE] 
        font-bold
        rounded"
    >
      <p className="font-bold text-[24px] ">Historial de actividades</p>
      <div className="flex 2xl:place-content-between sm:place-content-start sm:gap-3 2xl:gap-0 font-normal text-[16px] flex-wrap">
        <div className="flex gap-3 items-center ">
          <p>Mostrar:</p>
          <Select options={showOptions} placeholder="Selecciona" className="w-[200px]" />
          {/* <ChakraSelect /> */}
        </div>
        <div className="flex gap-3 items-center">
          <p>Usuario:</p>
          <Select options={userOptions} placeholder="Selecciona" className="w-[300px]" />
          {/* <ChakraSelect /> */}
        </div>
        <div className="flex gap-3 items-center  ">
          <p>Fecha:</p>
          {/* <Select options={userOptions} className="w-[300px]" /> */}
          <Input placeholder="Select Date and Time" className="w-[300px] h-[40px] " type="date" />
          {/* <ChakraSelect /> */}
        </div>
      </div>
      {updateHistoryValidation.map((history, i) => (
        // eslint-disable-next-line @typescript-eslint/no-use-before-define
        <HistoryInfoCard key={i} props={history} />
      ))}
    </div>
  );
}

const daysOfWeek = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
const months = [
  'enero',
  'febrero',
  'marzo',
  'abril',
  'mayo',
  'junio',
  'julio',
  'agosto',
  'septiembre',
  'octubre',
  'noviembre',
  'diciembre',
];

function HistoryInfoCard({ props }: HistoryCard & { props: { displayDate: boolean } }) {
  const date = new Date(props.time);
  const hours = date.getHours().toString().padStart(2, '0'); // Obtiene las horas y las convierte en cadena con ceros a la izquierda si es necesario
  const minutes = date.getMinutes().toString().padStart(2, '0'); // Obtiene los minutos y los convierte en cadena con ceros a la izquierda si es necesario
  const meridian = hours >= '12' ? 'PM' : 'AM';
  const formattedHours = (parseInt(hours, 10) % 12 || 12).toString().padStart(2, '0'); // Convierte las horas en formato de 12 horas

  const time = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${meridian}`;

  const dayOfWeek = daysOfWeek[date.getDay()];
  const dayOfMonth = date.getDate();
  const month = months[date.getMonth()];
  const year = date.getFullYear();

  const formattedDate = `${dayOfWeek} ${dayOfMonth} de ${month} del ${year}`;
  return (
    <>
      {props.displayDate && <div className="my-3">{formattedDate}</div>}
      {/* <div className="my-3">{formattedDate}</div> */}
      <div className="flex lg:flex-row gap-5  ">
        <div className="w-[20px] h-[20px] bg-[#5800F7] rounded-full"></div>
        <div className="flex flex-col lg:flex-row gap-2 lg:gap-0 place-content-between  px-4 border-[1px] border-[#EAECEE] rounded h-full  w-full py-2  ">
          <div className="flex flex-col 2xl:flex-row gap-3">
            <p>
              {props.step} {props.description ? ':' : ''}
            </p>
            <p>{props.description}</p>
          </div>
          <div className="flex gap-3 items-center mt-2 2xl:mt-0 self-end">
            <p className="font-normal">{time}</p>
            <Tooltip label={props.user.name} placement="top">
              <Avatar
                src={
                  props.user.image?.url ||
                  'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
                }
                w="20px"
                h="20px"
              />
            </Tooltip>
            {/* <p>{props.user.name}</p> */}
          </div>
        </div>
      </div>
    </>
  );
}
