import React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Countries, MexicoRegions, US_CITIES_NAMES_SHORT_CODES, USCITIESNAMES } from '@/constants';

type Props = {
  region: string;
  setRegion: (val: string) => void;
  country: Countries;
};

function SelectRegion({ region, setRegion, country }: Props) {
  function handleValueChange(value: string): void {
    setRegion(value);
  }

  const cities = {
    [Countries['United States']]: [
      {
        value: US_CITIES_NAMES_SHORT_CODES[USCITIESNAMES.Dallas],
        label: US_CITIES_NAMES_SHORT_CODES[USCITIESNAMES.Dallas],
      },
    ],
    [Countries.Mexico]: MexicoRegions,
    [Countries.Brazil]: [],
  };

  return (
    <div className="pr-2">
      <Select onValueChange={handleValueChange} value={region}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select Region" />
        </SelectTrigger>
        <SelectContent>
          <SelectGroup>
            <SelectLabel>Region</SelectLabel>
            <SelectItem value="none">None</SelectItem>
            {cities[country].map((city) => {
              return (
                <SelectItem key={city.label} value={city.value}>
                  {city.label}
                </SelectItem>
              );
            })}
          </SelectGroup>
        </SelectContent>
      </Select>
    </div>
  );
}

export default SelectRegion;
