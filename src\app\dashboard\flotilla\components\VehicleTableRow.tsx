import React from 'react';
import { Tr, Td, Checkbox } from '@chakra-ui/react';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';

interface VehicleTableRowProps {
  vehicle: VehicleWithQR;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
}

const VehicleTableRow: React.FC<VehicleTableRowProps> = ({ vehicle, isSelected, onSelect }) => {
  return (
    <Tr _hover={{ bg: 'gray.50' }}>
      <Td>
        <Checkbox isChecked={isSelected} onChange={(e) => onSelect(e.target.checked)} colorScheme="purple" />
      </Td>
      <Td fontWeight="medium">
        {vehicle.brand} {vehicle.model}
      </Td>
      <Td color="gray.600">{vehicle.vin}</Td>
      <Td color="gray.600">{vehicle.carNumber}</Td>
      <Td color="gray.600">{vehicle.year}</Td>
    </Tr>
  );
};

export default VehicleTableRow;
