import { VehicleResponse } from '@/actions/getVehicleData';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import Spinner from '@/components/Loading/Spinner';
import { adminsPayFlow, Countries, URL_API } from '@/constants';
import { Button, useToast } from '@chakra-ui/react';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface StartPayFlowProps {
  vehicle: VehicleResponse;
  driver: VehicleResponse['drivers'][number];
}

export default function StartPayFlow({ vehicle, driver }: StartPayFlowProps) {
  const { user: currentUser } = useCurrentUser();
  const [loading, setLoading] = useState(false);
  const [payFlow, setPayFlow] = useState<any>();
  const router = useRouter();
  const toast = useToast();
  const createToast = (title: string, description: string, status: 'success' | 'error' | 'info') => {
    return toast({
      title,
      description,
      status,
      position: 'top',
      duration: 5000,
      isClosable: true,
    });
  };

  const startPayFlow = async () => {
    try {
      const isCustomerBankAccountLinked =
        driver.userFromPaymentService?.stripeCustomer?.isCustomerBankAccountLinked;
      if (driver?.country === Countries['United States'] && !isCustomerBankAccountLinked) {
        createToast('Payment flow', 'The driver bank account has not been linked', 'error');
        return;
      }
      setLoading(true);
      await axios.post(
        `${URL_API}/associate/start-payment-flow`,
        {
          // email: (driver as { email: string }).email,
          vehicleId: vehicle._id,
          associateId: driver._id,
        },
        {
          headers: {
            Authorization: `Bearer ${currentUser.accessToken}`,
          },
        }
      );
      createToast('Flujo de pagos', 'Usuario agreado al flujo de pagos', 'success');
      router.refresh();
    } catch (error: any) {
      const errMsg = error.response?.data?.message || error.message;
      const message = errMsg.toLowerCase().includes('subscription already')
        ? 'El flujo ya ha sido iniciado, la suscripción ya existe'
        : errMsg;

      createToast('Flujo de pagos', message, 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const getPayFlow = async () => {
      try {
        const response = await axios.get(
          `${URL_API}/associate/get-payment-flow/${driver._id}?stockId=${vehicle._id}`,
          {
            headers: {
              Authorization: `Bearer ${currentUser.accessToken}`,
            },
          }
        );
        const data = response.data.data;
        // console.log('PAYFLOW', data);
        setPayFlow(data);
      } catch (error) {
        console.log(error);
      }
    };

    getPayFlow();
  }, [vehicle._id, currentUser.accessToken, driver?._id]);

  const notManuelEmail = driver.digitalSignature?.participants?.filter(
    (participant) => participant.email !== '<EMAIL>'
  );

  // console.log('driver.digitalSignature?.participants', driver.digitalSignature?.participants);
  // console.log('notManuelEmail', notManuelEmail);
  const allAreSigned = notManuelEmail?.every((participant) => participant.signed);

  return (
    <>
      {loading && <Spinner />}
      {adminsPayFlow.includes(currentUser.email) && allAreSigned && (
        <Button className="text-[#5800F7] font-bold border-[2px] border-[#5800F7]" onClick={startPayFlow}>
          {/* Crear Pagos */}
          {payFlow?.isCreated ? 'Pagos creados' : 'Crear Pagos'}
        </Button>
      )}
    </>
  );
}
