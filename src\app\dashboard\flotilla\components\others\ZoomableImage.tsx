import useHandleResize from '@/hooks/useHandleRezise';
import { useState } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';

const initialState = {
  limitToBounds: false,
  disabled: false,
  maxScale: 3,
  defaultScale: 0.9,
};

export default function ZoomableImage({ imageUrl, topPosition }: { imageUrl: string; topPosition?: string }) {
  const [settings] = useState(initialState);
  const { limitToBounds, disabled, maxScale } = settings;
  const { innerWidth: width, innerHeight: height } = window;

  const isMobile = useHandleResize({ breakpoint: 768 });

  return (
    <TransformWrapper
      limitToBounds={limitToBounds}
      disabled={disabled}
      maxScale={maxScale}
      pinch={{ disabled: false }}
      doubleClick={{ disabled: false }}
      wheel={{
        wheelDisabled: false,
      }}
    >
      <TransformComponent>
        <div
          className={`
          bg-contain
          bg-center
          bg-no-repeat
          rounded
          `}
          style={{
            backgroundImage: `url(${imageUrl})`,
            backgroundRepeat: 'no-repeat',
            width: isMobile ? width - 15 + 'px' : `${width - 400}px`,
            // height: '100%',
            height: isMobile ? '100%' : `${height - 15}px`,
            top: topPosition,
          }}
        />
      </TransformComponent>
    </TransformWrapper>
  );
}
