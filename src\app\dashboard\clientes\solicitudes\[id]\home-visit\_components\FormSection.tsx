import { ReactNode } from 'react';
import { IStepperButtonsProps, StepperButtons } from './StepperButtons';

interface IFormSection extends IStepperButtonsProps {
  children: ReactNode | ReactNode[];
}

export const FormSection = (props: IFormSection) => {
  const {
    children,
    goToNext,
    goToPrevious,
    isLoading,
    activeStep,
    homeVisit,
    showFinishWithoutCompleting,
    finishWithoutCompleting,
  } = props;
  return (
    <div className="space-y-6">
      <StepperButtons
        goToNext={goToNext}
        goToPrevious={goToPrevious}
        isLoading={isLoading}
        activeStep={activeStep}
        homeVisit={homeVisit}
        showFinishWithoutCompleting={showFinishWithoutCompleting}
        finishWithoutCompleting={finishWithoutCompleting}
      />
      <div className="bg-primaryOffWhite px-2 rounded-md">{children}</div>
    </div>
  );
};
