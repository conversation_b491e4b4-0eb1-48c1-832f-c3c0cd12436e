import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import {
  FaCarSide,
  FaChevronDown,
  FaRegUserCircle,
  FaProductHunt,
  FaFileInvoiceDollar,
} from 'react-icons/fa';
import { AiFillSchedule, AiFillAlert } from 'react-icons/ai';
import { Capabilities, Paths, Sections, Subsections } from '@/constants';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';

const PagosNavItems = () => {
  const ability = usePermissions();
  const pathname = usePathname();
  const navButton = {
    icon: <FaCarSide size={18} />,
    name: 'Pagos',
  };
  const subNavLinks = [
    {
      link: Paths.payments_clients,
      icon: <FaRegUserCircle size={16} />,
      name: 'Client<PERSON>',
      key: Subsections.Clients,
    },
    {
      link: Paths.payments_products,
      icon: <FaProductHunt size={16} />,
      name: 'Productos',
      key: Subsections.Products,
    },
    {
      link: Paths.payments_payments,
      icon: <FaFileInvoiceDollar size={16} />,
      name: 'Payments',
      prefetch: true,
      key: Subsections.Payments,
    },
    {
      link: Paths.payments_paymentSchedule,
      icon: <AiFillSchedule size={16} />,
      name: 'Calendario de pago',
      key: Subsections.PaymentSchedule,
    },
    {
      link: Paths.payments_paymentVerification,
      icon: <AiFillAlert size={16} />,
      name: 'Validacion de pago',
      key: Subsections.PaymentVerification,
    },
  ].filter((item) => canPerform(ability, Capabilities.View, Sections.Payments, item.key));

  return (
    <details className="group transition-all duration-150 ml-4 content-center h-10 open:h-auto overflow-hidden ">
      {pathname.includes('/dashboard/pagos') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 mr-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2">
          <FaCarSide size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Pagos{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname.includes('/dashboard/pagos') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500">
        {subNavLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={item.prefetch || false}>
              <button
                className={
                  pathname === item.link
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                style={{ width: '96%' }}
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
      </nav>
    </details>
  );
};

export default PagosNavItems;
