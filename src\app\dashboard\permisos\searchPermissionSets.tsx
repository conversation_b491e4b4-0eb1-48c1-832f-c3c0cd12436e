'use client';
import { AiOutlineSearch } from 'react-icons/ai';
import { usePermissionSets } from './PermisosProvider';

export default function SearchPermissionSets() {
  const { handleSearch } = usePermissionSets();
  return (
    <div className="relative">
      <input
        className="px-3 h-[40px] border-[#9CA3AF] border-[1px] !outline-none rounded relative"
        type="text"
        onChange={(e) => handleSearch(e)}
        placeholder="Buscar"
      />
      <div className="absolute top-0 right-0 text-[#9CA3AF] flex items-center h-full mr-2">
        <AiOutlineSearch size={26} />
      </div>
    </div>
  );
}
