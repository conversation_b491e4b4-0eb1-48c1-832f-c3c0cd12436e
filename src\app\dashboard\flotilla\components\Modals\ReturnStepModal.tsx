/* eslint-disable consistent-return */
'use client';
import { useReturnStepModal } from '@/zustand/modalStates';
import ModalContainer from './ModalContainer';
// import ReturnToVehicleReady from '../Layouts/returnToVehicleReady';
// import { Select } from '@chakra-ui/react';
import FormikContainer from '@/components/Formik/FormikContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import { useState } from 'react';
import Swal from 'sweetalert2';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';

const initialValues = {
  step: { value: '', label: 'Selecciona' },
  deleteAll: { label: 'No', value: 'no' },
};

export default function ReturnStepModal() {
  const returnStepModal = useReturnStepModal();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';
  const updateSideData = useUpdateSideData();

  const [selectedOption, setSelectedOption] = useState({ value: '', label: 'Selecciona' });

  const { user } = useCurrentUser();
  const { id } = useParams();
  const router = useRouter();
  const toast = useToast();
  const onSubmit = async (values: typeof initialValues) => {
    try {
      const response = await axios.put(
        `${URL_API}/stock/flows/return-to/${id}`,
        {
          deleteAll: values.deleteAll.value !== 'no',
          stepName: values.step.value,
        },
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      returnStepModal.onClose();
      toast({
        title: response.data.message || `Vehiculo regresado a ${values.step.value}`,
        status: 'info',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      await updateSideData(user);
      setTimeout(() => {
        const redirectPage = values.step.value === 'Vehiculo listo' ? 'stock' : 'assigned';
        if (window.location.pathname.includes('inactive') || window.location.pathname.includes('active')) {
          return router.push(
            `/dashboard/flotilla/inactive/${redirectPage}/${id}${
              country ? `?country=${encodeURI(country)}` : ''
            }`
          );
        } else {
          return router.refresh();
        }
      }, 3000);
    } catch (error: any) {
      const clientId = error.response?.data?.clientId;

      let objSwal: any = {
        icon: 'error',
        title: error.response?.data?.message || error.message || 'Hubo un error',
      };

      if (clientId) {
        objSwal.html = `
          <p>Detalle del cliente:</p>
          <a href="${window.location.origin}/dashboard/pagos/clientDetails?id=${clientId}" target="_blank" class="text-blue-500 underline">Ver detalle del cliente</a>
          `;
      }
      return Swal.fire(objSwal);
    }
  };

  const confirSubmit = async (values: typeof initialValues) => {
    Swal.fire({
      title: `¿Desea regresar a ${values.step.value}?`,
      text: 'Esta accion no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: `Si, regresar a ${values.step.value}`,
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  return (
    <ModalContainer title="Regresar paso" onClose={returnStepModal.onClose}>
      <FormikContainer
        onSubmit={async (values) => {
          await confirSubmit(values);
        }}
        initialValues={initialValues}
        onClose={returnStepModal.onClose}
        // hideFooter
      >
        <div className="flex flex-col gap-3">
          <SelectInput
            name="step"
            label="Selecciona el paso a regresar"
            options={[
              {
                label: 'Vehiculo listo',
                value: 'Vehiculo listo',
              },
              {
                label: 'Conductor asignado',
                value: 'Conductor asignado',
              },
            ]}
            onChange={(e) => {
              setSelectedOption({ value: e.value, label: e.label });
            }}
          />

          {selectedOption.value === 'Vehiculo listo' && (
            <>
              <p>Por default elimina solo el conductor actual cuando el selector esta en no</p>
              <SelectInput
                name="deleteAll"
                label="¿Desea eliminar a todos los usuarios?"
                options={[
                  { label: 'No', value: 'no' },
                  { label: 'Si', value: 'si' },
                ]}
              />
            </>
          )}
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
