'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
// eslint-disable-next-line import/no-extraneous-dependencies
import { z } from 'zod';
// eslint-disable-next-line import/no-extraneous-dependencies
import { zodResolver } from '@hookform/resolvers/zod';
// eslint-disable-next-line import/no-extraneous-dependencies
import { useForm } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { SquarePlus } from 'lucide-react';
import axios from 'axios';
import {
  countries,
  Countries,
  PAYMENT_API_SECRET,
  PAYMENTS_API_URL,
  SevenPercentTaxInDallas,
  USCITIESNAMES,
  US_CITIES_NAMES_SHORT_CODES,
  US_STATES_OPTIONS,
  US_CITIES_OPTIONS,
  MexicoRegions,
} from '@/constants';

const calculateSubTotalAndTax = (price: number, tax: number) => {
  if (!price || !tax) {
    return { subTotal: 0, tax: 0 };
  }
  const taxPercentOfPrice = (tax / 100) * price; // like tax is 7% so 7/100=0.07,  0.07 * 10 = 0.7
  const subTotal = Number(price) + Number(taxPercentOfPrice.toPrecision(3));
  return { subTotal: Number(subTotal), tax: Number(taxPercentOfPrice).toPrecision(3) };
};

const FormSchema = z.object({
  name: z.string().min(2, {
    message: 'Name must be at least 2 characters.',
  }),
  description: z.string(),
  price: z.coerce.number(),
  subTotal: z.coerce.number(),
  tax: z.coerce.number(),
  product_key: z.string(),
  unit_key: z.string(),
  region: z.string(),
  measurementUnit: z.string(),
  taxType: z.string(),
  taxFactor: z.string(),
  taxRate: z.coerce.number(),
  withHolding: z.boolean(),
  ivaIncluded: z.boolean(),

  country: z.string().min(2, {
    message: 'Country must be at least 2 characters.',
  }),
  state: z.string(),
});

type Props = {
  open: boolean;
  setOpen: (val: boolean) => void;
};

export function ProductModal({ open, setOpen }: Props) {
  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      subTotal: 0,
      tax: 0,
      product_key: '',
      unit_key: '',
      region: '',
      measurementUnit: '',
      taxType: '',
      taxFactor: '',
      taxRate: 0,
      withHolding: false,
      ivaIncluded: false,
      country: '',
      state: '',
    },
  });

  const country = form.watch('country');
  const state = form.watch('state');
  const region = form.watch('region');
  const price = form.watch('price');
  const usCities = state ? [US_CITIES_OPTIONS[USCITIESNAMES.Dallas]] : [];
  const isCountryUSA = country === Countries['United States'];
  const isCityDallas = region === USCITIESNAMES.Dallas;

  function onSubmit(data: z.infer<typeof FormSchema>) {
    try {
      if (data.country === Countries['United States']) {
        data.region = US_CITIES_NAMES_SHORT_CODES[data.region as keyof typeof US_CITIES_NAMES_SHORT_CODES];
      }

      axios
        .post(`${PAYMENTS_API_URL}/products`, data, {
          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
            'Content-Type': 'application/json',
          },
        })
        .then((response) => {
          if (response?.status === 200) {
            form.reset();
            setOpen(false);
          }
        });
    } catch (error) {
      console.log(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          onClick={() => {
            setOpen(true);
          }}
        >
          <SquarePlus className="h-4 w-4 pr-1" />
          Crear nuevo
        </Button>
      </DialogTrigger>
      <DialogContent className="w-2/5 max-h-screen overflow-y-auto">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogHeader>
              <DialogTitle>
                {isCountryUSA ? 'Create new product/service' : 'Crear nuevo producto / servicio'}
              </DialogTitle>
            </DialogHeader>
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3"> {isCountryUSA ? 'Country' : 'país'}</FormLabel>
                    <div className="basis-2/3">
                      <Select
                        onValueChange={(value) => {
                          form.reset();
                          form.setValue('country', value);
                        }}
                        value={field.value}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a Country" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {countries.map((_country) => {
                            return (
                              <SelectItem key={_country.label} value={_country.value}>
                                {_country.label}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">
                      {' '}
                      {isCountryUSA ? 'Product/service name' : 'Nombre del producto / servicio'}
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="basis-2/3"
                        placeholder={isCountryUSA ? 'Product/service name' : 'Nombre del producto / servicio'}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">
                      {isCountryUSA ? 'Description' : 'Dscripción'}{' '}
                    </FormLabel>
                    <FormControl>
                      <Input className="basis-2/3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">
                      {isCountryUSA ? 'Unit price' : 'Precio unitario'}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        className="basis-2/3"
                        {...field}
                        onChange={(e) => {
                          if (isCountryUSA && isCityDallas) {
                            const value = e.target.value;
                            const { subTotal, tax } = calculateSubTotalAndTax(
                              Number(value),
                              SevenPercentTaxInDallas
                            );
                            form.setValue('subTotal', subTotal);
                            form.setValue('tax', Number(tax));
                          }
                          field.onChange(e);
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="subTotal"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Sub Total</FormLabel>
                    <FormControl>
                      {isCountryUSA ? (
                        <Input type="number" className="basis-2/3" {...field} disabled={true} />
                      ) : (
                        <Input type="number" className="basis-2/3" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="tax"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Tax</FormLabel>
                    <FormControl>
                      <Input type="number" className="basis-2/3" {...field} disabled={isCountryUSA} />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="product_key"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Product Code</FormLabel>
                    <FormControl>
                      <Input className="basis-2/3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="unit_key"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Unit Code</FormLabel>
                    <FormControl>
                      <Input className="basis-2/3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            {isCountryUSA && (
              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center w-full">
                      <FormLabel className="basis-1/3">State</FormLabel>
                      <div className="basis-2/3">
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a State" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {US_STATES_OPTIONS.map((_state) => {
                              return (
                                <SelectItem key={_state.label} value={_state.value}>
                                  {_state.label}
                                </SelectItem>
                              );
                            })}
                          </SelectContent>
                        </Select>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="region"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Region</FormLabel>
                    <div className="basis-2/3">
                      <Select
                        onValueChange={(_region) => {
                          if (isCountryUSA && _region === USCITIESNAMES.Dallas) {
                            const { subTotal, tax } = calculateSubTotalAndTax(
                              Number(price),
                              SevenPercentTaxInDallas
                            );
                            form.setValue('subTotal', subTotal);
                            form.setValue('tax', Number(tax));
                            form.setValue('taxRate', SevenPercentTaxInDallas);
                          }
                          field.onChange(_region);
                        }}
                        value={field.value}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a region" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isCountryUSA
                            ? Array.isArray(usCities)
                              ? usCities.map((city) => {
                                  return (
                                    <SelectItem key={city.label} value={city.value}>
                                      {city.label}
                                    </SelectItem>
                                  );
                                })
                              : null
                            : MexicoRegions.map((_region) => {
                                return (
                                  <SelectItem key={_region.label} value={_region.value}>
                                    {_region.label}
                                  </SelectItem>
                                );
                              })}
                        </SelectContent>
                      </Select>
                    </div>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="measurementUnit"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">Unit of Measurement</FormLabel>
                    <FormControl>
                      <Input className="basis-2/3" {...field} />
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            {!isCountryUSA && (
              <FormField
                control={form.control}
                name="taxType"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center w-full">
                      <FormLabel className="basis-1/3">Tipo de impuesto</FormLabel>
                      <div className="basis-2/3">
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Tipo de impuesto" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="IVA">IVA</SelectItem>
                            <SelectItem value="ISR">ISR</SelectItem>
                            <SelectItem value="IEPS">IEPS</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            )}
            {!isCountryUSA && (
              <FormField
                control={form.control}
                name="taxFactor"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center w-full">
                      <FormLabel className="basis-1/3">Factor del impuesto</FormLabel>
                      <div className="basis-2/3">
                        <Select onValueChange={field.onChange} value={field.value} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Factor del impuesto" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Tasa">Tasa</SelectItem>
                            <SelectItem value="Cuota">Cuota</SelectItem>
                            <SelectItem value="Exento">Exento</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="taxRate"
              render={({ field }) => (
                <FormItem>
                  <div className="flex flex-row items-center w-full">
                    <FormLabel className="basis-1/3">{isCountryUSA ? 'Tax Rate' : 'Tasa'}</FormLabel>
                    <FormControl>
                      {isCountryUSA ? (
                        <Input
                          type="string"
                          className="basis-2/3"
                          {...field}
                          value={isCityDallas ? SevenPercentTaxInDallas : 0}
                          disabled={true}
                        />
                      ) : (
                        <Input className="basis-2/3" {...field} />
                      )}
                    </FormControl>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            {!isCountryUSA && (
              <FormField
                control={form.control}
                name="withHolding"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center w-full">
                      <FormLabel className="basis-1/3"> Impuesto incluido en el precio</FormLabel>
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            )}
            {!isCountryUSA && (
              <FormField
                control={form.control}
                name="ivaIncluded"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center w-full">
                      <FormLabel className="basis-1/3">Retenido por tu cliente</FormLabel>
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormMessage />
                    </div>
                  </FormItem>
                )}
              />
            )}
            <DialogFooter>
              <Button type="submit">Save changes</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
