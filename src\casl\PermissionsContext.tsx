'use client';
import { createContext, useContext, useState, useEffect } from 'react';
import { AppAbility, createAbility } from './createAbility';
import { Section } from '@/actions/getPermissionSetById';
import { signOut } from 'next-auth/react';

interface ContextProps {
  ability: AppAbility | null;
}

const PermissionsContext = createContext<ContextProps>({
  ability: null,
});

export const usePermissions = (): AppAbility | null => {
  const context = useContext(PermissionsContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context.ability;
};

export const PermissionsProvider = ({
  permissions,
  children,
}: {
  permissions: Section[];
  children: React.ReactNode;
}) => {
  const [ability, setAbility] = useState<AppAbility | null>(null);

  useEffect(() => {
    if (!permissions) {
      signOut(); // logout users if permissions missing
      return;
    }

    const userAbility = createAbility(permissions); // Map permissions to CASL ability
    setAbility(userAbility);
  }, [permissions]);

  return <PermissionsContext.Provider value={{ ability }}>{children}</PermissionsContext.Provider>;
};
