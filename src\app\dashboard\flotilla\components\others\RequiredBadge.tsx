import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import React from 'react';
import { BadgeTranslations, BadgeTranslationsMX } from '../translations/statusTranslations';

const RequiredBadge = () => {
  const { isCountryUSA } = useCountry();
  const required = isCountryUSA ? BadgeTranslations.required : BadgeTranslationsMX.required;
  return (
    <span className="inline-flex items-center text-[14px] bg-red-100 border-[1.5px] border-red-500 px-2 py-1 rounded-sm">
      <span className="w-2 h-2 bg-red-600 rounded-full mr-2"></span>
      <span className="font-bold text-red-700">{required}</span>
    </span>
  );
};

export default RequiredBadge;
