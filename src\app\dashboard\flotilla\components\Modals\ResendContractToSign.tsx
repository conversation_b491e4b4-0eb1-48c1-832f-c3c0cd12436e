import { VehicleResponse } from '@/actions/getVehicleData';
import SelectInput from '@/components/Inputs/SelectInput';
// import CustomModal from '@/components/Modals/CustomModal';
import PrimaryButton from '@/components/PrimaryButton';
import ModalContainer from './ModalContainer';
import FormikContainer from '@/components/Formik/FormikContainer';
import { useState } from 'react';
import { useFormikContext } from 'formik';
import { usePathname } from 'next/navigation';
import {
  cities,
  CONTRACT_REGIONS,
  Countries,
  federalEntities,
  getFullAddressString,
  URL_API,
} from '@/constants';
import { pdf } from '@react-pdf/renderer';
import { DocumentComponent } from '@/pdfComponents/contract/ContractDocument';
import { getHeadContract } from '@/pdfComponents/contract/data/ContractHeadData';
import axios from 'axios';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useToast } from '@chakra-ui/react';
// import { DocumentComponentUS } from '@/pdfComponents/contract/ContractDocumentUS';

interface ResendContractToSignProps {
  // driver: VehicleResponse['drivers'][number];
  associateId: string;
  vehicleId: string;
  lastDriver: VehicleResponse['drivers'][number];
  vehicleDetail: VehicleResponse;
}

export default function ResendContractToSign({ vehicleDetail, lastDriver }: ResendContractToSignProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const driverCity = vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.city
    .trim()
    .toLowerCase();
  const combinedValues = {
    // ...initialValues,
    // ...initValues,
    ...lastDriver,
    contractNumber: vehicleDetail.carNumber,

    street: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.addressStreet || '',
    exterior: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.exterior || '',
    interior: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.interior || '',
    delegation: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.delegation || '',
    state: federalEntities[vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.state] || '',
    fullAddress: getFullAddressString(vehicleDetail.drivers[vehicleDetail.drivers.length - 1]),
    // state: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.state || '',
    postalCode: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.postalCode || '',
    colony: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.address.colony || '',
    // federalEntity: federalEntities[vehicleDetail.vehicleState],
    insurance: { value: 'qualitas', label: 'Qualitas' },
    city:
      cities[vehicleDetail.vehicleState] ||
      CONTRACT_REGIONS.find((region) => {
        if (
          region.value.trim().toLowerCase() === driverCity ||
          region.label.trim().toLowerCase() === driverCity
        ) {
          return {
            value: region.value,
            label: region.label,
          };
        }
        return {
          value: '',
          label: '',
        };
      }),
    paymentsDone: '',
    totalPays: '',
    policyNumber: vehicleDetail.policiesArray[vehicleDetail.policiesArray.length - 1]?.policyNumber,
    plates: vehicleDetail.carPlates?.plates,
    circulationCardNumber: vehicleDetail.circulationCard?.number,
    deliverDate: vehicleDetail.deliveredDate,
    // finalPrice: vehicleDetail.to
    finalPrice: 100000,
    downPayment: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.contractData?.downPayment || 0,
    weeklyRent: vehicleDetail.drivers[vehicleDetail.drivers.length - 1]?.contractData?.weeklyRent || 0,
    withAval: { label: 'Sin Aval', value: '0' },
    // isElectric: vehicleDetail.isElectric,
    ...vehicleDetail,
  };

  const isCountryUSA = vehicleDetail.country === Countries['United States'];
  const regenerateContractText = isCountryUSA ? 'Regenerate contract' : 'Regenerar contrato';

  return (
    <>
      <PrimaryButton
        onClick={setIsModalOpen.bind(null, true)}
        className="bg-transparent border-2 border-primaryBtn text-primaryBtn items-center "
      >
        {regenerateContractText}
      </PrimaryButton>

      {isModalOpen && (
        <ModalContainer
          title={regenerateContractText}
          onClose={() => {
            setIsModalOpen(false);
          }}
        >
          {isCountryUSA ? (
            <div>
              <p>Contract Regenerate Feature is not available for US as of now</p>
            </div>
          ) : (
            <ContractRegenerateMX
              combinedValues={combinedValues}
              vehicleDetail={vehicleDetail}
              setIsModalOpen={setIsModalOpen}
            />
          )}
        </ModalContainer>
      )}
    </>
  );
}

const ContractRegenerateMX = (props: any) => {
  const { user } = useCurrentUser();
  const toast = useToast();
  const { combinedValues, vehicleDetail, setIsModalOpen } = props;

  const totalPays =
    props.combinedValues.contractData.allPayments.length === 156
      ? undefined
      : props.combinedValues.contractData.allPayments.length;

  return (
    <FormikContainer
      initialValues={{
        ...combinedValues,
      }}
      onSubmit={async (data) => {
        if (data.withAval.value === '1') {
          if (!data.avalData?.email || !data.avalData.name || !data.avalData.phone) {
            return toast({
              title: 'Faltan datos del aval',
              description: 'Por favor llena los datos del aval',
              status: 'info',
              position: 'top',
              duration: 5000,
              isClosable: true,
            });
          }
        }

        const values = {
          ...data,
          totalPays,
          withAval: data.withAval.value === '1',
        };

        if (typeof values.city === 'string') {
          values.city = {
            label: CONTRACT_REGIONS.find((c) => c.value === values.city)?.label || cities[values.city].label,
            value: values.city,
          };
        } else {
          values.city = {
            label:
              CONTRACT_REGIONS.find((c) => c.value === values.city.value)?.label ||
              cities[values.city.value].label,
            value: values.city.value,
          };
        }

        console.log('city value', values.city.value);
        const firstName1 = values.firstName as string;
        const lastName1 = values.lastName as string;
        const extension = vehicleDetail.extensionCarNumber ? `-${vehicleDetail.extensionCarNumber}` : '';
        const isElectric = vehicleDetail.isElectric;
        const headContract = getHeadContract(values.city.value, firstName1, lastName1, isElectric);
        console.log('headContract', headContract);
        console.log('creating blob');
        const blob = await pdf(
          <DocumentComponent form={values} headContract={headContract} totalWeeks={values.totalPays} />
        ).toBlob();

        console.log('blob created', blob);
        const buffer = await blob.arrayBuffer();

        const firstName = values.firstName.split('')[0].toUpperCase() + values.firstName.slice(1);
        const lastName = values.lastName.split('')[0].toUpperCase() + values.lastName.slice(1);

        const contract = new File(
          [buffer],
          `${firstName}${lastName}-${values.contractNumber}${extension}.pdf`,
          {
            type: 'application/pdf',
          }
        );

        // download the file to the user's computer
        // const url = window.URL.createObjectURL(contract);
        // const a = document.createElement('a');
        // a.style.display = 'none';
        // a.href = url;
        // // the filename you want

        // a.download = `${firstName}${lastName}-${values.contractNumber}.pdf`;
        // document.body.appendChild(a);

        // a.click();
        // window.URL.revokeObjectURL(url);

        try {
          await axios.patch(
            `${URL_API}/contract/associated/update-file`,
            {
              contract,
              stockId: vehicleDetail._id,
            },
            {
              headers: {
                'Content-Type': 'multipart/form-data',
                Authorization: `Bearer ${user.accessToken}`,
              },
            }
          );
          toast({
            title: 'Contrato actualizado',
            description: 'El contrato se ha reemplazado correctamente',
            status: 'success',
            position: 'top',
            duration: 3000,
            isClosable: true,
          });
          setTimeout(() => {
            return window.location.reload();
          }, 3000);
        } catch (error) {
          console.log('error', error);
          console.error('Error al subir el archivo', error);

          return toast({
            title: 'Error al subir el contrato',
            description: 'Hubo un error al subir el contrato, intenta de nuevo',
            status: 'error',
            position: 'top',
            duration: 5000,
            isClosable: true,
          });
        } finally {
          setIsModalOpen(false);
        }

        return true;
      }}
      onClose={() => {
        setIsModalOpen(false);
      }}
      addFooterBtns={
        <>
          <FooterPreviewBtn />
        </>
      }
      validateOnMount={false}
      validateChanges={true}
    >
      <div className="flex flex-col gap-4">
        <SelectInput
          label="Elige si el contrato es con o sin aval"
          name="withAval"
          options={[
            { label: 'Sin Aval', value: '0' },
            { label: 'Con Aval', value: '1' },
          ]}
        />
      </div>
    </FormikContainer>
  );
};

function FooterPreviewBtn() {
  const context = useFormikContext();
  // console.log('Footer', values);
  const location = usePathname();
  const carId = location.split('/')[4];
  return (
    <div className="flex gap-4">
      <PrimaryButton
        className="bg-gray-500"
        onClick={() => {
          // const { values } = context;
          const values = context.values as any;

          const obj = {
            ...values,
            totalPays:
              values.contractData.allPayments.length === 156
                ? undefined
                : values.contractData.allPayments.length,
            withAval: values.withAval.value === '1',
          };

          if (typeof obj.city === 'string') {
            obj.city = {
              label: CONTRACT_REGIONS.find((c) => c.value === obj.city)?.label || cities[obj.city].label,
              value: obj.city,
            };
          } else {
            obj.city = {
              label:
                CONTRACT_REGIONS.find((c) => c.value === obj.city.value)?.label ||
                cities[obj.city.value].label,
              value: obj.city.value,
            };
          }

          localStorage.setItem('contractForm-' + carId, JSON.stringify(obj));
          window.open(location + '/pdf', '_blank');
        }}
      >
        Preview
      </PrimaryButton>
    </div>
  );
}
