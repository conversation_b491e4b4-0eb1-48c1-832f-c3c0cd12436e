// import getCurrentUser from '@/actions/getCurrentUser';
// import FlotillaCards from '../components/FlotillaCards';
// import VehiclesContext from '../components/vehiclesContext';
// import getAllVehiclesCache from '@/actions/getAllVehicles';

// export const metadata = {
//   title: 'Flotilla',
//   description: 'Esto es la flotilla',
// };

// export default async function Stock() {
//   const user = await getCurrentUser();
//   if (!user) return null;

//   const result = await getAllVehiclesCache();
//   console.log('result', result);
//   if (!result) return null;
//   // console.log('response page, ', result);
//   const filtered = result.filter((vehicle) => vehicle.status.toLowerCase() === 'invoiced');
//   const initialState = {
//     originalData: filtered,
//     filteredData: filtered,
//     filters: [],
//   };

//   return (
//     <VehiclesContext initialState={initialState} user={user}>
//       <FlotillaCards route="invoiced" page="Facturado (pre-stock)" />
//     </VehiclesContext>
//   );
// }
import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { listStatus } from '../activos/[id]/lib';
import { combineSearchParamsAndFilters } from '@/constants';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface InvoicedPageProps {
  searchParams: Record<string, string>;
}

export default async function InvoicedPage({ searchParams }: InvoicedPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 50,
    listStatus: ['invoiced'],
    searchParams: definitiveFilters,
    excludeStatus: listStatus,
  });

  if (!result) return null;

  return (
    <VehicleInfiniteList route="stock" page="Stock" data={result.stock} totalCount={result.totalCount} />
  );
}
