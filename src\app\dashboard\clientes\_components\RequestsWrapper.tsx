'use client';

import { ReactNode } from 'react';
import CountrySelector from './CountrySelector';
import { CountryProvider, useCountry } from '../../providers/CountryProvider';

const SectionHeader = () => {
  const { isCountryUSA } = useCountry();

  return (
    <h1 className="text-[32px] font-bold text-[#262D33]">{isCountryUSA ? 'Requests' : 'Solicitudes'}</h1>
  );
};

export const RequestsWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <>
      <CountryProvider>
        <div className="mb-4 flex flex-row justify-between items-center px-4 ">
          <SectionHeader />
          <div className="flex gap-4">
            <CountrySelector />
          </div>
        </div>
        {children}
      </CountryProvider>
    </>
  );
};
