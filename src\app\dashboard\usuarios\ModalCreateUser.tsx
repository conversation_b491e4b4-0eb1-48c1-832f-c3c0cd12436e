'use client';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  ModalCloseButton,
  ModalContent,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  useToast,
  Checkbox,
} from '@chakra-ui/react'; // Added Checkbox import
import React, { useState } from 'react';
import { createUserInvitation } from '@/validatorSchemas/createUserInvitation';
import { FormikValues } from 'formik';
import CustomInput from '@/components/Inputs/CustomInput';
import { AiOutlinePlus } from 'react-icons/ai';
import SelectInput from '@/components/Inputs/SelectInput';
import axios from 'axios';
import { MyUser } from '@/actions/getCurrentUser';
import { useSession } from 'next-auth/react';
import Swal from 'sweetalert2';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { CONTRACT_REGIONS, cities, areaOptions, Roles, Areas } from '@/constants';
import FormikContainer from '@/components/Formik/FormikContainer';
import { getRolesByArea } from '@/utils/roleHelpers';

export interface Option {
  value: string;
  label: string;
}

export default function ModalCreateUser() {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [allowedRegions, setAllowedRegions] = useState<string[]>([]);
  const [, setSelectedArea] = useState('');
  const [availableRoles, setAvailableRoles] = useState<Option[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>(''); // Added state for selected role

  const toast = useToast();

  const onChange = (option: Option) => {
    const newRegion = option.value;

    if (newRegion === 'todos') {
      const allRegions = CONTRACT_REGIONS.map((city) => city.value);
      setAllowedRegions(allRegions);
    } else {
      if (!allowedRegions.includes(newRegion)) {
        setAllowedRegions([...allowedRegions, newRegion]);
      }
    }
  };

  const handleAreaChange = (option: Option, formik: any) => {
    const selected = option.value;
    setSelectedArea(selected);

    setAvailableRoles(getRolesByArea(selected, user?.role));

    // Reset the role field in Formik
    formik.setFieldValue('role', '');
  };
  const handleRoleChange = (option: Option) => {
    setSelectedRole(option.value); // Update selected role state
  };

  const removeRegion = (region: string) => {
    const filterNewRegions = allowedRegions.filter((r) => r !== region);
    setAllowedRegions(filterNewRegions);
  };

  const filterdCities = CONTRACT_REGIONS.filter((city) => !allowedRegions.includes(city.value));
  const isSuperAdmin = user?.role === Roles.Superadmin && user?.area === Areas.Superadmin;

  const area = user?.area ?? '';
  const availableAreas = isSuperAdmin ? areaOptions : areaOptions.filter((opt) => opt.value === area);

  const url = useCurrentUrl();

  const sendInvitation = async (body: FormikValues) => {
    const data = {
      ...body,
      frontUrl: window.location.origin,
    };

    const response = await axios.post(`${url}/auth/sendInvitation`, data, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return response;
  };

  const onSubmit = async (values: FormikValues) => {
    const data = {
      ...values,
      adminId: user.id,
      role: values.role.value,
      city: values.city.value,
      area: values.area.value,
      allowedRegions,
    };
    try {
      const response = await sendInvitation(data);
      Swal.fire({
        title: 'Creación exitosa',
        text: response.data.message,
        icon: 'success',
        confirmButtonText: 'Cerrar',
      })
        .then((result) => {
          if (result.isConfirmed) {
            navigator.clipboard.writeText(response.data.password);
            const message = response.data.message;

            if (message.includes('portapapeles')) {
              toast({
                title: 'Usuario Auditor creado',
                description: message + ', cierra esta alerta para recargar la página y ver al nuevo usuario.',
                position: 'top',
                status: 'success',
                duration: null,
                isClosable: true,
                onCloseComplete: () => {
                  window.location.reload();
                },
              });
            }
          }
        })
        .finally(() => {
          setAllowedRegions([]);
        });
    } catch (error: any) {
      Swal.fire({
        title: 'Algo salió mal',
        text: error.response.data.message,
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    }
    onClose();
  };

  const defaultValues = {
    name: '',
    email: '',
    city: '',
    role: '',
    area: '',
    addRegions: '',
    blockLeadAssignment: false,
  };

  return (
    <>
      <div className="relative">
        <button className="bg-[#5800F7] text-white pl-5 pr-1 h-[40px] rounded w-[180px]" onClick={onOpen}>
          Agregar Usuario
        </button>
        <div className="absolute top-0 left-[10px] text-white flex items-center h-full mr-2">
          <AiOutlinePlus size={22} />
        </div>
      </div>

      <Modal closeOnOverlayClick={true} isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Agregar Usuario</ModalHeader>
          <ModalCloseButton />
          {/* <Box px={6} pb={6}> */}
          <FormikContainer
            initialValues={defaultValues}
            onSubmit={onSubmit}
            validatorSchema={createUserInvitation}
            validateOnMount={true}
            onClose={onClose}
            footerClassName="flex gap-3 justify-end pb-[25px] px-[25px]"
          >
            <ModalBody pb={6}>
              <div className="grid gap-2">
                <CustomInput name="name" label="Nombre" type="text" />
                <CustomInput name="email" label="Correo electronico" type="email" />
                <SelectInput name="area" label="Área" options={availableAreas} onChange={handleAreaChange} />
                <SelectInput name="role" label="Rol" options={availableRoles} onChange={handleRoleChange} />
                {selectedRole === 'agent' && (
                  <Checkbox name="blockLeadAssignment" defaultChecked={false}>
                    Block Lead Assignment
                  </Checkbox>
                )}
                <SelectInput name="city" label="Oficina" options={CONTRACT_REGIONS} />
                <SelectInput
                  name="addRegions"
                  label="Agregar Ciudades permitidas"
                  options={[{ value: 'todos', label: 'Todos' }, ...filterdCities]}
                  onChange={onChange}
                />
                <div className="flex flex-wrap gap-2 ">
                  {allowedRegions.map((region) => {
                    return (
                      <div className="flex gap-2 border-2 border-gray-400 rounded px-3 py-1 " key={region}>
                        <p>{cities[region].label}</p>
                        <div
                          onClick={() => removeRegion(region)}
                          className="cursor-pointer"
                          role="button"
                          tabIndex={0}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              removeRegion(region);
                            }
                          }}
                        >
                          X
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </ModalBody>
          </FormikContainer>
        </ModalContent>
      </Modal>
    </>
  );
}
