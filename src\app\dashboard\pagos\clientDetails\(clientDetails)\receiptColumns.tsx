'use client';

import { ColumnDef } from '@tanstack/react-table';

// eslint-disable-next-line import/no-extraneous-dependencies
import { CirclePlus, Download } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Receipt } from '../../types';
import { Copy, Mail, Link, FileText } from 'lucide-react';
import { PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { toast } from 'sonner';
import axios from 'axios';

export const receiptColumns: ColumnDef<Receipt>[] = [
  //   {
  //     accessorKey: 'customer.legal_name',
  //     header: 'CLIENTE',
  //     cell: ({ row }) => {
  //       return (
  //         <div className="flex flex-row items-center">
  //           <div className="font-medium">{row?.original?.customer?.legal_name}</div>
  //         </div>
  //       );
  //     },
  //   },
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row?.original?.id}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'total',
    header: 'TOTAL',
    cell: ({ row }) => {
      return (
        <div className="flex flex-row items-center">
          <div className="font-medium">{row?.original?.total}</div>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: 'ESTADO',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.status}</div>;
    },
  },
  {
    accessorKey: 'created_at',
    header: 'CREADO',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.created_at}</div>;
    },
  },
  {
    accessorKey: 'expires_at',
    header: 'VÁLIDO HASTA',
    cell: ({ row }) => {
      const date = new Date(row?.original?.expires_at);
      const formatted = date.toLocaleDateString();
      return <div className="font-medium">{formatted}</div>;
    },
  },
  //   {
  //     accessorKey: 'payment_method',
  //     header: 'FORMA DE PAGO',
  //     cell: ({ row }) => {
  //       return <div className="font-medium">{row?.original?.payment_method}</div>;
  //     },
  //   },
  {
    accessorKey: 'download',
    header: 'FUENTE',
    cell: ({ row }) => {
      const downloadFileHandler = () => {
        const downloadUrl = `${PAYMENTS_API_URL}/payments/receipt/download/${row?.original?.id}`;
        axios({
          url: downloadUrl, //your url
          method: 'GET',
          responseType: 'blob', // important

          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
            'Cache-control': 'no-cache',
            Pragma: 'no-cache',
            Expires: '0',
          },
        }).then((response) => {
          // create file link in browser's memory
          const href = URL.createObjectURL(response.data);

          // create "a" HTML element with href to file & click
          const link = document.createElement('a');
          link.href = href;
          link.setAttribute('download', `${row?.original?.id}.pdf`); //or any other extension
          document.body.appendChild(link);
          link.click();

          // clean up "a" element & remove ObjectURL
          document.body.removeChild(link);
          URL.revokeObjectURL(href);
        });
      };
      return (
        <div className="font-medium">
          <Download className="hover:cursor-pointer" onClick={downloadFileHandler} />
        </div>
      );
    },
  },
  {
    accessorKey: 'actions',
    header: 'ACCIONES',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cell: ({ row }) => {
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <CirclePlus />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem>
              <FileText className="h-4 w-4 pr-1" />
              Emitir factura
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link className="h-4 w-4 pr-1" />
              Asociar Recurso
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => {
                // navigator.clipboard.writeText(`${PAGOS_PAYMENT_URL}` + row.original.id);
                toast('URL de pago copiada al portapapeles');
              }}
            >
              <Copy className="h-4 w-4 pr-1" />
              Copiar al portapapeles
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Mail className="h-4 w-4 pr-1" />
              Enviar por correo
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
