import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField, HookFormRadixUISelect } from '../HookFormField';
import { useEffect, useState } from 'react';
import { FormSectionHeader } from '../FormHeaderSection';
import { FormSection } from '../FormSection';
import { HomeVisitStepsStatus, yesOrNoOptions, YesOrNoOptions } from '@/constants';
import { IStepperButtonsProps } from '../StepperButtons';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { FiPhoneCall } from 'react-icons/fi';
import { NoSelected, Steps, toastConfigs } from '.';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

enum MaritalStatus {
  Single = 'Single',
  Married = 'Married',
  Widowed = 'Widowed',
  Divorced = 'Divorced',
}

enum MaritalStatusMXMappings {
  Single = 'Soltero',
  Married = 'Casado',
  Widowed = 'Viudo',
  Divorced = 'Divorciado',
}

const maritalStatusOptions = [
  { label: MaritalStatusMXMappings.Single, value: MaritalStatus.Single },
  {
    label: MaritalStatusMXMappings.Married,
    value: MaritalStatus.Married,
  },
  { label: MaritalStatusMXMappings.Divorced, value: MaritalStatus.Divorced },
  { label: MaritalStatusMXMappings.Widowed, value: MaritalStatus.Widowed },
];

const FamilyInformationSchema = z
  .object({
    maritalStatus: z.string({
      required_error: translations.es.MaritalStatusRequired,
    }),
    dependents: z.string({
      required_error: translations.es.DependentsRequired,
    }),
    spouseOrPartnerIncome: z.string({
      required_error: translations.es.SpouseOrPartnerIncomeRequired,
    }),
    partnerSourceOfIncome: z.string({
      required_error: translations.es.PartnerSourceOfIncomeRequired,
    }),
    partnerName: z.string(),
    partnerPhone: z.string(),
    noOfDependents: z.preprocess(
      (val) => (val === '' ? 0 : Number(val)),
      z.number({
        required_error: translations.es.NoOfDependentsRequired,
      })
    ),
    dependendsInfo: z.array(
      z.object({
        dependendName: z.string({
          required_error: translations.es.DependentsNameRequired,
        }),
        dependendPhone: z.string({
          required_error: translations.es.DependentsPhoneRequired,
        }),
        dependentRelationship: z.string({
          required_error: translations.es.DependentsRelationshipRequired,
        }),
      })
    ),
  })
  .superRefine((val, ctx) => {
    if (val.maritalStatus === MaritalStatus.Married) {
      const partnerPhoneStr = val.partnerPhone;

      if (partnerPhoneStr.length > 0) {
        if (partnerPhoneStr.length !== 10) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['partnerPhone'],
            message: translations.es.PartnerPhoneLengthErrorMsg,
          });
        } else if (!/^\d+$/.test(partnerPhoneStr)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['partnerPhone'],
            message: translations.es.PhoneFormatErrorMsg,
          });
        }
      }
    }

    if (val.dependents === YesOrNoOptions.No) {
      val.noOfDependents = 0;
      return;
    }
    if (val.dependents === YesOrNoOptions.Yes) {
      if (val.noOfDependents === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: translations.es.NoOfDependentsPositiveErrorMsg,
          path: ['noOfDependents'],
        });
        return;
      }

      val.dependendsInfo.forEach((dependend, index) => {
        const dependendPhoneStr = dependend.dependendPhone;

        if (dependendPhoneStr.length > 0) {
          if (dependendPhoneStr.length !== 10) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: translations.es.PhoneLengthErrorMsg,
              path: ['dependendsInfo', index, 'dependendPhone'],
            });
          } else if (!/^\d+$/.test(dependendPhoneStr)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: translations.es.PhoneFormatErrorMsg,
              path: ['dependendsInfo', index, 'dependendPhone'],
            });
          }
        }
      });
    }
  });

interface IFamilyInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function FamilyInformation(props: IFamilyInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, personalData, homeVisit } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof FamilyInformationSchema>>({
    resolver: zodResolver(FamilyInformationSchema),
    defaultValues: {
      maritalStatus: personalData.maritalStatus || MaritalStatus.Single,
      dependents: personalData.dependents || NoSelected,
      spouseOrPartnerIncome: personalData.spouseOrPartnerIncome || NoSelected,
      partnerSourceOfIncome: personalData.partnerSourceOfIncome || '',
      partnerName: personalData.partnerName || '',
      partnerPhone: personalData.partnerPhone || '',
      noOfDependents: personalData.noOfDependents || 0,
      dependendsInfo: personalData.dependendsInfo || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'dependendsInfo',
  });

  const maritalStatus = form.watch('maritalStatus');
  const dependents = form.watch('dependents');
  const noOfDependents = form.watch('noOfDependents');
  const spouseOrPartnerIncome = form.watch('spouseOrPartnerIncome');

  useEffect(() => {
    const targetCount = noOfDependents;
    const currentCount = fields.length;

    if (currentCount < targetCount) {
      for (let i = currentCount; i < targetCount; i++) {
        append({
          dependendName: '',
          dependendPhone: '',
          dependentRelationship: '',
        });
      }
    } else if (currentCount > targetCount) {
      for (let i = currentCount; i > targetCount; i--) {
        remove(i - 1);
      }
    }
  }, [append, fields.length, noOfDependents, remove]);

  async function onSubmit(data: z.infer<typeof FamilyInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);

      let isDataCompleted = true;
      if (data.maritalStatus === MaritalStatus.Married) {
        if (data.spouseOrPartnerIncome === YesOrNoOptions.Yes) {
          isDataCompleted =
            data.partnerSourceOfIncome !== '' && data.partnerName !== '' && data.partnerPhone !== '';
        } else {
          isDataCompleted = data.partnerName !== '' && data.partnerPhone !== '';
        }
      } else if (data.dependents === YesOrNoOptions.Yes) {
        isDataCompleted =
          data.noOfDependents !== 0 &&
          data.dependendsInfo.every((val) => {
            return val.dependendName !== '' && val.dependentRelationship !== '';
          });
      } else if (data.dependents === NoSelected) {
        isDataCompleted = false;
      }

      const payload = {
        personalData: {
          maritalStatus: data.maritalStatus,
          dependents: data.dependents,
          spouseOrPartnerIncome: data.spouseOrPartnerIncome,
          partnerSourceOfIncome: data.partnerSourceOfIncome,
          partnerName: data.partnerName,
          partnerPhone: data.partnerPhone,
          noOfDependents: data.noOfDependents,
          dependendsInfo: data.dependendsInfo,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            family: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Family, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Family, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.FamilyInformation} />
      <Form {...form}>
        <form>
          <HookFormRadixUISelect
            control={form.control}
            fieldName="maritalStatus"
            selectOptions={maritalStatusOptions}
            formLabel={translations.es.MaritalStatus}
            className="w-3/6 py-2"
          />
          {maritalStatus === MaritalStatus.Married && (
            <>
              <div className="flex gap-4 py-2">
                <div className="w-3/6">
                  <HookFormRadixUISelect
                    control={form.control}
                    fieldName="spouseOrPartnerIncome"
                    selectOptions={yesOrNoOptions}
                    formLabel={translations.es.DoesYourPartnerGenerateAnyKindOfIncome}
                  />
                </div>
                {spouseOrPartnerIncome === YesOrNoOptions.Yes && (
                  <HookFormRadixUIField
                    form={form}
                    fieldName="partnerSourceOfIncome"
                    formLabel={translations.es.WhatIsYourPartnerSourceOfIncome}
                  />
                )}
              </div>

              <div className="w-3/6 py-2">
                <HookFormRadixUIField
                  form={form}
                  fieldName="partnerName"
                  formLabel={translations.es.PartnerName}
                  className="py-2"
                />

                <HookFormRadixUIField
                  form={form}
                  fieldName="partnerPhone"
                  formLabel={translations.es.PartnerPhone}
                  Icon={FiPhoneCall}
                  prefixString={'+52'}
                />
              </div>
            </>
          )}

          <HookFormRadixUISelect
            control={form.control}
            fieldName="dependents"
            selectOptions={yesOrNoOptions}
            formLabel={translations.es.DoYouHaveOtherDependents}
            className="w-3/6 py-2"
          />

          {dependents === YesOrNoOptions.Yes && (
            <div>
              <HookFormRadixUIField
                form={form}
                fieldName="noOfDependents"
                formLabel={translations.es.HowManyDependents}
                type="number"
                className="w-3/6 py-2"
              />

              <div>
                {fields.map((field, index) => {
                  return (
                    <div key={field.id}>
                      <HookFormRadixUIField
                        form={form}
                        fieldName={`dependendsInfo.${index}.dependendName`}
                        formLabel={translations.es.DependentName}
                        id={`dependendsInfo.${index}.dependendName`}
                        className="w-3/6 py-2"
                      />

                      <div className="flex gap-4 py-2">
                        <HookFormRadixUIField
                          form={form}
                          fieldName={`dependendsInfo.${index}.dependendPhone`}
                          formLabel={translations.es.Phone}
                          id={`dependendsInfo.${index}.dependendPhone`}
                          Icon={FiPhoneCall}
                          prefixString={'+52'}
                        />

                        <HookFormRadixUIField
                          form={form}
                          fieldName={`dependendsInfo.${index}.dependentRelationship`}
                          formLabel={translations.es.Relationship}
                          id={`dependendsInfo.${index}.dependentRelationship`}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </form>
      </Form>
    </FormSection>
  );
}
