import { Document, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { DeclarationList, DeclarationList2 } from './(_components)/DeclarationList';
import { ClauList } from './(_components)/Clausulas';
import { stateDependingCity } from '@/constants';
import { get20PercentOfTotalPrice } from '../utils/getTotalPrice';

interface AgreementTerminationDocumentPDFProps {
  city: string;
  firstName: string;
  lastName: string;
  contractTerminationDate: string;
  contractNumber: string;
  deliveryDate: string;
  fullAddress: string;
  weeklyRent: number;
  totalPays: number;
}

export default function AgreementTerminationDocumentPDF({
  city,
  firstName,
  lastName,
  contractTerminationDate,
  contractNumber,
  fullAddress,
  deliveryDate,
  weeklyRent,
  totalPays,
}: AgreementTerminationDocumentPDFProps) {
  const fullName = `${firstName} ${lastName}`.toUpperCase();

  const totalPrice = get20PercentOfTotalPrice(weeklyRent, totalPays);

  const state = stateDependingCity[city] || city;

  const isEdoMx = city === 'edomx';

  const stateValue = isEdoMx
    ? 'PARA EL DISTRITO FEDERAL (AHORA CIUDAD DE MÉXICO)'
    : `DEL ESTADO DE ${state.toUpperCase()}`;

  return (
    <>
      <Document>
        <Page style={styles.page} size="A4" wrap>
          <View style={styles.body}>
            <View style={{ rowGap: '30px' }}>
              <Text style={styles.text}>
                CONVENIO DE TERMINACIÓN ANTICIPADA QUE EN TÉRMINOS DE LO ESTABLECIDO EN{' '}
                {getChapterOnCity(city)} DEL CÓDIGO CIVIL {stateValue} QUE CELEBRAN POR UNA PARTE E-MKT GOODS
                DE MÉXICO, S.A.P.I. DE C.V. POR CONDUCTO DE SU REPRESENTANTE LEGAL EL C. MAIRON ESTEBAN
                SANDOVAL GÓMEZ, A QUIEN EN LO SUCESIVO SE LE DENOMINARÁ “EL ARRENDADOR” O “EL SUBARRENDADOR”
                DEPENDIENDO DE LA NATURALEZA DEL CONTRATO DEL QUE SE TRATE Y POR OTRA PARTE EL C. {fullName},
                QUIEN EN LO SUCESIVO SE LE DENOMINARÁ “EL CONDUCTOR”, RESPECTO DEL CONTRATO DE ARRENDAMIENTO O
                SUBARRENDAMIENTO DE NÚMERO {contractNumber} QUIENES SE OBLIGAN AL TENOR DE LAS SIGUIENTES
                DECLARACIONES Y CLAUSULAS:
              </Text>
            </View>
            <View style={styles.declContainer}>
              <Text style={styles.title}> D E C L A R A C I O N E S </Text>

              <DeclarationList
                deliveryDate={deliveryDate}
                fullName={fullName}
                contractNumber={contractNumber}
              />
              <DeclarationList2 fullAddress={fullAddress} />

              <ClauList
                contractTerminationDate={contractTerminationDate}
                contractNumber={contractNumber}
                city={city}
                pagareAmount={totalPrice}
              />
            </View>
            <View style={styles.content}>
              <Text style={[styles.text, styles.signature]}>______________________________</Text>
              <Text style={[styles.text, styles.signature]}>{fullName}</Text>
            </View>
          </View>
        </Page>
      </Document>
    </>
  );
}

const chapters: Record<string, string> = {
  tij: 'EL CAPÍTULO IX, ARTÍCULO 2363',
  gdl: 'EL CAPÍTULO Xll, ARTÍCULO 2144',
  pbe: 'EL CAPÍTULO VII, ARTÍCULO 1563',
  mer: 'SECCIÓN QUINTA, ARTÍCULO 1629',
  qro: 'EL CAPÍTULO IX, ARTÍCULO 2386',
  leo: 'EL CAPÍTULO IX, ARTÍCULO 1999',
  ags: 'EL CAPÍTULO IX, ARTÍCULO 2360',
  slp: 'EL CAPÍTULO IX , ARTÍCULO 2319',
  mty: 'EL CAPÍTULO IX, ARTÍCULO 2383',
  cdmx: 'EL CAPÍTULO IX, ARTÍCULO 2489',
  edomx: 'EL CAPÍTULO IX, ARTÍCULO 2489',
};

function getChapterOnCity(city: string) {
  return chapters[city] || chapters.cdmx;
}

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    alignContent: 'center',
    width: '100%',
    paddingVertical: 60,
  },
  body: {
    marginHorizontal: '10%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },

  body2: {
    marginVertical: '10%',
  },
  viewer: {
    width: '80%',
    height: '100vh',
  },
  text: {
    fontSize: 11,
    textAlign: 'justify',
    fontFamily: 'Helvetica',
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
  },
  tablesContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },

  declContainer: {
    marginTop: '20px',
    flexDirection: 'column',
    rowGap: 20,
    width: '100%',
  },

  table: {
    display: 'table' as unknown as 'flex',
    width: '25%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableHead: {
    borderStyle: 'solid',
    fontSize: 6,
    textAlign: 'center',
    flexDirection: 'row',
    backgroundColor: '#C3C6CB',
  },
  tableHeadCell: {
    textAlign: 'left',
    marginTop: 5,
    fontSize: 10,
  },

  tableHeadCellTitle: {
    textAlign: 'center',
    fontSize: 6,
    fontFamily: 'Helvetica',
    paddingVertical: 5,
  },
  tableRow: {
    flexDirection: 'row',
  },

  tableRowLast: {
    flexDirection: 'row',
  },

  tableCol: {
    width: '33.3333%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tableCol2: {
    width: '33.3333%',
    borderStyle: 'solid',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderRightWidth: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: 'auto',
    fontSize: 6,
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    rowGap: 2,
    marginTop: '25px',
    // center the content
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center', // Añade esta línea
  },
  signature: {
    textAlign: 'center', // Añade este estilo
  },
});
