'use client';
import { useState } from 'react';
import {
  <PERSON>kra<PERSON>rovider,
  Box,
  Input,
  Button,
  Text,
  VStack,
  Alert,
  AlertIcon,
  Flex,
  Link,
  Spinner,
  Heading,
  useToast,
} from '@chakra-ui/react';
import { URL_API, PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';

interface Payment {
  id: string;
  total: number;
  status: string;
  isPaid: boolean;
  paidAt: string;
}

interface PlatesData {
  plate: string;
  brand: string;
  model: string;
  carNumber: string;
  name: string;
  payments: {
    lastPayment: Payment;
    last5Payments: Payment[];
  };
}

interface UnlockPaymentResponse {
  message: string;
  link?: string;
  status?: string;
}

interface GPSErrorProps {
  payment: [
    {
      id: string;
      type: string;
      url: string;
    }
  ];
}
interface GPSProps {
  message: string;
  result: {
    status: string;
    message: string;
  };
}

const LicensePlateSearch = () => {
  const [licensePlate, setLicensePlate] = useState('');
  const [loading, setLoading] = useState(false);
  const [plates, setPlates] = useState<PlatesData | null>(null);
  const [unlockPayment, setUnlockPayment] = useState<UnlockPaymentResponse | null>(null);
  const [unlockPaymentLoading, setUnlockPaymentLoading] = useState(false);
  const [gpsStatus, setGpsStatus] = useState<GPSProps | null>(null);
  const [gpsErrorStatus, setGpsErrorStatus] = useState<GPSErrorProps | null>(null);
  const [showUnlockPayment, setShowUnlockPayment] = useState(false);
  const [showGPSStatus, setShowGPSStatus] = useState(false);
  const [gpsLoading, setGpsLoading] = useState(false); // Nuevo estado para manejar la carga del GPS
  const [error, setError] = useState<string | null>(null);

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const toast = useToast();

  const handleSearch = async () => {
    if (!licensePlate) {
      toast({
        title: 'Error',
        description: 'Por favor, ingresa una placa válida.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setLoading(true);
    setError(null);
    setPlates(null);
    setGpsStatus(null);
    setShowUnlockPayment(false);
    setShowGPSStatus(false);

    try {
      const response = await fetch(`${URL_API}/associate/get-by-plates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({ plates: licensePlate }),
      });

      if (!response.ok) {
        throw new Error('No se pudo obtener la información de la placa.');
      }

      const { data } = await response.json();
      setPlates(data);
    } catch (err) {
      console.error(err);
      setError((err as Error).message);
      toast({
        title: 'Error',
        description: (err as Error).message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleGPS = async () => {
    setShowGPSStatus(true); // Muestra la sección del GPS
    setGpsLoading(true); // Activa el estado de carga del GPS
    setGpsStatus(null); // Resetea el estado del GPS
    setGpsErrorStatus(null); // Resetea el estado de error del GPS

    try {
      const gpsResponse = await fetch(`${URL_API}/associatePayments/review-unlook-payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({ plates: licensePlate }),
      });

      if (gpsResponse.status === 402) {
        const errorData = await gpsResponse.json();
        setGpsErrorStatus(errorData);
        throw new Error('Existen pagos pendientes');
      }

      if (!gpsResponse.ok) {
        throw new Error('No se pudo obtener la información del GPS.');
      }

      const gpsData = await gpsResponse.json();
      setGpsStatus(gpsData);
    } catch (err) {
      console.error(err);
      setError((err as Error).message);
      toast({
        title: 'Error',
        description: (err as Error).message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setGpsLoading(false); // Desactiva el estado de carga del GPS
    }
  };

  const handlePayment = async () => {
    if (!plates) return;

    setUnlockPaymentLoading(true);
    setUnlockPayment(null);
    setShowUnlockPayment(true);

    try {
      const unlockResponse = await fetch(`${PAYMENTS_API_URL}/payments/unlock-payment-by-plates`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
        body: JSON.stringify({ plates: licensePlate }),
      });

      if (!unlockResponse.ok) {
        throw new Error('No se pudo obtener la información del pago de desbloqueo.');
      }
      const { data } = await unlockResponse.json();
      setUnlockPayment(data);
    } catch (err) {
      console.error(err);
      setError((err as Error).message);
      toast({
        title: 'Error',
        description: (err as Error).message,
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setUnlockPaymentLoading(false);
    }
  };

  return (
    <ChakraProvider>
      <Box className="min-h-screen bg-gray-50">
        <Box className="max-w-4xl mx-auto p-6">
          <VStack spacing={6} align="center">
            <Heading as="h1" size="xl" color="purple.600" textAlign="center">
              Verificación de pagos por placa
            </Heading>

            <Input
              placeholder="Ingresa la placa"
              value={licensePlate}
              onChange={(e) => setLicensePlate(e.target.value)}
              width="300px"
              size="lg"
              focusBorderColor="purple.500"
            />

            <Button
              className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
              textColor={'white'}
              onClick={handleSearch}
              isLoading={loading}
              loadingText="Buscando..."
              width="300px"
              size="lg"
            >
              Buscar
            </Button>

            {error && (
              <Alert status="error" width="300px" borderRadius="md">
                <AlertIcon />
                {error}
              </Alert>
            )}

            {plates && (
              <Flex direction="column" gap={6} width="100%">
                <Flex gap={6}>
                  <Box flex={1} p={4} bg="gray.100" borderRadius="md">
                    <Text fontWeight="bold">Información de la Placa:</Text>
                    <Text>Contrato: {plates.carNumber}</Text>
                    <Text>Marca: {plates.brand}</Text>
                    <Text>Modelo: {plates.model}</Text>
                    <Text>Propietario: {plates.name}</Text>
                  </Box>

                  <Box flex={1} p={4} bg="gray.100" borderRadius="md">
                    <Text fontWeight="bold">Último Pago:</Text>
                    <Text>ID: {plates.payments.lastPayment.id}</Text>
                    <Text>Total: {plates.payments.lastPayment.total}</Text>
                    <Text>Estatus: {plates.payments.lastPayment.status}</Text>
                    <Text>Pagado: {plates.payments.lastPayment.isPaid ? 'Sí' : 'No'}</Text>
                    {plates.payments.lastPayment.paidAt && (
                      <Text>
                        Pagado el: {new Date(plates.payments.lastPayment.paidAt).toLocaleDateString()}
                      </Text>
                    )}
                  </Box>
                </Flex>

                <Box p={4} bg="gray.100" borderRadius="md">
                  <Text fontWeight="bold">Pago de Desbloqueo:</Text>
                  <Button
                    className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
                    textColor={'white'}
                    onClick={handlePayment}
                    isLoading={unlockPaymentLoading}
                    loadingText="Procesando..."
                    mt={2}
                  >
                    Generar pago de desbloqueo
                  </Button>
                  {showUnlockPayment && (
                    <Box mt={4}>
                      {unlockPayment?.status || unlockPayment?.link ? (
                        <>
                          <Text>Estado del Pago: {unlockPayment.status}</Text>
                          {unlockPayment?.link && (
                            <Text>
                              Link de Pago:{' '}
                              <Link color="purple.500" href={unlockPayment.link} target="_blank" isExternal>
                                {unlockPayment.link}
                              </Link>
                            </Text>
                          )}
                        </>
                      ) : (
                        <Text>No hay pago de reactivación pendiente.</Text>
                      )}
                    </Box>
                  )}
                </Box>

                <Box p={4} bg="gray.100" borderRadius="md">
                  <Text fontWeight="bold">Desbloqueo de GPS:</Text>
                  <Button
                    className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
                    textColor={'white'}
                    onClick={handleGPS}
                    isLoading={gpsLoading}
                    loadingText="Procesando..."
                    mt={2}
                  >
                    Desbloquear GPS
                  </Button>
                  {showGPSStatus && (
                    <Box mt={4}>
                      {gpsLoading ? ( // Muestra el Spinner si el GPS está cargando
                        <Spinner size="md" />
                      ) : gpsErrorStatus ? ( // Muestra el error si existe
                        <>
                          <Text fontWeight="bold">Error: Existen pagos pendientes</Text>
                          <Box mt={2}>
                            {gpsErrorStatus.payment.map((payment, index) => (
                              <Box key={index} mt={2}>
                                <Text>
                                  <strong>ID:</strong> {payment.id}
                                </Text>
                                <Text>
                                  <strong>Tipo:</strong> {payment.type}
                                </Text>
                                <Text>
                                  <strong>URL de pago:</strong>{' '}
                                  <Link color="purple.500" href={payment.url} target="_blank" isExternal>
                                    {payment.url}
                                  </Link>
                                </Text>
                              </Box>
                            ))}
                          </Box>
                        </>
                      ) : gpsStatus ? ( // Muestra el estado del GPS si está disponible
                        <>
                          {gpsStatus.result.status === 'success' ? (
                            <Text>Desbloqueado</Text>
                          ) : (
                            <Text>Bloqueado</Text>
                          )}
                        </>
                      ) : (
                        <Text>No se pudo obtener el estado del GPS.</Text>
                      )}
                    </Box>
                  )}
                </Box>
              </Flex>
            )}
          </VStack>
        </Box>
      </Box>
    </ChakraProvider>
  );
};

export default LicensePlateSearch;
