'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import { useParams } from 'next/navigation';
import InputDate from '@/components/Inputs/InputDate';
// import { finishServiceSchema } from '@/validatorSchemas/changeStatusSchema';
import { useAdendumGenerator } from '@/zustand/modalStates';
import { useMemo, useState } from 'react';
import { StockService } from '@/actions/getStockServices';
import { differenceInWeeks, parseISO, format, isMonday } from 'date-fns';
import { nextMondayDateFns } from '@/pdfComponents/contract/data/Lunes';
import PrimaryButton from '@/components/PrimaryButton';
import { Driver, VehicleResponse } from '@/actions/getVehicleData';
import SelectInput from '@/components/Inputs/SelectInput';
import CustomInput from '@/components/Inputs/CustomInput';
import { cities } from '@/constants';
import { es } from 'date-fns/locale';
import { LegalProcess } from '@/actions/getLegalProcesses';
import { useToast } from '@chakra-ui/react';

const initialValues = {
  dateIn: '',
  dateOut: '',
  comments: '',
  lastPaymentNumber: '',
  dateFinished: '',
  serviceImgs: '',
  adendumSelect: { label: 'Agregar semanas (automatico al elegir la fecha)', value: 'add-weeks' },
  divideWeeks: '',
  status: { label: 'Taller', value: 'in-service' },
  addAmount: 0,
};

interface FinishInServiceProps {
  lastService: StockService;
  lastLegalProcess: LegalProcess;
  associate: Driver;
  vehicle: VehicleResponse;
  contractData: VehicleResponse['drivers'][number]['contractData'];
}

type AdendumOptions = 'add-weeks' | 'divide-weeks';

// const statusOptionsObj: Record<'in-service' | 'awaiting-insurance' | 'legal-process', string> = {
//   'in-service': 'Taller',
//   'awaiting-insurance': 'Esperando seguro',
//   'legal-process': 'Proceso legal',
// };

// const validators: any = {
//   'in-service': finishServiceSchema,
// };

export default function AdendunModal({
  lastService,
  associate,
  vehicle,
  contractData,
  lastLegalProcess,
}: FinishInServiceProps) {
  const adendumGeneratorModal = useAdendumGenerator();

  const [dateFinished, setDateFinished] = useState<Date | null>(null);
  // const dateInMonday = nextMonday(new Date());
  // console.log('dateInMonday', dateInMonday);
  const getTotalOfDiffWeeks = useMemo(() => {
    if (dateFinished) {
      /* PARSE DATES WITHOUT CHANGING THE DATE DAY FOR UTC HOURS CONVERTION */
      const dateFinishParsed = parseISO(dateFinished.toISOString().substring(0, 10));
      const dateInParsed = parseISO(lastService?.dateIn || lastLegalProcess?.date);

      /* VALIDATE IF THE DATES ARE MONDAY EACH ONE */
      const isInMonday = isMonday(dateInParsed);
      const isFinishMonday = isMonday(dateFinishParsed);

      // console.log('isInMonday', isInMonday);
      // console.log('isFinishMonday', isFinishMonday);

      /* IF IS NOT MONDAY, GET THE NEXT MONDAY TO COMPARE WITH MONDAY TO MONDAY  */
      const dateOutMonday = isFinishMonday ? dateFinishParsed : nextMondayDateFns(dateFinishParsed);
      const dateInMonday = isInMonday
        ? dateInParsed
        : nextMondayDateFns(lastService?.dateIn || lastLegalProcess?.date);

      const diffWeeks = differenceInWeeks(nextMondayDateFns(dateOutMonday), dateInMonday);

      return diffWeeks + (isInMonday && isFinishMonday ? 1 : 0);
    }
    return null;
  }, [dateFinished, lastService?.dateIn, lastLegalProcess?.date]);

  const [adendumOption, setAdendumOption] = useState<AdendumOptions>(
    initialValues.adendumSelect.value as AdendumOptions
  );

  const [divideWeeks, setDivideWeeks] = useState<number | null>(null);

  const [addAmount, setAddAmount] = useState<number>(0);

  const totalAcumulation = getTotalOfDiffWeeks
    ? addAmount + getTotalOfDiffWeeks * contractData.weeklyRent
    : null;

  return (
    <ModalContainer
      title={`Generar adendum `}
      onClose={adendumGeneratorModal.onClose}
      classAnimation="animate__fadeIn"
      removeScrollBar={false}
    >
      <FormikContainer
        onSubmit={async () => {}}
        showSubmitButton={false}
        onClose={adendumGeneratorModal.onClose}
        initialValues={initialValues}
        confirmBtnText={`Generar adendum `}
        footerClassName="flex gap-3 pt-[20px] justify-center"
        // validatorSchema={validators[selectedOption.value]}
        addFooterBtns={
          <>
            {dateFinished && (
              <AdendumButton
                associate={associate}
                dateIn={
                  nextMondayDateFns(lastService?.dateIn || lastLegalProcess.date)
                    .toISOString()
                    .split('T')[0]
                }
                vehicle={vehicle}
                contractData={contractData}
                dateFinished={nextMondayDateFns(dateFinished)}
                differenceWeeks={getTotalOfDiffWeeks as number}
                method={adendumOption}
                divideWeeks={divideWeeks}
                addAmount={addAmount}
              />
            )}
          </>
        }
      >
        <div className="flex flex-col gap-[20px]">
          <h3 className="font-semibold">Llena los campos para generar el adendum</h3>
          {/* <SelectInput
            label="Elije el estatus a finalizar"
            name="status"
            options={[{ label: 'Taller', value: 'in-service' }]}
            onChange={(option) => {
              setSelectedOption(option);
            }}
          /> */}
          {/* {selectedOption.value === 'in-service' && ( */}
          <SelectInput
            label="Elije el metodo de adendum"
            name="adendumSelect"
            options={[
              { label: 'Agregar semanas (automatico al elegir la fecha)', value: 'add-weeks' },
              { label: 'Dividir entre determinadas semanas', value: 'divide-weeks' },
            ]}
            onChange={(option) => {
              setAdendumOption(option.value as AdendumOptions);
            }}
          />

          <p>
            Fecha de inicio en taller:{' '}
            {/* {(format(parseISO(lastService.dateIn), "dd 'de' MMMM 'de' yyyy"), { locale: es })} */}
            {format(parseISO(lastService?.dateIn || lastLegalProcess.date), "dd 'de' MMMM 'de' yyyy", {
              locale: es,
            })}
          </p>

          <InputDate
            name="dateFinished"
            label="Fecha de entrega de vehiculo"
            onChange={(date) => {
              if (date.length < 9) return;
              const dateParsed = parseISO(date);
              setDateFinished(dateParsed);
            }}
          />

          {getTotalOfDiffWeeks && <p>Semanas en taller: {getTotalOfDiffWeeks}</p>}
          {adendumOption === 'divide-weeks' && (
            <>
              <CustomInput
                name="divideWeeks"
                label="Semanas a dividir"
                type="number"
                onChange={async (value) => {
                  if (!value) return setDivideWeeks(null);
                  else return setDivideWeeks(Number(value));
                }}
              />
              {divideWeeks && totalAcumulation && (
                <div className="flex flex-col gap-3">
                  <p>Semanas en taller: {getTotalOfDiffWeeks}</p>
                  <p>Pago semanal {contractData.weeklyRent} </p>
                  <p>Total acumulado: {totalAcumulation.toFixed(2)}</p>
                  <p>
                    Pago total de {(totalAcumulation / divideWeeks + contractData.weeklyRent).toFixed(2)} por
                    las siguientes {divideWeeks} semanas
                  </p>
                  {/* <p></p> */}
                </div>
              )}
              <CustomInput
                name="addAmount"
                label="Agregar monto (Opcional)"
                type="number"
                allowDecimals
                onChange={async (value) => {
                  if (!value) {
                    return setAddAmount(0);
                  } else return setAddAmount(Number(value));
                }}
              />
            </>
          )}
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}

interface AdendumButtonProps {
  associate: Driver;
  contractData: VehicleResponse['drivers'][number]['contractData'];
  vehicle: VehicleResponse;
  dateIn: string;
  dateFinished: Date;
  differenceWeeks: number;
  method: 'add-weeks' | 'divide-weeks';
  divideWeeks?: number | null;
  addAmount: number;
}

function AdendumButton({
  associate,
  dateFinished,
  contractData,
  vehicle,
  dateIn,
  differenceWeeks,
  method,
  divideWeeks,
  addAmount,
}: AdendumButtonProps) {
  // const { id } = useParams();
  // console.log('vehicle id: ', vehicle.vehicleState);

  const data = {
    fullName: associate.firstName + ' ' + associate.lastName,
    contract: contractData.contractNumber,
    city: cities[vehicle.vehicleState].label,
    deliveredDate: contractData.deliveredDate,
    allPayments: contractData.allPayments,
    weeklyRent: contractData.weeklyRent,
    dateFinished: dateFinished.toISOString().split('T')[0],
    dateIn,
    differenceWeeks,
    method,
    divideWeeks,
    contractId: contractData.id,
    addAmount: addAmount || 0,
  };
  const adendumGeneratorModal = useAdendumGenerator();

  const toast = useToast();

  const { id } = useParams();
  return (
    <PrimaryButton
      onClick={() => {
        // const path = `/dashboard/flotilla/adendum/${id}/${associateId}`;

        if (method === 'add-weeks' && differenceWeeks <= 0) {
          return toast({
            title: 'Error',
            // description: 'No se puede agregar 0 semanas al contrato',
            // use backticks for description
            description: `No se puede agregar ${differenceWeeks} semanas al contrato`,
            status: 'error',
            position: 'top',
            duration: 3000,
            isClosable: true,
          });
        }

        localStorage.setItem(`adendumData-${id}`, JSON.stringify(data));
        window.open(`/dashboard/flotilla/adendum/${id}`, '_blank');
        return adendumGeneratorModal.onClose();
      }}
    >
      Generar adendum
    </PrimaryButton>
  );
}
