'use client';
/* eslint-disable consistent-return */
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import axios from 'axios';
import { allCategory, URL_API } from '@/constants';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import {
  IconButton,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  useToast,
} from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import InputDate from '@/components/Inputs/InputDate';
import { useOpenFinishTaller } from '@/zustand/modalStates';
import { useEffect, useState } from 'react';
import InputMultipleFiles from '@/components/Inputs/InputMultipleFiles';
import SelectInput from '@/components/Inputs/SelectInput';
import {
  finishAwaitingInsuranceSchema,
  finishLegalProcess,
  finishServiceSchema,
} from '@/validatorSchemas/changeStatusSchema';
import { Participant, VehicleResponse } from '@/actions/getVehicleData';
import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { AiOutlineEye } from 'react-icons/ai';
import InputFile from '@/components/Inputs/InputFile';
import CustomInput from '@/components/Inputs/CustomInput';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import * as Yup from 'yup';

const statusOptionsObj: { [key: string]: string } = {
  'in-service': 'Taller',
  'awaiting-insurance': 'Seguro',
  'legal-process': 'Proceso legal',
  legal: 'Proceso legal',
  workshop: 'Taller',
  insurance: 'Seguro',
  delivered: 'Entregado',
  sold: 'Vendido',
  utilitary: 'Utilitario',
  adendum: 'Adendum',
};

const validators: { [key: string]: any } = {
  'in-service': finishServiceSchema,
  'legal-process': finishLegalProcess,
  'awaiting-insurance': finishAwaitingInsuranceSchema,
  default: Yup.object().shape({
    dateFinished: Yup.string().required('Required'),
    cancelStatus: Yup.object().required('Required'),
    canceledReason: Yup.string().when('cancelStatus', {
      is: (value: any) => value?.value === 'Si',
      then: (schema) => schema.required('Required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  }),
};
const avoidEmail = '<EMAIL>';

export default function FinishServiceModal({ vehicle }: { vehicle: VehicleResponse }) {
  const finishTallerModal = useOpenFinishTaller();
  const { id } = useParams();
  const [data] = useState<any>(JSON.parse(localStorage.getItem(`adendumData-${id}`) || 'null'));
  const router = useRouter();
  const { user } = useCurrentUser();
  const toast = useToast();
  const [nameFile, setNameFile] = useState('');
  const updateSideData = useUpdateSideData();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  const initialValues = {
    dateFinished: '',
    status: { label: 'Taller', value: vehicle.status || 'in-service' },
    returnDriver: { label: 'Selecciona', value: '' },
    reason: { label: 'Selecciona', value: '' },
    canceledReason: '',
    adendumDoc: '',
    serviceImgs: '',
    cancelStatus: {
      label: `${
        vehicle.category === 'sold' ||
        vehicle.category === 'delivered' ||
        vehicle.category === 'adendum' ||
        vehicle.category === 'utilitary'
          ? 'Si'
          : 'No'
      }`,
      value: `${
        vehicle.category === 'sold' ||
        vehicle.category === 'delivered' ||
        vehicle.category === 'adendum' ||
        vehicle.category === 'utilitary'
          ? 'Si'
          : 'No'
      }`,
    },
  };

  const [returnDriverOption, setReturnDriverOption] = useState({ label: '', value: '' });
  const [cancelStatus, setCancelStatus] = useState(initialValues.cancelStatus);
  const { adendumDigitalSignatures, loading } = useGetAdendumDigitalSignatures({ vehicle });
  const onSubmit = async (values: typeof initialValues) => {
    if (values.cancelStatus.value === 'No') {
      if (!adendumDigitalSignatures.length) {
        return toast({
          title: 'No se puede finalizar el proceso',
          description: 'No se ha generado el adendum',
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      }

      const lastAdendum = adendumDigitalSignatures[adendumDigitalSignatures.length - 1];

      const participants = lastAdendum.participants;
      if (participants.length < 1) {
        return toast({
          title: 'No se puede finalizar el proceso',
          description: 'No se ha generado el adendum',
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      }

      const clientHaveSigned = participants.filter(
        (participant) => participant.email !== avoidEmail && participant.signed
      );

      console.log('participantsFiltered', clientHaveSigned);

      if (clientHaveSigned.length < 1) {
        return toast({
          title: 'El cliente no ha firmado el adendum',
          description: 'No se puede finalizar el proceso',
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      }
    }

    const dataSend = {
      ...values,
      cancelStatus: values.cancelStatus.value === 'Si',
    };

    try {
      const response = await axios.patch(`${URL_API}/stock/changeStatus-finish-process/${id}`, dataSend, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      if (values.returnDriver.value === 'No') {
        const res = await axios.patch(
          `${URL_API}/stock/sendReadmission/${id}`,
          {
            readmissionDate: values.dateFinished,
            readmissionReason: values.reason.value,
          },
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          }
        );

        toast({
          title: res.data.message,
          description: 'Actualizando pagina...',
          duration: 3000,
          status: 'success',
          position: 'top',
        });
        await updateSideData(user);
        router.refresh();
        return router.push(
          window.location.pathname.includes('inactive')
            ? `/dashboard/flotilla/inactive/collection/${id}${
                country ? `?country=${encodeURI(country)}` : ''
              }`
            : `/dashboard/flotilla/reingresos/${id}${country ? `?country=${encodeURI(country)}` : ''}`
        );
      } else {
        toast({
          title: response.data.message,
          description: 'Actualizando pagina...',
          duration: 3000,
          status: 'success',
          position: 'top',
        });
        localStorage.removeItem(`adendumData-${id}`);
        await updateSideData(user);
        return window.location.pathname.includes('inactive')
          ? router.push(`/dashboard/flotilla/active/${id}${country ? `?country=${encodeURI(country)}` : ''}`)
          : router.refresh();
      }
    } catch (error: any) {
      toast({
        title: error.response.data.message,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
      console.log('error response data: ', error.response.data);
    } finally {
      // helpers.setSubmitting(false);
      finishTallerModal.onClose();
      // setNameFile('');
    }
  };

  const confirmSubmit = async (values: typeof initialValues) => {
    try {
      if (values.cancelStatus.value === 'No') {
        const res = await axios.get(`${URL_API}/stock/changeStatus-validate-finish-process/${id}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        // console.log('res', res.data);
        if (
          (vehicle.status !== 'awaiting-insurance' || vehicle.category !== 'insurance') &&
          (vehicle.status !== 'legal-process' || vehicle.category !== 'legal') &&
          !res.data.canFinishProcess
        )
          return toast({
            title: 'No se puede finalizar el proceso',
            description: 'No se ha generado el adendum',
            duration: 6000,
            status: 'error',
            position: 'top',
          });
      }
    } catch (error) {
      return toast({
        title: 'Hubo un error inesperado',
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    }

    return Swal.fire({
      title: '¿Estás seguro?',
      text: `El proceso ${
        cancelStatus.value === 'Si' ? 'se cancelará, finalizara' : 'finalizará'
      } y regresará a Activo`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Si, enviar',
      cancelButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };
  // const [selectedOption, setSelectedOption] = useState({ label: 'Taller', value: 'in-service' });

  if (loading) return <p>Cargando...</p>;

  // console.log('data', data);
  // const lastAdendum = adendumDigitalSignatures=[adendumDigitalSignatures.length - 1];
  // console.log('adendumDigitalSignatures', adendumDigitalSignatures);
  // console.log('adendumDigitalSignatures length', adendumDigitalSignatures.length);
  return (
    <ModalContainer
      title={`Finalizar ${statusOptionsObj[vehicle.category || vehicle.status]}`}
      onClose={finishTallerModal.onClose}
      classAnimation="animate__fadeIn"
    >
      <FormikContainer
        onSubmit={confirmSubmit}
        onClose={finishTallerModal.onClose}
        initialValues={initialValues}
        confirmBtnText={`Finalizar ${statusOptionsObj[vehicle.category || vehicle.status]}`}
        footerClassName="flex gap-3 pt-[20px] justify-center"
        validatorSchema={validators[vehicle.status] || validators.default}
        isInvalidated={cancelStatus.value === 'Si'}
      >
        <div className="flex flex-col gap-[20px]">
          {data?.dateFinished ? (
            <p>
              Fecha seleccionada en el adendum:{' '}
              {format(parseISO(data?.dateFinished), "dd 'de' MMMM 'de' yyyy", {
                locale: es,
              })}
            </p>
          ) : (
            <p>Adendum aún no generado</p>
          )}
          <SelectInput
            label="Cancelar adenmum"
            name="cancelStatus"
            options={[
              { label: 'No', value: 'No' },
              { label: 'Si', value: 'Si' },
            ].filter((option) => {
              if (
                option.value === 'No' &&
                (vehicle.category === 'sold' ||
                  vehicle.category === 'delivered' ||
                  vehicle.category === 'adendum' ||
                  vehicle.category === 'utilitary')
              ) {
                return false;
              }
              return true;
            })}
            onChange={(option) => {
              setCancelStatus(option);
            }}
          />
          {cancelStatus.value === 'No' && (
            <>
              {(vehicle.status === 'in-service' || vehicle.category === 'workshop') && (
                <>
                  <InputDate name="dateFinished" label="Fecha de entrega" />
                  <InputMultipleFiles
                    name="serviceImgs"
                    label="Subir imagenes de servicio (5 minimo)"
                    accept="all-images"
                    buttonText="Subir archivo"
                    placeholder="No mayor a 2mb"
                  />
                  <>
                    {adendumDigitalSignatures.length > 0 && (
                      <>
                        <p>Visualiza el estatus de firma del adendum</p>

                        <PopoverForSignatures
                          participants={
                            adendumDigitalSignatures[adendumDigitalSignatures.length - 1].participants
                          }
                          signed={adendumDigitalSignatures[adendumDigitalSignatures.length - 1].signed}
                        />
                      </>
                    )}
                  </>
                </>
              )}
              {(vehicle.status === 'legal-process' || vehicle.category === 'legal') && (
                <>
                  {data?.dateFinished ? (
                    <p>
                      Fecha seleccionada en el adendum:{' '}
                      {format(parseISO(data?.dateFinished), "dd 'de' MMMM 'de' yyyy", {
                        locale: es,
                      })}
                    </p>
                  ) : (
                    <p>Adendum aún no generado</p>
                  )}

                  <InputDate name="dateFinished" label="Fecha de entrega" />

                  <SelectInput
                    label="¿Regresar a el mismo conductor?"
                    name="returnDriver"
                    options={[
                      { label: 'No', value: 'No' },
                      { label: 'Si', value: 'Si' },
                    ]}
                    onChange={(option) => {
                      console.log(option);
                      setReturnDriverOption(option);
                    }}
                  />
                  {returnDriverOption.value === 'No' && (
                    <>
                      <p>Se enviará a reingresos</p>
                      <SelectInput
                        name="reason"
                        label="Seleccionar razón"
                        options={[
                          { value: 'no-payment', label: 'Falta de pago' },
                          { value: 'canceled', label: 'Cancelación de contrato' },
                          { value: 'other', label: 'Otro' },
                        ]}
                      />
                    </>
                  )}
                  {returnDriverOption.value === 'Si' && (
                    <>
                      <InputFile
                        name="adendumDoc"
                        label="Adendum firmado (Optional)"
                        accept="pdf"
                        nameFile={nameFile}
                        handleSingleSetName={setNameFile}
                        buttonText="Subir"
                        placeholder="No mayor a 3 mb"
                      />
                      {adendumDigitalSignatures.length > 0 && (
                        <PopoverForSignatures
                          participants={
                            adendumDigitalSignatures[adendumDigitalSignatures.length - 1].participants
                          }
                          signed={adendumDigitalSignatures[adendumDigitalSignatures.length - 1].signed}
                        />
                      )}
                    </>
                  )}
                </>
              )}
              {vehicle.category === allCategory.insurance && (
                <>
                  <InputDate name="dateFinished" label="Fecha de entrega" />
                  <InputFile
                    name="adendumDoc"
                    label="Adendum firmado"
                    accept="pdf"
                    nameFile={nameFile}
                    handleSingleSetName={setNameFile}
                    buttonText="Subir"
                    placeholder="No mayor a 3 mb"
                  />

                  {adendumDigitalSignatures.length > 0 && (
                    <>
                      <p>Visualiza el estatus de firma del adendum</p>
                      <PopoverForSignatures
                        participants={
                          adendumDigitalSignatures[adendumDigitalSignatures.length - 1].participants
                        }
                        signed={adendumDigitalSignatures[adendumDigitalSignatures.length - 1].signed}
                      />
                    </>
                  )}
                </>
              )}
              {vehicle.category === allCategory.sold && (
                <InputDate name="dateFinished" label="Fecha de entrega" />
              )}
              {vehicle.category === allCategory.delivered && (
                <InputDate name="dateFinished" label="Fecha de entrega" />
              )}
              {vehicle.category === allCategory.utilitary && (
                <InputDate name="dateFinished" label="Fecha de entrega" />
              )}
              {vehicle.category === allCategory.adendum && (
                <InputDate name="dateFinished" label="Fecha de entrega" />
              )}
            </>
          )}

          {cancelStatus.value === 'Si' && (
            <CustomInput name="canceledReason" type="text" label="Razón / comentario de cancelación" />
          )}
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}

type AdendumDigitalSignature = {
  participants: Participant[];
  documentID: string;
  url: string;
  signed: boolean;
  isSent: boolean;
};

function useGetAdendumDigitalSignatures({ vehicle }: { vehicle: VehicleResponse }) {
  const [data, setData] = useState<AdendumDigitalSignature[]>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useCurrentUser();
  const toast = useToast();

  const lastDriver = vehicle.drivers[vehicle.drivers.length - 1];

  useEffect(() => {
    const getAdendumData = async () => {
      try {
        const response = await axios.get(`${URL_API}/associate/adendumDigitalSignature/${lastDriver._id}`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        });
        console.log('response', response.data);
        setData(response.data.data);
      } catch (error: any) {
        toast({
          title: error.response.data.message,
          duration: 6000,
          status: 'error',
          position: 'top',
        });
      } finally {
        setLoading(false);
      }
    };

    getAdendumData();
  }, [user, lastDriver._id, toast]);

  return { adendumDigitalSignatures: data, loading };
}

export function PopoverForSignatures({
  participants,
  signed,
}: {
  participants: Participant[];
  signed: boolean;
}) {
  const signedText = 'Firmado';
  const notSignedText = 'No firmado';

  const toast = useToast();

  return (
    <Popover>
      <PopoverTrigger>
        <IconButton className="w-fit" icon={<AiOutlineEye />} aria-label="see-allowed-regions" />
      </PopoverTrigger>
      <PopoverContent minW="300px">
        <PopoverHeader fontWeight="semibold">{signed ? signedText : notSignedText}</PopoverHeader>
        <PopoverArrow />
        <PopoverCloseButton />
        <PopoverBody w="100%" zIndex={200} className="flex flex-col gap-2">
          {/* <div>hola</div> */}
          {participants.map((participant, i) => (
            <div key={i} className="w-full flex justify-between  ">
              <div className="flex gap-2 max-w-[190px] items-center">
                <p>{participant.name}</p>
                <p className="w-[75px]">{participant.signed ? '✅' : '❌'}</p>
              </div>
              <button
                className="text-[#5800F7] text-[14px]"
                type="button"
                onClick={() => {
                  navigator.clipboard.writeText(participant.urlSign);
                  toast({
                    title: 'URL Copiada',
                    description: 'URL copiada al portapapeles',
                    status: 'success',
                    position: 'top',
                    duration: 5000,
                    isClosable: true,
                  });
                }}
              >
                Copiar URL
              </button>
            </div>
          ))}
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
}
