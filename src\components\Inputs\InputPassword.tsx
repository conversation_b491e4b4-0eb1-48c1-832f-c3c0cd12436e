import React from 'react';
import { ErrorMessage, Field, useField } from 'formik';
import { CiLock } from 'react-icons/ci';

interface InputProps {
  label: string;
  name: string;
}

export default function InputPassword({ label, name }: InputProps) {
  const [, meta] = useField(name);

  const hasError = meta.touched && meta.error;

  return (
    <div className="flex flex-col">
      <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor={name}>
        {label}
      </label>
      <div className="flex">
        <Field
          type="password"
          id={name}
          name={name}
          // placeholder={placeholder}
          className={`
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            border-y-2
            border-l-2
            text-black 
            rounded-l
            px-3 
            h-[40px] 
            w-full
            outline-none 
          `}
        />
        <div
          className={`
            w-[40px] 
            h-[40px] 
            border-solid
            border-r-2
            border-y-2
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            bg-[#EAECEE]
            rounded-r 
            flex 
            items-center 
            justify-center
          `}
        >
          <CiLock color={hasError ? 'red' : '#9CA3AF'} size={20} />
        </div>
      </div>
      {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
    </div>
  );
}
