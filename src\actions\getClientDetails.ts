import { User } from '@/app/dashboard/pagos/types';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import axios from 'axios';

export async function getClientDetails(id: string): Promise<User> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/clients/${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}
