import { translations } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/translations';

export const generateMessagesForBlockSlotsErrors = (slotsErrors: Array<Record<string, any>>) => {
  let finalMessage = '';
  slotsErrors.forEach((slotError) => {
    const { startDate, startTime, endTime, slotNotFoundCode, slotIsNotAvailableToBlockCode } = slotError;
    let message = `Ranura ${startDate} de ${startTime} a ${endTime}: `;
    if (slotNotFoundCode === 'SLOT_NOT_FOUND_WITH_GIVEN_DATE_TIME') {
      message += translations.es.SLOT_NOT_FOUND_WITH_GIVEN_DATE_TIME;
    } else if (slotIsNotAvailableToBlockCode === 'SLOT_IS_NOT_AVAILABLE_TO_BLOCK') {
      message += translations.es.SLOT_IS_NOT_AVAILABLE_TO_BLOCK;
    } else {
      message += translations.es.SLOT_DETAILS_ARE_NOT_AVAILABLE;
    }
    finalMessage += message;
    finalMessage += '\n';
  });
  return finalMessage;
};

export const generateMessagesForAddSlotsErrors = (slotsErrors: Array<Record<string, any>>) => {
  let finalMessage = '';
  slotsErrors.forEach((slotError) => {
    const { startDate, startTime, endTime, slotAlreadyExistsCode } = slotError;
    let message = `Ranura ${startDate} de ${startTime} a ${endTime}: `;
    if (slotAlreadyExistsCode === 'SLOT_ALREADY_EXISTS') {
      message += translations.es.SLOT_ALREADY_EXISTS;
    }

    finalMessage += message;
    finalMessage += '\n';
  });
  return finalMessage;
};
