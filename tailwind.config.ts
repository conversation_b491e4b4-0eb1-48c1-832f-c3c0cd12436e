/* eslint-disable import/no-extraneous-dependencies */
import type { Config } from 'tailwindcss';

const config = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: '',
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      fontSize: {
        interBold32: ['32px', '32px'],
      },
      fontFamily: {
        inter: ['var(--font-inter)'],
        poppins: ['var(--font-poppins)'],
      },
      colors: {
        primaryVibrantPurpleGradient: '#6210FF',
        primaryRoyalBlue: '#2B76C2',
        primarySoftBlue: '#84A0C8',
        primaryNavyBlue: '#1D4F79',
        primarySlateBlueGray: '#586D79',
        primaryHeading: '#262D33',
        primarySlateBlue: '#374957',
        primaryBlueGray: '#5A7190',
        primaryPrussianBlue: '#0A293B',
        primaryPurple: '#5800F7',
        primaryLightPastelPink: '#FDE1E1',
        primaryDarkRed: '#B20101',
        primaryLightPastelGreen: '#E8FAEB',
        primaryDarkGreen: '#067F20',
        validationLight: '#EAECEE',
        otherAqua: '#5CAFFC',
        validationGreen: '#29CC97',
        validationYellow: '#FFAB00',
        primaryOffWhite: '#FAFAFA',
        textGray1: '#262D33',
        textGray2: '#464E5F',
        primaryGray1: '#00000040',
        primaryGray2: '#00000073',
        primaryGray3: '#6E6D6D',
        primaryLightPastelBlueGray: '#CFD8E1',
        primaryPaleBlueGray: '#A5B9C9',
        primaryLightGray: '#F6F6F6',
        primaryGrayBadge: '#EFF1F2',
        primaryMystic: '#E8EDF2',
        primaryBorderGray: '#EAECF0',
        primaryTypography: '#475467',
        primaryBackground: '#FFFFFF',
        primaryGray: '#101828',
        error: {
          text: '#E14942',
          border: '#E14942',
        },
        modalText: '#262D33',
        primaryBtn: '#5800F7',
        primaryBtnHover: '#5800F7',
        ligthRed: '#E9CBC9',
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'primary-gradient': 'linear-gradient(91.35deg, #6210FF 3.4%, #A74DF9 100%)',
        'primary-gray-1': '#0000001A',
        'primary-gray-2': '#00000073',
        'primary-gray-3': '#6E6D6D',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
} satisfies Config;

export default config;
