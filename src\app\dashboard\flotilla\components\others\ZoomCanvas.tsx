/* eslint-disable react-hooks/exhaustive-deps */
import React, { useRef, useMemo, useEffect } from 'react';

interface ZoomImageProps {
  image: string; // Asumo que la imagen es un enlace URL
}

const ZoomImage: React.FC<ZoomImageProps> = ({ image }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const observer = useRef<ResizeObserver | null>(null);
  const background = useMemo(() => new Image(), [image]);

  // This will make the component responsive.
  // Container will always adapt the size, and the canvas will be redrawn
  useEffect(() => {
    observer.current = new ResizeObserver((entries) => {
      entries.forEach(({ target }) => {
        const { width, height } = background;
        // If width of the container is smaller than image, scale image down
        if (target.clientWidth < width) {
          // Calculate scale
          const scale = target.clientWidth / width;

          // Redraw image
          if (canvasRef.current) {
            canvasRef.current.width = width * scale;
            canvasRef.current.height = height * scale;
            const ctx = canvasRef.current.getContext('2d');
            if (ctx) {
              ctx.drawImage(background, 0, 0, width * scale, height * scale);
            }
          }
        }
      });
    });

    if (containerRef.current) {
      observer.current.observe(containerRef.current);
    }

    return () => {
      if (observer.current && containerRef.current) {
        observer.current.unobserve(containerRef.current);
      }
    };
  }, [background]);

  useEffect(() => {
    background.src = image;

    if (canvasRef && canvasRef.current) {
      background.onload = () => {
        // Get the image dimensions
        const { width, height } = background;

        // Asegurarse de que canvasRef.current no sea nulo
        const canvas = canvasRef.current;
        if (canvas) {
          canvas.width = width;
          canvas.height = height;
        }

        // Draw the image
        const ctx = canvas?.getContext('2d');
        if (ctx) {
          ctx.drawImage(background, 0, 0);
        }
      };
    }
  }, [background, image]);

  return (
    <div className="w-full h-full z-[100] absolute " ref={containerRef}>
      <canvas ref={canvasRef} />
    </div>
  );
};

export default ZoomImage;
