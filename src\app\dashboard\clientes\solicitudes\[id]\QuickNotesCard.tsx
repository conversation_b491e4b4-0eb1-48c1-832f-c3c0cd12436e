'use client';
import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>ing,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Text,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  HStack,
  VStack,
  Avatar,
  Textarea,
  Flex,
  Spacer,
  Tooltip,
} from '@chakra-ui/react';
import { RefreshCcw, MoveLeft, MoveRight } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';

type NoteApiResponse = {
  notes: Array<{
    _id: string;
    content: string;
    author: {
      _id: string;
      name: string;
      email: string;
    };
    createdAt: string;
  }>;
  total: number;
  page: number;
  totalPages: number;
};

type PaginationProps = {
  total_pages: number;
  current_page: number;
  onPageChange: (i: number) => void;
};

const Pagination = ({ total_pages, current_page, onPageChange }: PaginationProps) => {
  return (
    <Box mt={4} mb={4}>
      <HStack spacing={2} justifyContent="center">
        <Button
          size="sm"
          variant="outline"
          onClick={() => onPageChange(current_page - 1)}
          isDisabled={current_page === 1}
          leftIcon={<MoveLeft />}
        ></Button>

        <Text>{'Page ' + current_page + ' of ' + total_pages}</Text>

        <Button
          size="sm"
          variant="outline"
          border={2}
          onClick={() => onPageChange(current_page + 1)}
          isDisabled={current_page === total_pages}
          rightIcon={<MoveRight />}
        ></Button>
      </HStack>
    </Box>
  );
};

export default function QuickNotesCard({ admissionRequestId }: { admissionRequestId: string }) {
  const itemsPerPage = 5;
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const [notes, setNotes] = useState<NoteApiResponse['notes']>([]);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [newNote, setNewNote] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [total, setTotal] = useState<Number>(0);

  const loadNotes = async (page: number = 1) => {
    setLoading(true);
    setError(null);
    try {
      const res = await fetch(
        `${URL_API}/notes//admission-request/${admissionRequestId}?page=${page}&limit=${itemsPerPage}`,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      if (!res.ok) throw new Error('Failed to fetch notes');

      const result: NoteApiResponse = await res.json();
      if (result.notes.length === 0) {
        throw new Error('No hay notas creadas aún');
      }
      setNotes(result.notes);
      setTotalPages(result.totalPages);
      setCurrentPage(result.page);
      setTotal(result.total);
    } catch (err: any) {
      console.error('Error fetching notes:', err);
      setError(err.message || 'Failed to load notes');
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   loadNotes(currentPage);
  // }, [currentPage]);

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      loadNotes(newPage);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewNote(e.target.value);
  };

  const handleSubmitNote = async () => {
    const content = newNote.trim();
    if (!content) return;

    setSubmitting(true);
    setError(null);

    try {
      const res = await fetch(`${URL_API}/notes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({
          content,
          admissionRequestId,
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to save note');
      }

      // Clear input
      setNewNote('');

      // Optionally refresh notes to include new one
      loadNotes(currentPage);
    } catch (err: any) {
      setError(err.message || 'Failed to submit note');
      console.error('Error submitting note:', err);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex>
          <Heading size="md" color="gray.600">
            {`Notas rápidas (${total})`}
          </Heading>
          <Spacer />
          {notes.length !== 0 && (
            <Button
              colorScheme="purple"
              leftIcon={<RefreshCcw />}
              variant={'outline'}
              onClick={() => loadNotes(1)}
              isLoading={loading}
            >
              Refrescar
            </Button>
          )}
        </Flex>
      </CardHeader>
      <CardBody>
        <Box>
          {/* Show "No Notes" message */}
          {!loading && notes.length === 0 && (
            <Button
              leftIcon={<RefreshCcw></RefreshCcw>}
              colorScheme="purple"
              variant={'outline'}
              width={'full'}
              onClick={() => {
                loadNotes();
              }}
            >
              Cargar notas
            </Button>
          )}

          {/* Show error message */}
          {error && (
            <Text color="red.500" textAlign="center" my={4}>
              {error}
            </Text>
          )}

          {/* Render notes accordion */}
          {!loading &&
            !error &&
            notes.map((note) => {
              const date = new Date(note.createdAt).toLocaleDateString();
              const time = new Date(note.createdAt).toLocaleTimeString();

              return (
                <Accordion allowMultiple key={note._id} padding={2} borderRadius={2}>
                  <AccordionItem>
                    <h2>
                      <AccordionButton _expanded={{ bg: 'gray.200' }}>
                        <Box flex="1" textAlign="left" paddingRight={2}>
                          <Flex>
                            <Text fontSize="sm">
                              {date} • {time}
                            </Text>
                            <Spacer />
                            <Tooltip label={note.author?.name || 'Unknown'}>
                              <Avatar size="xs" name={note.author?.name || 'Unknown'} />
                            </Tooltip>
                          </Flex>
                        </Box>
                        <AccordionIcon />
                      </AccordionButton>
                    </h2>
                    <AccordionPanel pb={4}>{note.content}</AccordionPanel>
                  </AccordionItem>
                </Accordion>
              );
            })}

          {/* Pagination */}
          {!loading && !error && totalPages > 1 && (
            <Pagination total_pages={totalPages} current_page={currentPage} onPageChange={handlePageChange} />
          )}

          {/* New Note Input */}
          <VStack spacing={2} mt={4}>
            <Textarea
              placeholder="Escribe tu nota aquí..."
              value={newNote}
              onChange={handleInputChange}
              maxLength={600}
              resize="none"
              h="100px"
            />
            <Flex width={'full'} paddingLeft={2} paddingRight={2}>
              <Text fontSize="sm">{newNote.length}/600</Text>
              <Spacer />
              <Button
                sx={{
                  color: 'white',
                  h: '40px',
                }}
                className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
                onClick={handleSubmitNote}
                variant={'solid'}
                isLoading={submitting}
                isDisabled={!newNote.trim() || submitting}
              >
                Publicar
              </Button>
            </Flex>
          </VStack>
        </Box>
      </CardBody>
    </Card>
  );
}
