/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-hooks/rules-of-hooks */
'use client';
import { URL_API } from '@/constants';
import { flexRender, getCoreRowModel, getPaginationRowModel, useReactTable } from '@tanstack/react-table';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useCurrentUser } from '../../providers/CurrentUserProvider';
import CellAction from './CellAction';
import { ColumnDef } from '@tanstack/react-table';
import { formatDateTime } from '@/utils/dates';
import {
  useAppointmentModal,
  useOpenAppointmentDetailModal,
  useOpenVendorServiceModal,
} from '@/zustand/modalStates';
import VendorServiceModal from './Modals/VendorServiceModal';
import { Button } from '@/components/ui/button';
import ModalContainer from './Modals/ModalContainer';
import AppointmentScheduler from '@/components/AppointmentScheduler';
import SelectInput from '@/components/Inputs/SelectInput';
import FormikContainer from '@/components/Formik/FormikContainer';
import { appointmentService } from '@/services/appointmentService';
import { useVehicleDetailData } from './Providers/VehicleDetailDataProvider';
import { cn } from '@/lib/utils';
import { useToast } from '@chakra-ui/react';
import { AppointmentEventModal } from './Modals/AppointmentEventModal';

const columns: ColumnDef<any>[] = [
  {
    header: 'ID',
    accessorKey: '_id',
    cell: ({ row }) => {
      const toast = useToast();
      return (
        <p
          className="w-[100px] cursor-pointer"
          onClick={() => {
            // copy to clipboard
            navigator.clipboard.writeText(row.original._id);
            toast({
              title: 'ID copiado al portapapeles',
              position: 'top',
              status: 'success',
              duration: 3000,
              isClosable: true,
            });
          }}
        >
          {row.original._id?.slice(0, 8) || 'N/A'}
        </p>
      );
    },
  },
  {
    header: '¿Es cita?',
    accessorKey: 'isAppointment',
    cell: ({ row }) => {
      return (
        <div className={cn('font-bold', row.original.isAppointment ? 'text-green-500' : '')}>
          {row.original.isAppointment ? 'Si' : 'No'}
        </div>
      );
    },
  },
  {
    header: 'Fecha y hora de Registro / Cita',
    accessorKey: 'createdAt',
    cell: ({ row }) => {
      const createdAtDateStr = row.getValue('createdAt');
      const isAppointment = row.original.isAppointment;
      let formatted = '';
      if (createdAtDateStr) {
        // Format like this: 2022-10-10 10:10

        // formatted = formatDateTime(row.original.createdAt);
        formatted = isAppointment
          ? formatDateTime(row.original.startTime)
          : formatDateTime(row.original.createdAt);
      }

      return <div className="font-bold">{formatted}</div>;
    },
  },
  {
    header: 'Fecha y hora de Salida / Finalización Cita',
    accessorKey: 'completedAt',
    cell: ({ row }) => {
      const completedAtDateStr = row.getValue('completedAt');
      const isAppointment = row.original.isAppointment;

      let formatted = '';
      if (completedAtDateStr) {
        // Format like this: 2022-10-10 10:10
        formatted = isAppointment
          ? formatDateTime(row.original.endTime)
          : formatDateTime(row.original.completedAt);
      }

      return <div className="font-bold">{formatted}</div>;
    },
  },
  {
    header: 'Taller',
    accessorKey: 'workshop.name',
  },
  {
    header: 'Status',
    accessorKey: 'status',
  },
  {
    header: 'Acciones',
    id: 'actions',
    cell: ({ row }) => {
      const { onOpen, setServiceData } = useOpenVendorServiceModal();
      const { onOpen: onOpenAppointmentDetail, setAppointmentData } = useOpenAppointmentDetailModal();
      const isAppointment = row.original.isAppointment;

      const maintenanceActions = [
        {
          label: () => `Ver detalle`,
          onClick: () => {
            setServiceData(row.original);
            onOpen();
          },
        },
      ];
      const appointmentActions = [
        {
          label: () => `Ver detalle de cita`,
          onClick: () => {
            setAppointmentData(row.original);
            onOpenAppointmentDetail();
          },
        },
      ];
      const actions = isAppointment ? appointmentActions : maintenanceActions;

      return <CellAction data={row.original} actions={actions} />;
    },
  },
];

export default function ServicesTableDetail() {
  const [services, setServices] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { onClose: onCloseAppointmentDetail, isOpen: isAppointmentModalOpen } =
    useOpenAppointmentDetailModal();

  const table = useReactTable({
    data: services,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const { id } = useParams<{ id: string }>();

  const { user } = useCurrentUser();
  useEffect(() => {
    const fetchMaintenancesByVehicleId = async () => {
      try {
        setIsLoading(true);
        const { data: response } = await axios.get(`${URL_API}/vendor-platform/vehicles/${id}/services`, {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
            adpt: 'true',
          },
        });
        setServices(response.data);
      } catch (error: any) {
        // console.error(error);
        console.log('error', error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchMaintenancesByVehicleId();
  }, [id, user.accessToken]);

  const associateData = useVehicleDetailData().associateData;

  // console.log('lastScheduledAppointment', lastScheduledAppointment);

  useEffect(() => {
    const fetchLastScheduledAppointment = async () => {
      try {
        const data = await appointmentService.getLastScheduledppointmentByAssociateId(associateData._id);
        if (!data.data) return;
        // insert in services array the last scheduled appointment
        const addService = {
          // _id: data.data._id,
          _id: data.data._id,
          isAppointment: true,
          createdAt: data.data.startTime,
          completedAt: data.data.endTime,
          workshop: { name: data.data.workshop.name },
          status: data.data.status,
          ...data.data,
        };
        // console.log('Last scheduled appointment', addService);
        const newServices = [addService, ...services];
        setServices(newServices);
      } catch (error: any) {
        console.error(error);
      }
    };

    fetchLastScheduledAppointment();
  }, [associateData._id, isLoading]);

  if (isLoading) {
    return <div>Cargando...</div>;
  }

  return (
    <>
      <div className="flex justify-between">
        <p className="text-[24px] mb-[5px]">Servicios / Mantenimientos</p>
        <CreateNextAppointmentModal />
      </div>
      <div
        className="
      w-full max-h-[300px] overflow-y-auto
      "
      >
        <VendorServiceModal />
        <table className="w-full h-[max-content] caption-bottom text-sm divide-y divide-gray-200">
          <thead className="[&_tr]:border-b">
            {table.getHeaderGroups().map((headerGroup, i) => (
              <tr
                key={i}
                className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted"
              >
                {headerGroup.headers.map((header, index) => {
                  return (
                    <th
                      key={index}
                      className="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>

          <tbody className="[&_tr:last-child]:border-0">
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, i) => {
                return (
                  <tr
                    className="even:bg-transparent odd:bg-gray-200 "
                    key={i}
                    data-state={row.getIsSelected() && 'selected'}
                  >
                    {row.getVisibleCells().map((cell, index, cells) => (
                      <td
                        key={cell.id}
                        className={`
                    ${index === cells.length - 1 && 'rounded-r-lg'} 
                    ${index === 0 && 'rounded-l-lg'}
                    py-2 px-4  text-left align-middle [&:has([role=checkbox])]:pr-0`}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={columns.length} className="h-24 text-center">
                  No hay resultados.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {isAppointmentModalOpen && (
        <AppointmentEventModal
          isOpen={isAppointmentModalOpen}
          onClose={onCloseAppointmentDetail}
          organizationTimezone="America/Mexico_City" // Ajusta según tu zona horaria
        />
      )}
    </>
  );
}

function CreateNextAppointmentModal() {
  const { onOpen, onClose, isOpen } = useAppointmentModal();

  const [workshopOptions, setWorkshopOptions] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [selectedWorkshop, setSelectedWorkshop] = useState<any>();
  const [selectedServiceType, setSelectedServiceType] = useState<any>();

  useEffect(() => {
    if (isOpen) {
      const fetchWorkshops = async () => {
        try {
          const { data } = await appointmentService.getAllWorkshops();
          setWorkshopOptions(data);
        } catch (error: any) {
          console.error(error);
        }
      };

      fetchWorkshops();
    }
  }, [isOpen]);

  useEffect(() => {
    if (selectedWorkshop && selectedWorkshop.name) {
      const fetchServiceTypes = async () => {
        try {
          const { data } = await appointmentService.getServiceTypes(selectedWorkshop.organizationId);
          setServiceTypes(data);
        } catch (error: any) {
          console.error(error);
        }
      };

      fetchServiceTypes();
    }
  }, [selectedWorkshop]);

  if (!isOpen) {
    return (
      <Button
        className="bg-primary text-white"
        onClick={() => {
          onOpen();
        }}
      >
        Agendar proxima cita
      </Button>
    );
  }

  return (
    <>
      <Button
        className="bg-primary text-white"
        onClick={() => {
          onOpen();
        }}
      >
        Agendar proxima cita
      </Button>
      <ModalContainer title="Calendario de disponibilidad" onClose={onClose}>
        <FormikContainer
          hideFooter
          onSubmit={async (values) => {
            console.log(values);
          }}
          initialValues={{
            workshop: selectedWorkshop
              ? { label: selectedWorkshop.name, value: selectedWorkshop._id }
              : { label: 'Selecciona', value: '' },
            serviceType: selectedServiceType
              ? { label: selectedServiceType.name, value: selectedServiceType._id }
              : { label: 'Selecciona', value: '' },
          }}
        >
          <div className="flex flex-col gap-4">
            <SelectInput
              label="Taller"
              name="workshop"
              options={workshopOptions.map((workshop: any) => ({
                label: workshop.name,
                value: workshop._id,
              }))}
              onChange={(option, form) => {
                const findWorkshop = workshopOptions.find((workshop: any) => workshop._id === option.value);
                if (findWorkshop) {
                  setSelectedWorkshop(findWorkshop);
                }
                setSelectedServiceType(null);
                form.setFieldValue('serviceType', { label: 'Selecciona', value: '' });
              }}
            />

            <SelectInput
              label="Tipo de servicio"
              name="serviceType"
              options={serviceTypes.map((serviceType: any) => ({
                label: serviceType.name,
                value: serviceType._id,
              }))}
              onChange={(option) => {
                // setSelectedServiceType(option);
                const findServiceType = serviceTypes.find(
                  (serviceType: any) => serviceType._id === option.value
                );
                if (findServiceType) {
                  setSelectedServiceType(findServiceType);
                }
              }}
            />

            {selectedWorkshop?._id && selectedServiceType?._id && (
              <AppointmentScheduler
                workshopId={selectedWorkshop._id}
                serviceType={selectedServiceType}
                onClose={onClose}
              />
            )}
          </div>
        </FormikContainer>
      </ModalContainer>
    </>
  );
}
