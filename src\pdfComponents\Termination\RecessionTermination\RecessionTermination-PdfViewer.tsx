'use client';
import { StyleSheet } from '@react-pdf/renderer';
import dynamic from 'next/dynamic';
import React from 'react';
import RecessionTerminationDocumentPDF from './RecessionTermination-DocumentPDF';

const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), {
  ssr: false,
});

const styles = StyleSheet.create({
  viewer: {
    width: '80%',
    height: '100vh',
  },
});

export default function RecessionTerminationPDFViewer() {
  return (
    <>
      <PDFViewer style={styles.viewer} showToolbar={false}>
        <RecessionTerminationDocumentPDF
          contractNumber="2012-3"
          firstName="John"
          lastName="Doe"
          deliveryDate="2022-12-12"
          city="mty"
          fullAddress="address"
          contractTerminationDate="2022-12-12"
          vin="1G1JC1249T7250077"
          year="2023"
          color="GRIS"
          model="MG5"
          brand="MG"
          plates="JN31NFF23"
        />
      </PDFViewer>
    </>
  );
}
