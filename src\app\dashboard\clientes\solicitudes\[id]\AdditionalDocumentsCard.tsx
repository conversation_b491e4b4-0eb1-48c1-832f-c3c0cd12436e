'use client';
import { useState } from 'react';
import { QueryLink } from '@/components/QueryLink';
import { Capabilities, Sections, Subsections, URL_API } from '@/constants';

import {
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  TableContainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Box,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  Icon,
  Collapse,
} from '@chakra-ui/react';
import { useTransition } from 'react';
import {
  AdmissionRequestDocumentType,
  AdmissionRequestStatus,
  RequestDocumentStatus,
  RequestDocumentsAnalysisStatus,
} from '../enums';
import { RequestDocumentTypeTranslations } from '../data';
import { FaEllipsisH } from 'react-icons/fa';
import { MdExpandMore, MdExpandLess } from 'react-icons/md';
import Swal from 'sweetalert2';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { useRouter } from 'next/navigation';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { Button } from '@/components/ui/button';
import { Loader } from 'lucide-react';
import {
  DocumentBadge,
  formatDocumentStatus,
  canUploadDocument,
  isDocumentApproved,
  canExecuteAnalysis,
  DocumentAnalysisStatus,
  DocumentAnalysisDetails,
} from './DocumentsAnalysisDetails';
import { getSuggestedStatus } from '../utils/documentAnalysisUtils';

interface RequestDocument {
  mediaId: string | null;
  status: RequestDocumentStatus;
  type: AdmissionRequestDocumentType;
  media?: {
    url: string;
    fileName: string;
    mimeType: string;
  };
  analysisResult?: {
    status: 'valid' | 'invalid' | 'pending' | 'error';
    extractedData?: any;
    validationErrors: string[];
    validationWarnings: string[];
    confidence: number;
  };
}

interface DocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus;
  documents: RequestDocument[];
}

function formatDocumentType(type: AdmissionRequestDocumentType) {
  return RequestDocumentTypeTranslations[type] || type;
}

export default function AdditionalDocumentsCard({
  requestId,
  additionalDocumentsAnalysis,
}: {
  requestId: string;
  additionalDocumentsAnalysis: DocumentsAnalysis;
  status: AdmissionRequestStatus;
}) {
  const toast = useToast();
  const router = useRouter();
  const [isTransitioning, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRow = (documentType: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [documentType]: !prev[documentType],
    }));
  };

  const isDocumentsAnalysisApproved =
    additionalDocumentsAnalysis?.status === RequestDocumentsAnalysisStatus.approved;
  const isDocumentsAnalysisRejected =
    additionalDocumentsAnalysis?.status === RequestDocumentsAnalysisStatus.rejected;
  const isAnalysisButtonEnabled =
    (additionalDocumentsAnalysis?.documents && canExecuteAnalysis(additionalDocumentsAnalysis.documents)) ||
    false;

  function handleReject(type: AdmissionRequestDocumentType) {
    Swal.fire({
      animation: false,
      title: '¿Estás seguro de querer rechazar este documento?',
      text: 'Esta acción no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, rechazar',
      cancelButtonText: 'Cancelar',
      preConfirm: async () => {
        try {
          await fetch(`${URL_API}/admission/requests/${requestId}/documents/${type}/reject`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
        } catch (error) {
          Swal.showValidationMessage(`Request failed: ${error}`);
        }
      },
    }).then((result) => {
      if (result.isConfirmed) {
        startTransition(() => {
          toast({
            title: 'Documento actualizado',
            status: 'success',
          });
          router.refresh();
        });
      }
    });
  }

  async function handleApproveDocument(type: AdmissionRequestDocumentType) {
    try {
      await fetch(`${URL_API}/admission/requests/${requestId}/documents/${type}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      startTransition(() => {
        toast({
          title: 'Documento actualizado',
          status: 'success',
        });
        router.refresh();
      });
    } catch (error) {
      Swal.showValidationMessage(`Request failed: ${error}`);
    }
    return false;
  }

  const ability = usePermissions();
  const canUploadNewDocument = canPerform(
    ability,
    Capabilities.UploadDocument,
    Sections.Clients,
    Subsections.Admissions
  );
  const canReplaceDocument = canPerform(
    ability,
    Capabilities.ReplaceDocument,
    Sections.Clients,
    Subsections.Admissions
  );
  const canApproveDocument = canPerform(
    ability,
    Capabilities.ApproveDocument,
    Sections.Clients,
    Subsections.Admissions
  );
  const canDeclineDocument = canPerform(
    ability,
    Capabilities.DeclineDocument,
    Sections.Clients,
    Subsections.Admissions
  );
  async function handleExecuteAnalysis() {
    try {
      setIsLoading(true);
      await fetch(`${URL_API}/admission/requests/${requestId}/documents-analysis/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
        body: JSON.stringify({ documentClassification: 'additional_documents' }),
      });

      startTransition(() => {
        toast({
          title: 'Análisis ejecutado correctamente',
          status: 'success',
        });
        router.refresh();
      });
    } catch (error) {
      toast({
        title: 'Error al ejecutar el análisis',
        status: 'error',
        description: String(error),
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            Documentos Adicionales
          </Heading>
          <Flex gap={3} alignItems="center">
            {
              <Button
                size="sm"
                variant="default"
                className="bg-purple-600 hover:bg-purple-700"
                disabled={!isAnalysisButtonEnabled || isTransitioning || isLoading}
                onClick={handleExecuteAnalysis}
              >
                {isLoading || isTransitioning ? (
                  <Loader className="h-4 w-4 animate-spin" />
                ) : (
                  'Ejecutar análisis'
                )}
              </Button>
            }
            {isDocumentsAnalysisApproved && (
              <Text color="green.500" fontSize="sm">
                Preaprobado
              </Text>
            )}
            {isDocumentsAnalysisRejected && (
              <Text color="red.500" fontSize="sm">
                Rechazado
              </Text>
            )}
          </Flex>
        </Flex>
      </CardHeader>
      {additionalDocumentsAnalysis && (
        <CardBody>
          <TableContainer fontSize="sm">
            <Table variant="striped">
              <Thead>
                <Tr>
                  <Th>Nombre</Th>
                  <Th>Documento</Th>
                  <Th>Estatus</Th>
                  <Th textAlign="center">Estatus Sugerido</Th>
                  <Th textAlign="right">Acciones</Th>
                </Tr>
              </Thead>
              <Tbody>
                {additionalDocumentsAnalysis.documents.map((document: RequestDocument) => (
                  <>
                    <Tr
                      key={document.type}
                      onClick={() => document.analysisResult && toggleRow(document.type)}
                      cursor={document.analysisResult ? 'pointer' : 'default'}
                      _hover={document.analysisResult ? { bg: 'gray.50' } : {}}
                    >
                      <Td p={2}>
                        <Flex alignItems="center" justifyContent="space-between">
                          <Box>{formatDocumentType(document.type)}</Box>
                          {document.analysisResult && document.analysisResult.status !== 'pending' && (
                            <DocumentAnalysisStatus
                              analysisResult={document.analysisResult}
                              expandRowIcon={
                                <Icon
                                  as={expandedRows[document.type] ? MdExpandLess : MdExpandMore}
                                  color="gray.500"
                                  fontSize="md"
                                  ml={1}
                                />
                              }
                            />
                          )}
                        </Flex>
                      </Td>
                      <Td p={2}>
                        {document.media && (
                          <a href={document.media.url} target="_blank" rel="noopener noreferrer">
                            <DocumentBadge fileName={document.media.fileName} />
                          </a>
                        )}
                      </Td>
                      <Td p={2}>{formatDocumentStatus(document.status)}</Td>
                      <Td p={2} textAlign="center">
                        {getSuggestedStatus(document.analysisResult?.status) && (
                          <Text color={getSuggestedStatus(document.analysisResult?.status)?.color}>
                            {getSuggestedStatus(document.analysisResult?.status)?.text}
                          </Text>
                        )}
                      </Td>
                      <Td p={2} textAlign="right" pr={8}>
                        {
                          <Menu>
                            <MenuButton
                              onClick={(e) => e.stopPropagation()} // Prevent row toggle when clicking menu
                            >
                              <FaEllipsisH />
                            </MenuButton>
                            <MenuList onClick={(e) => e.stopPropagation()}>
                              {!canUploadDocument(document.status) &&
                                !isDocumentApproved(document.status) &&
                                canApproveDocument && (
                                  <MenuItem
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleApproveDocument(document.type);
                                    }}
                                    sx={{
                                      _hover: {
                                        bg: 'gray.50',
                                      },
                                    }}
                                  >
                                    Preaprobar
                                  </MenuItem>
                                )}
                              {!canUploadDocument(document.status) && canReplaceDocument && (
                                <QueryLink
                                  query={{
                                    dialog: 'upload-document',
                                    documentType: document.type,
                                    actionType: 'replace',
                                  }}
                                >
                                  <MenuItem
                                    sx={{
                                      _hover: {
                                        bg: 'gray.50',
                                      },
                                    }}
                                  >
                                    Reemplazar documento
                                  </MenuItem>
                                </QueryLink>
                              )}
                              {!canUploadDocument(document.status) && canDeclineDocument && (
                                <MenuItem
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleReject(document.type);
                                  }}
                                  color="red.500"
                                  sx={{
                                    _hover: {
                                      bg: 'gray.50',
                                    },
                                  }}
                                >
                                  Rechazar
                                </MenuItem>
                              )}
                              {canUploadDocument(document.status) && canUploadNewDocument && (
                                <QueryLink
                                  query={{
                                    dialog: 'upload-document',
                                    documentType: document.type,
                                    actionType: 'upload',
                                  }}
                                >
                                  <MenuItem
                                    sx={{
                                      _hover: {
                                        bg: 'gray.50',
                                      },
                                    }}
                                  >
                                    Subir documento
                                  </MenuItem>
                                </QueryLink>
                              )}
                            </MenuList>
                          </Menu>
                        }
                      </Td>
                    </Tr>
                    {document.analysisResult && expandedRows[document.type] && (
                      <Tr>
                        <Td colSpan={5} p={0}>
                          <Collapse in={expandedRows[document.type]} animateOpacity>
                            <DocumentAnalysisDetails analysisResult={document.analysisResult} />
                          </Collapse>
                        </Td>
                      </Tr>
                    )}
                  </>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </CardBody>
      )}
    </Card>
  );
}
