/* eslint-disable @next/next/no-img-element */
'use client';
import React, { useState } from 'react';
import ZoomableImage from './ZoomableImage';
import { AiOutlineClose, AiFillInfoCircle } from 'react-icons/ai';
import { FiDownload } from 'react-icons/fi';
import { handleDownload } from '@/utils/handle-download';
import { cn } from '@/lib/utils';
import { Box, Link, useToast } from '@chakra-ui/react';

interface ZoomImageProps {
  imageUrl: string;
  w?: string;
  h?: string;
  objectFit?: 'contain' | 'cover' | '';
  name?: string;
}

const ZoomImage = ({ imageUrl, w, h, objectFit = 'contain', name }: ZoomImageProps) => {
  const [zoomActive, setZoomActive] = useState(false);
  const [downloading, setDownloading] = useState(false);

  const handleZoomClick = () => {
    setZoomActive(true);
  };

  const handleCloseZoom = () => {
    setZoomActive(false);
  };

  const element = document.getElementById('modal-container');

  const isModalOpen = !!element;

  const position = isModalOpen
    ? parseInt(element?.getBoundingClientRect().top.toString()) * -1 + 70 + 'px'
    : '70px';

  const toast = useToast();

  return (
    <>
      {zoomActive && (
        <div
          className="
            flex 
            fixed 
            top-0 
            left-0 
            z-50 
            justify-center 
            w-full 
            h-full 
            bg-black 
            bg-opacity-50"
        >
          <div className="h-full flex justify-end relative" style={{ top: position }}>
            <div
              className="
                fixed 
                z-[100]
                flex gap-3
                items-center
              "
              style={{ top: position }}
            >
              <button
                disabled={downloading}
                // className="rounded-full bg-[#5800F7] p-2 flex justify-center items-center cursor-pointer "
                className={cn(
                  'rounded-full  p-2 flex justify-center items-center cursor-pointer',
                  downloading ? 'bg-gray-500' : 'bg-[#5800F7]'
                )}
                onClick={async () => {
                  setDownloading(true);
                  try {
                    await handleDownload(imageUrl, name as string);

                    toast({
                      title: 'Archivo descargado correctamente.',
                      status: 'success',
                      duration: 5000,
                      position: 'top',
                      isClosable: true,
                    });
                  } catch (error: any) {
                    console.error('Error al descargar el archivo:', error.message);
                    const status = error.response?.status;

                    if (status === 403) {
                      return toast({
                        title: 'No se pudo descargar el archivo debido a que la url del archivo expiró.',
                        description: 'Por favor, refresca la página y vuelve a intentarlo.',
                        status: 'error',
                        duration: 5000,
                        position: 'top',
                        isClosable: true,
                      });
                    }
                    toast({
                      // title: 'Hubo algun error desconocido al descargar el archivo.',
                      // description: 'Intenta descargarlo manualmente con esta url: ',
                      render: ({ onClose }) => {
                        return (
                          <Box className="py-3 pl-[40px] pr-[40px] bg-red-500 text-white rounded-md flex flex-col gap-1 relative">
                            <AiFillInfoCircle size={18} className="absolute left-2 top-4 " />

                            <button className="absolute top-2 right-2" onClick={onClose}>
                              <AiOutlineClose size={20} color="white" strokeWidth="2px" />
                            </button>

                            <p className="font-bold text-[18px] ">
                              Hubo un error desconocido al descargar el archivo.
                            </p>
                            <p>Intenta descargarlo manualmente con esta url:</p>
                            <Link
                              href={imageUrl}
                              target="_blank"
                              rel="noreferrer"
                              // className="text-blue-500"
                            >
                              Click aquí
                            </Link>
                          </Box>
                        );
                      },
                      status: 'error',
                      duration: 6000,
                      position: 'top',
                      isClosable: true,
                    });
                  } finally {
                    setTimeout(() => {
                      setDownloading(false);
                    }, 1200);
                  }
                }}
              >
                <FiDownload size={26} color="white" />
              </button>
              <div
                className="
                  w-[60px] h-[60px] 
                  flex
                  gap-3
                  justify-center
                  items-center
                  border-solid 
                  border-white 
                  rounded-full
                  border-[4px]
                  cursor-pointer
                "
                onClick={handleCloseZoom}
              >
                <AiOutlineClose size={30} color="white" />
              </div>
            </div>
            <ZoomableImage imageUrl={imageUrl} topPosition={position} />
            {/* <div
              style={{ width: '100%', height: '100%', top: position }}
            > */}
            {/* </div> */}
          </div>
        </div>
      )}

      <div
        className={`
          bg-${objectFit}
          bg-center
          bg-no-repeat
          cursor-pointer
          rounded
        `}
        style={{
          backgroundImage: `url(${imageUrl})`,
          width: `${w ? w : '128px'}`,
          height: `${h ? h : '80px'}`,
          backgroundRepeat: 'no-repeat',
        }}
        onClick={handleZoomClick}
      />
    </>
  );
};

export default ZoomImage;
