import { ErrorMessage, Field, useField } from 'formik';

interface InputProps {
  label: string;
  name: string;
  placeholder: string;
  countryCode?: string;
}

export default function InputPhone({ label, name, placeholder, countryCode }: InputProps) {
  const [field, meta] = useField(name);

  const hasError = meta.touched && meta.error;
  const reg = new RegExp('^[0-9]*$');

  return (
    <div className="flex flex-col">
      <label className="block text-gray-700 mb-2" htmlFor={name}>
        {label}
      </label>
      <div className="flex relative items-center">
        <div
          className={`
            w-[40px] 
            h-[40px] 
            border-solid
            border-l-2
            border-r-0
            border-y-2
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            bg-[#EAECEE]
            text-[#9CA3AF]
            text-[14px]
            rounded-l 
            px-3
            pt-1
            flex 
            items-center 
            justify-center
          `}
        >
          {countryCode ? countryCode : '+52'}
          {/* <BsCurrencyDollar color={hasError ? 'red' : '#9CA3AF'} size={20} /> */}
        </div>
        <Field
          type="string"
          id="emailLogin"
          value={field.value}
          name={name}
          placeholder={placeholder}
          onChange={(event: any) => {
            if (!reg.test(event.target.value)) return;
            field.onChange(event);
          }}
          onKeyDown={(event: any) => {
            if (['e', 'E', '+', '-', '.'].includes(event.key)) return event.preventDefault();
            return null;
          }}
          className={`
            border ${hasError ? 'border-red-500' : 'border-[#9CA3AF]'}
            border-y-2
            border-r-2
            text-black 
            rounded-r
            px-3 
            h-[40px] 
            w-full
            outline-none 
          `}
        />
        {/* <div className="absolute right-0 pr-3">MX</div> */}
      </div>
      {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
    </div>
  );
}
