/* eslint-disable consistent-return */
/* eslint-disable prettier/prettier */
/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import { VehicleResponse } from '@/actions/getVehicleData';
import CustomInput from '@/components/Inputs/CustomInput';
import InputFile from '@/components/Inputs/InputFile';
import CustomModal from '@/components/Modals/CustomModal';
import SelectInput from '@/components/Inputs/SelectInput';
import { /* Editable, EditableInput, EditablePreview, */ Grid, Menu, MenuButton, MenuItem, MenuList, Tooltip, useToast } from '@chakra-ui/react';
import axios from 'axios';
import { FormikValues } from 'formik';
import Image from 'next/image';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { MdPictureAsPdf } from 'react-icons/md';
import { SlOptions } from 'react-icons/sl';
import { useDetail } from '../detailContext';
import { Countries, MEXICAN_CURRENCY, URL_API, US_CURRENCY, allCategory, allStatus, colors, editTextBtn, platformOptions, platforms, svgColors, teamLeads, DocumentCategory, Capabilities, Sections, Subsections } from '@/constants';
import DocumentDisplay from '@/components/DocumentDisplay';
import { getDocumentInfoMessage } from '@/utils/documentInfoMessages';
import { differenceInDays, differenceInWeeks, format, parseISO } from 'date-fns';
import { ISODateString } from 'next-auth';
import { siguienteLunes } from '@/pdfComponents/contract/data/Lunes';
import moment from 'moment';
import DischargeModal from '../Modals/DischargeModal';
import DetailDischargeModal from '../Modals/DetailDischargeModel';
import { extractDataFromDocument, showOCRToast } from '@/utils/ocrApi';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useFormikContext } from 'formik';
import OCRConfirmationModal from '@/components/Modals/OCRConfirmationModal';
import ModalContainer from '../Modals/ModalContainer';
import { useContractDetailState } from '@/hooks/useContractDetail';
import { es } from 'date-fns/locale';
import VehicleSVGV2 from '@/svgsComponents/VehicleSVG_V2';
import { useAdendumGenerator, useOpenChangeStatusModal, useOpenDetailDischargeModal, useOpenDischargeModal, useOpenFinishOverHaulingModal, useOpenFinishTaller, useOpenOverHaulingModal, useOpenServiceDetail } from '@/zustand/modalStates';
import ChangeStatus from '../Modals/ChangeStatus';
import { availableStatus, colorAndTextOnCategory, colorAndTextOnSubCategory } from '@/components/stockCard/Card';
import FinishServiceModal from '../Modals/FinishInService';
import AdendunModal from '../Modals/AdendumModal';
import OverHaulingModal from '../Modals/OverHaulingModal';
import FinishOverHaulingModal from '../Modals/FinishOverHaulingModal';
import { Leaf } from 'lucide-react';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { VehicleCategoryTranslations, VehicleCategoryTranslationsMX, VehicleSubCategoryTranslations, VehicleSubCategoryTranslationsMX } from '../translations/statusTranslations';
import { DateTime } from 'luxon';
import { receptionDateSchema } from '@/validatorSchemas/createStockSchema';
import CustomDatePicker from '@/components/Inputs/CustomDatePicker';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import { ShowCalculatedWeeks } from '@/components/Modals/ShowCalculatedWeeks';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { getStatusLabel } from '../Modals/PhysicalStatusUpdateModal'; // Import the helper function

// QR Scan Imports
import { initiateQrScanAction } from '@/services/vehicleQrService';
import PhysicalStatusUpdateModal from '../Modals/PhysicalStatusUpdateModal'; // Assuming this will be the new modal component
import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import { PhysicalVehicleStatus } from '@/constants';

interface VehicleDetailProps {
  data: VehicleResponse;
  associateData?: VehicleResponse['drivers'][number];
}

interface QrModalState {
  isOpen: boolean;
  vehicleId: string | null;
  currentStatus: PhysicalVehicleStatus | string | null;
  nextStatusToDisplay: PhysicalVehicleStatus | string | null;
  nextStepOptions?: { value: PhysicalVehicleStatus; label: string }[];
  confirmationToken: string | null;
  message: string | null;
  actionAvailable: boolean;
}

export default function VehicleDetail({ data, associateData }: VehicleDetailProps) {
  const ability = usePermissions();
  const updateSideData = useUpdateSideData();
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const { user } = useCurrentUser();
  // const { setVehicleData } = useVehicleDetailData();
  const toast = useToast();
  const { country } = useCountry();

  const [qrModalState, setQrModalState] = useState<QrModalState>({
    isOpen: false,
    vehicleId: null,
    currentStatus: null,
    nextStatusToDisplay: null,
    nextStepOptions: [],
    confirmationToken: null,
    message: null,
    actionAvailable: false,
  });

  const initialValues = useMemo(() => ({
    carNumber: data.carNumber,
    model: data.model,
    brand: data.brand,
    version: data.version,
    year: data.year,
    color: colors.find((color) => color.value === data.color) || { value: data.color, label: data.color },
    isElectric: data.isElectric ? { value: 'true', label: 'Si' } : { value: 'false', label: 'No' },
    platform: platformOptions.find((platform) => platform.value === data.platform) || { value: '', label: '' },
    vin: data.vin,
    owner: data.owner,
    billAmount: data.billAmount,
    km: data.km || 0,
    bill: '',
    billNumber: data.billNumber || '',
    billDate: data.billDate,
    receptionDate: data.receptionDate || '',
    transferredTo: data.transferredTo,
    purchaseAgreement: data.purchaseAgreement,
    soldInvoicePdf: data.soldInvoicePdf,
  }), [data]);

  useEffect(() => {
    const vehicleId = data._id; // Get vehicleId from prop    

    let qrScanToken: string | null = null;
    if (typeof window !== 'undefined') { // Ensure window is available (client-side)
        const paramsFromWindow = new URLSearchParams(window.location.search);
        qrScanToken = paramsFromWindow.get('qr_scan_token');

    } else {
        console.log('[QR EFFECT] window object not available yet.');
    }

    
    if (qrScanToken && vehicleId && user?.accessToken) {
      if (qrModalState.isOpen || qrModalState.vehicleId === vehicleId) {
          return;
      }

      initiateQrScanAction(vehicleId, qrScanToken, user.accessToken)
        .then(response => {
          setQrModalState({
            isOpen: true,
            vehicleId: vehicleId,
            currentStatus: response.currentPhysicalStatus || 'N/A',
            nextStatusToDisplay: response.nextPhysicalStatusToDisplay || null,
            nextStepOptions: response.nextStepOptions || [],
            confirmationToken: response.confirmationToken || null,
            message: response.message,
            actionAvailable: response.actionAvailable,
          });
          // Clean URL after state is set
          router.replace(pathname, { scroll: false });
        })
        .catch(error => {
          setQrModalState(prev => ({ 
              ...prev, 
              vehicleId: vehicleId, // Mark as processed even on error
              message: error.message || 'Failed to process QR scan.', 
              actionAvailable: false, 
              isOpen: true, 
          })); 
          // Clean URL after state is set, even on error
          router.replace(pathname, { scroll: false }); 
        });
    } else {
      if (!qrScanToken) console.log('[QR EFFECT] Reason: qrScanToken is missing or null (from window.location).');
      if (!vehicleId) console.log('[QR EFFECT] Reason: vehicleId is missing or null.');
      if (!user?.accessToken) console.log('[QR EFFECT] Reason: user.accessToken is missing or null.');
    }
  // Removed searchParams from dependency array as we are reading from window now.
  // Kept user?.accessToken, data._id etc. Also added modal state checks.
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data._id, user?.accessToken, pathname, router, qrModalState.isOpen, qrModalState.vehicleId]);

  const billAmount = Number(data.billAmount);
  const stepNumber = data.step.stepNumber;
  const isCarBlocked = data.isBlocked;
  const countryQueryParam = searchParams ? searchParams.get('country') : '';

  const isMexico = countryQueryParam === 'Mexico';
  const categoryTranslations = isMexico ? VehicleCategoryTranslationsMX : VehicleCategoryTranslations;
  const subCategoryTranslations = isMexico
    ? VehicleSubCategoryTranslationsMX
    : VehicleSubCategoryTranslations;

  const receptionDateWarning = country.value === Countries['United States'] 
    ? 'Removing the reception date will move the vehicle to the "In preparation" category. Are you sure you want to continue?' 
    : 'Al eliminar la fecha de recepción, el vehículo pasará a la categoría "En preparación". ¿Está seguro de que desea continuar?';

  const isCountryUs = data?.country === Countries['United States'];
  const roundedNumber =
    billAmount.toLocaleString(isCountryUs ? "en-us" : "es-MX", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }) + (isCountryUs ? ` ${US_CURRENCY}` : ` ${MEXICAN_CURRENCY}`);


  // const modalDetail = useOpenDetailModal();
  const dischargeModal = useOpenDischargeModal();
  const dischargeDetailModal = useOpenDetailDischargeModal();

  const changeStatus = useOpenChangeStatusModal();
  const finishTallerModal = useOpenFinishTaller();
  const adendumGeneratorModal = useAdendumGenerator();

  const overHaulingModal = useOpenOverHaulingModal();
  const finishOverHauling = useOpenFinishOverHaulingModal();

  const { handleIsAssocOpen } = useDetail();

  const onSubmit = useCallback(
    async (values: FormikValues) => {
      // setIsLoading(true);
      const form = {
        ...values,
        km: Number(values.km || 0),
        billAmount: Number(values.billAmount),
        transferredTo: values?.transferredTo?.value || null,
        isElectric: values.isElectric.value === 'true',
        platform: values.platform.value,
        color: values.color.value,
        historyData: {
          userId: user._id,
          step: 'DESCRIPCIÓN ACTUALIZADA',
          description: '', // Esto se actualizará para mostrar que cambio exacto se hizo
        },
      };
      const response = await axios.patch(`${URL_API}/stock/update/description/${data._id}`, form, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `bearer ${user.accessToken}`,
        },
      })

      setTimeout(async () => {
        if (initialValues.receptionDate && !values.receptionDate) {
          await updateSideData(user)
          router.refresh();
        } else {
          window.location.reload(); // Router.refresh() is somehow not working will try to fix it later
        }
      }, 3050);
      return response;
    },
    [user, data._id, initialValues.receptionDate, router, updateSideData]
  );

  const canToRecovery = canPerform(ability,Capabilities.ToRecovery, Sections.Fleet, Subsections.General);
  const canChangeStatus = canPerform(ability,Capabilities.ChangeStatus, Sections.Fleet, Subsections.General);

  const dropDownOptions = useMemo(() => {
    const options = [];
    if (data.category === allCategory.withdrawn) options.push({ label: 'Ver Detalles', onClick: () => dischargeDetailModal.onOpen() });

    if (user.role === "superadmin" && data.step.stepNumber > 4 && canToRecovery)
      options.push({ label: 'Dar de baja', onClick: () => dischargeModal.onOpen() });

    if (data.step.stepNumber >= 4 && data.vehicleStatus === allStatus.active && canChangeStatus)
      options.push({ label: 'Cambiar status', onClick: () => changeStatus.onOpen() });

    if ((data.category === allCategory.workshop || data.category === allCategory.legal) && user.role === 'superadmin' && data.step.stepNumber >= 4)
      options.push({ label: 'Generar adendum', onClick: () => adendumGeneratorModal.onOpen() });

    if ((data.category === allCategory.stock) && user.role === 'superadmin')
      options.push({ label: 'Enviar a Revisión', onClick: () => overHaulingModal.onOpen() });

    if ((data.category === allCategory.revision) && user.role === 'superadmin')
      options.push({ label: 'Finalizar proceso de revisión', onClick: () => finishOverHauling.onOpen() });

    if (availableStatus.includes(data.category) && user.role === 'superadmin' && data.step.stepNumber >= 4)
      options.push({ label: 'Finalizar proceso de estatus', onClick: () => finishTallerModal.onOpen() });

    else options.push({ label: '', onClick: () => { } });
    return options
  },
    [
      data,
      dischargeModal,
      dischargeDetailModal,
      adendumGeneratorModal,
      user.role,
      changeStatus,
      finishTallerModal,
      overHaulingModal,
      finishOverHauling,
    ])

  const canEditVehicle = canPerform(ability, Capabilities.EditVehicle, Sections.Fleet, Subsections.General);
  const contractDetailModal = useContractDetailState();
  const serviceDetailModal = useOpenServiceDetail();

  return (
    <>

      {contractDetailModal.isOpen && <ContractDetailModal />}
      {serviceDetailModal.isOpen && <ServiceDetail />}
      {dischargeModal.isOpen && <DischargeModal />}
      {changeStatus.isOpen && <ChangeStatus />}
      {adendumGeneratorModal.isOpen &&
        <AdendunModal
          lastService={data.stockServices[data.stockServices.length - 1]}
          lastLegalProcess={data.legalProcesses[data.legalProcesses.length - 1]}
          associate={data.drivers[data.drivers.length - 1]}
          contractData={data.drivers[data.drivers.length - 1].contractData}
          vehicle={data}
        />
      }
      {finishTallerModal.isOpen && <FinishServiceModal vehicle={data} />}
      {overHaulingModal.isOpen && <OverHaulingModal />}
      {finishOverHauling.isOpen && <FinishOverHaulingModal />}

      {(data.dischargedData && dischargeDetailModal.isOpen)
        && <DetailDischargeModal dischargedData={data.dischargedData} />}

      {qrModalState.isOpen && qrModalState.vehicleId && user?.accessToken && (
        <PhysicalStatusUpdateModal
          isOpen={qrModalState.isOpen}
          onClose={() => setQrModalState(prev => ({ ...prev, isOpen: false }))}
          vehicleId={qrModalState.vehicleId}
          currentStatus={qrModalState.currentStatus}
          nextStatusToDisplay={qrModalState.nextStatusToDisplay}
          nextStepOptions={qrModalState.nextStepOptions}
          confirmationToken={qrModalState.confirmationToken}
          message={qrModalState.message}
          actionAvailable={qrModalState.actionAvailable}
          accessToken={user.accessToken} 
          onStatusConfirmed={(newStatus: PhysicalVehicleStatus) => {
            setQrModalState(prev => ({ ...prev, isOpen: false }));
            
            toast({
              title: country.value === Countries['United States'] ? "Status Updated" : "Estado Actualizado",
              description: country.value === Countries['United States'] 
                ? `The vehicle's physical status has been updated to ${getStatusLabel(newStatus, country.value)}.`
                : `El estado físico del vehículo se actualizó a ${getStatusLabel(newStatus, country.value)}.`,
              status: "success",
              duration: 5000,
              isClosable: true,
              position: 'top',
            });

            router.refresh(); 
          }}
        />
      )}

      <Grid
        templateColumns={{ '2xl': 'repeat(2, minmax(45%, 55%))', base: 'repeat(1, 100%)' }}
        columnGap="30px"
        rowGap="30px"
        w="100%"
        mb="20px"
      >
        <div
          className={`
          relative z-0
          flex justify-center
          object-contain
          w-full
          `
          }
        >
          {
            data.isElectric && <Leaf className="absolute text-green-700 -rotate-12 left-24 top-4 sm:left-32 sm:top-8 w-8 h-8 lg:w-16 lg:h-16 stroke-[2] " />
          }
          <VehicleSVGV2 color={svgColors[data.color.toUpperCase()] || 'white'} />
          {
            user.role !== 'auditor' && isCarBlocked && <div className="absolute right-0 px-2 py-1 my-4 font-bold text-white bg-red-500 rounded-l-lg w-25">
              Bloqueado
            </div>
          }
          {user.role !== 'auditor' && (
            <div
              className={`absolute right-0 px-2 py-1 font-bold text-white ${data.subCategory && data.subCategory !== ''
                ? colorAndTextOnSubCategory[data.subCategory].bgColor
                : data?.category && data?.category !== ''
                  ? colorAndTextOnCategory[data.category].bgColor
                  : null
                } rounded-l-lg w-25`}
            >
              {data.subCategory && data.subCategory !== ''
                ? subCategoryTranslations?.[data.subCategory as keyof typeof subCategoryTranslations]
                : data.category && data.category !== ''
                  ? categoryTranslations?.[data.category as keyof typeof categoryTranslations]
                  : null
              }
            </div>
          )}

          {
            (data.category === allCategory.withdrawn && data.dischargedData) && (
              <div className='w-3/4 h-[50px] rounded bg-ligthRed text-error-text flex items-center justify-between px-[20px] font-[600] absolute z-10 bottom-[20px] '>
                <div className='flex items-center gap-3'>
                  <div
                    className='
                    w-[20px] h-[20px]
                    border-[2px]
                  border-error-border text-error-text
                    rounded-full
                    font-bold
                    flex
                    justify-center items-center
                    p-[2px]
                  '
                >
                  <p className='p-2'>!</p>
                </div>
                <p>{data.dischargedData.reason}</p>
              </div>
              <SlOptions onClick={dischargeDetailModal.onOpen} className='cursor-pointer' />
            </div>
          )
        }
        {(associateData && stepNumber >= 3 && data.category !== allCategory.withdrawn) && (
          <AssociateModal associateData={associateData} data={data} handleIsAssocOpen={handleIsAssocOpen} />
        )}
          {!data.newCar && data.step.stepName.toLowerCase() === 'vehiculo listo' && (
            /* SHOULD DISPLAY AT THE BOTTOM OF THE IMAGE */
            <div className='absolute bottom-[-5]  lg:bottom-20 left-0 flex flex-col gap-2'>
              <ShowCalculatedWeeks stockId={data._id} />
            </div>
          )}
      </div>
      <div className="flex flex-col w-auto gap-4" daya-cy='description' >
        <div className="flex items-center justify-between w-full" >
          <h3 className="text-[24px] font-bold text-[#464E5F] ">
            {country.value === Countries['United States'] ? "Vehicle Data" : "Datos del vehiculo"}
          </h3>
          <CustomModal
            // testId="edit-desc"
            testId="edit-description"
            confirmButtonText={editTextBtn}
            header={country.value === Countries['United States'] ? "Edit Vehicle" : "Editar vehiculo"}
            initialValues={initialValues}
            onCloseModal={() => {}}
            isPrimaryButton={true}
            isUpdate
            size="xl"
            dropDownBtn
            dropDownOptions={dropDownOptions}
            updateIconColor="#5800F7"
            onSubmit={onSubmit}
            body={<Body data={data} userEmail={user.email} receptionDateWarning={receptionDateWarning} />}
            validatorSchema={
              (data.receptionDate && data.step?.stepNumber > 2)
                ? receptionDateSchema(data.receptionDate, user?.email)
                : receptionDateSchema('', user?.email)
            }
            openButtonText={country.value === Countries['United States'] ? "Edit" : "Editar"}
            reloadWindow={false}
            canEdit={canEditVehicle}
          />
        </div>
        <div>
          <div className="h-[50px] flex justify-between items-center bg-[#EAECEE] rounded px-4">
            <p>{country.value === Countries['United States'] ? "Vehicle Country" : "País del Vehículo"}</p>
            <p>{data?.country ? data.country : Countries.Mexico } </p>
          </div>
          <div className="h-[50px] flex justify-between items-center rounded px-4">
            <p>{country.value === Countries['United States'] ? "Year" : "Año"}</p>
            <p>{data.year}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center bg-[#EAECEE] rounded px-4">
            <p>{country.value === Countries['United States'] ? "Color" : "Color"}</p>
            <p>{data.color}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center rounded px-4">
            <p>VIN</p>
            <p>{data.vin}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center bg-[#EAECEE] rounded px-4">
            <p>{country.value === Countries['United States'] ? "Mileage" : "Kilometraje"}</p>
            <p>{data.km || 0} {country.value === Countries['United States'] ? "mi" : "km"}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center rounded px-4">
            <p>{country.value === Countries['United States'] ? "Owner" : "Dueño"}</p>
            <p>{data.owner}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center bg-[#EAECEE] rounded px-4">
            <p>{country.value === Countries['United States'] ? "Transferred to" : "Cedido a"}</p>
            <p>{data.transferredTo || ''}</p>
          </div>

          <div>
            <div className="h-[50px] flex justify-between items-center rounded px-4">
              <p>{country.value === Countries['United States'] ? "Platform" : "Plataforma"}</p>
              <>{platforms.find((platform) => platform.toLocaleLowerCase() === data.platform) || ''}</>
            </div>
          </div>

          <div className="h-[50px] flex justify-between items-center bg-[#EAECEE] rounded px-4">
            <p>{country.value === Countries['United States'] ? "Invoice Value" : "Valor de la factura"}</p>
            <p className="flex items-center">$ {roundedNumber}</p>
          </div>
          <div className="h-[50px] flex justify-between items-center rounded px-4">
            <p>Fecha de recepción</p>
            {data.receptionDate ? (
              <div>
                {isCountryUs ? (
                  <div>
                    {format(parseISO(data.receptionDate), 'MM/dd/yyyy')}
                  </div>
                ) : (
                  <Tooltip
                    label={format(parseISO(data.receptionDate), "dd 'de' MMMM 'de' yyyy", { locale: es })}
                    aria-label="recieved-date-tooltip"
                    placement="top"
                  >
                    {format(parseISO(data.receptionDate), 'dd/MM/yyyy')}
                  </Tooltip>
                )}
              </div>
            ) : (
              <p>Auto no recibido aún</p>
              )}
            </div>
            <div className="h-[50px] flex justify-between items-center  rounded px-4">
            <p>Factura</p>
              {
                data.bill ? (
                  <div className='w-[max-content] flex items-center gap-3 ' >

                    {
                      data.billDate && (
                        <div className="flex items-center gap-1">
                          <p>Fecha: </p>
                          {
                            isCountryUs ? (<div>
                              {format(parseISO(data.billDate), 'MM/dd/yyyy')}
                            </div>) : (<Tooltip label={format(parseISO(data.billDate), "dd 'de' MMMM 'de' yyyy", { locale: es })} aria-label='bill-date-tooltip' placement='top'  >
                              {format(parseISO(data.billDate), 'dd/MM/yyyy')}
                            </Tooltip>)
                          }
                        </div>
                      )
                    }

                    <div
                      className="
                      h-[28px]
                      w-[max-content]
                      px-2
                      flex items-center
                      gap-1
                      text-[#5800F7]
                      rounded
                      cursor-pointer
                      mt-1
                    "
                      style={{ backgroundColor: 'rgba(88, 0, 247, 0.2)' }}
                      onClick={() => {
                        window.open(data.bill?.url);
                      }}
                    >
                      <div>
                        <MdPictureAsPdf size={20} />
                      </div>
                      <p>{data.billNumber || 'factura'}</p>
                    </div>
                  </div>
                ) : (
                  <p>Sin factura cargada</p>
                )
              }
            </div>
          </div>
        </div>
      </Grid>
    </>
  );
}

// BODY FORM

function Body({ data, userEmail, receptionDateWarning }:
  { data: VehicleResponse, userEmail: string, receptionDateWarning: string }) {
  const [nameFiles, setNameFiles] = useState({
    vehiclePhoto: '',
    bill: data.bill?.originalName,
  });
  const { user } = useCurrentUser();
  const toast = useToast();
  const { setFieldValue } = useFormikContext();
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  
  // Check if vehicle already has bill-related data to show the fields
  const hasExistingBillData = data.billNumber || data.billDate || data.billAmount;
  const [hasSuccessfulExtraction, setHasSuccessfulExtraction] = useState(!!hasExistingBillData);

  // OCR Confirmation Modal state
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [pendingOCRResult, setPendingOCRResult] = useState<any>(null);
  const [pendingFile, setPendingFile] = useState<File | null>(null);

  // Get info message using centralized utility
  const infoMessage = getDocumentInfoMessage(DocumentCategory.FACTURE, false);

  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const handleFactureUpload = async (fileList: FileList | null) => {
    if (!fileList || !fileList[0] || !user?.accessToken) return;

    const file = fileList[0];
    setIsProcessingOCR(true);

    try {
      const result = await extractDataFromDocument(
        {
          document: file,
          documentType: 'FACTURE',
          vehicleId: data._id,
        },
        user.accessToken
      );

      if (result.success) {
        // Process valid document - extract facture data
        if (result.data) {
          // Auto-fill the extracted fields
          if (result.data.billNumber) {
            setFieldValue('billNumber', result.data.billNumber);
          }
          if (result.data.billDate) {
            setFieldValue('billDate', result.data.billDate);
          }
          if (result.data.billAmount) {
            setFieldValue('billAmount', result.data.billAmount);
          }

          // Set successful extraction state to show the fields
          setHasSuccessfulExtraction(true);
        }        // Show appropriate toast message
        showOCRToast(toast, result);
      } else {
        // Show confirmation modal for errors instead of hard rejection
        setPendingOCRResult(result);
        setPendingFile(file);
        setIsConfirmationModalOpen(true);
        setIsProcessingOCR(false);
        return;
      }
    } catch (error) {
      console.error('OCR extraction failed:', error);

      // Show generic error for processing failures
      toast({
        title: 'Error al procesar documento',
        description: 'No se pudo procesar el documento. Por favor, intente con otro archivo.',
        status: 'error',
        duration: 5000,
      });

      // Clear the file from form and display on processing error
      setFieldValue('bill', null);
      handleSetName('bill', '');
      setHasSuccessfulExtraction(false);
    } finally {
      setIsProcessingOCR(false);
    }
  };

  // Handle confirmation modal actions
  const handleConfirmWithErrors = () => {
    if (pendingOCRResult && pendingFile) {
      // Process the document despite errors
      if (pendingOCRResult.data) {
        // Auto-fill the extracted fields
        if (pendingOCRResult.data.billNumber) {
          setFieldValue('billNumber', pendingOCRResult.data.billNumber);
        }
        if (pendingOCRResult.data.billDate) {
          setFieldValue('billDate', pendingOCRResult.data.billDate);
        }
        if (pendingOCRResult.data.billAmount) {
          setFieldValue('billAmount', pendingOCRResult.data.billAmount);
        }

        // Set successful extraction state to show the fields
        setHasSuccessfulExtraction(true);
      }
      
      showOCRToast(toast, pendingOCRResult, true); // Show warning toast for accepted invalid document
      
      // Set the file in the form
      setFieldValue('bill', pendingFile);
      handleSetName('bill', pendingFile.name);
      
      // Reset modal state
      setIsConfirmationModalOpen(false);
      setPendingOCRResult(null);
      setPendingFile(null);
    }
  };

  const handleCancelWithErrors = () => {
    // Clear everything and reset modal state
    setFieldValue('bill', null);
    handleSetName('bill', '');
    setHasSuccessfulExtraction(false);
    setIsConfirmationModalOpen(false);
    setPendingOCRResult(null);
    setPendingFile(null);
  };

  return (
    <div className="grid gap-2">
      <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
        <div className="flex flex-col">
          <label className="block text-gray-700 text-[16px] mb-2">ID o No. de Vehiculo</label>
          <div
            className="border-[#9CA3AF]
            border-2
            text-black
            rounded
            px-3
            py-2
            h-[40px]
            w-full
            outline-none
            bg-[#EAECEE]
            "
          >
            {data.carNumber}
          </div>
        </div>
        <CustomInput label="Marca" name="brand" type="text" />
        <CustomInput label="Modelo" name="model" type="text" />
        <CustomInput label="Version" name="version" type="text" />
        <CustomInput label="Año" name="year" type="text" />
        {/* <CustomInput label="Color" name="color" type="text" /> */}
        <SelectInput label="Color" name="color" options={colors} inputId="203" dataCy="color-selector" />

        <CustomInput label="VIN o Serie" name="vin" type="text" />
        <CustomInput label="Dueño" name="owner" type="text" />
        
        {/* Show bill-related fields if vehicle has existing data OR after successful OCR extraction */}
        {(hasSuccessfulExtraction || hasExistingBillData) && (
          <>
            <CustomInput 
              label="Valor factura" 
              name="billAmount" 
              type="text" 
            />
            <CustomDatePicker 
              label="Fecha de factura" 
              name="billDate" 
              useDefaultValidation={true}
            />
            <CustomInput 
              label="No. Factura" 
              name="billNumber" 
              type="text" 
            />
          </>
        )}
        
        <CustomInput label="Kilometros" name="km" type="number" />
        <SelectInput 
          label="Cedido a" 
          name="transferredTo" 
          options={[
            { value: 'i80-1', label: 'i80-1' },
            { value: 'oneCarNow', label: 'oneCarNow' },
          ]}
        />
        <CustomDatePicker 
          label="Fecha de recepción" 
          name="receptionDate" 
          allowClear={
            teamLeads.includes(userEmail) && Boolean(data.receptionDate) && data.step?.stepNumber < 3
          } 
          message={receptionDateWarning}
        />

        <SelectInput
          label="¿Es auto electrico?"
          options={[
            { value: 'false', label: 'No' },
            { value: 'true', label: 'Si' },
          ]}
          name="isElectric"
        />

        <SelectInput
          label="Plataforma"
          name="platform"
          options={platformOptions}
        />

      </div>
      <InputFile
        accept="pdf"
        buttonText={isProcessingOCR ? 'procesando...' : 'Subir archivo'}
        handleSetName={handleSetName}
        label="Factura PDF"
        name="bill"
        // nameFile={nameFiles.bill}
        nameFile={nameFiles.bill}
        placeholder="Subir un archivo"
        onChange={handleFactureUpload}
        isLoading={isProcessingOCR}
        loadingText="procesando..."
        infoMessage={infoMessage}
      />

      {/* OCR Confirmation Modal */}
      <OCRConfirmationModal
        isOpen={isConfirmationModalOpen}
        onClose={() => setIsConfirmationModalOpen(false)}
        onConfirm={handleConfirmWithErrors}
        onCancel={handleCancelWithErrors}
        ocrResult={pendingOCRResult}
        documentType="FACTURE"
      />

    </div>
  );
}

// ASSOCIATE MODAL

interface AssociateModalProps extends VehicleDetailProps {
  handleIsAssocOpen: () => void;
  associateData: VehicleResponse['drivers'][number];
}

/* ASSOCIATE MODAL */

function AssociateModal({ data, associateData, handleIsAssocOpen }: AssociateModalProps) {
  const nextMonday = data.firstDeliveryDate ? siguienteLunes(moment(data.firstDeliveryDate)) : null
  const firstDelivery = nextMonday ?
    new Date(data.firstDeliveryDate as ISODateString) : null
  const now = new Date();
  // Calcula la diferencia en meses
  const diffWeeks = firstDelivery ? differenceInWeeks(now, firstDelivery) : null;

  const contractDetailModal = useContractDetailState();
  const serviceDetailModal = useOpenServiceDetail();
  const ability = usePermissions();
  const canViewDriverProfile = canPerform(
    ability,
    Capabilities.ViewDriverProfile,
    Sections.Fleet,
    Subsections.General
  );
  const canViewContractDetails = canPerform(
    ability,
    Capabilities.ViewContractDetails,
    Sections.Fleet,
    Subsections.General
  );

  return (
    <>
      <div
        className={`
        w-[90%]
        h-[76px]
        bg-[#FFFFFF]
        absolute z-[5]
        flex justify-between
        gap-4 3xl:gap-0
        drop-shadow-xl
        shadow-md
        rounded
        bottom-[20px]
        p-[15px]
        text-[14px]
      `}
      >
        <div className="flex gap-3 ">
          <div>
            <Image src="/userDefaultAvatar.svg" alt="default" width="46" height="46" />
          </div>
          <div className="flex flex-col justify-center h-full ">
            <p className="text-[18px] text-[#5800F7] font-[600] overflow-hidden ">
              {associateData.firstName} {associateData.lastName}
            </p>
            <p>
              {/* {
              data.deliveredDate ? `${diferenciaMeses} de 36 meses` : 'Aun no se agenda una cita'
            } */}
              {
                data.readmissionDate ?
                  <p className='flex items-center'>
                    <div
                      className='
                    w-[20px] h-[20px]
                    border-[2px]
                    border-[#FFAB00] text-[#FFAB00]
                    rounded-full
                    font-bold
                    flex
                    justify-center items-center
                    p-[2px]
                    mr-2
                    '
                    >
                      <p className='p-2'>!</p>
                    </div>
                    Reingreso: {format(parseISO(data.readmissionDate), 'dd/MM/yyyy hh:mm a')}
                  </p>
                  : data.deliveredDate ?
                    `${(diffWeeks && diffWeeks > 156) ? 156 : diffWeeks} de 156 semanas`
                    : 'Aun no se agenda una cita'
              }
            </p>
          </div>
        </div>
        <div className="flex flex-col items-end text-[#9CA3AF]">

          <Menu >
            <MenuButton><SlOptions color="#5800F7" size={18} height="18px" /></MenuButton>
            <MenuList className=' text-textGray2'>
              {canViewDriverProfile && <MenuItem onClick={handleIsAssocOpen}>Ver perfil</MenuItem>}
              {
                associateData.contractData && canViewContractDetails && (
                  <MenuItem onClick={() => {
                    contractDetailModal.setContractData(associateData.contractData)
                    contractDetailModal.onOpen()
                  }}>Ver detalles del contrato</MenuItem>)
              }
              {
                data.category === allCategory.workshop && (
                  <MenuItem onClick={() => {
                    serviceDetailModal.setServiceData(data.stockServices[data.stockServices.length - 1])
                    serviceDetailModal.onOpen()

                  }}>Ver detalle de Taller</MenuItem>)
              }
            </MenuList>
          </Menu>
          {/* <SlOptions cursor="pointer" size={18} height="18px" onClick={handleIsAssocOpen} /> */}
          {associateData.unSignedContractDoc ? (
            <DocumentDisplay
              url={associateData.unSignedContractDoc?.url || ''}
              docName={associateData.unSignedContractDoc?.originalName.slice(0, 15) + '...' || ''}
              textColor="#5800F7"
              backgroundColor="rgba(88, 0, 247, 0.2)"
            />
          ) : (
            <p className='text-[14px] mt-[6px] '>Aún no se genera el contrato</p>
          )}
        </div>
      </div>
    </>
  )
}

function ContractDetailModal() {
  const contractDetailModal = useContractDetailState();
  const data = contractDetailModal.contractData;
  // console.log('data', data)
  const deliveryDate = data?.deliveryData?.isoStringRealDate || data?.deliveredDate;

  const formattedDate = DateTime.fromISO(deliveryDate).toFormat(
    "dd 'de' LLLL 'de' yyyy",
    { locale: 'es' }
  );

  return (
    <ModalContainer title="Detalle del contrato" onClose={() => contractDetailModal.onClose()}  >
      <div className="w-full mb-[20px]">

        <div className="flex p-3 bg-gray-100 rounded">
          <div className="w-1/2 ">Fecha de entrega: </div>
          <div className="w-1/2 ">{formattedDate || ''}</div>
        </div>

        <div className="flex p-3 ">
          <div className="w-1/2 ">Renta semanal: </div>
          <div className="w-1/2 ">$ {data.weeklyRent.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} MXN</div>
        </div>

        <div className="flex p-3 bg-gray-100">
          <div className="w-1/2 ">Precio final: </div>
          <div className="w-1/2 ">$ {data.finalPrice.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} MXN</div>
        </div>

        <div className="flex p-3 rounded">
          <div className="w-1/2 ">Precio total: </div>
          <div className="w-1/2 ">$ {data.totalPrice.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} MXN</div>
        </div>

        <div className="flex p-3 bg-gray-100 ">
          <div className="w-1/2 ">Enganche: </div>
          <div className="w-1/2 ">
            $ {data.downPayment?.toLocaleString('es-MX', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) || '0'} MXN
          </div>
        </div>

      </div>
    </ModalContainer>
  );
}

function ServiceDetail() {
  const serviceDetailModal = useOpenServiceDetail();
  const data = serviceDetailModal.serviceData;

  const today = new Date();
  const initial = new Date(data.dateIn);
  // const date = compareD
  const differenceDays = differenceInDays(today, initial);

  const diff2 = data.dateFinished ? differenceInDays(new Date(data.dateFinished), initial) : null;
  return (
    <ModalContainer title="Detalle del envio a taller" onClose={() => serviceDetailModal.onClose()}  >
      {/* <h1>Hola</h1> */}
      <div className="w-full mb-[20px]">

        <div className="flex p-3 bg-gray-100 rounded">
          <div className="w-1/2 ">Fecha de ingreso: </div>
          <div className="w-1/2 ">{format(parseISO(data.dateIn), "dd 'de' MMMM 'de' yyyy", { locale: es })} </div>
        </div>

        <div className="flex p-3 ">
          <div className="w-1/2 ">Fecha tentativa de salida: </div>
          <div className="w-1/2 "> {format(parseISO(data.dateOut), "dd 'de' MMMM 'de' yyyy", { locale: es })}</div>
        </div>

        <div className="flex p-3 ">
          <div className="w-1/2 ">Fecha de salida: </div>
          <div className="w-1/2 ">
            {data.dateFinished && format(parseISO(data.dateFinished), "dd 'de' MMMM 'de' yyyy", { locale: es })}
          </div>
        </div>

        <div className="flex p-3 ">
          <div className="w-1/2 ">Dias transcurrido: </div>
          <div className="w-1/2 ">
            {data.dateFinished ? diff2 : differenceDays}
          </div>
        </div>

        <div className="flex p-3 bg-gray-100">
          <div className="w-1/2 ">Comentarios: </div>
          <div className="w-1/2 ">{data.comments}</div>
        </div>

      </div>
    </ModalContainer>
  );
}
