import * as Yup from 'yup';

export const editUserValidationSchema = (initialArea: string) =>
  Yup.object().shape({
    area: Yup.object().nullable(), // no required(), no fields required inside

    role: Yup.object()
      .nullable()
      .when('area', {
        is: (area: { value: string }) => area?.value !== initialArea,
        then: (schema) =>
          schema
            .shape({
              label: Yup.string().required(),
              value: Yup.string().required('Rol es obligatorio si se cambia el área'),
            })
            .required('Rol es obligatorio si se cambia el área'),
        otherwise: (schema) => schema.notRequired(),
      }),
  });
