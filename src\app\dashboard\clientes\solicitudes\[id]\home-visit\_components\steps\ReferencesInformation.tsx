import { Form, FormLabel } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField } from '../HookFormField';
import { FormSection } from '../FormSection';
import { FormSectionHeader } from '../FormHeaderSection';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { Steps, toastConfigs } from '.';
import { FiPhoneCall } from 'react-icons/fi';
import { HomeVisitStepsStatus } from '@/constants';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

const ReferencesInformationSchema = z
  .object({
    reference1Name: z.string(),
    reference1Phone: z.string(),
    reference1Relationship: z.string(),
    reference1Address: z.string(),
    reference2Name: z.string(),
    reference2Phone: z.string(),
    reference2Relationship: z.string(),
    reference2Address: z.string(),
    reference3Name: z.string(),
    reference3Phone: z.string(),
    reference3Relationship: z.string(),
    reference3Address: z.string(),
    learnAboutOcn: z.string(),
  })
  .superRefine((val, ctx) => {
    const reference1Phone = val.reference1Phone;
    if (reference1Phone.length > 0) {
      if (reference1Phone.length !== 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference1Phone'],
          message: translations.es.PhoneLengthErrorMsg,
        });
      } else if (!/^\d+$/.test(reference1Phone)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference1Phone'],
          message: translations.es.PhoneFormatErrorMsg,
        });
      }
    }

    const reference2Phone = val.reference2Phone;
    if (reference2Phone.length > 0) {
      if (reference2Phone.length !== 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference2Phone'],
          message: translations.es.PhoneLengthErrorMsg,
        });
      } else if (!/^\d+$/.test(reference2Phone)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference2Phone'],
          message: translations.es.PhoneFormatErrorMsg,
        });
      }
    }

    const reference3Phone = val.reference3Phone;
    if (reference3Phone.length > 0) {
      if (reference3Phone.length !== 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference3Phone'],
          message: translations.es.PhoneLengthErrorMsg,
        });
      } else if (!/^\d+$/.test(reference3Phone)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['reference3Phone'],
          message: translations.es.PhoneFormatErrorMsg,
        });
      }
    }
  });

const ReferenceFieldInformation = [
  {
    referenceNameFieldName: 'reference1Name',
    referencePhoneFieldName: 'reference1Phone',
    referenceRelationshipFieldName: 'reference1Relationship',
    referenceAddressFieldName: 'reference1Address',
  },
  {
    referenceNameFieldName: 'reference2Name',
    referencePhoneFieldName: 'reference2Phone',
    referenceRelationshipFieldName: 'reference2Relationship',
    referenceAddressFieldName: 'reference2Address',
  },
  {
    referenceNameFieldName: 'reference3Name',
    referencePhoneFieldName: 'reference3Phone',
    referenceRelationshipFieldName: 'reference3Relationship',
    referenceAddressFieldName: 'reference3Address',
  },
];

export default function ReferencesInformation(props: any) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const {
    id: requestId,
    personalData: { references, learnAboutOcn },
    homeVisit,
  } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof ReferencesInformationSchema>>({
    resolver: zodResolver(ReferencesInformationSchema),
    defaultValues: {
      reference1Name: references.reference1Name || '',
      reference1Phone: references.reference1Phone || '',
      reference1Relationship: references.reference1Relationship || '',
      reference1Address: references.reference1Address || '',
      reference2Name: references.reference2Name || '',
      reference2Phone: references.reference2Phone || '',
      reference2Relationship: references.reference2Relationship || '',
      reference2Address: references.reference2Address || '',
      reference3Name: references.reference3Name || '',
      reference3Phone: references.reference3Phone || '',
      reference3Relationship: references.reference3Relationship || '',
      reference3Address: references.reference3Address || '',
      learnAboutOcn: learnAboutOcn || '',
    },
  });

  async function onSubmit(data: z.infer<typeof ReferencesInformationSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);

      const isDataCompleted = Object.entries(data).every(([, value]) => {
        return value !== '';
      });
      const payload = {
        personalData: {
          references: {
            reference1Name: data.reference1Name,
            reference1Phone: data.reference1Phone,
            reference1Relationship: data.reference1Relationship,
            reference1Address: data.reference1Address,
            reference2Name: data.reference2Name,
            reference2Phone: data.reference2Phone,
            reference2Relationship: data.reference2Relationship,
            reference2Address: data.reference2Address,
            reference3Name: data.reference3Name,
            reference3Phone: data.reference3Phone,
            reference3Relationship: data.reference3Relationship,
            reference3Address: data.reference3Address,
          },
          learnAboutOcn: data.learnAboutOcn,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            references: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.References, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.References, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.ReferencesInformation} />
      <Form {...form}>
        <form>
          {ReferenceFieldInformation.map((referenceField, index) => {
            return (
              <ReferencesFormSection
                key={referenceField.referenceNameFieldName}
                form={form}
                sectionHeader={`${translations.es.Reference} ${index + 1}`}
                referenceNameFieldName={referenceField.referenceNameFieldName}
                referenceNameFormLabel={translations.es.Name}
                referenceNamePlaceholder={translations.es.Name}
                referencePhoneFieldName={referenceField.referencePhoneFieldName}
                referencePhoneFormLabel={translations.es.Phone}
                referencePhonePlaceholder={translations.es.Phone}
                referenceRelationshipFieldName={referenceField.referenceRelationshipFieldName}
                referenceRelationshipFormLabel={translations.es.Relationship}
                referenceRelationshipPlaceholder={translations.es.Relationship}
                referenceAddressFieldName={referenceField.referenceAddressFieldName}
                referenceAddressFormLabel={translations.es.Address}
                referenceAddressPlaceholder={translations.es.Address}
              />
            );
          })}
          <HookFormRadixUIField
            form={form}
            fieldName="learnAboutOcn"
            formLabel={translations.es.HowDidYouLearnAboutOneCarNow}
            className="w-3/6 py-2"
          />
        </form>
      </Form>
    </FormSection>
  );
}

interface IReferencesForm {
  form: any;
  referenceNameFieldName: string;
  referencePhoneFieldName: string;
  referenceRelationshipFieldName: string;
  referenceAddressFieldName: string;
  referenceNameFormLabel: string;
  referencePhoneFormLabel: string;
  referenceRelationshipFormLabel: string;
  referenceAddressFormLabel: string;
  referenceNamePlaceholder: string;
  referencePhonePlaceholder: string;
  referenceRelationshipPlaceholder: string;
  referenceAddressPlaceholder: string;
  sectionHeader: string;
}

const ReferencesFormSection = (props: IReferencesForm) => {
  const {
    form,
    referenceNameFieldName,
    referencePhoneFieldName,
    referenceRelationshipFieldName,
    referenceAddressFieldName,
    referenceNameFormLabel,
    referencePhoneFormLabel,
    referenceRelationshipFormLabel,
    referenceAddressFormLabel,
    sectionHeader,
  } = props;

  return (
    <>
      <section className="py-2">
        <FormLabel className="basis-1/3 text-primaryBlueGray text-sm">{sectionHeader}</FormLabel>
        <div className="flex py-2 gap-2">
          <HookFormRadixUIField
            form={form}
            fieldName={referenceNameFieldName}
            formLabel={referenceNameFormLabel}
          />

          <HookFormRadixUIField
            form={form}
            fieldName={referencePhoneFieldName}
            formLabel={referencePhoneFormLabel}
            Icon={FiPhoneCall}
            prefixString={'+52'}
          />
        </div>

        <div className="flex py-2 gap-2">
          <HookFormRadixUIField
            form={form}
            fieldName={referenceRelationshipFieldName}
            formLabel={referenceRelationshipFormLabel}
          />

          <HookFormRadixUIField
            form={form}
            fieldName={referenceAddressFieldName}
            formLabel={referenceAddressFormLabel}
          />
        </div>

        <hr className="bg-primaryLightPastelBlueGray" />
      </section>
    </>
  );
};
