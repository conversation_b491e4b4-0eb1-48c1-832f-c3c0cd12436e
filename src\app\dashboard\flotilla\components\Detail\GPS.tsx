// 'use client';
// import { useState } from 'react';
// import CustomInput from '@/components/Inputs/CustomInput';
// import CustomModal from '@/components/Modals/CustomModal';
// import { addGPSSchema } from '@/validatorSchemas/vehicleDetailSchema';
// import { useSession } from 'next-auth/react';
// import { MyUser } from '@/actions/getCurrentUser';
// import { FormikValues } from 'formik';
// import axios from 'axios';
// import useCurrentUrl from '@/app/hooks/useCurrentUrl';
// import Spinner from '@/components/Loading/Spinner';
// import { editTextBtn } from '@/constants';
// import { useToast } from '@chakra-ui/react';

// interface GPSProps {
//   gpsNumber?: string;
//   gpsSerie?: string;
//   vehicleId: string;
//   isCarBlocked: boolean;
// }

// export default function GPS({ gpsNumber, gpsSerie, vehicleId, isCarBlocked }: GPSProps) {
//   const toast = useToast();
//   const [isLoading, setIsLoading] = useState(false);
//   const { data: session } = useSession();
//   const user = session?.user as unknown as MyUser;

//   const url = useCurrentUrl();

//   const onSubmit = async (values: FormikValues, isEditing?: boolean) => {
//     setIsLoading(true);
//     const form = {
//       ...values,
//       historyData: {
//         userId: user.id,
//         step: 'DOCUMENTACIÓN',
//         description: isEditing ? 'GPS Editado' : 'GPS Agregado', // Esto se actualizará para mostrar que cambio exacto se hizo
//       },
//       isEditing: isEditing,
//     };
//     const response = await axios
//       .patch(`${url}/stock/update/gps/${vehicleId}`, form, {
//         headers: {
//           Authorization: `bearer ${session ? user.accessToken : null}`,
//         },
//       })
//       .catch((err) => {
//         console.error(err);
//       })
//       .finally(() => setIsLoading(false));
//     return response;
//   };

//   const onSubmitBlock = async (gps: String, comando: String) => {
//     setIsLoading(true);
//     const form = {
//       historyData: {
//         userId: user.id,
//         step: 'GPS',
//         description: isCarBlocked ? 'Vehículo desbloqueado' : 'Vehículo bloqueado', // Esto se actualizará para mostrar que cambio exacto se hizo
//       },
//     };
//     const response = await axios
//       .post(
//         `${url}/gps`,
//         { ...form, gps, comando },
//         {
//           headers: {
//             Authorization: `bearer ${session ? user.accessToken : null}`,
//           },
//         }
//       )
//       .then(() => {
//         toast({
//           title: 'GPS',
//           position: 'top',
//           description: 'Se ha bloqueado el vehículo',
//           status: 'success',
//           duration: 5000,
//           isClosable: true,
//         });
//         setTimeout(() => {
//           window.location.reload();
//         }, 2500);
//       })
//       .catch((err) => {
//         console.error(err);
//         toast({
//           title: 'Error',
//           position: 'top',
//           description: err.response.data.error,
//           status: 'error',
//           duration: 5000,
//           isClosable: true,
//         });
//       })
//       .finally(() => setIsLoading(false));
//     return response;
//   };

//   const body = (
//     <div className="flex flex-col w-full gap-3 overflow-hidden">
//       <CustomInput label="Nombre" name="gpsNumber" type="text" />
//       <CustomInput label="No. de serie" name="gpsSerie" type="text" />
//     </div>
//   );

//   const initialValues = { gpsNumber: '', gpsSerie: '' };
//   const backendValues = { gpsNumber: gpsNumber && gpsNumber, gpsSerie: gpsSerie && gpsSerie };

//   if (isLoading) return <Spinner />;

//   return (
//     <div
//       className="
//         w-[316px]
//         bg-[white]
//         flex flex-col
//         gap-3
//         py-[25px]
//         px-[20px]
//         border-[1px]
//         border-[#EAECEE]
//         font-bold
//         rounded"
//     >
//       {gpsNumber && gpsSerie ? (
//         <>
//           <div className="flex justify-between">
//             <p className="font-bold text-[24px] ">GPS</p>
//             <CustomModal
//               testId="gps"
//               openButtonText="Editar GPS"
//               confirmButtonText={editTextBtn}
//               isPrimaryButton={true}
//               // validatorSchema={addGPSSchema}
//               body={body}
//               initialValues={backendValues}
//               onCloseModal={() => {}}
//               header="Editar GPS"
//               isUpdate
//               updateIconColor="#5800F7"
//               onSubmit={(values) => onSubmit(values, true)}
//             />
//           </div>
//           <div className="flex flex-col gap-3 text-[18px]">
//             <p>{gpsNumber}</p>
//             <div className="flex justify-between text-[16px]">
//               <p className="font-normal">No. serie</p>
//               <p>{gpsSerie}</p>
//             </div>
//             {isCarBlocked ? (
//               <button onClick={() => onSubmitBlock(gpsNumber, 'desbloqueo')}>
//                 <p className="text-[#5800F7] border-[1px] border-[#5800F7] rounded p-1">
//                   Desbloquear vehículo
//                 </p>
//               </button>
//             ) : (
//               <button onClick={() => onSubmitBlock(gpsNumber, 'bloqueo')}>
//                 <p className="text-red-500 border-[1px] border-red-500 rounded p-1">Bloquear vehículo</p>
//               </button>
//             )}
//           </div>
//         </>
//       ) : (
//         <>
//           <p className="font-bold text-[24px] ">GPS</p>
//           <p className="text-[#9CA3AF]">Este vehículo aún no hay un GPS asignado</p>
//           <CustomModal
//             testId="gps"
//             openButtonText="Agregar GPS"
//             confirmButtonText="Agregar"
//             isPrimaryButton={true}
//             validatorSchema={addGPSSchema}
//             body={body}
//             initialValues={initialValues}
//             onCloseModal={() => {}}
//             header="Agregar GPS"
//             onSubmit={async (values) => onSubmit(values, false)}
//           />
//         </>
//       )}
//     </div>
//   );
// }
