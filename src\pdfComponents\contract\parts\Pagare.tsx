/* eslint-disable max-len */
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import PagareGris from '../assets/images/logoGris.png';
import { NumerosALetras } from 'numero-a-letras';
import moment from 'moment';
import 'moment/locale/es';
import { AvalData } from '@/actions/getVehicleData';

const styles = StyleSheet.create({
  body: {
    flexDirection: 'column',
    rowGap: 1,
    marginBottom: '20px',
  },
  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    marginTop: '70px',
    opacity: 0.2,
    height: '500px',
    width: '100%',
  },
  anexoTitle: {
    textAlign: 'center',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    zIndex: 1,
  },

  anexoText: {
    color: 'black',
    // fontFamily: 'Helvetica-Bold',
    fontSize: 11,
    textAlign: 'justify',
    // lineHeight: '2px',
    // zIndex: 1,
  },

  viewMain: {
    rowGap: 3,
    zIndex: 1,
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  content: {
    width: '40%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    rowGap: 10,
  },
  names: {
    fontSize: 10,
    textAlign: 'left',
    fontFamily: 'Helvetica-Bold',
    marginLeft: 20,
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 10,
  },
});

interface PagareProps {
  city: string;
  firstName: string;
  lastName: string;
  weeklyRent: number;
  priceWrite: string;
  totalPays: number;
  date: string;
  fullAddress: string;
  avalData: AvalData;
}

export default function Pagare({ firstName, lastName, weeklyRent, city, totalPays, ...rest }: PagareProps) {
  // const totalPrice = weeklyRent * (156 - (paymentsDone || 0));
  const totalPrice = weeklyRent * (totalPays ? totalPays : 156);
  const priceParsed = totalPrice.toLocaleString('es-MX', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  const text = NumerosALetras(totalPrice).split(' ');

  const resolveOne =
    text[0] === 'Un'
      ? NumerosALetras(totalPrice).replace(' Pesos 00/100 M.N.', '').replace(' Peso 00/100 M.N.', '')
      : NumerosALetras(totalPrice)
          .replace('Un', 'Uno')
          .replace(' Pesos 00/100 M.N.', '')
          .replace(' Peso 00/100 M.N.', '');

  const day = moment(rest.date).format('DD [de] MMMM [de] YYYY');

  const dateParse = moment(rest.date).format('DD-MM-YYYY');

  return (
    <View style={styles.body} break>
      {/* eslint-disable-next-line jsx-a11y/alt-text */}
      <Image src={PagareGris.src} style={styles.viewBackground} />
      <Text style={styles.anexoTitle}>
        PAGARÉ QUE SE SUSCRIBE EN TÉRMINOS DE LO DISPUESTO POR EL ARTÍCULO 170 Y DEMÁS RELATIVOS Y APLICABLES
        DE LA LEY GENERAL DE TÍTULOS Y OPERACIONES DE CRÉDITO.
      </Text>
      <View style={styles.viewMain}>
        <Text style={styles.anexoText}>Total: ${priceParsed || '538,200.00'} M.N.</Text>
        <Text style={styles.anexoText}>
          Lugar y fecha de suscripción: {city}, México, a {day}.
        </Text>
        <Text style={styles.anexoText}>
          Por valor pactado, el suscrito {firstName} {lastName} incondicionalmente promete pagar a la orden de
          E-MKT GOODS DE MÉXICO, S.A.P.I. de C.V., sus cesionarios o sucesores, la cantidad principal de $
          {priceParsed || '53,200.00'} M.N. ({resolveOne} Pesos 00/100 Moneda Nacional) al momento en el que
          dicho título de crédito le sea puesto a la vista para su pago único; señalando como domicilio para
          efectuar el pago el ubicado en Prolongación Paseo de la Reforma 1015 PISO 5 INT 140, Santa Fe
          Cuajimalpa, Cuajimalpa
        </Text>
        <Text style={styles.anexoText}>
          <Text style={styles.interesMora}>Intereses Moratorios. {}</Text>
          En el evento en que el Suscriptor incumpla con el pago de la totalidad de la cantidad principal
          señalada en este pagaré, se causarán intereses moratorios a una tasa de 3% ( tres por ciento)
          mensual sobre la cantidad total señalada al inicio de este documento, durante todo el tiempo en que
          dure la mora y hasta el pago total de la misma. El pago de intereses moratorios se deberá efectuar
          adicionado con el IVA correspondiente. El Suscriptor señala como domicilio donde puede ser requerido
          de pago, el ubicado en {rest.fullAddress}.
        </Text>
        <View style={{ flexDirection: 'column', gap: 0 }}>
          <Text style={styles.anexoText}>
            Firma por aval este instrumento mercantil el C. {rest.avalData.name} quien manifiesta ser solvente
            económicamente para en su caso hacer pago de este instrumento mercantil y señalando como domicilio
            para ser requerido de la obligación jurídica que asume, el ubicado en{' '}
            <Text style={styles.anexoText}>{rest.avalData.address}</Text>
          </Text>
          {/* <Text style={styles.anexoText}>
            ___________________________________________________________________________
            ______________________________________________________________________________
          </Text> */}
        </View>
        <Text style={styles.anexoText}>
          Este Pagaré que consta en 1 (una) página se suscribe y entrega en el estado de {city}, México, a
          {day}.
        </Text>
      </View>
      <View style={styles.containerS}>
        <View style={styles.content}>
          <Text style={styles.names}>Suscriptor de este pagaré</Text>
          <Text style={styles.dateAndWho}>______________________________</Text>
          <Text style={styles.dateAndWho}>Fecha: {dateParse}</Text>
          <Text style={styles.dateAndWho}>
            Por: {firstName} {lastName}
          </Text>
        </View>

        <View style={styles.content}>
          <Text style={styles.names}>Por Aval de este pagaré</Text>
          <Text style={styles.dateAndWho}>______________________________</Text>
          {/* <View style={styles.gap} > */}
          <Text style={styles.dateAndWho}>Fecha: {dateParse}</Text>
          <Text style={styles.dateAndWho}>Por: {rest.avalData.name}</Text>
          {/* </View> */}
        </View>
      </View>
    </View>
  );
}
