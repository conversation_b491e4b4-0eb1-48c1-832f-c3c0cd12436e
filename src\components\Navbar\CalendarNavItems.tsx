import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { Capabilities, Paths, Sections, Subsections } from '@/constants';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import { FaChevronDown, FaUserFriends, FaFileAlt, FaClock } from 'react-icons/fa';
import { FiCalendar } from 'react-icons/fi';

const CalendarNavItems = () => {
  const ability = usePermissions();
  const pathname = usePathname();
  const navButton = {
    icon: <FiCalendar size={18} />,
    name: 'Calendario',
  };
  const subNavLinks = [
    {
      link: Paths.calendar_appointmentScheduler,
      icon: <FaFileAlt size={16} />,
      name: 'Appointment Scheduler',
      key: Subsections.AppointmentScheduler,
    },
    {
      link: Paths.calendar_calendarPreview,
      icon: <FaClock size={16} />,
      name: 'Calendar Preview',
      key: Subsections.CalendarPreview,
    },
  ].filter(
    (item) =>
      canPerform(ability, Capabilities.View, Sections.Calendar, item.key) ||
      canPerform(ability, Capabilities.ViewAllCalendar, Sections.Calendar, item.key) ||
      canPerform(ability, Capabilities.ViewMyCalendar, Sections.Calendar, item.key)
  );

  return (
    <details className="group transition-all duration-150 ml-4 content-center h-auto open:h-auto overflow-visible ">
      {pathname.includes('/dashboard/calendar') ? (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2 bg-primaryPurple text-white">
          {navButton.icon}
          <span className="text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 text-white ">
            {navButton.name}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={'white'} />
          </span>
        </summary>
      ) : (
        <summary className="transition-all duration-500 flex cursor-pointer items-center rounded-lg px-4 py-2">
          <FaUserFriends size={18} />
          <span className="text-gray-600 text-[20px] font-semibold leading-tight ml-3.5 transition duration-300 ">
            {' '}
            Calendario{' '}
          </span>
          <span className="ml-auto shrink-0 transition duration-300 group-open:-rotate-180 ">
            <FaChevronDown color={pathname.includes('/dashboard/clientes') ? 'white' : 'black'} />
          </span>
        </summary>
      )}

      <nav className="mt-1.5 ml-8 flex flex-col transition-all duration-500">
        {subNavLinks.map((item, key) => {
          return (
            <Link href={item.link} key={key} prefetch={false}>
              <button
                className={
                  pathname === item.link
                    ? 'flex items-center rounded-lg px-4 py-2 text-white bg-primaryPurple'
                    : 'flex items-center rounded-lg px-4 py-2 hover:bg-gray-100 hover:text-gray-700'
                }
                style={{ width: '96%' }}
                key={key}
              >
                {item.icon}
                <span className="ml-3 text-sm font-medium"> {item.name} </span>
              </button>
            </Link>
          );
        })}
      </nav>
    </details>
  );
};

export default CalendarNavItems;
