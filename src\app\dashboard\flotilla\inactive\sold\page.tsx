import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';
import VehicleTable from '../../components/VehicleTable';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function SoldPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });
  //console.log(`definitiveFilters: ${definitiveFilters}`);

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 10,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'inactive',
      category: definitiveFilters?.category || 'sold',
      country: definitiveFilters?.country,
    },
  });

  const page = {
    name: 'Inactive',
    api: 'inactive',
    count: 12,
  };

  const subPage = {
    name: 'Sold',
    api: 'sold',
    count: 12,
  };

  if (!result) return null;

  return (
    <VehicleTable
      route="sold"
      page={page}
      subPage={subPage}
      data={result.stock}
      totalCount={result.totalCount}
      subOptions={[]}
    />
  );
}
