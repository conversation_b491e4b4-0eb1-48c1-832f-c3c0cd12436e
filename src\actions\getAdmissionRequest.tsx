import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';
import axios from 'axios';
import { ApiPath } from '@/constants/api.endpoints';

interface GetAdmissionRequestProps {
  id: string;
}

export default async function getAdmissionRequest({ id }: GetAdmissionRequestProps) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const res = await fetch(`${URL_API}/admission/requests/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const response = await res.json();

    return response;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export async function getCalendarSchedule() {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const { data } = await axios.get(`${URL_API}${ApiPath.CALENDAR_SCHEDULE}/${user.id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return data.data;
  } catch (error) {
    return null;
  }
}

export async function getAllEvents(selectedUsers: string) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const apiUrl = new URL(`${URL_API}${ApiPath.CALENDAR_EVENTS}/${user.id}`);

    const selectedUsersArray = selectedUsers ? selectedUsers.split(',') : [];
    if (selectedUsersArray.length > 0) {
      apiUrl.searchParams.set('selectedUsers', selectedUsers);
    }
    const { data } = await axios.get(apiUrl.toString(), {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    /**
     * will add later once we have enough data
     */
    // if (startDate) {
    //   url.searchParams.append('startDate', startDate);
    // } else {
    //   const current01Date = new Date();
    //   current01Date.setDate(1);
    //   startDate = current01Date.toISOString().split('T')[0];
    // }

    // if (endDate) {
    //   url.searchParams.append('endDate', endDate);
    // } else {
    //   const currentLastDate = new Date();
    //   currentLastDate.setMonth(currentLastDate.getMonth() + 1);
    //   currentLastDate.setDate(0);
    //   endDate = currentLastDate.toISOString().split('T')[0];
    // }
    return data.data;
  } catch (err) {
    console.error(err);
    return null;
  }
}
