// src/casl/createAbility.ts
import { Section } from '@/actions/getPermissionSetById';
import { createMongoAbility, MongoAbility, AbilityTuple } from '@casl/ability';

export type AppAbility = MongoAbility<AbilityTuple>;

export const createAbility = (raw: Section[] | undefined | null): AppAbility => {
  const permissions: Section[] = Array.isArray(raw) ? raw : [];

  const rules = permissions.map((permission) => {
    const { section, subSection, capability } = permission;
    return {
      action: capability,
      subject: `${section}:${subSection || 'default'}`,
    };
  });

  return createMongoAbility(rules);
};
