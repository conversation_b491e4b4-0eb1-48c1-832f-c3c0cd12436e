'use client';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import axios from 'axios';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { ChangeEvent, useRef } from 'react';
import { TbCameraPlus } from 'react-icons/tb';
import { useCurrentUser } from '../providers/CurrentUserProvider';

export default function EditImage() {
  const { user } = useCurrentUser();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const toast = useToast();
  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const onChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await axios.patch(
        `${URL_API}/user/${user._id}`,
        {
          image: file,
        },
        { headers: { 'Content-Type': 'multipart/form-data', Authorization: `bearer ${user.accessToken}` } }
      );
      // console.log(res.data);
      router.refresh();
      toast({
        title: 'Imagen actualizada',
        status: 'success',
        duration: 3000,
        isClosable: true,
        position: 'top',
      });
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    }
  };

  return (
    <div className="w-full relative">
      <Image
        className="rounded-full w-full h-auto"
        width="1000"
        height="1000"
        alt="avatar profile"
        src={
          user.image?.url
            ? user.image.url
            : 'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
        }
      />
      <div
        className="
          w-[40px] h-[40px] 
          flex justify-center 
          items-center bg-[#5800F7]
          rounded-full 
          cursor-pointer
          absolute top-[100px] right-0 
        "
        onClick={handleButtonClick}
      >
        <TbCameraPlus color="white" size={24} />
      </div>
      <input type="file" className="hidden" ref={fileInputRef} onChange={onChange} accept="image/*" />
    </div>
  );
}
