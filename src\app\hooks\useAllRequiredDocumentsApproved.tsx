import {
  AdmissionRequestDocumentType,
  AdmissionRequestStatus,
  RequestDocumentStatus,
} from '../dashboard/clientes/solicitudes/enums';
import { DocumentsAnalysis } from '../dashboard/clientes/solicitudes/[id]/DocumentsAnalysisActions';

const useAllRequiredDocumentsApproved = (
  isCountryUSA: boolean,
  status: AdmissionRequestStatus,
  documentsAnalysis: DocumentsAnalysis
) => {
  if (status === AdmissionRequestStatus.documents_analysis) {
    if (isCountryUSA) {
      return documentsAnalysis.documents.every(
        (document) => document.status === RequestDocumentStatus.approved
      );
    } else {
      const requiredDocuments: string[] = Object.values(AdmissionRequestDocumentType);
      return requiredDocuments.every((documentType: string) => {
        const requiredDocument = documentsAnalysis.documents.find(
          (document) => document.type === documentType
        );
        const result = requiredDocument && requiredDocument.status === RequestDocumentStatus.approved;
        return result;
      });
    }
  }
  return false;
};

export default useAllRequiredDocumentsApproved;
