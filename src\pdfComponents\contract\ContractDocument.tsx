/* eslint-disable prettier/prettier */
/* eslint-disable jsx-a11y/alt-text */
import List, {
  ClauItemSemi,
  ClauList,
  ClauListEV,
  ClauList2,
  ClauList3,
  DeclaItem,
  DeclaItem2,
  DefItem,
} from '@/pdfComponents/contract/parts/Definiciones';
import Pagare from '@/pdfComponents/contract/parts/Pagare';
import AnexoB from '@/pdfComponents/contract/parts/AnexoB';
import { Table } from '@/pdfComponents/contract/parts/Table';
import Garantia from '@/pdfComponents/contract/parts/Garantia';
import Signatures from '@/pdfComponents/contract/parts/Signatures';
import UsoFactura from '@/pdfComponents/contract/parts/UsoFactura';
import HeaderImg from '@/pdfComponents/contract/assets/Header.png';
import { ClauItem, ClauItem2, ClauItem3, ListC } from '@/pdfComponents/contract/parts/Clausulas';
import { Document, Page, Text, View, StyleSheet, Font, Image } from '@react-pdf/renderer';
import ActaEntrega from '@/pdfComponents/contract/parts/Acta Entrega';
import AvisoPrivacidad from '@/pdfComponents/contract/parts/AvisoPrivacidad';
import DatosContacto from '@/pdfComponents/contract/parts/DatosContacto';
import AnexoC from '@/pdfComponents/contract/parts/AnexoC';
import moment from 'moment';
import { useMemo } from 'react';
import PagareWithNoAval from './parts/PagareWithNoAval';

Font.register({ family: 'Helvetica', fonts: [] });
Font.register({ family: 'Helvetica-Oblique', fonts: [] });
Font.register({ family: 'Helvetica-BoldOblique', fonts: [] });
Font.register({ family: 'Helvetica-Bold', fonts: [] });

// Create styles
const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },
  body: {
    marginLeft: '10%',
    marginRight: '10%',
  },
  headText: {
    width: '100%',
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },
  definicionesText: {
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },

  viewer: {
    width: '100%',
    height: '100vh',
  },

  defContainer: {
    marginTop: '20px',
  },

  declContainer: {
    marginTop: '20px',
    flexDirection: 'column',
    rowGap: 20,
  },

  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },
  tableDescription: {
    fontSize: 8,
    marginTop: '10px',
  },
  pageNumber: {
    fontSize: '11px',
    textAlign: 'right',
    paddingTop: '15px',
    marginRight: '40px',
  },

  showContractNumber: {
    fontSize: '10px',
    textAlign: 'center',
    position: 'absolute',
    width: '100%',
    bottom: '30px',
    // left: '280px',
    color: 'grey',
  },
  header: {
    width: '100vw',
  },

  calendarPage: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },
});

export function DocumentComponent({ form, headContract }: any) {
  const justDate = form.deliverDate.split('T')[0];
  const justHour = form.deliverDate.split('T')[1];
  const hour12Format = moment(justHour, 'HH:mm').format('hh:mm A');

  const startDate = useMemo(() => {
    if (form.startDate) {
      return form.startDate.split('T')[0];
    }
    return null;
  }, [form.startDate]);

  const paym = form.totalPays ? Number(form.totalPays) : 156;

  const roundedMonths = Math.round(paym / 4.33);
  return (
    <Document>
      {/*render a single page*/}
      <Page style={styles.page} size="A4" wrap>

        <Image style={styles.header} src={HeaderImg.src} fixed />

        <View style={styles.body}>
          <Text style={styles.headText}>
            {/* ESTO ES EL TEXTO INICIAL CON LOS NOMBRES Y EL ESTADO */}
            {headContract}
          </Text>

          <View style={styles.defContainer}>
            <Text style={styles.title}> D E F I N I C I O N E S </Text>

            <List>
              <DefItem isElectric={form.isElectric} />
            </List>
          </View>

          <View style={styles.declContainer}>
            <Text style={styles.title}> D E C L A R A C I O N E S </Text>
            <Text style={styles.definicionesText}>
              {' '}
              PRIMERA.- DECLARA “EL ARRENDADOR”, A TRAVÉS DE SU REPRESENTANTE LEGAL QUE:{' '}
            </Text>
            <List>
               <DeclaItem />
            </List>
          </View>

          <View style={styles.declContainer}>
            <Text style={styles.title}> D E C L A R A C I O N E S </Text>
            <Text style={styles.definicionesText}> SEGUNDA.- DECLARA “EL ARRENDATARIO”: </Text>
            <List>
              <DeclaItem2 />
            </List>
          </View>

          <View style={styles.declContainer}>
            <Text style={styles.title}> C L Á U S U L A S </Text>
            <ListC>
              {/* <ClauItem /> */}
              {form.newCar ?
                <ClauItem isElectric={form.isElectric} /> :
                <ClauItemSemi months={roundedMonths} isElectric={form.isElectric} />
              }
            </ListC>

            <Text style={styles.definicionesText}>
              {' '}
              En el cumplimiento del presente contrato “EL ARRENDADOR” se obliga a:{' '}
            </Text>
            <List>
              {form.isElectric ? <ClauListEV /> : <ClauList />}
            </List>

            <Text style={styles.definicionesText}>
              {' '}
              Para los efectos de este contrato, son obligaciones de “EL ARRENDATARIO”:{' '}
            </Text>
            <List>
              <ClauList2 isElectric={form.isElectric} />
            </List>
            <ListC>
              <ClauItem2 isElectric={form.isElectric} />
            </ListC>

            <List>
              <ClauList3 isElectric={form.isElectric} />
            </List>

            <ListC>
              <ClauItem3
                deliverDate={form.deliverDate}
                finalPrice={form.finalPrice}
                city={form.city.value}
                isElectric={form.isElectric}
              />
            </ListC>
          </View>

          <Signatures firstName={form.firstName} lastName={form.lastName} />

          <Table
            contract={form.contractNumber}
            months={roundedMonths}
            totalPays={form.totalPays}
            extensionCarNumber={form.extensionCarNumber}
            phone={form.phone}
            deliverDate={justDate}
            weeklyRent={form.weeklyRent}
            firstName={form.firstName}
            lastName={form.lastName}
            rfc={form.rfc}
            email={form.email}
            street={form.street}
            streetF={form.streetF}
            exterior={form.exterior}
            exteriorF={form.exteriorF}
            interior={form.interior}
            interiorF={form.interiorF}
            colony={form.colony}
            colonyF={form.colonyF}
            postalNumber={form.postalCode}
            postalNumberF={form.postalNumberF}
            alcaldia={form.delegation}
            alcaldiaF={form.alcaldiaF}
            entidad={form.state.value}
            entidadF={form.entidadF}
            branch={form.brand}
            model={form.model}
            version={form.version}
            landline={form.landline}
            deliverHour={hour12Format}
            insurance={form.insurance.label}
            facturaAddress={form.facturaAddress}
            downPayment={form.downPayment}
          />

          <Signatures firstName={form.firstName} lastName={form.lastName} />

          <AnexoB
            city={form.city.value}
            date={form.deliverDate}
            firstName={form.firstName}
            lastName={form.lastName}
            brand={form.brand}
            model={form.model}
            version={form.version || 'No aplica'}
            vin={form.vin || 'No aplica'}
            policyNumber={form.policyNumber || 'No aplica'}
            plates={form.plates || 'No aplica'}
            km={form.km || 0}
            circulationCardNumber={form.circulationCardNumber || 0}
            newCar={form.newCar}
          />

          <AnexoC
            deliverDate={startDate ? startDate : justDate}
            weeklyRent={form.weeklyRent}
            totalWeeks={form.totalPays}
          />

          <Text style={{ fontFamily: 'Helvetica', fontSize: '8px', marginTop: '15px' }}>
            **El pago semanal señalado en el presente contrato por concepto de arrendamiento, se ajustará al
            año calendario, esto es, el primer día de los dos siguientes años, de conformidad con la inflación
            generada en el ejercicio anterior, publicada por el Banco Nacional de México. Esta modificación se
            verá reflejada en este anexo, generandose así una tabla actualizada con el nuevo costo semanal del
            arrendamiento.**
          </Text>

          <Signatures firstName={form.firstName} lastName={form.lastName} />

          <ActaEntrega
            contract={form.contractNumber}
            extensionCarNumber={form.extensionCarNumber}
            firstName={form.firstName}
            lastName={form.lastName}
            brand={form.brand}
            model={form.model}
            version={form.version || 'No aplica'}
            vin={form.vin || 'No aplica'}
            policyNumber={form.policyNumber || 'No aplica'}
            plates={form.plates || 'No aplica'}
            km={form.km || 0}
          />

          <Garantia firstName={form.firstName} lastName={form.lastName} />

          <AvisoPrivacidad firstName={form.firstName} lastName={form.lastName} />

          <UsoFactura
            city={form.city.label}
            date={form.deliverDate}
            firstName={form.firstName}
            lastName={form.lastName}
            brand={form.brand}
            model={form.model}
            version={form.version || 'No aplica'}
            vin={form.vin || 'No aplica'}
            policyNumber={form.policyNumber || 'No aplica'}
            plates={form.plates || 'No aplica'}
            km={form.km || 0}
          />

          <DatosContacto contacts={form.contacts} />

          {form.withAval ? (
            <Pagare
              firstName={form.firstName}
              lastName={form.lastName}
              weeklyRent={form.weeklyRent}
              city={form.city.label}
              priceWrite={form.finalSaleText}
              totalPays={form.totalPays}
              date={form.deliverDate}
              fullAddress={form.fullAddress}
              avalData={form.avalData}
            />

          ) : (
            <PagareWithNoAval
              fullName={`${form.firstName} ${form.lastName}`}
              state={form.state.value}
              address={form.fullAddress}
              deliveryDate={form.deliverDate}
              // totalPrice={form.finalPrice}
              totalPays={form.totalPays}
              weeklyRent={form.weeklyRent}
            />
          )}

        </View>

        <Text
          style={styles.showContractNumber}
          render={() => `${form.extensionCarNumber ? `${form.contractNumber}-${form.extensionCarNumber}` : form.contractNumber}`}
          fixed
        />
        <Text
          style={styles.pageNumber}
          render={({ pageNumber }) => `${pageNumber < 12 ? pageNumber : ''}`}
          fixed
        />
      </Page>
      {/* {
              form.calendar.isPersonal === 'true' ? <CalendarPage dateString={form.calendar.initialPayment} /> : null
          } */}
    </Document>
  );
}


