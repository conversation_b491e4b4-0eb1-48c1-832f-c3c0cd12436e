import { Text, View } from '@react-pdf/renderer';
import { readmissionDocStyles } from '../ReadmissionDocument';

interface SignsProps {
  name: string;
}

export default function Signs(props: SignsProps) {
  return (
    <View style={readmissionDocStyles.viewMain}>
      <View style={readmissionDocStyles.containerS}>
        <View style={readmissionDocStyles.content}>
          <View
            style={{
              marginTop: '100px',
              display: 'flex',
              width: '100%',
              gap: 8,
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {/* <Image src={sign} style={readmissionDocStyles.viewBackground} /> */}
            <Text style={{ ...readmissionDocStyles.dateAndWho }}>____________________________________</Text>
            <Text style={{ ...readmissionDocStyles.dateAndWho }}>
              E-MKT GOODS DE MÉXICO, S.A.P.I. DE C.V.
            </Text>
            <Text style={readmissionDocStyles.dateAndWho}>Por medio de su representante</Text>
          </View>
        </View>
      </View>
      <View style={readmissionDocStyles.containerS}>
        <View style={readmissionDocStyles.content}>
          <View
            style={{
              marginTop: '100px',
              display: 'flex',
              width: '100%',
              gap: 8,
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {/* <Image src={sign} style={readmissionDocStyles.viewBackground} /> */}
            <Text style={{ ...readmissionDocStyles.dateAndWho }}>____________________________________</Text>
            <Text style={{ ...readmissionDocStyles.dateAndWho }}>{props.name}</Text>
            <Text style={readmissionDocStyles.dateAndWho}>Por su propio derecho</Text>
          </View>
        </View>
      </View>
    </View>
  );
}
