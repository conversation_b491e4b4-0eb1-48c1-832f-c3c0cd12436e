'use client';
import { StyleSheet } from '@react-pdf/renderer';
import dynamic from 'next/dynamic';
import React from 'react';
import AgreementTerminationDocumentPDF from './AgreementTermination-DocumentPDF';

const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), {
  ssr: false,
});

const styles = StyleSheet.create({
  viewer: {
    width: '80%',
    height: '100vh',
  },
});

export default function AgreementTerminationPDFViewer() {
  return (
    <>
      <PDFViewer /* key={viewerKey} */ style={styles.viewer} showToolbar={false}>
        <AgreementTerminationDocumentPDF
          city="edomx"
          firstName="Pedro"
          lastName="Martinez"
          weeklyRent={1000}
          totalPays={10}
          contractNumber="123456"
          contractTerminationDate="2022-12-31"
          deliveryDate="2022-12-31"
          fullAddress="Calle 123, Colonia Centro, Monterrey Nuevo León, México"
        />
      </PDFViewer>
    </>
  );
}
