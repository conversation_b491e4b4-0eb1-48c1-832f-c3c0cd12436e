import AuthCricle1 from '@/components/Auth/AuthCricle1';
import AuthLogo from '@/components/Auth/AuthLogo';
import AuthCircle2 from '@/components/Auth/AuthCircle2';
import AuthContainer from '@/components/Auth/AuthContainer';
import AuthCricle3 from '@/components/Auth/AuthCricle3';
import AuthCircle4 from '@/components/Auth/AuthCircle4';
import AuthResetPassword from '@/components/Auth/AuthResetPassword';

export const metadata = {
  title: 'Recuperar contraseña',
};

export default function RecoverPassword() {
  return (
    <main className="w-full h-[100vh] relative bg-[#FAFAFF] overflow-hidden ">
      {/* <Image alt="background" src="/images/loginBackground.png" fill className="object-cover absolute z-0" /> */}
      <div className="w-full h-[100vh] flex flex-col justify-center items-center relative z-10 gap-4">
        <AuthCricle1 />
        <AuthLogo />
        <div className="relative w-full flex justify-center">
          <AuthCircle2 />
          <AuthContainer>
            <AuthResetPassword />
          </AuthContainer>
        </div>
      </div>
      <AuthCricle3 />
      <AuthCircle4 />
    </main>
  );
}
