import { ApiPath } from '@/constants/api.endpoints';
import { usePathname, useRouter } from 'next/navigation';

export const useStepperNavigation = () => {
  const router = useRouter();
  const pathName = usePathname();

  const naviteToClientDetailsPage = () => {
    router.replace(pathName.replace(`/${ApiPath.HOME_VISIT}`, ''));
    router.refresh();
  };

  return {
    naviteToClientDetailsPage,
  };
};
