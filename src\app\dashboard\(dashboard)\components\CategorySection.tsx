'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>on, <PERSON><PERSON><PERSON><PERSON>, MenuItem, Box, Text, Button } from '@chakra-ui/react';
import { AiOutlineDown } from 'react-icons/ai';

function TotalStatusSection({
  categoryName,
  total,
  subcategories,
  categoryTranslations,
  subCategoryTranslations,
}: {
  categoryName: string;
  total: number;
  subcategories?: Record<string, number>;
  categoryTranslations?: any;
  subCategoryTranslations?: any;
}) {
  return (
    <Box
      bg="white"
      shadow="sm"
      rounded="md"
      minWidth="150px"
      maxWidth="200px"
      height="60px"
      p={3}
      display="flex"
      justifyContent="space-between"
      alignItems="center"
    >
      {/* Category Name */}
      <Text fontSize="sm" fontWeight="medium">
        {categoryTranslations?.[categoryName as string]}
      </Text>

      {/* Total and Dropdown */}
      <Box display="flex" alignItems="center" gap={2}>
        {/* Total */}
        <Text fontSize="md" fontWeight="bold">
          {total}
        </Text>

        {/* Dropdown */}
        {subcategories && Object.keys(subcategories).length > 0 && (
          <Menu>
            <MenuButton
              as={Button}
              variant="unstyled"
              height="auto"
              minWidth="auto"
              p={0}
              _hover={{ bg: 'none' }}
              _focus={{ boxShadow: 'none' }}
            >
              <AiOutlineDown color="#5800F7" size={14} />
            </MenuButton>
            <MenuList bg="gray.50" p={1} minWidth="150px" borderRadius="md">
              {Object.entries(subcategories).map(([subName, subCount], index) => (
                <MenuItem
                  key={index}
                  _hover={{ bg: 'none' }}
                  _focus={{ bg: 'none' }}
                  pointerEvents="none" // Disable interactivity
                  display="flex"
                  justifyContent="space-between"
                  fontSize="sm"
                  px={4}
                  py={2}
                  color="gray.700"
                >
                  <Box display="flex" justifyContent="space-between" width="100%" gap={'2%'}>
                    <Text>{subCategoryTranslations?.[subName as string]}</Text>
                    <Text>({subCount})</Text>
                  </Box>
                </MenuItem>
              ))}
            </MenuList>
          </Menu>
        )}
      </Box>
    </Box>
  );
}

export default TotalStatusSection;
