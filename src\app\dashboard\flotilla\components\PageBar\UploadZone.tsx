import React from 'react';
import { useState, useRef } from 'react';
import { Image, Tooltip, useToast } from '@chakra-ui/react';
import FileIcon from '../others/FileIcon';
import ProgressBar from '../others/ProgressBar';
import RemoveIcon from '../others/RemoveIcon';
import { documentUploadTranslationsMX } from '@/constants/translations';

interface FileUploaderProps {
  translations?: any;
  /** File input accept attribute, e.g. ".pdf,.png" */
  accept: string;
  /** Allowed file extensions without dots, e.g. ["pdf", "png"] */
  allowedExtensions: string[];
  multiple?: boolean;
  maxFiles?: number;
  /** Maximum file size in bytes */
  maxFileSize?: number;
  isLoading?: boolean;
  loaderDuration?: number;
  /** Callback invoked with selected files array */
  onFilesChange: (files: File[]) => void;
  /** Optional upload progress percentage (0-100) */
  progress?: number;
  /** Customizabled text labels */
  labels?: {
    uploadingText?: string;
    selectedFilesTitle?: string;
    moreLabel?: string;
    lessLabel?: string;
    emptyPlaceholder: React.ReactNode;
  };
}

// Helper to filter out duplicate files by content hash and rename duplicates by appending an incremented marker
async function processFiles(
  newFiles: File[],
  existingList: { file: File; hash: string }[]
): Promise<{ file: File; hash: string }[]> {
  const results: { file: File; hash: string }[] = [];
  for (const file of newFiles) {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hash = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');
    if (existingList.some((obj) => obj.hash === hash)) {
      continue;
    }
    const name = file.name;
    const dotIndex = name.lastIndexOf('.');
    const base = dotIndex !== -1 ? name.slice(0, dotIndex) : name;
    const ext = dotIndex !== -1 ? name.slice(dotIndex) : '';
    const count = existingList.filter((obj) => {
      const existingName = obj.file.name;
      return existingName === name || existingName.startsWith(base + ' ');
    }).length;
    const newName = count ? `${base} ${count + 1}${ext}` : name;
    const newFile = new File([file], newName, { type: file.type });
    results.push({ file: newFile, hash });
  }
  return results;
}

export default function FileUploader({
  translations = documentUploadTranslationsMX, // Default to MX translations; replace with dynamic locale logic
  accept,
  allowedExtensions,
  multiple = true,
  maxFiles = 200,
  maxFileSize = 5 * 1024 * 1024, // Default 5MB in bytes
  isLoading = false,
  loaderDuration = 0,
  onFilesChange,
  progress,
  labels,
}: FileUploaderProps) {
  const [fileNames, setFileNames] = useState<string[]>([]);
  const [showAllFiles, setShowAllFiles] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const filesRef = useRef<{ file: File; hash: string }[]>([]);
  const toast = useToast();

  const defaultLabels = {
    uploadingText: translations.uploadingText,
    selectedFilesTitle: translations.selectedFilesTitle,
    moreLabel: translations.moreLabel,
    lessLabel: translations.lessLabel,
    filesRequirements: translations.filesRequirements(maxFiles, Math.round(maxFileSize / (1024 * 1024))),
    allowedFormats: translations.allowedFormats(allowedExtensions),
    emptyPlaceholder: <p className="mt-4">{translations.emptyPlaceholder}</p>,
  };
  const mergedLabels = { ...defaultLabels, ...(labels || {}) };
  const {
    uploadingText,
    selectedFilesTitle,
    moreLabel,
    lessLabel,
    filesRequirements,
    allowedFormats,
    emptyPlaceholder,
  } = mergedLabels;

  const validateFiles = (filesList: FileList): boolean => {
    const invalid = Array.from(filesList).filter((f) => {
      const ext = f.name.toLowerCase().split('.').pop() || '';
      return !allowedExtensions.includes(ext);
    });
    if (invalid.length) {
      toast({
        title: translations.allowedFormats(allowedExtensions),
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
      return false;
    }
    if (maxFileSize) {
      const tooLarge = Array.from(filesList).filter((f) => f.size > maxFileSize);
      if (tooLarge.length) {
        toast({
          title: translations.filesRequirements(maxFiles, Math.round(maxFileSize / (1024 * 1024))),
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return false;
      }
    }
    return true;
  };

  const updateFileList = () => {
    const filesArr = filesRef.current.map((obj) => obj.file);
    setFileNames(filesArr.map((f) => f.name));
    onFilesChange(filesArr);
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const fl = e.target.files;
    if (fl && fl.length) {
      if (!validateFiles(fl)) {
        e.target.value = '';
        return;
      }
      const processed = await processFiles(Array.from(fl), filesRef.current);
      if (!processed.length) {
        toast({
          title: translations.duplicateFileError,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        e.target.value = '';
        return;
      }
      if (processed.length < fl.length) {
        toast({
          title: translations.duplicateFileError,
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
      }
      if (filesRef.current.length + processed.length > maxFiles) {
        toast({
          title: filesRequirements,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        e.target.value = '';
        return;
      }
      filesRef.current.push(...processed);
      e.target.value = '';
      updateFileList();
    }
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    const items = e.dataTransfer.files;
    if (items.length && validateFiles(items)) {
      const processed = await processFiles(Array.from(items), filesRef.current);
      if (!processed.length) {
        toast({
          title: translations.duplicateFileError,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      if (processed.length < items.length) {
        toast({
          title: translations.duplicateFileError,
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
      }
      if (filesRef.current.length + processed.length > maxFiles) {
        toast({
          title: filesRequirements,
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      filesRef.current.push(...processed);
      updateFileList();
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleBrowseClick = () => fileInputRef.current?.click();

  const toggleShowAllFiles = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowAllFiles(!showAllFiles);
  };

  const handleRemoveFile = (i: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    filesRef.current.splice(i, 1);
    updateFileList();
  };

  const displayed = showAllFiles ? fileNames : fileNames.slice(0, 4);

  return (
    <div
      className="border-2 border-dashed border-[#5E5E5E] rounded-lg p-10 bg-[#F2F2F2] text-center cursor-pointer"
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onClick={handleBrowseClick}
    >
      <div className="flex flex-col items-center justify-center">
        <Image src="/images/Upload.svg" alt="Upload icon" className="mb-4" />
        <p className="mb-2 text-sm text-gray-500">{filesRequirements}</p>
        <p className="mb-2 text-sm text-gray-500">{allowedFormats}</p>
        {isLoading ? (
          <div className="w-full">
            <div className="flex justify-between mb-4">
              <p className="font-medium">{uploadingText}</p>
              <p className="text-sm">({fileNames.length} files)</p>
            </div>
            {/* Use actual progress percentage if provided, otherwise fallback to loaderDuration animation */}
            <ProgressBar progress={progress} duration={loaderDuration} />
          </div>
        ) : fileNames.length ? (
          <div className="w-full">
            <div className="flex justify-between mb-4">
              <p className="font-medium">{selectedFilesTitle}</p>
              <p className="text-sm">({fileNames.length} files)</p>
            </div>
            <div
              className={`grid grid-cols-4 gap-4 ${
                !showAllFiles ? 'max-h-[200px] overflow-hidden' : 'max-h-[400px] overflow-y-auto'
              }`}
            >
              {displayed.map((name, i) => (
                <div key={i} className="relative p-3 bg-white rounded-lg shadow-sm">
                  <button
                    type="button"
                    onClick={(e) => handleRemoveFile(i, e)}
                    className="absolute top-1 right-1 text-gray-400 hover:text-red-500 z-10"
                  >
                    <RemoveIcon />
                  </button>
                  <FileIcon size={32} className="mb-2" />
                  <Tooltip label={name}>
                    <p className="truncate w-full text-sm">{name}</p>
                  </Tooltip>
                </div>
              ))}
            </div>
            {fileNames.length > 4 && (
              <button type="button" onClick={toggleShowAllFiles} className="mt-4 text-[#5800F7] font-medium">
                {showAllFiles ? lessLabel : moreLabel}
              </button>
            )}
          </div>
        ) : (
          <>{emptyPlaceholder}</>
        )}
        <input
          type="file"
          accept={accept}
          multiple={multiple}
          className="hidden"
          ref={fileInputRef}
          onChange={handleFileSelect}
        />
      </div>
    </div>
  );
}
