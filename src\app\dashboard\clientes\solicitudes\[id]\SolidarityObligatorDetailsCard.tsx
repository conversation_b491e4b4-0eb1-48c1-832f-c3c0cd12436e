import CustomInput from '@/components/Inputs/CustomInput';
import InputFile from '@/components/Inputs/InputFile';
import {
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  Modal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Stack,
  Text,
  useToast,
} from '@chakra-ui/react';
import { Form, Formik } from 'formik';
import * as Yup from 'yup';
import { useState, useTransition } from 'react';
import { FaAddressCard, FaEnvelope, FaPencilAlt, FaPhone, FaUser } from 'react-icons/fa';
import { addAvalData } from '@/actions/addAvalData';
import { RequestDocument } from '@/components/DriverDetails/DriverDocumentsCard';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';

export default function SolidarityObligatorDetailsCard({
  requestId,
  user,
  solidarityObligatorDetails,
  setDocumentsAnalysis,
  avalDocs,
  setSelectedDriverData,
}: {
  requestId?: string;
  user?: MyUser;
  solidarityObligatorDetails?: SolidarityObligatorDetails;
  setDocumentsAnalysis?: React.Dispatch<React.SetStateAction<{}>>;
  avalDocs?: RequestDocument[];
  setSelectedDriverData?: React.Dispatch<React.SetStateAction<{}>>;
}) {
  const [nameFiles, setNameFiles] = useState({
    solidarity_obligor_identity_card_front: '',
    solidarity_obligor_identity_card_back: '',
  });
  const toast = useToast();
  const [isTransitioning, startTransition] = useTransition();

  const { data: session } = useSession();
  const sessionUser = session?.user as unknown as MyUser;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formValues, setFormValues] = useState({
    name: '',
    phone: '',
    email: '',
    location: '',
    solidarity_obligor_identity_card_front: '',
    solidarity_obligor_identity_card_back: '',
  });

  const mergeDocumentsAnalysis = (prevState: any, newDocumentsAnalysis: any) => {
    if (!prevState || !prevState.documents) {
      return newDocumentsAnalysis;
    }

    if (!newDocumentsAnalysis || !newDocumentsAnalysis?.documents) {
      return prevState;
    }

    const existingDocsMap = new Map(prevState?.documents?.map((doc: any) => [doc.type, doc]));

    newDocumentsAnalysis?.documents?.forEach((newDoc: any) => {
      existingDocsMap?.set(newDoc.type, newDoc);
    });

    return {
      status: newDocumentsAnalysis.status,
      documents: Array.from(existingDocsMap?.values()),
    };
  };

  const setFormValuesFromAvalData = () => {
    let frontDoc: RequestDocument | undefined;
    let backDoc: RequestDocument | undefined;
    if (avalDocs) {
      const flattenedDocs = avalDocs?.flat() || [];

      frontDoc = flattenedDocs.find(
        (doc: RequestDocument) => doc?.type === 'solidarity_obligor_identity_card_front'
      );
      backDoc = flattenedDocs.find(
        (doc: RequestDocument) => doc?.type === 'solidarity_obligor_identity_card_back'
      );

      setNameFiles({
        solidarity_obligor_identity_card_front: frontDoc?.media?.fileName || '',
        solidarity_obligor_identity_card_back: backDoc?.media?.fileName || '',
      });
      setFormValues((prevValues) => ({
        ...prevValues,
        solidarity_obligor_identity_card_front: frontDoc?.media?.fileName || '',
        solidarity_obligor_identity_card_back: backDoc?.media?.fileName || '',
      }));
    }
    if (solidarityObligatorDetails) {
      setFormValues((prev) => ({
        ...prev,
        name: solidarityObligatorDetails.name || '',
        phone: solidarityObligatorDetails.phone || '',
        email: solidarityObligatorDetails.email || '',
        location: solidarityObligatorDetails.location || '',
      }));
    }
  };

  const handleSetNames = (name: string, value: string) => {
    setNameFiles((prev: any) => ({ ...prev, [name]: value }));
  };

  const onClose = () => {
    setNameFiles({
      solidarity_obligor_identity_card_front: '',
      solidarity_obligor_identity_card_back: '',
    });
    setIsModalOpen(false);
  };

  const validationSchema = Yup.object({
    name: Yup.string().required('Nombre es requerido').min(2).max(255),
    phone: Yup.string().required('Teléfono es requerido').min(10).max(14),
    email: Yup.string().email('Email inválido').required('Email es requerido'),
    location: Yup.string().required('Dirección es requerida'),
    solidarity_obligor_identity_card_front: Yup.string().required('INE frontal es requerido'),
    solidarity_obligor_identity_card_back: Yup.string().required('INE reverso es requerido'),
  });

  function extractRequestId(defaultId?: string): string | undefined {
    const currentPath = window.location.pathname;
    return currentPath.includes('clientes/solicitudes')
      ? currentPath.split('/').pop() || defaultId
      : defaultId;
  }

  function handleApiResponse(response: any) {
    if (response.success) {
      if (window.location.pathname.includes('clientes/solicitudes')) {
        startTransition(() => {
          setTimeout(() => {
            window.location.reload();
          }, 2500);

          toast({
            title: 'información del endosante actualizada exitosamente',
            description: 'Recargando página...',
            status: 'success',
          });
        });
      } else {
        toast({
          title: 'información del endosante actualizada exitosamente',
          status: 'success',
        });
        setDocumentsAnalysis?.((prevState: any) =>
          mergeDocumentsAnalysis(prevState, response?.data?.documentsAnalysis)
        );
        setSelectedDriverData?.((prev: any) => ({
          ...prev,
          avalData: response?.data?.avalData,
        }));
      }
    } else {
      toast({
        title: 'error al actualizar la información del endosante',
        status: 'error',
      });
    }
  }

  const handleSubmit = async (values: any) => {
    setIsSubmitting(true);

    const formData = new FormData();
    formData.append('name', values.name);
    formData.append('phone', values.phone);
    formData.append('email', values.email);
    formData.append('location', values.location);

    if (values.solidarity_obligor_identity_card_front) {
      formData.append(
        'solidarity_obligor_identity_card_front',
        values.solidarity_obligor_identity_card_front
      );
    }
    if (values.solidarity_obligor_identity_card_back) {
      formData.append('solidarity_obligor_identity_card_back', values.solidarity_obligor_identity_card_back);
    }

    try {
      const authUser = user || sessionUser;
      const submissionRequestId = extractRequestId(requestId);

      const response = await addAvalData({
        requestId: submissionRequestId,
        requestPayload: formData,
        user: authUser,
      });
      handleApiResponse(response);
    } catch (error) {
      console.error('Error uploading Aval data:', error);
      toast({
        title: 'error al actualizar la información del endosante',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsSubmitting(false);
      onClose();
    }
  };

  const ability = usePermissions();
  const canAdd = canPerform(ability, Capabilities.AddGuarantee, Sections.Clients, Subsections.Admissions);
  const canEdit = canPerform(ability, Capabilities.EditGuarantee, Sections.Clients, Subsections.Admissions);

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            Detalles del Obligado Solidario
          </Heading>
          {solidarityObligatorDetails && canEdit && (
            <FaPencilAlt
              color="#5800F7"
              size={18}
              cursor="pointer"
              onClick={() => {
                setIsModalOpen(true);
                setFormValuesFromAvalData();
              }}
            />
          )}
        </Flex>
      </CardHeader>

      <CardBody>
        {solidarityObligatorDetails ? (
          <>
            <Stack direction={'row'} spacing={8} mt={2}>
              <Flex align="center">
                <FaUser size={14} />
                <Text ml={2} fontSize="sm" color="gray.900">
                  {solidarityObligatorDetails?.name || 'N/A'}
                </Text>
              </Flex>
              <Flex align="center">
                <FaPhone size={14} />
                <Text ml={2} fontSize="sm" color="gray.900">
                  {solidarityObligatorDetails?.phone || 'N/A'}
                </Text>
              </Flex>
            </Stack>
            <Stack direction={'row'} spacing={8} mt={2}>
              <Flex align="center">
                <FaEnvelope size={14} />
                <Text ml={2} fontSize="sm" color="gray.900">
                  {solidarityObligatorDetails?.email || 'N/A'}
                </Text>
              </Flex>
              <Flex align="center">
                <FaAddressCard size={14} />
                <Text ml={2} fontSize="sm" color="gray.900">
                  {solidarityObligatorDetails?.location || 'N/A'}
                </Text>
              </Flex>
            </Stack>
          </>
        ) : (
          <>
            <Text textAlign={'center'}>Datos del Deudor Solidario no encontrados</Text>
            <Flex justifyContent="center" mt={4}>
              {canAdd && (
                <Button
                  className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
                  onClick={() => {
                    setIsModalOpen(true);
                    setFormValuesFromAvalData();
                  }}
                >
                  Agregar Aval
                </Button>
              )}
            </Flex>
          </>
        )}
      </CardBody>

      <Modal isOpen={isModalOpen} onClose={onClose} size={'xl'}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Agregar Aval</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Formik initialValues={formValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
              {({ dirty }) => (
                <Form>
                  <div className="grid grid-cols-2 gap-4">
                    <CustomInput label="Nombre" name="name" type="text" />
                    <CustomInput label="Teléfono" name="phone" type="text" />
                    <CustomInput label="Email" name="email" type="text" />
                    <CustomInput label="Dirección" name="location" type="text" />

                    <InputFile
                      name="solidarity_obligor_identity_card_front"
                      label="INE del Obligado Solidario (Frontal)"
                      accept="all-images"
                      nameFile={nameFiles.solidarity_obligor_identity_card_front}
                      handleSetName={handleSetNames}
                      placeholder=""
                      buttonText="Subir archivo"
                    />
                    <InputFile
                      name="solidarity_obligor_identity_card_back"
                      label="INE del Obligado Solidario (Reverso)"
                      accept="all-images"
                      nameFile={nameFiles.solidarity_obligor_identity_card_back}
                      handleSetName={handleSetNames}
                      placeholder=""
                      buttonText="Subir archivo"
                    />
                  </div>
                  <ModalFooter>
                    <Button
                      sx={{
                        color: 'white',
                        h: '40px',
                      }}
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      type="submit"
                      disabled={!dirty || isSubmitting || isTransitioning}
                      isLoading={isSubmitting || isTransitioning}
                    >
                      Guardar
                    </Button>
                  </ModalFooter>
                </Form>
              )}
            </Formik>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Card>
  );
}
