import CustomInput from '@/components/Inputs/CustomInput';
import InputFile from '@/components/Inputs/InputFile';
import InputPrice from '@/components/Inputs/InputPrice';
import { Countries, MEXICAN_CURRENCY, URL_API, US_COLORS, US_CURRENCY, colors } from '@/constants';
import SelectInput from '@/components/Inputs/SelectInput';
import InputDate from '@/components/Inputs/InputDate';
import { useFormikContext } from 'formik';
import { CalenderComp } from '@/components/CustomCalender';
import { format } from 'date-fns';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import lodash from 'lodash';
import { toast } from 'sonner';

interface IVariation {
  version: string;
  year: number;
}
interface IModel {
  name: string;
  variations: IVariation[];
}
interface ICar extends Document {
  make: string;
  models: IModel[];
}

export const MaxicoVehicleRegisterationForm = (props: any) => {
  const { contractNumber, handleSetName, nameFiles, country } = props;

  const [vehicleData, setVehicleData] = useState<ICar[]>();
  const [makeList, setMakeList] = useState<string[]>([]);
  const [makeVal, setMakeVal] = useState('');
  const [modelList, setModleList] = useState<string[]>([]);
  const [modelVal, setModelVal] = useState<string>('');
  const [versionList, setVersionList] = useState<string[]>([]);
  const [versionVal, setVersionVal] = useState<string>('');
  const [yearList, setYearList] = useState<number[]>([]);

  const form = useFormikContext();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  useEffect(() => {
    //fetch vehicle data form server
    async function getVehicleData(accessToken: string) {
      try {
        const response = await fetch(`${URL_API}/car/cars`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        // Parse the JSON data
        const result = await response.json();
        setVehicleData(result);
      } catch (error) {
        // Set the error state if something went wrong
        toast.error('Unable to fetch car data. Please contact admin');
        console.log(error);
      }
    }
    getVehicleData(user.accessToken);
  }, [user?.accessToken]);

  useEffect(() => {
    if (vehicleData && vehicleData.length > 0) {
      const makeData: string[] = [];
      vehicleData.forEach((vehicle) => {
        makeData.push(vehicle.make);
      });
      makeData.sort();
      setMakeList(lodash.cloneDeep(makeData));
    }
    setModleList([]);
    setVersionList([]);
    setYearList([]);
  }, [vehicleData]);

  useEffect(() => {
    if (vehicleData && vehicleData.length > 0 && makeVal) {
      const modelData = vehicleData.find((vehicle) => vehicle.make === makeVal);
      // If the vehicle is found, return the list of model names
      if (modelData) {
        const updateList = lodash.cloneDeep(modelData.models.map((model) => model.name));
        updateList.sort();
        setModleList(updateList);
      }
      setModelVal('');
      setVersionList([]);
      setVersionVal('');
      setYearList([]);
      form.setFieldValue('model', { label: 'Selecciona', value: '' });
      form.setFieldValue('version', { label: 'Selecciona', value: '' });
      form.setFieldValue('year', { label: 'Selecciona', value: '' });
    }
  }, [makeVal]);

  useEffect(() => {
    if (vehicleData && vehicleData.length > 0 && modelVal) {
      const makeDataVal = vehicleData.find((vehicle) => vehicle.make === makeVal);

      if (makeDataVal) {
        // Find the model object that matches the given model
        const modelObj = makeDataVal.models.find((m) => m.name === modelVal);

        if (modelObj) {
          // Return the list of variations for the model
          const uniqueVersion = new Set<string>();
          modelObj.variations.forEach((variation) => {
            uniqueVersion.add(variation.version);
          });
          const updatedList = lodash.cloneDeep(Array.from(uniqueVersion).sort());
          setVersionList(updatedList);
        }
      }
      setVersionVal('');
      setYearList([]);
      form.setFieldValue('version', { label: 'Selecciona', value: '' });
      form.setFieldValue('year', { label: 'Selecciona', value: '' });
    }
  }, [modelVal]);

  useEffect(() => {
    if (vehicleData && vehicleData.length > 0 && versionVal) {
      const makeDataVal = vehicleData.find((vehicle) => vehicle.make === makeVal);

      if (makeDataVal) {
        // Find the model object that matches the given model
        const modelObj = makeDataVal.models.find((m) => m.name === modelVal);

        if (modelObj) {
          // Return the list of variations for the model
          const uniqueYear = new Set<number>();
          const variations = modelObj.variations.filter((variation) => {
            return variation.version === versionVal;
          });
          variations.forEach((variation) => {
            uniqueYear.add(variation.year);
          });
          const updatedList = lodash.cloneDeep(Array.from(uniqueYear).sort());
          setYearList(updatedList);
        }
      }
      form.setFieldValue('year', { label: 'Selecciona', value: '' });
    }
  }, [versionVal]);

  return (
    <div className="grid gap-2">
      <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
        <div className="flex flex-col">
          <label className="block text-gray-700 text-[16px] mb-2">ID o No. de Vehiculo</label>
          <div
            data-cy="car_number"
            className="border-[#9CA3AF]
                border-2
                text-black
                rounded
                px-3
                py-2
                h-[40px]
                w-full
                outline-none
                background-color-[#F3F4F6]
                "
          >
            {contractNumber}
          </div>
        </div>
        <SelectInput
          label="Marca"
          name="brand"
          dataCy="brand-selector"
          onChange={(option) => {
            setMakeVal(lodash.cloneDeep(option.value));
          }}
          options={makeList.map((make) => ({
            value: make,
            label: make,
          }))}
        />
        <SelectInput
          label="Modelo"
          name="model"
          dataCy="model-selector"
          onChange={(option) => {
            setModelVal(lodash.cloneDeep(option.value));
          }}
          options={modelList.map((model) => ({
            value: model,
            label: model,
          }))}
        />
        <SelectInput
          label="Version"
          name="version"
          dataCy="version-selector"
          onChange={(option) => {
            setVersionVal(lodash.cloneDeep(option.value));
          }}
          options={versionList.map((version) => ({
            value: version,
            label: version,
          }))}
        />
        <SelectInput
          label="Año"
          name="year"
          dataCy="year-selector"
          options={yearList.map((year) => ({
            value: year.toString(),
            label: year.toString(),
          }))}
        />
        {/* <CustomInput label="Color" name="color" type="text" /> */}
        <SelectInput label="Color" name="color" options={colors} inputId="203" dataCy="color-selector" />
        <CustomInput label="VIN o Serie" name="vin" type="text" />
        <CustomInput label="Dueño" name="owner" type="text" />
        <InputPrice
          label="Valor factura"
          name="billAmount"
          placeholder="0.00"
          currencySymbol={country === Countries.Mexico ? 'MX' : 'USD'}
        />
        <CustomInput label="No. Factura" name="billNumber" type="text" />
        <CustomInput label="Kilometros" name="km" type="number" />
        <InputDate label="Fecha de recepción" name="receptionDate" />
      </div>
      <div className="grid grid-cols-2 ">
        <InputFile
          accept="pdf"
          buttonText="Subir archivo"
          handleSetName={handleSetName}
          label="Factura PDF"
          name="bill"
          nameFile={nameFiles.bill}
          placeholder="Subir un archivo"
        />
        <span className="ml-2">
          <InputDate label="Fecha de factura" name="billDate" />
        </span>
      </div>

      <SelectInput
        label="¿Es auto electrico?"
        options={[
          { value: 'false', label: 'No' },
          { value: 'true', label: 'Si' },
        ]}
        name="isElectric"
      />
    </div>
  );
};

export const USVehicleRegisterationForm = (props: any) => {
  const { contractNumber, handleSetName, nameFiles, country } = props;
  const form = useFormikContext();
  return (
    <div className="grid gap-2">
      <div className="grid w-full grid-cols-2 gap-x-4 gap-y-3">
        <div className="flex flex-col">
          <label className="block text-gray-700 text-[16px] mb-2">{'Vehicle Id or No'}</label>
          <div
            data-cy="car_number"
            className="border-[#9CA3AF]
            border-2
            text-black
            rounded
            px-3
            py-2
            h-[40px]
            w-full
            outline-none
            background-color-[#F3F4F6]
            "
          >
            {contractNumber}
          </div>
        </div>
        <CustomInput label="Brand" name="brand" type="text" />
        <CustomInput label="Model" name="model" type="text" />
        <CustomInput label="Version" name="version" type="text" />
        <CustomInput label="Year" name="year" type="text" />
        <SelectInput
          label="Color"
          name="color"
          options={US_COLORS}
          inputId="203"
          dataCy="color-selector"
          placeholder={'Select'}
        />
        <CustomInput label="VIN or Serial" name="vin" type="text" />
        <CustomInput label="Owner" name="owner" type="text" />
        <InputPrice
          label="Invoice value"
          name="billAmount"
          placeholder="0.00"
          currencySymbol={country === Countries.Mexico ? MEXICAN_CURRENCY : US_CURRENCY}
        />
        <CustomInput label="Miles" name="mi" type="number" />
        <div>
          <label className="block text-gray-700 text-[16px] mb-2">{'Recieved date'}</label>
          <CalenderComp
            form={form}
            fieldName="receptionDate"
            onChange={(date) => {
              const formattedDate = format(new Date(date as Date), 'yyyy-MM-dd');
              form.setFieldTouched('receptionDate', true);
              form.setFieldValue('receptionDate', formattedDate);
            }}
          />
        </div>
        <CustomInput label="Bill Number" name="billNumber" type="text" />
      </div>
      <div className="grid grid-cols-2 ">
        <InputFile
          accept="pdf"
          buttonText="Upload file"
          handleSetName={handleSetName}
          label="PDF invoice"
          name="bill"
          nameFile={nameFiles.bill}
          placeholder="Upload a file"
        />
        <span className="ml-2">
          <label className="block text-gray-700 text-[16px] mb-2">{'Invoice date'}</label>
          <CalenderComp
            form={form}
            fieldName="billDate"
            onChange={(date) => {
              const formattedDate = format(new Date(date as Date), 'yyyy-MM-dd');
              form.setFieldTouched('billDate', true);
              form.setFieldValue('billDate', formattedDate);
            }}
          />
        </span>
      </div>
    </div>
  );
};

export const VehicleRegisteration = (props: any) => {
  const { country, contractNumber, handleSetName, nameFiles } = props;

  return (
    <div>
      {country === Countries.Mexico ? (
        <MaxicoVehicleRegisterationForm
          contractNumber={contractNumber}
          handleSetName={handleSetName}
          nameFiles={nameFiles}
          country={country}
        />
      ) : country === Countries['United States'] ? (
        <USVehicleRegisterationForm
          country={country}
          contractNumber={contractNumber}
          nameFiles={nameFiles}
          handleSetName={handleSetName}
        />
      ) : null}
    </div>
  );
};
