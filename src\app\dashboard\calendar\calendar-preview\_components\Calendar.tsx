'use client';
import { ReactNode, useState } from 'react';
import { Calendar, Formats, luxonLocalizer, Messages, Event, View } from 'react-big-calendar';
import 'react-big-calendar/lib/css/react-big-calendar.css';
import { DateTime } from 'luxon';
import { EventModal } from './EventModal';
import { cn } from '@/lib/utils';
import { AppointmentStatus } from '../../types';

const messages: Messages = {
  date: 'Fecha',
  time: 'Hora',
  event: 'Evento',
  allDay: 'Todo el día',
  week: 'Semana',
  work_week: 'Semana laboral',
  day: 'Día',
  month: 'Mes',
  previous: 'Anterior',
  next: 'Siguiente',
  yesterday: 'Ayer',
  tomorrow: 'Mañana',
  today: 'Hoy',
  agenda: 'Agenda',
  showMore: (total: number) => `+${total} más`,
  noEventsInRange: 'No hay citas en este rango.',
};

const formats: Formats = {
  dateFormat: 'dd',
  dayFormat: (date: Date) => {
    const day = DateTime.fromJSDate(date).setLocale('es').toFormat('dd ccc');
    const [dayNum, dayName] = day.split(' ');
    return `${dayNum} ${dayName.charAt(0).toUpperCase() + dayName.slice(1)}`;
  },
  dayHeaderFormat: (date: Date) => {
    const day = DateTime.fromJSDate(date).setLocale('es').toFormat('cccc d');
    const [dayName, dayNum] = day.split(' ');
    return `${dayName.charAt(0).toUpperCase() + dayName.slice(1)} ${dayNum}`;
  },
  monthHeaderFormat: (date: Date) => {
    const month = DateTime.fromJSDate(date).setLocale('es').toFormat('MMMM yyyy');
    const [monthName, year] = month.split(' ');
    return `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} ${year}`;
  },
  dayRangeHeaderFormat: ({ start, end }: any) => {
    const months = [
      'Enero',
      'Febrero',
      'Marzo',
      'Abril',
      'Mayo',
      'Junio',
      'Julio',
      'Agosto',
      'Septiembre',
      'Octubre',
      'Noviembre',
      'Diciembre',
    ];

    const startDate = DateTime.fromJSDate(start);
    const endDate = DateTime.fromJSDate(end);

    if (startDate.month === endDate.month) {
      return `${startDate.day} - ${endDate.day} de ${months[startDate.month - 1]}`;
    }

    return `${startDate.day} de ${months[startDate.month - 1]} - ${endDate.day} de ${
      months[endDate.month - 1]
    }`;
  },
  eventTimeRangeFormat: () => '',
};

interface ICalendarPreviewProps {
  events: Event[];
  children: ReactNode;
}

const eventStyleGetter = (event: any) => {
  const borderColor = event?.user?.homeVisitorColor || '#54A0CA';

  return {
    style: {
      color: '#3B3E45',
      backgroundColor: '#F3F4F6',
      borderRadius: '4px',
      borderColor: '#000',
      borderWidth: '.13em',
      border: 0,
      borderLeft: `5px solid ${borderColor}`,
    },
  };
};

const EventComponent = ({ event }: { event: any }) => {
  const cancelledStatusStyles =
    event.status === AppointmentStatus.canceled ? 'line-through decoration-red-500' : '';
  const completedStatusStyles =
    event.status === AppointmentStatus.completed ? 'line-through decoration-green-500' : '';

  return (
    <>
      <span className={cn('font-bold text-[#3B3E45] text-xs ', cancelledStatusStyles, completedStatusStyles)}>
        {event?.user?.name}
      </span>
      <span className="text-xs block text-[#666E7D]">{event?.title}</span>
    </>
  );
};

const EventComponentMonthView = ({ event }: { event: any }) => {
  const cancelledStatusStyles =
    event.status === AppointmentStatus.canceled ? 'line-through decoration-red-500' : '';
  const completedStatusStyles =
    event.status === AppointmentStatus.completed ? 'line-through decoration-green-500' : '';
  return (
    <span className={cn('text-sm font-bold text-[#3B3E45]', cancelledStatusStyles, completedStatusStyles)}>
      {event.user?.name}
    </span>
  );
};

const CustomWeekDayHeader = ({ label }: { label: string }) => (
  <div className="bg-white text-[#3B3E45] text-center p-2">{label}</div>
);

export default function CalendarPreview(props: ICalendarPreviewProps) {
  const { events, children } = props;

  const [selectedEvent, setSelectedEvent] = useState(null);
  const [view, setView] = useState<View>('week');

  const eventsDb = Array.isArray(events)
    ? events.map((event: any) => {
        const startTimeLocal = DateTime.fromISO(event.startTime, { zone: 'utc' }).setZone('local').toJSDate();
        const endTimeLocal = DateTime.fromISO(event.endTime, { zone: 'utc' }).setZone('local').toJSDate();
        return {
          ...event,
          start: startTimeLocal,
          end: endTimeLocal,
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        };
      })
    : [];
  const locale = Intl.DateTimeFormat().resolvedOptions().locale;

  return (
    <div>
      <p className="text-gray-700 text-bold text-2xl">Calendario de citas</p>
      {children}
      <div style={{ height: 600 }} className={`${view}-view py-4`}>
        <Calendar
          className=" "
          localizer={luxonLocalizer(DateTime)}
          events={eventsDb}
          min={new Date('2024-12-20T00:00:00')}
          max={new Date('2024-12-20T23:59:59')}
          startAccessor="start"
          endAccessor="end"
          step={30}
          timeslots={1}
          views={['month', 'week', 'day']}
          culture={locale}
          defaultView={view}
          onView={(calendarView) => setView(calendarView)}
          messages={messages}
          formats={formats}
          eventPropGetter={eventStyleGetter}
          onSelectEvent={(event: any) => setSelectedEvent(event)}
          components={{
            event: EventComponent,
            month: {
              event: EventComponentMonthView,
              header: CustomWeekDayHeader,
            },
          }}
          popup
        />
        {selectedEvent && (
          <EventModal isOpen={!!selectedEvent} onClose={() => setSelectedEvent(null)} event={selectedEvent} />
        )}
      </div>
    </div>
  );
}
