import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lex, Heading, useToast } from '@chakra-ui/react';
import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useState } from 'react';
import { HookFormRadixUIField } from '../home-visit/_components/HookFormField';
import { useRouter } from 'next/navigation';
import { updateAdmissionRequestPersonalData } from '@/actions/postAdmissionRequest';

const CreditCheckSchema = z.object({
  motorVehicleRecordCheck: z.string().min(1, { message: 'MVR check is required' }),
});

export const MotorVehicleRecordCheck = (props: any) => {
  const { admissionRequest } = props;
  const { id: requestId, personalData } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();

  const form = useForm<z.infer<typeof CreditCheckSchema>>({
    resolver: zodResolver(CreditCheckSchema),
    defaultValues: {
      motorVehicleRecordCheck: personalData?.motorVehicleRecordCheck || '',
    },
  });
  async function onSubmit(data: z.infer<typeof CreditCheckSchema>) {
    try {
      setIsSubmitting(true);
      const payload = {
        motorVehicleRecordCheck: data.motorVehicleRecordCheck,
      };
      const response = await updateAdmissionRequestPersonalData({
        requestId: requestId,
        payload: payload,
      });
      if (response && response.success) {
        toast({
          title: 'Motor Vehicle Record Check updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
        router.refresh();
      }
    } catch (error) {
      toast({
        title: 'Error updating Credit Check',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {'Motor Vehicle Record Check (Cisive)'}
          </Heading>
        </Flex>
      </CardHeader>
      {
        <Form {...form}>
          <form className="py-2 px-6" onSubmit={form.handleSubmit(onSubmit)}>
            <HookFormRadixUIField
              form={form}
              fieldName="motorVehicleRecordCheck"
              formLabel={''}
              placeholder="Enter MVR check result"
              type="textarea"
            />

            <Button
              variant={'outline'}
              className={'border border-primaryPurple text-primaryPurple my-2 rounded-md '}
              type="submit"
              isLoading={isSubmitting}
              isDisabled={isSubmitting}
              colorScheme="purple"
              size="sm"
            >
              {'Save & Update'}
            </Button>
          </form>
        </Form>
      }
    </Card>
  );
};
