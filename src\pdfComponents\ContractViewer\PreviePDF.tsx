'use client';
import { useContract } from '@/app/dashboard/flotilla/components/Detail/GenerateContract';
import { usePathname, useRouter } from 'next/navigation';
import { getHeadContract } from '../contract/data/ContractHeadData';
import { usePDF } from '@react-pdf/renderer';
import { DocumentComponent } from '../contract/ContractDocument';
import { Countries } from '@/constants';
import { DocumentComponentUS } from '../contract/ContractDocumentUS';

export default function PreviewPDF() {
  // console.log(instance);
  const location = usePathname();

  const carId = location.split('/')[4];
  // console.log(carId, localStorage.getItem('contractForm-' + carId));
  const storage = localStorage.getItem('contractForm-' + carId) || '{}';
  const formContract = JSON.parse(storage);

  const router = useRouter();
  const { form } = useContract();

  console.log('form contract', formContract);

  const definitiveForm = formContract ? { ...formContract } : { ...form };
  const isElectric = definitiveForm.isElectric;

  const headContract = getHeadContract(
    definitiveForm.city?.value,
    definitiveForm?.firstName,
    definitiveForm?.lastName,
    isElectric
  );

  if (!definitiveForm.firstName)
    return (
      <div className="flex flex-col">
        <p>
          Previsualización no disponible debido que no se ha generado una previsualización para este vehiculo
          o ya se generó
        </p>
        <button
          className="bg-gray-400 px-3 py-2 w-[max-content] rounded "
          onClick={() => router.push(location.replace('/pdf', ''))}
        >
          Regresar
        </button>
      </div>
    );

  return <DisplayPDF definitiveForm={definitiveForm} headContract={headContract} />;
}

function DisplayPDF({ definitiveForm, headContract }: { definitiveForm: any; headContract: any }) {
  let isUSA = false;
  if (definitiveForm?.country?.value === Countries['United States']) {
    isUSA = true;
  } else if (definitiveForm?.country === Countries['United States']) {
    isUSA = true;
  }
  const [instance] = usePDF({
    document: isUSA ? (
      <DocumentComponentUS form={definitiveForm} headContract={headContract} />
    ) : (
      <DocumentComponent form={definitiveForm} headContract={headContract} />
    ),
  });

  if (instance.loading)
    return (
      <div className="fixed top-0 left-0 w-full h-full z-[100] bg-gray-500 ">
        <div className="flex justify-center items-center h-full">
          <div className="bg-white p-5 rounded">
            <p>Generando contrato...</p>
          </div>
        </div>
      </div>
    );

  if (instance.error) return <div>Something went wrong: {instance.error}</div>;

  return (
    <>
      <div className="fixed top-0 left-0 w-full h-[100vh] z-[100] bg-gray-500 ">
        <iframe src={instance.url!} style={{ width: '100%', height: '100%' }} title="pdf"></iframe>
      </div>
    </>
  );
}
