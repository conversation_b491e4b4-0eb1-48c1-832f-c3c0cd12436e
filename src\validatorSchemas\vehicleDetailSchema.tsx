import * as Yup from 'yup';

const today = new Date();
const maxDate = new Date('2100-01-01');

export const addPlacasSchema = Yup.object().shape({
  plates: Yup.string().max(12, 'Maximo 12 caracteres').required('No. de placas requerido'),
});

export const addPlacasSchemaNotAdmin = Yup.object().shape({
  plates: Yup.string().max(12, 'Maximo 12 caracteres').required('No. de placas requerido'),
  frontImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
  backImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .required('Debe seleccionar una imagen'),
  platesDocument: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .required('Debe seleccionar un archivo'),
});

export const addCirculationCard = Yup.object().shape({
  number: Yup.string().max(16, 'Maximo 16 caracteres').required('No. de placas requerido'),
  validity: Yup.date()
    .min(today, 'La fecha no puede ser en el pasado')
    .max(maxDate, 'La fecha debe ser antes del año 2100')
    .required('Vigencia requerida'),
  frontImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .test('required', 'Debe seleccionar una imagen', function (value) {
      const { isEditing } = this.parent;
      return isEditing ? true : value != null;
    }),
  backImg: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo imagen', (value) => {
      if (!value) return true;
      const fileTypes = ['image/jpeg', 'image/jpg', 'image/png'];
      const fileType = (value as File).type;
      return fileTypes.includes(fileType);
    })
    .test('required', 'Debe seleccionar una imagen', function (value) {
      const { isEditing, vehicleState } = this.parent;
      // Back image is only required for CDMX vehicles
      const isCDMXVehicle = vehicleState === 'cdmx';
      if (isEditing || !isCDMXVehicle) return true;
      return value != null;
    }),
});

export const addGPSSchema = Yup.object().shape({
  gpsNumber: Yup.string().max(16, 'Maximo 16 caracteres').required('Nombre del gps requerido'),
  gpsSerie: Yup.string().max(16, 'Maximo 16 caracteres').required('No. de serie requerido'),
});

export const addPolicySchema = Yup.object().shape({
  policyNumber: Yup.string().required('No. de póliza requerido'),
  insurer: Yup.string().required('Aseguradora requerida'),
  validity: Yup.date()
    .min(today, 'La fecha no puede ser en el pasado')
    .max(maxDate, 'La fecha debe ser antes del año 2100')
    .required('Vigencia requerida'),
  broker: Yup.string().required('Broker requerido'),
  policyDocument: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .test('required', 'Debe seleccionar un archivo', function (value) {
      const { isEditing } = this.parent;
      return isEditing ? true : value != null;
    }),
});

export const addTenancy = Yup.object().shape({
  payment: Yup.string().min(3, 'Minimo 3 caracteres').required('payment requerido'),
  validity: Yup.date()
    .min(today, 'La fecha no puede ser en el pasado')
    .max(maxDate, 'La fecha debe ser antes del año 2100')
    .required('Vigencia requerida'),
  tenancyDocument: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .test('required', 'Debe seleccionar un archivo', function (value) {
      const { isEditing } = this.parent;
      return isEditing ? true : value != null;
    }),
});

export const addFactureSchema = Yup.object().shape({
  billNumber: Yup.string().required('Número de factura requerido'),
  billDate: Yup.date()
    .max(new Date(), 'La fecha no puede ser en el futuro')
    .required('Fecha de factura requerida'),
  billAmount: Yup.number().positive('El monto debe ser positivo').required('Monto de factura requerido'),
  bill: Yup.mixed()
    .test('fileType', 'El archivo debe ser de tipo PDF', (value) => {
      if (!value) return true;
      return value && (value as File).type === 'application/pdf';
    })
    .test('required', 'Debe seleccionar un archivo', function (value) {
      const { isEditing } = this.parent;
      return isEditing ? true : value != null;
    }),
});
