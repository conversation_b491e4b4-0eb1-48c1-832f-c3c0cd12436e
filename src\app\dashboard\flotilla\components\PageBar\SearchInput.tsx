import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { AiOutlineClose, AiOutlineSearch } from 'react-icons/ai';
import { VehicleCategoryTranslationsMX } from '../translations/statusTranslations';
import { Countries } from '@/constants';

export default function SearchInput({
  page = null,
  vehicleStatus = null,
  category = null,
  subCategory = null,
}: {
  page?: string | null;
  vehicleStatus?: string | null;
  category?: string | null;
  subCategory?: string | null;
}) {
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const search = useSearchParams();

  const router = useRouter();

  const searchFilter = search ? search.get('q') : null;
  const country = search ? search.get('country') : '';
  const onSearch = (event: React.FormEvent) => {
    event.preventDefault();
    const encodedSearchQuery = encodeURI(searchQuery);

    let url = `/dashboard/flotilla/${
      page?.toLowerCase() || ''
    }/search?q=${encodedSearchQuery}&country=${country}`;

    if (vehicleStatus) {
      //encodeURI(vehicleStatus)
      url += `&vehicleStatus=${encodeURI(vehicleStatus)}`;
    }

    if (category) {
      url += `&category=${encodeURI(category)}`;
    }

    if (subCategory) {
      url += `&subCategory=${encodeURI(subCategory)}`;
    }

    router.push(url);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchQuery(value);
    setSearchValue(value);

    if (value === '') {
      router.push(`/dashboard/flotilla/${page || 'stock'}${country ? `?country=${country}` : ''}`);
    }
  };

  useEffect(() => {
    if (searchFilter) {
      setIsSearchActive(true);
      setSearchValue(searchFilter);
    }
  }, [searchFilter]);

  const getSearchPlaceholder = (): string => {
    const isMexico = country && country.toLocaleLowerCase() === Countries.Mexico.toLowerCase();

    if (!category) {
      return isMexico ? 'Buscar...' : 'Search...';
    }

    if (isMexico) {
      const translatedCategory =
        VehicleCategoryTranslationsMX[category as keyof typeof VehicleCategoryTranslationsMX];
      return category === 'in-preparation'
        ? `Buscar ${translatedCategory}...`
        : `Buscar en ${translatedCategory}...`;
    }

    return `Search in ${category}...`;
  };

  return (
    <form className="relative" onSubmit={onSearch} onFocus={() => setIsSearchActive(true)}>
      <input
        id="search"
        className="px-10 h-[40px] border-[#9CA3AF] border-[1px] !outline-none rounded relative focus:ring-[#5800F7] focus:border-[#5800F7]"
        type="text"
        onChange={handleInputChange}
        value={searchValue}
        placeholder={getSearchPlaceholder()}
        onFocus={() => setIsSearchActive(true)}
        onBlur={() => setIsSearchActive(false)}
      />
      <div
        className={
          isSearchActive
            ? 'absolute top-0  text-[#5800F7] flex items-center h-full mr-2'
            : 'absolute top-0  text-[#9CA3AF] flex items-center h-full mr-2'
        }
      >
        <button type="submit" className="px-2" disabled={!searchValue}>
          <AiOutlineSearch size={26} />
        </button>
      </div>
      <div
        className={
          isSearchActive ? 'absolute top-0 right-0 text-[#5800F7] flex items-center h-full mr-2' : 'hidden'
        }
      >
        <Link
          href={`/dashboard/flotilla/${page || 'stock'}${country ? `?country=${country}` : ''}`}
          prefetch={false}
        >
          <AiOutlineClose size={26} />
        </Link>
      </div>
    </form>
  );
}
