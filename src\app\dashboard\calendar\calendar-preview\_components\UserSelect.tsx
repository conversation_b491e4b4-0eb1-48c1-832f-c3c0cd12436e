'use client';
import Select from 'react-select';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';

export const UserSelect = (props: any) => {
  const { data } = props;
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const homeVisitors = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) return [];

    return data.map(({ _id, name }) => ({
      value: _id,
      label: name,
    }));
  }, [data]);

  const [currentHomeVisitor, setCurrentHomeVisitor] = useState<any[]>([]);

  useEffect(() => {
    const selectedUsers = searchParams.get('selectedUsers')?.split(',');
    if (selectedUsers) {
      const selectedHomeVisitors = homeVisitors.filter((homeVisitor) =>
        selectedUsers.includes(homeVisitor.value)
      );
      setCurrentHomeVisitor(selectedHomeVisitors);
    }
  }, [homeVisitors, searchParams]);

  const handleChange = (selectedOptions: any) => {
    const newSelectedOptions = selectedOptions || [];
    setCurrentHomeVisitor(newSelectedOptions);

    const params = new URLSearchParams(searchParams.toString());
    if (newSelectedOptions.length > 0) {
      params.set('selectedUsers', newSelectedOptions.map((option: any) => option.value).join(','));
    } else {
      params.delete('selectedUsers');
    }

    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  };

  return (
    <div className="max-w-lg">
      <Select
        value={currentHomeVisitor}
        onChange={handleChange}
        isMulti
        name="homeVisitors"
        options={homeVisitors}
        className="basic-multi-select"
        classNamePrefix="select"
        placeholder="All Home Visitor"
      />
    </div>
  );
};
