import { DocumentCategory } from '@/constants';

// Define the document categories that only accept images
const IMAGE_ONLY_CATEGORIES = [
  DocumentCategory.CIRCULATION_CARD_FRONT,
  DocumentCategory.CIRCULATION_CARD_BACK,
  DocumentCategory.PLATES_FRONT,
  DocumentCategory.PLATES_BACK,
];

// Define the allowed extensions
const IMAGE_EXTENSIONS = ['png', 'jpg', 'jpeg'];
const DEFAULT_EXTENSIONS = ['pdf', ...IMAGE_EXTENSIONS];

/**
 * Determine if a document category only accepts images
 * @param category The document category to check
 * @returns Boolean indicating if the category only accepts images
 */
export const isImageOnlyCategory = (category: DocumentCategory | string): boolean => {
  return IMAGE_ONLY_CATEGORIES.includes(category as DocumentCategory);
};

/**
 * Get the allowed file extensions based on document category
 * @param category The document category
 * @param format 'array' | 'string' - The return format
 * @returns The allowed extensions as an array or string based on the format parameter
 */
export const getAllowedFileExtensions = (
  category: DocumentCategory | string,
  format: 'array' | 'string' = 'array'
): string[] | string => {
  const extensions = isImageOnlyCategory(category) ? IMAGE_EXTENSIONS : DEFAULT_EXTENSIONS;

  if (format === 'string') {
    return extensions.map((ext) => `.${ext}`).join(',');
  }

  return extensions;
};
