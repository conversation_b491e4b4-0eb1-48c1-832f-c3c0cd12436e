'use client';

import React, { useEffect, useState } from 'react';
import SelectAssociate from './(schedule)/SelectAssociate';
import { PaymentSchedule, User } from '../types';
import axios from 'axios';
import { PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { Skeleton } from '@/components/ui/skeleton';
import ScheduleTable from './(schedule)/ScheduleTable';
import { utils, writeFile } from 'xlsx';
import { Button } from '@/components/ui/button';
import { redirect } from 'next/navigation';
import { useSession } from 'next-auth/react';

async function getPaymentScheduleData(associateData: User) {
  let res = null;
  try {
    const url = new URL(`${PAYMENTS_API_URL}/paymentsLoanTape/associateId/${associateData.associateId}`);
    res = await axios(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data : [];
}

async function getClient(id?: string): Promise<User> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/clients/${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

function Schedule() {
  const [associateData, setAssociateData] = useState<User>();
  const [paymentScheduleData, setPaymentScheduleData] = useState<PaymentSchedule[]>([]);
  const [loading, setLoading] = useState(false);

  const { status } = useSession();

  useEffect(() => {
    if (associateData) {
      setLoading(true);
      getClient(associateData.id).then((res) => {
        getPaymentScheduleData(res).then((response) => {
          setLoading(false);
          const paymentScheduleRes = new Array<any>();
          if (response && response.data) {
            for (const [, value] of Object.entries(response.data)) {
              paymentScheduleRes.push(value);
            }
          }
          setPaymentScheduleData(paymentScheduleRes);
        });
      });
    }
  }, [associateData]);

  const downloadExcel = (jsonData: any, fileName = 'data.xlsx') => {
    // Create a new workbook and worksheet
    const data = jsonData.map((val: any) => {
      const {
        termNo,
        streamNo,
        contractNumber,
        associateId,
        amount,
        fee,
        dueDate,
        totalContactValue,
        amountOutstanding,
        loanType,
        assignmentDate,
      } = val;
      return {
        'Debtor Number': associateId,
        'Loan Id': contractNumber,
        'Term No': termNo,
        'Stream Number': streamNo,
        'Assignment Date': assignmentDate,
        'Date Due': dueDate,
        'Amount Due': amount,
        Fee: fee,
        'Total Contract Value': totalContactValue,
        'Amount Outstanding': amountOutstanding,
        'Loan Type': loanType,
      };
    });
    const worksheet = utils.json_to_sheet(data);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, 'TAB');

    // Generate the Excel file and download it
    writeFile(workbook, fileName);
  };

  const handleDownload = (data: any) => {
    downloadExcel(data, `${associateData?.contractNumber}.xlsx`);
  };

  if (status === 'unauthenticated') {
    return redirect('/');
  }

  return (
    <section className="py-8">
      <div className="container relative">
        <h1 className="mb-6 text-3xl font-bold">Payment Schedule</h1>
        {associateData ? <br /> : <p>Please select client</p>}
        <SelectAssociate associateData={associateData} setAssociateData={setAssociateData}></SelectAssociate>
        <div className="flex absolute top-0 right-0">
          {associateData && paymentScheduleData.length > 0 ? (
            <Button
              onClick={() => {
                handleDownload(paymentScheduleData);
              }}
            >
              Download Excel
            </Button>
          ) : (
            ''
          )}
        </div>
        {loading ? (
          <Skeleton className="w-full h-[300px]"></Skeleton>
        ) : (
          // <DataTable columns={columns} data={paymentScheduleData} />
          <ScheduleTable paymentSchedule={paymentScheduleData}></ScheduleTable>
        )}
      </div>
    </section>
  );
}

export default Schedule;
