import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField } from '../HookFormField';
import { FormSectionHeader } from '../FormHeaderSection';
import { FormSection } from '../FormSection';
import { FiPhoneCall } from 'react-icons/fi';
import { BiHomeAlt } from 'react-icons/bi';
import { TbWorld } from 'react-icons/tb';
import { IStepperButtonsProps } from '../StepperButtons';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { Steps, toastConfigs } from '.';
import { HomeVisitStepsStatus } from '@/constants';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

const ContactFormSchema = z
  .object({
    phoneNumber: z
      .string({ required_error: translations.es.PhoneRequired })
      .min(10, { message: translations.es.PhoneLengthErrorMsg })
      .max(10, { message: translations.es.PhoneLengthErrorMsg }),
    homePhone: z
      .string({ required_error: translations.es.HomePhoneRequired })
      .max(10, { message: translations.es.HomePhoneLengthErrorMsg }),
    email: z
      .string({ required_error: translations.es.EmailRequired })
      .email({ message: translations.es.EmailFormatErrorMsg }),
  })
  .superRefine((val, ctx) => {
    const phoneNumberStr = val.phoneNumber;
    if (phoneNumberStr.length > 0) {
      if (phoneNumberStr.length !== 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['phoneNumber'],
          message: translations.es.PhoneLengthErrorMsg,
        });
      } else if (!/^\d+$/.test(phoneNumberStr)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['phoneNumber'],
          message: translations.es.PhoneFormatErrorMsg,
        });
      }
    }
    const homePhoneStr = val.homePhone;
    if (homePhoneStr.length > 0) {
      if (homePhoneStr.length !== 10) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['homePhone'],
          message: translations.es.PhoneLengthErrorMsg,
        });
      } else if (!/^\d+$/.test(homePhoneStr)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['homePhone'],
          message: translations.es.PhoneFormatErrorMsg,
        });
      }
    }
  });

interface IContactInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function ContactInformation(props: IContactInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, personalData, homeVisit } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const phoneNumberWithoutMXCountryCode = personalData.phone?.replace('+52', '');
  const homePhoneWithoutMXCountryCode = personalData.homePhone?.replace('+52', '');

  const form = useForm<z.infer<typeof ContactFormSchema>>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      phoneNumber: phoneNumberWithoutMXCountryCode || '',
      homePhone: homePhoneWithoutMXCountryCode || '',
      email: personalData.email || '',
    },
  });

  async function onSubmit(data: z.infer<typeof ContactFormSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);
      const isDataCompleted = Object.entries(data).every(([key, value]) => {
        if (key === 'homePhone') {
          return true;
        }
        return value !== '';
      });
      const payload = {
        personalData: {
          phone: '+52' + data.phoneNumber,
          homePhone: '+52' + data.homePhone,
          email: data.email,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            contact: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Contact, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Contact, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.ContactInformation} />
      <Form {...form}>
        <form>
          <div className="flex py-4 gap-4">
            <HookFormRadixUIField
              form={form}
              fieldName="phoneNumber"
              formLabel={translations.es.Phone}
              Icon={FiPhoneCall}
              prefixString={'+52'}
            />
            <HookFormRadixUIField
              form={form}
              fieldName="homePhone"
              formLabel={translations.es.HomePhone}
              Icon={BiHomeAlt}
              prefixString={'+52'}
            />

            <HookFormRadixUIField
              form={form}
              fieldName="email"
              formLabel={translations.es.Email}
              Icon={TbWorld}
            />
          </div>
        </form>
      </Form>
    </FormSection>
  );
}
