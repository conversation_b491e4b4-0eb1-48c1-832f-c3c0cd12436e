'use client';
import React, { useState } from 'react';
import SelectRegion from '../productos/(productos)/SelectRegion';
import { PaymentModal } from '@/components/Modals/PaymentModal';
import { getCookie, setCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Countries, Sections, Subsections } from '@/constants';

export default function BarButtons() {
  const [open, setOpen] = useState(false);
  const router = useRouter();

  function setRegion(value: string) {
    const paymentsPageCookies = {
      region: value,
    };
    setCookie('payments-pagination', JSON.stringify({ pageIndex: 0, pageSize: 10 }));
    setCookie('payments-page-filters', JSON.stringify(paymentsPageCookies));
    router.refresh();
  }

  const region = JSON.parse(getCookie('payments-page-filters') || '{}').region || '';

  const ability = usePermissions();
  const canAdd = canPerform(ability, Capabilities.Add, Sections.Payments, Subsections.Payments);

  return (
    <>
      <div className="flex justify-end pb-4">
        <SelectRegion region={region} setRegion={setRegion} country={Countries.Mexico} />
        {canAdd && <PaymentModal open={open} setOpen={setOpen} />}
      </div>
    </>
  );
}
