import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';

// interface DocumentData {
//   url: string;
//   docId: string;
//   originalName: string;
// }

export interface Address {
  exterior: number;
  interior: number;
  street: string;
  addressStreet: string;
  zip: number;
}

export interface Associate {
  _id: string;
  active: boolean;
  address: Address;
  colony: string;
  postalCode: number;
  city: string;
  documents: string[];
  email: string;
  image: string;
  isVerified: boolean;
  name: string;
  firstName: string;
  lastName: string;
  phone: string;
  vehiclesId: any[];
}

export default async function getDriverById(id: string) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const response = await axios.get(`${URL_API}/associate/getAssociate/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return response.data as Associate;
  } catch (error: any) {
    return null;
  }
}
