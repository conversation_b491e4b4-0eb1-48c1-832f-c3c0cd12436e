'use client';
import { useEffect, useState } from 'react';
import { Loader } from 'lucide-react';
import EmptyState from '@/components/EmptyState';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import AnalysisCard from './AnalysisCard';
import { useCountry } from './detail';

type ScoreData = {
  score: number;
  category: 'low' | 'medium' | 'high';
};

export default function ScorecardChartFallback({ requestId }: { requestId: string }) {
  const { isCountryUSA } = useCountry();
  const riskAnalysisText = isCountryUSA ? 'Risk analysis' : 'Análisis de riesgo';
  // const seeDetailsText = isCountryUSA ? 'See detail' : 'Ver detalle';

  const [scoreData, setScoreData] = useState<ScoreData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  useEffect(() => {
    async function fetchScore() {
      setIsLoading(true);
      if (user?.accessToken) {
        try {
          const response = await fetch(`${URL_API}/mlservice/risk-scoring/${requestId}`, {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
          const data = await response.json();
          if (data.score !== undefined && data.category) {
            setScoreData({
              score: data.score * 100,
              category: data.category,
            });
          }
        } catch (error) {
          console.error('Error fetching score:', error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    }
    fetchScore();
  }, [requestId, user]);

  if (isLoading) return <Loader />;
  if (!scoreData) return <EmptyState title="No score data" minH={48} />;

  const numericScore = Math.round(scoreData.score);

  return (
    <AnalysisCard
      title={riskAnalysisText}
      numericScore={numericScore}
      category={scoreData.category}
      // seeDetailsText={seeDetailsText}
    />
  );
}
