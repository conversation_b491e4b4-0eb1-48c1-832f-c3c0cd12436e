/* eslint-disable prettier/prettier */
'use client';
import InputDate from '@/components/Inputs/InputDate';
import SelectInput from '@/components/Inputs/SelectInput';
import CustomModal from '@/components/Modals/CustomModal';
import { readmissionSchema } from '@/validatorSchemas/readmissionSchema';
import { FormikValues } from 'formik';
import axios from 'axios';
import { cities, URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import Spinner from '@/components/Loading/Spinner';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import PrimaryButton from '@/components/PrimaryButton';
import { getPromissoryNoteTerminationFile } from '@/pdfComponents/Termination/PagareTermination/utils/getPromissoryNoteTerminationFile';
import { useVehicleDetailData } from '../Providers/VehicleDetailDataProvider';
import { getAgreementTerminationFile } from '@/pdfComponents/Termination/AgreementTermination/utils/getAgreementTerminationFile';

export default function ReadmissionButton() {
  const updateSideData = useUpdateSideData();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { user } = useCurrentUser();
  const { id } = useParams();
  const router = useRouter();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  const allData = useVehicleDetailData();

  // console.log('associateData', allData.associateData);

  const fullAddress = useMemo(() => {
    const associate = allData.associateData;
    const addressStreet = associate.address.addressStreet;
    const exterior = associate.address.exterior.toString();
    const interior = associate.address.interior ? associate.address.interior.toString() : '';

    if (addressStreet.includes(exterior) && addressStreet.includes(interior)) {
      // Si addressStreet ya contiene exterior e interior, no los añadimos
      return `${addressStreet.trim()}, ${associate.address?.colony + ',' || ''}${cities[associate.city]?.label || associate.city.trim()}, C.P. ${associate.address.postalCode.toString().trim()}`;
    } else {
      // Si no, los añadimos
      return `${addressStreet.trim()} ${exterior.trim()} ${interior.trim()}, ${associate.address?.colony + ',' || ''}${cities[associate.city]?.label || associate.city.trim()}, C.P. ${associate.address.postalCode.toString().trim()}`;
    }
  }, [allData.associateData]);

  const onSubmit = async (values: FormikValues) => {

    const extensionCarNumber = allData.vehicleData.extensionCarNumber;
    const contractNumber = extensionCarNumber ? `${allData.vehicleData.carNumber}-${extensionCarNumber}` : allData.vehicleData.carNumber;

    const promissoryNoteTermination = await getPromissoryNoteTerminationFile({
      city: allData.vehicleData.vehicleState,
      firstName: allData.associateData.firstName,
      lastName: allData.associateData.lastName,
      weeklyRent: allData.associateData.contractData.weeklyRent,
      totalPays: allData.associateData.contractData.allPayments.length,
      date: values.readmissionDate,
      fullAddress,
    });

    const agreementTermination = await getAgreementTerminationFile({
      city: allData.vehicleData.vehicleState,
      firstName: allData.associateData.firstName,
      lastName: allData.associateData.lastName,
      weeklyRent: allData.associateData.contractData.weeklyRent,
      totalPays: allData.associateData.contractData.allPayments.length,
      fullAddress,
      contractTerminationDate: values.readmissionDate,
      deliveryDate: allData.associateData.contractData.deliveryData?.isoStringRealDate ||
        allData.associateData.contractData.deliveredDate,
      contractNumber,
    });

    const objData = {
      ...values,
      readmissionReason: values.readmissionReason.value,
      promissoryNoteTermination,
      agreementTermination,
    };

    try {
      const response = await axios.patch(`${URL_API}/stock/sendReadmission/${id}`, objData, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      console.log('response', response.data);
      toast({
        title: response.data.message || 'Reingreso exitoso',
        status: 'success',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      await updateSideData(user);
      localStorage.removeItem(`readmission-${id}`);
      router.refresh();
      router.push(
        window.location.pathname.includes('active')
          ? `/dashboard/flotilla/inactive/collection/${id}${country ? `?country=${encodeURI(country)}` : ''}`
          : `/dashboard/flotilla/reingresos/${id}${country ? `?country=${encodeURI(country)}` : ''}`
      );
    } catch (error) {
      toast({
        title: 'Algo fallo',
        status: 'error',
        duration: 3000,
        position: 'top',
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const confirm = async (values: FormikValues, { resetForm }: any) => {
    return Swal.fire({
      title: '¿Estás seguro de querer reingresar este vehículo?',
      text: 'Al hacer esto, el vehículo se moverá a la sección de “Reingresos” y no podrás regresarlo a "Activos" hasta completar el proceso',
      icon: 'warning',
      showCloseButton: true,
      confirmButtonText: '¡Sí, reingresar!',
      showDenyButton: true,
      denyButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        setIsLoading(true);
        await onSubmit(values);
        resetForm();
      }
    });
  };

  return (
    <>
      {isLoading && <Spinner />}
      <CustomModal
        isPrimaryButton
        plusButton={false}
        header="Reingresar"
        openButtonText="Reingresar"
        onCloseModal={() => {}}
        confirmButtonText="Reingresar"
        validatorSchema={readmissionSchema}
        initialValues={{ readmissionReason: '', readmissionDate: '' }}
        onSubmit={async (values, { resetForm }, onClose) => {
          onClose();
          await confirm(values, resetForm);
        }}
        body={<Body />}
        previewButton={(form) => {
          const values = form.values;
          const { readmissionReason, readmissionDate } = values;

          if (!readmissionReason || !readmissionDate) {
            return (
              <PrimaryButton className="bg-gray-400" disabled>
                Preview (Faltan datos)
              </PrimaryButton>
            );
          }

          return (
            <>
              <PrimaryButton
                className="bg-gray-500"
                onClick={() => {

                  const associate = allData.associateData;
                  const contractData = associate.contractData;
                  const extensionCarNumber = allData.vehicleData.extensionCarNumber;
                  const carNumber = allData.vehicleData.carNumber;
                  const contractNumber = extensionCarNumber ? `${carNumber}-${extensionCarNumber}` : carNumber;

                  // const deliveryData = allData.vehicleData.ma
                  const deliveryDate = allData.associateData.contractData.deliveryData?.isoStringRealDate ||
                    allData.associateData.contractData.deliveredDate;

                  localStorage.setItem(
                    `readmission-${id}`,
                    JSON.stringify({
                      city: allData.vehicleData.vehicleState,
                      firstName: associate.firstName,
                      lastName: associate.lastName,
                      totalPays: contractData.allPayments.length,
                      weeklyRent: contractData.weeklyRent,
                      date: readmissionDate,
                      fullAddress,
                      deliveryDate,
                      contractNumber,
                    })
                  );
                  window.open(`/dashboard/flotilla/reingresos/terminacion/pagare/${id}/pdf`, '_blank');
                }}
              >
                Preview
              </PrimaryButton>
            </>
          );
        }}
      />
    </>
  );
}

const options = [
  { value: 'no-payment', label: 'Falta de pago' },
  { value: 'canceled', label: 'Cancelación de contrato' },
  { value: 'other', label: 'Otro' },
];

function Body() {
  return (
    <div className="w-full flex flex-col gap-3">
      <SelectInput name="readmissionReason" label="Motivo de reingreso" options={options} />
      <InputDate name="readmissionDate" label="Fecha y hora" includeHours />
    </div>
  );
}
