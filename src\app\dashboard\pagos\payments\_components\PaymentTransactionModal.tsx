import {
  Flex,
  Input,
  Table,
  TableContainer,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Button,
  IconButton,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  useToast,
  Spinner,
  Divider,
} from '@chakra-ui/react';
import { AddIcon, DeleteIcon } from '@chakra-ui/icons';
import { useEffect, useState } from 'react';
import { usePaymentTransactionState } from '@/hooks/usePaymentTransaction';
import { PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import axios from 'axios';

type PaymentTransaction = {
  id: string;
  depositant: string;
  depositantClabe: string;
  description: string;
  amount: number;
  senderRfc: string;
  claveRastreo: string;
  depositDate: string;
};

const PaymentTransactionModal = () => {
  const toast = useToast();
  const router = useRouter();
  // State for modal visibility
  const { isOpen, onClose, paymentId, paymentAmount, updatePayments, setUpdatePayments } =
    usePaymentTransactionState();

  // Loading state
  const [loading, setLoading] = useState(false);

  // Transactions state
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);

  // Selected transactions state
  const [selectedTransactions, setSelectedTransactions] = useState<PaymentTransaction[]>([]);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // State for filters
  const [filters, setFilters] = useState<Partial<PaymentTransaction>>({
    depositant: '',
    depositantClabe: '',
    description: '',
    senderRfc: '',
    claveRastreo: '',
  });

  // Fetch filtered transactions
  const fetchFilteredTransactions = async () => {
    try {
      setLoading(true); // Start loading
      // Construct query parameters dynamically
      const queryParams = new URLSearchParams();
      queryParams.append('paymentId', paymentId);
      queryParams.append('limit', '10');
      queryParams.append('page', currentPage.toString());

      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          queryParams.append(key, value.toString());
        }
      });

      // API URL
      const apiUrl = `${PAYMENTS_API_URL}/payments/filtered?${queryParams.toString()}`;

      // Replace this with actual API call
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${PAYMENT_API_SECRET}`,
        },
      });
      const data = await response.json();

      // Update transactions and pagination
      setTransactions(
        data.data.map((item: any) => ({
          id: item.id,
          depositant: item.body.data.depositant,
          depositantClabe: item.body.data.depositant_clabe,
          description: item.body.data.description,
          amount: item.body.data.amount,
          senderRfc: item.body.data.sender_rfc,
          claveRastreo: item.body.data.clave_rastreo,
          depositDate: item.body.data.deposit_date,
        }))
      );
      setTotalPages(data.meta.lastPage);
    } catch (error) {
      console.error('Error fetching filtered transactions:', error);
      toast({
        description: 'Error fetching transactions',
        status: 'error',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to handle selecting a transaction
  const handleSelectTransaction = (transaction: PaymentTransaction) => {
    // Check if the transaction already exists in the selectedTransactions array
    const isAlreadySelected = selectedTransactions.some(
      (selected) =>
        selected.id === transaction.id &&
        selected.depositant === transaction.depositant &&
        selected.depositantClabe === transaction.depositantClabe &&
        selected.description === transaction.description &&
        selected.amount === transaction.amount &&
        selected.senderRfc === transaction.senderRfc &&
        selected.claveRastreo === transaction.claveRastreo &&
        selected.depositDate === transaction.depositDate
    );

    if (!isAlreadySelected) {
      setSelectedTransactions([...selectedTransactions, transaction]);
    } else {
      toast({
        description: 'Transaction already selected',
        status: 'warning',
        position: 'top',
        duration: 2000,
        isClosable: true,
      });
    }
  };

  // Function to handle removing a selected transaction
  const handleRemoveTransaction = (index: number) => {
    const updatedTransactions = [...selectedTransactions];
    updatedTransactions.splice(index, 1);
    setSelectedTransactions(updatedTransactions);
  };

  // Calculate the sum of selected transactions
  const totalAmount = selectedTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

  useEffect(() => {
    const getData = async () => {
      await fetchFilteredTransactions();
    };
    getData();
  }, [currentPage]);

  async function handleMarkAsPaid() {
    try {
      await axios.post(
        `${PAYMENTS_API_URL}/payments/mark-as-paid`,
        {
          paymentId: paymentId,
          selectedTransactions,
        },
        {
          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
          },
        }
      );

      return true;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  function formatDate(inputDateStr: string) {
    if (inputDateStr) {
      // Step 1: Parse the input date string
      const date = new Date(inputDateStr);

      // Step 2: Extract individual components
      const day = String(date.getDate()).padStart(2, '0'); // Day of the month (1-31)
      const month = String(date.getMonth() + 1).padStart(2, '0'); // Month (0-11, so add 1)
      const year = date.getFullYear(); // Full year (e.g., 2024)

      const hours = String(date.getHours()).padStart(2, '0'); // Hours (0-23)
      const minutes = String(date.getMinutes()).padStart(2, '0'); // Minutes (0-59)
      const seconds = String(date.getSeconds()).padStart(2, '0'); // Seconds (0-59)

      // Step 3: Combine into the desired format
      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    }
    return '-';
  }

  return (
    <Modal isOpen={isOpen} onClose={() => onClose()} size="full">
      <ModalOverlay />
      <ModalContent>
        {/* Modal Header */}
        <ModalHeader>Identificar Transacciones</ModalHeader>
        <ModalCloseButton />

        {/* Modal Body */}
        <ModalBody p={4}>
          {/* Dynamic Filters */}
          <Flex mb={4} gap={2} flexWrap="wrap">
            <Input
              placeholder="Depositante"
              value={filters.depositant}
              onChange={(e) => setFilters({ ...filters, depositant: e.target.value })}
              w="180px"
            />
            <Input
              placeholder="Depositante Clabe"
              value={filters.depositantClabe}
              onChange={(e) => setFilters({ ...filters, depositantClabe: e.target.value })}
              w="210px"
            />
            <Input
              placeholder="Descripción"
              value={filters.description}
              onChange={(e) => setFilters({ ...filters, description: e.target.value })}
              w="180px"
            />
            <Input
              placeholder="RFC"
              value={filters.senderRfc}
              onChange={(e) => setFilters({ ...filters, senderRfc: e.target.value })}
              w="180px"
            />
            <Input
              placeholder="Clave Rastreo"
              value={filters.claveRastreo}
              onChange={(e) => setFilters({ ...filters, claveRastreo: e.target.value })}
              w="220px"
            />
            {/* Apply Filter Button */}
            <Button
              className="font-semibold  bg-white text-[#5800F7] px-2.5 py-2 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
              onClick={async () => {
                if (currentPage !== 1) {
                  setCurrentPage(1);
                } else {
                  await fetchFilteredTransactions();
                }
              }}
              mb={4}
            >
              Aplicar Filtros
            </Button>
          </Flex>

          {loading && (
            <Flex
              position="absolute"
              top="0"
              left="0"
              right="0"
              bottom="0"
              bg="rgba(255, 255, 255, 0.8)"
              alignItems="center"
              justifyContent="center"
              zIndex="10"
            >
              <Spinner size="lg" color="blue.500" />
            </Flex>
          )}

          {/* Transaction Table */}
          <TableContainer>
            <Table variant="simple" colorScheme="gray" mb={4}>
              <Thead>
                <Tr>
                  <Th>Depositante</Th>
                  <Th>Depositante Clabe</Th>
                  <Th>Descripción</Th>
                  <Th>Monto</Th>
                  <Th>RFC</Th>
                  <Th>Clave Rastreo</Th>
                  <Th>Fecha de depósito</Th>
                  <Th>Acción</Th>
                </Tr>
              </Thead>
              <Tbody>
                {transactions.length > 0 ? (
                  transactions.map((transaction, index) => {
                    // Check if the transaction is selected
                    const isSelected = selectedTransactions.some(
                      (selected) => selected.id === transaction.id
                    );

                    return (
                      <Tr key={index} bg={isSelected ? 'blue.100' : undefined}>
                        <Td>{transaction.depositant}</Td>
                        <Td>{transaction.depositantClabe}</Td>
                        <Td>{transaction.description}</Td>
                        <Td>{transaction.amount}</Td>
                        <Td>{transaction.senderRfc}</Td>
                        <Td>{transaction.claveRastreo}</Td>
                        <Td>{formatDate(transaction.depositDate)}</Td>
                        <Td>
                          <IconButton
                            aria-label="Select Transaction"
                            icon={<AddIcon />}
                            onClick={() => handleSelectTransaction(transaction)}
                          />
                        </Td>
                      </Tr>
                    );
                  })
                ) : (
                  <Tr>
                    <Td colSpan={7} textAlign="center">
                      {loading ? 'Loading...' : 'No transactions found.'}
                    </Td>
                  </Tr>
                )}
              </Tbody>
            </Table>
          </TableContainer>
          {/* Pagination Controls */}
          <Flex justifyContent="center" alignItems="center" m={4} p={2}>
            <Button
              isDisabled={currentPage === 1 || loading}
              onClick={async () => {
                setCurrentPage(currentPage - 1);
              }}
            >
              Previous
            </Button>
            <Text>
              Page {currentPage} of {totalPages}
            </Text>
            <Button
              isDisabled={currentPage === totalPages || loading}
              onClick={async () => {
                setCurrentPage(currentPage + 1);
              }}
            >
              Next
            </Button>
          </Flex>
          <Divider orientation="horizontal" />
          {/* Selected Transactions Section */}
          <Text fontWeight="bold" mt={4} mb={2}>
            Transacciones Seleccionadas
          </Text>
          <TableContainer>
            <Table variant="striped" colorScheme="gray" mb={4}>
              <Thead>
                <Tr>
                  <Th>Depositante</Th>
                  <Th>Depositante Clabe</Th>
                  <Th>Descripción</Th>
                  <Th>Monto</Th>
                  <Th>RFC</Th>
                  <Th>Clave Rastreo</Th>
                  <Th>Fecha de depósito</Th>
                  <Th>Acción</Th>
                </Tr>
              </Thead>
              <Tbody>
                {selectedTransactions.length === 0 ? (
                  <Tr key={0}>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                    <Td>-</Td>
                  </Tr>
                ) : (
                  ''
                )}
                {selectedTransactions.map((transaction, index) => (
                  <Tr key={index}>
                    <Td>{transaction.depositant}</Td>
                    <Td>{transaction.depositantClabe}</Td>
                    <Td>{transaction.description}</Td>
                    <Td>{transaction.amount}</Td>
                    <Td>{transaction.senderRfc}</Td>
                    <Td>{transaction.claveRastreo}</Td>
                    <Td>{formatDate(transaction.depositDate)}</Td>
                    <Td>
                      <IconButton
                        aria-label="Remove Transaction"
                        icon={<DeleteIcon />}
                        onClick={() => handleRemoveTransaction(index)}
                      />
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
          {/* Sum Display */}
          <Text mb={4}>
            Sum: <strong>${parseFloat(totalAmount.toFixed(2))}</strong>
          </Text>
        </ModalBody>

        {/* Modal Footer */}
        <ModalFooter>
          <Flex justifyContent="start" alignItems="center" width={'full'}>
            <Text fontWeight="bold">Payment Amount: ${paymentAmount.toFixed(2)}</Text>
          </Flex>
          <Flex justifyContent="end" alignItems="center">
            <Button
              className="font-semibold  bg-white text-[#5800F7] px-2.5 py-2 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
              mr={2}
              onClick={() => onClose()}
            >
              Cancelar
            </Button>
            <Button
              sx={{
                color: 'white',
              }}
              isDisabled={totalAmount < paymentAmount}
              className={'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'}
              onClick={() => {
                onClose();
                Swal.fire({
                  title: '¿Estás seguro de que quieres marcar el pago como pagado?',
                  text: 'Esta acción no se puede revertir.',
                  icon: 'warning',
                  showCancelButton: true,
                  confirmButtonColor: '#3085d6',
                  cancelButtonColor: '#d33',
                  confirmButtonText: 'Confirmar',
                  cancelButtonText: 'Cancelar',
                }).then(async (result) => {
                  if (result.isConfirmed) {
                    setLoading(true);
                    await handleMarkAsPaid();
                    setLoading(false);
                    setUpdatePayments(!updatePayments);
                    toast({
                      title: 'Pago marcado como pagado',
                      position: 'top',
                      status: 'success',
                      duration: 5000,
                      isClosable: true,
                    });

                    router.refresh();
                  }
                });
              }}
            >
              Marcar como Pagado
            </Button>
          </Flex>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default PaymentTransactionModal;
