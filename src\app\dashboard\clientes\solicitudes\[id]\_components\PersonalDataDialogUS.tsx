'use client';
import {
  <PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  ModalHeader,
  ModalOverlay,
  ModalContent,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Button,
  ModalFooter,
  SimpleGrid,
  Box,
  useToast,
  InputGroup,
  InputLeftAddon,
  Select,
  useDisclosure,
} from '@chakra-ui/react';
import { useParams, useRouter } from 'next/navigation';
import { Formik, FormikValues, Form, Field, FieldProps } from 'formik';
import {
  getUSCitiesBasedOnState,
  getUSStatesOptions,
  US_COUNTRY_CODE,
  US_STATES_DEFAULT_CITIES,
  USSTATES,
  USVehiclesList,
} from '@/constants';
import { useState } from 'react';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { editPersonalDataSchemaUS } from '@/validatorSchemas/editPersonalDataSchema';
import { format } from 'date-fns';
import { CalenderComp } from '../../../../../../components/CustomCalender';
import { FaPencilAlt } from 'react-icons/fa';

interface Response {
  data: any;
  error: { code: string; message: string; errors: Array<Record<string, any>> };
  pagination: unknown;
  success: boolean;
}

const updatePersonalData = async ({
  customerId,
  accessToken,
  payload,
}: {
  customerId: string;
  accessToken: string;
  payload: any;
}) => {
  try {
    const res = await fetch(`${URL_API}/admission/requests/${customerId}/personal-data`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(payload),
    });
    const response: Response = await res.json();
    if (res.ok && response.success) {
      return response.data;
    }
    const isErrorsArray = Array.isArray(response.error.errors);
    const errorMessage = isErrorsArray
      ? response.error.errors.reduce((acc: string, error: any) => {
          const message = `${error.path[0]}, ${error.message}.\n`;
          return acc + message;
        }, '')
      : response.error.message;
    throw new Error(errorMessage, { cause: 'server-error' });
  } catch (err: any) {
    if (err.cause === 'server-error') {
      throw err;
    }
    throw new Error('Network error');
  }
};

interface PersonalData {
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  phone?: string | null;
  birthdate?: string | null;
  postalCode?: string | null;
  city?: string;
  state?: string;
  neighborhood?: string | null;
  street?: string | null;
  streetNumber?: string | null;
  department?: string | null;
  ssn?: string | null;
  rideShareTotalRides?: number | null;
  avgEarningPerWeek?: number | null;
  vehicleSelected?: string | null;
}

export function PersonalDataDialogUS({ data }: { data: PersonalData }) {
  const router = useRouter();
  const toast = useToast();
  const params = useParams();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const { isOpen, onOpen, onClose } = useDisclosure();

  function closeModal() {
    onClose();
    router.refresh();
  }

  const initialValues: PersonalData = {
    firstName: data.firstName || '',
    lastName: data.lastName || '',
    email: data.email || '',
    phone: data.phone?.startsWith(US_COUNTRY_CODE)
      ? data.phone.replace(US_COUNTRY_CODE, '')
      : data.phone || '',
    birthdate: data.birthdate || '',
    postalCode: data.postalCode || '',
    city: data.city || US_STATES_DEFAULT_CITIES[USSTATES.Texas][0].value,
    state: data.state || USSTATES.Texas,
    neighborhood: data.neighborhood || '',
    street: data.street || '',
    streetNumber: data.streetNumber || '',
    department: data.department || '',
    ssn: data.ssn || '',
    rideShareTotalRides: data.rideShareTotalRides || 0,
    avgEarningPerWeek: data.avgEarningPerWeek || 0,
    vehicleSelected: data.vehicleSelected || '',
  };

  async function handleSubmit(values: FormikValues) {
    if (!user) return null;
    setIsSubmitting(true);
    const payload = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      phone: values.phone ? `${US_COUNTRY_CODE}${values.phone.trim()}` : null,
      birthdate: format(new Date(values.birthdate), 'yyyy-MM-dd'),
      postalCode: values.postalCode,
      city: values.city,
      state: values.state,
      neighborhood: values.neighborhood,
      street: values.street,
      streetNumber: values.streetNumber,
      department: values.department,
      ssn: values.ssn,
      rideShareTotalRides: Number(values.rideShareTotalRides),
      avgEarningPerWeek: Number(values.avgEarningPerWeek),
      vehicleSelected: values.vehicleSelected,
    };
    try {
      await updatePersonalData({
        customerId: params.id as string,
        accessToken: user.accessToken as string,
        payload,
      });
      toast({
        title: 'Updated personal data',
        status: 'success',
        position: 'top',
      });
      closeModal();
    } catch (err: any) {
      toast({
        title: err.message,
        status: 'error',
        position: 'top',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <>
      <FaPencilAlt
        color="#5800F7"
        size={18}
        onClick={() => {
          onOpen();
        }}
      />
      {
        <Modal isOpen={isOpen} onClose={closeModal}>
          <ModalOverlay />
          <ModalContent minWidth="fit-content">
            <ModalHeader>{'Edit'}</ModalHeader>
            <ModalCloseButton />
            <Formik
              initialValues={initialValues}
              validationSchema={editPersonalDataSchemaUS}
              onSubmit={handleSubmit}
            >
              {({ errors, touched, setFieldValue, values }) => {
                return (
                  <Form>
                    <ModalBody>
                      <SimpleGrid columns={2} spacing={4}>
                        <Box gridColumn="span 1">
                          <FormControl isInvalid={!!(touched.firstName && errors.firstName)}>
                            <FormLabel>{'FirstName'}</FormLabel>
                            <Field name="firstName" as={Input} />
                            <FormErrorMessage>{errors.firstName}</FormErrorMessage>
                          </FormControl>
                        </Box>
                        <Box gridColumn="span 1">
                          <FormControl isInvalid={!!(touched.lastName && errors.lastName)}>
                            <FormLabel>{'LastName'}</FormLabel>
                            <Field name="lastName" as={Input} />
                            <FormErrorMessage>{errors.lastName}</FormErrorMessage>
                          </FormControl>
                        </Box>
                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.email && errors.email)}>
                            <FormLabel>{'Email'}</FormLabel>
                            <Field name="email" as={Input} />
                            <FormErrorMessage>{errors.email}</FormErrorMessage>
                          </FormControl>
                        </Box>
                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.phone && errors.phone)}>
                            <FormLabel>Phone</FormLabel>
                            <InputGroup>
                              <InputLeftAddon>{US_COUNTRY_CODE}</InputLeftAddon>
                              <Field name="phone" as={Input} />
                            </InputGroup>
                            <FormErrorMessage>{errors.phone}</FormErrorMessage>
                          </FormControl>
                        </Box>
                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.birthdate && errors.birthdate)}>
                            <FormLabel>{'Birthdate'}</FormLabel>
                            <Field
                              name="birthdate"
                              component={({ form }: FieldProps) => {
                                return <CalenderComp form={form} fieldName="birthdate" />;
                              }}
                            />
                            <FormErrorMessage>{errors.birthdate}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.ssn && errors.ssn)}>
                            <FormLabel>SSN</FormLabel>
                            <Field
                              name="ssn"
                              as={Input}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                const upperCaseValue = e.target.value;
                                setFieldValue('ssn', upperCaseValue);
                              }}
                            />
                            <FormErrorMessage>{errors.ssn}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.street && errors.street)}>
                            <FormLabel>{'Street address'}</FormLabel>
                            <Field name="street" as={Input} />
                            <FormErrorMessage>{errors.street}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.department && errors.department)}>
                            <FormLabel>{'Street address line 2'}</FormLabel>
                            <Field name="department" as={Input} />
                            <FormErrorMessage>{errors.department}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.state && errors.state)}>
                            <FormLabel>{'State'}</FormLabel>
                            <Field name="state" as={Select} placeholder="State">
                              {getUSStatesOptions().map((state, index) => (
                                <option key={index} value={state.value}>
                                  {state.label}
                                </option>
                              ))}
                            </Field>
                            <FormErrorMessage>{errors.state}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.city && errors.city)}>
                            <FormLabel>{'City'}</FormLabel>
                            <Field name="city" as={Select} placeholder="City">
                              {(values.state ? getUSCitiesBasedOnState(values.state as string) : []).map(
                                (city, index) => (
                                  <option key={index} value={city.value}>
                                    {city.label}
                                  </option>
                                )
                              )}
                            </Field>
                            <FormErrorMessage>{errors.city}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.postalCode && errors.postalCode)}>
                            <FormLabel>Zip </FormLabel>
                            <Field name="postalCode" as={Input} />
                            <FormErrorMessage>{errors.postalCode}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 1">
                          <FormControl
                            isInvalid={!!(touched.rideShareTotalRides && errors.rideShareTotalRides)}
                          >
                            <FormLabel>{'Ride share total rides'}</FormLabel>
                            <Field name="rideShareTotalRides" as={Input} />
                            <FormErrorMessage>{errors.rideShareTotalRides}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 1">
                          <FormControl isInvalid={!!(touched.avgEarningPerWeek && errors.avgEarningPerWeek)}>
                            <FormLabel>{'Avg earning per week'}</FormLabel>
                            <Field name="avgEarningPerWeek" as={Input} placeholder="0.00" />
                            <FormErrorMessage>{errors.avgEarningPerWeek}</FormErrorMessage>
                          </FormControl>
                        </Box>

                        <Box gridColumn="span 2">
                          <FormControl isInvalid={!!(touched.vehicleSelected && errors.vehicleSelected)}>
                            <FormLabel>{'Vehicle selected'}</FormLabel>
                            <Field name="vehicleSelected" as={Select} placeholder="Vehicle selected">
                              {USVehiclesList.map((vehicle, index) => (
                                <option key={index} value={vehicle.value}>
                                  {vehicle.label}
                                </option>
                              ))}
                            </Field>
                            <FormErrorMessage>{errors.vehicleSelected}</FormErrorMessage>
                          </FormControl>
                        </Box>
                      </SimpleGrid>
                    </ModalBody>
                    <ModalFooter gap={3}>
                      <Button
                        sx={{
                          color: '#5800F7',
                          borderColor: '#5800F7 !important',
                          border: '1px',
                          h: '40px',
                        }}
                        onClick={closeModal}
                        disabled={isSubmitting}
                      >
                        Cancel
                      </Button>
                      <Button
                        sx={{
                          color: 'white',
                          h: '40px',
                        }}
                        className={'bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]'}
                        type="submit"
                        disabled={isSubmitting}
                        isLoading={isSubmitting}
                      >
                        Save
                      </Button>
                    </ModalFooter>
                  </Form>
                );
              }}
            </Formik>
          </ModalContent>
        </Modal>
      }
    </>
  );
}
