/* eslint-disable react-hooks/exhaustive-deps */
'use client';
/* eslint-disable react-hooks/rules-of-hooks */
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { URL_API } from '@/constants';
import axios from 'axios';
import { redirect, useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
// import { useCreateAndedumState } from '@/zustand/andedumState';
import AdendumPageClient from './client';
import { AssociatePaymentType } from './types';

// const fechaIsoString = format(parse(payment.day, "dd-MM-yyyy", new Date()), "yyyy-MM-dd");

// console.log(fechaIsoString);

type ObjectOfArray = { day: string; number: string };

type Data = {
  allPayments: ObjectOfArray[];
  allPDFPayments: ObjectOfArray[];
  dateFinished: string;
  dateIn: string;
  fullName: string;
  address: string;
  contract: string;
  differenceWeeks: number;
  weeklyRent: string;
  deliveredDate: string;
  city: string;
  divideWeeks?: number | null;
  method: 'add-weeks' | 'divide-weeks';
  contractId: string;
};

export default function AdendumPage() {
  const { vehicleId } = useParams();
  const [data] = useState<Data | null>(
    JSON.parse(localStorage.getItem(`adendumData-${vehicleId}`) || 'null')
  );
  const { user } = useCurrentUser();
  const [fetchData, setFetchData] = useState<AssociatePaymentType | null>(null);
  useEffect(() => {
    async function getAssociatePayment() {
      if (!user) {
        return redirect('/');
      }
      try {
        const response = await axios.get(
          `${URL_API}/associatePayments/getByVehicleId/${vehicleId}?contractId=${data?.contractId}`,
          {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
            },
          }
        );
        return setFetchData(response.data.associatePayment as AssociatePaymentType);
      } catch (error) {
        return null;
      }
    }
    if (data) {
      getAssociatePayment();
    }
  }, []);

  if (!data) return <div>No adendum</div>;
  if (!fetchData) return <div>Cargando datos...</div>;
  // console.log('data', data);
  return (
    <div className="fixed top-0 left-0 w-full h-full overflow-y-auto z-[50] bg-gray-500 ">
      <AdendumPageClient fetchData={fetchData} />
    </div>
  );
}
