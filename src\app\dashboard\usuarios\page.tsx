import UserTable from './userTable';
import getUsers from '@/actions/getUsers';
import UsersProvider from './UsersProvider';
import SearcherUsers from './searchUsers';
import ModalCreateUser from './ModalCreateUser';
import getUserById from '@/actions/getUserById';
import { redirect } from 'next/navigation';
import { Capabilities, Sections, Subsections } from '@/constants';
import { getServerAbility } from '@/casl/getServerAbility';
import { canPerform } from '@/casl/canPerform';

export const metadata = {
  title: 'Usuarios',
  description: 'Esto es el dashboard',
};

export default async function UsersPage() {
  const user = await getUserById();
  if (!user) return redirect('/');

  const ability = await getServerAbility();

  const users = await getUsers();
  if (!users) return null;

  const canAdd = canPerform(ability, Capabilities.Add, Sections.UserManagement, Subsections.Users);

  return (
    <UsersProvider usersFetch={users}>
      <div className="mb-4 flex flex-row justify-between items-center">
        <h1 className="text-[32px] font-bold text-[#262D33]">Usuarios</h1>
        <div className="flex gap-4">
          <SearcherUsers />
          {canAdd && <ModalCreateUser />}
        </div>
      </div>
      <UserTable />
    </UsersProvider>
  );
}
