'use client';
import { createContext, useState, useContext, useEffect, ChangeEvent } from 'react';
import axios from 'axios';
import { PermissionSetResponse } from '@/actions/getPermissionSetById';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';

interface ContextProps {
  permissionSets: PermissionSetResponse[];
  handleSearch: (event: ChangeEvent<HTMLInputElement>) => void;
  setPermissionSets: React.Dispatch<React.SetStateAction<PermissionSetResponse[]>>;
}

export const PermissionSetsContext = createContext<ContextProps | null>(null);

export const usePermissionSets = () => {
  const context = useContext(PermissionSetsContext);
  if (!context) throw new Error('Permission context not found');
  return context;
};

interface ProviderProps {
  children: React.ReactNode;
}

export default function PermisosProvider({ children }: ProviderProps) {
  const [permissionSets, setPermissionSets] = useState<PermissionSetResponse[]>([]);
  const { data: session } = useSession();
  const url = useCurrentUrl();
  const user = session?.user as unknown as MyUser;

  const fetchData = async () => {
    if (!user?.accessToken) return;
    try {
      const response = await axios.get(`${url}/permissionSet/getAllPermissionSets`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      setPermissionSets(response?.data?.permissionSets ?? []);
    } catch (e) {
      setPermissionSets([]); // fallback
    }
  };

  useEffect(() => {
    fetchData();
  }, [user?.accessToken]);

  const handleSearch = (event: ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.trim().toLowerCase();
    if (!value) return fetchData(); // Reset search
    setPermissionSets((prev) =>
      prev.filter(
        (p) =>
          p.name.toLowerCase().includes(value) ||
          p.role.toLowerCase().includes(value) ||
          p.area.toLowerCase().includes(value)
      )
    );
  };

  return (
    <PermissionSetsContext.Provider value={{ permissionSets, handleSearch, setPermissionSets }}>
      {children}
    </PermissionSetsContext.Provider>
  );
}
