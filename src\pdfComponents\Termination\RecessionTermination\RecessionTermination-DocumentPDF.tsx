import { Document, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { DeclarationList } from './(_components)/DeclarationList';
import { DateTime } from 'luxon';

interface RecessionTerminationDocumentPDFProps {
  city: string;
  firstName: string;
  lastName: string;
  contractTerminationDate: string;
  contractNumber: string;
  deliveryDate: string;
  fullAddress: string;
  brand: string;
  model: string;
  color: string;
  year: string;
  vin: string;
  plates: string;
}

export default function RecessionTerminationDocumentPDF({
  city,
  firstName,
  lastName,
  // contractTerminationDate,
  contractNumber,
  // fullAddress,
  deliveryDate,
  brand,
  model,
  color,
  year,
  vin,
  plates,
}: RecessionTerminationDocumentPDFProps) {
  const fullName = `${firstName} ${lastName}`.toUpperCase();

  const parsedDeliveryDate = DateTime.fromISO(deliveryDate)
    .setLocale('es')
    .toLocaleString(DateTime.DATE_FULL);

  return (
    <>
      <Document>
        <Page style={styles.page} size="A4" wrap>
          <View style={styles.body}>
            <View style={{ rowGap: '30px' }}>
              <Text style={styles.text}>
                RESCISIÓN DEL CONTRATO DE ARRENDAMIENTO NÚMERO {contractNumber}, QUE EN TÉRMINOS DE LO
                ESTABLECIDO EN EL {variableTerms(city).toUpperCase()}, LLEVA A CABO E-MKT GOODS DE MÉXICO,
                S.A.P.I. DE C.V. POR CONDUCTO DE SU APODERADO LEGAL C. RICARDO HARO MANCERA.
              </Text>
            </View>
            <View style={styles.declContainer}>
              <DeclarationList
                deliveryDate={deliveryDate}
                fullName={fullName}
                contractNumber={contractNumber}
                city={city}
              />
              <View
                style={{
                  flexDirection: 'column',
                  rowGap: 10,
                  marginTop: '20px',
                }}
              >
                <Text style={styles.title}>DETERMINACIÓN LEGAL.</Text>
                <Text style={styles.contentText}>
                  <Text style={[styles.definicionesText]}>ÚNICO. -</Text>
                  Se determina rescindir el contrato de arrendamiento de fecha {parsedDeliveryDate}, al cual
                  se le asignó el número {contractNumber} respecto del vehículo de marca {brand}/{model},
                  Color {color}, Modelo {year}, Placa {plates} y número de serie {vin}, por el incumplimiento
                  del conductor.
                </Text>
              </View>
            </View>
            <View style={[styles.content, { marginTop: '40px' }]}>
              <Text style={[styles.text, styles.signature]}>______________________________</Text>
              <Text style={[styles.text, styles.signature]}>E-MKT GOODS DE MÉXICO, S.A.P.I. DE C.V.</Text>
              <Text style={[styles.text, styles.signature]}>Por medio de su Apoderado Legal.</Text>
              <Text style={[styles.text, styles.signature]}>LIC. RICARDO HARO MANCERA. </Text>
            </View>

            <View
              id="signatures"
              style={{
                // center the content and full width
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: '20px',
              }}
            >
              <View style={styles.containerS}>
                <View style={styles.signatureContent}>
                  <Text style={styles.names}>______________________________________</Text>
                  <Text style={styles.names}>LIC. HECTOR JONATHAN CHAMERRY CAMACHO</Text>
                </View>

                <View style={styles.signatureContent}>
                  <Text style={styles.names}>______________________________________</Text>
                  <Text style={styles.names}>LIC. LUIS FELIPE GARCIA REYES</Text>
                </View>
              </View>
            </View>
          </View>
        </Page>
      </Document>
    </>
  );
}

function variableTerms(city: string) {
  // TO DO: Add the corresponding terms for each city (waiting for the legal team to provide them)
  const terms: Record<string, { term: string }> = {
    mty: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE NUEVO LEÓN`,
    },
    cdmx: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE CIUDAD DE MÉXICO`,
    },
    edomx: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE MÉXICO`,
    },
    gdl: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE JALISCO`,
    },
    qro: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE QUERÉTARO`,
    },
    tij: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE BAJA CALIFORNIA`,
    },
    moka: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE MOKA`,
    },
    pbe: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE PUEBLA`,
    },
    tol: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE TOLUCA`,
    },
    ptv: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE PUERTO VALLARTA`,
    },
    tep: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE NAYARIT`,
    },
    col: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE COLIMA`,
    },
    sal: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE COAHUILA`,
    },
    torr: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE COAHUILA`,
    },
    dur: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE DURANGO`,
    },
    mxli: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE BAJA CALIFORNIA`,
    },
    her: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE SONORA`,
    },
    chi: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE CHIHUAHUA`,
    },
    leo: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE GUANAJUATO`,
    },
    ags: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE AGUASCALIENTES`,
    },
    slp: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE SAN LUIS POTOSÍ`,
    },
    mer: {
      term: `CAPÍTULO IX, ARTÍCULO 2383 DEL CÓDIGO CIVIL DEL ESTADO DE YUCATÁN`,
    },
  };

  // return terms[city].term;
  const term = terms[city] ? terms[city].term : terms.cdmx.term;
  return term;
}

const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    alignContent: 'center',
    width: '100%',
    paddingVertical: 60,
  },
  body: {
    marginHorizontal: '10%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },

  body2: {
    marginVertical: '10%',
  },
  viewer: {
    width: '80%',
    height: '100vh',
  },
  text: {
    fontSize: 11,
    textAlign: 'justify',
    fontFamily: 'Helvetica',
  },
  textBold: {
    fontFamily: 'Helvetica-Bold',
  },
  tablesContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },

  declContainer: {
    marginTop: '20px',
    flexDirection: 'column',
    rowGap: 20,
    width: '100%',
  },
  definicionesText: {
    fontSize: '8px',
    textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },
  title: {
    textAlign: 'center',
    fontWeight: 800,
    fontSize: 8,
    marginBottom: 10,
    textTransform: 'uppercase',
    fontFamily: 'Helvetica-Bold',
  },
  contentText: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  table: {
    display: 'table' as unknown as 'flex',
    width: '25%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableHead: {
    borderStyle: 'solid',
    fontSize: 6,
    textAlign: 'center',
    flexDirection: 'row',
    backgroundColor: '#C3C6CB',
  },
  tableHeadCell: {
    textAlign: 'left',
    marginTop: 5,
    fontSize: 10,
  },

  tableHeadCellTitle: {
    textAlign: 'center',
    fontSize: 6,
    fontFamily: 'Helvetica',
    paddingVertical: 5,
  },
  tableRow: {
    flexDirection: 'row',
  },

  tableRowLast: {
    flexDirection: 'row',
  },

  tableCol: {
    width: '33.3333%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tableCol2: {
    width: '33.3333%',
    borderStyle: 'solid',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderRightWidth: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: 'auto',
    fontSize: 6,
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    rowGap: 2,
    marginTop: '25px',
    // center the content
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center', // Añade esta línea
  },
  signature: {
    textAlign: 'center', // Añade este estilo
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  signatureContent: {
    width: '50%',
    flexDirection: 'column',
    justifyContent: 'center',
    rowGap: 5,
  },
  names: {
    fontSize: 8,
    textAlign: 'center',
    fontFamily: 'Helvetica',
  },
});
