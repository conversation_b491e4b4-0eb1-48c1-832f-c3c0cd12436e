import React from 'react';
import PaymentsClientPage from './client';
import { cookies } from 'next/headers';
import { getPayments } from '@/actions/getPayments';
import { getCookie } from 'cookies-next';
import { PaymentsHeader } from './_components/PaymentsHeader';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';

interface PaymentsProps {
  searchParams: Record<string, string>;
}

/**
 * adding this in order to increase the duration of the page rendering because the get Payments api is
 * taking relatively long to responsd.
 */
export const maxDuration = 60;

export default async function PaymentsPage({ searchParams }: PaymentsProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');

  const paginationCookie = getCookie('payments-pagination', { cookies });
  const pagination = paginationCookie ? JSON.parse(paginationCookie) : { pageIndex: 0, pageSize: 10 };

  const payments = await getPayments(pagination, searchParams);

  return (
    <section className="py-8">
      <div className="container">
        <PaymentsHeader />
        {payments ? (
          <PaymentsClientPage
            data={payments.payments}
            allRecords={payments.allRecords}
            total={payments.totalQuery}
          />
        ) : (
          <p>No se encontraron pagos</p>
        )}
      </div>
    </section>
  );
}
