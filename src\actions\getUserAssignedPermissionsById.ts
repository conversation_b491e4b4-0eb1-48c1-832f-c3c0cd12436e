import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { Section } from './getPermissionSetById';

export interface UserAssignedPermissionsResponse {
  user: User;
  permissions: Section[];
}

export interface User {
  name: string;
  role: string;
  area: string;
  email: string;
  _id: string;
  permissions: Section[];
}

const getUserAssignedPermissionsById = async (userId: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/user/assignedPermissions/${userId}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const permissionsData = response.data;
    return permissionsData as UserAssignedPermissionsResponse;
  } catch (error: any) {
    return null;
  }
};

export default getUserAssignedPermissionsById;
