/**
 * Returns form data stringyfied ready to be send to backend
 */

export default function generateFormData<T>(dataObj: T) {
  const formData = new FormData();

  for (let key in dataObj) {
    const value = dataObj[key];

    if (value instanceof File) {
      formData.append(key, value);
    }
    if (value instanceof FileList) {
      for (let i = 0; i < value.length; i++) {
        formData.append(key, value[i]);
      }
    } else {
      formData.append(key, JSON.stringify(value));
    }
  }

  return formData;
}
