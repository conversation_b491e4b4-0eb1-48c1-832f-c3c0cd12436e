import axios from 'axios';
import { URL_API } from '@/constants';
import { ApiPath } from '@/constants/api.endpoints';

export async function addAvalData({ requestId, requestPayload, user }: any) {
  try {
    if (!user) return null;

    const response = await axios.patch(
      `${URL_API}${ApiPath.ADMISSION_REQUEST}${requestId}/aval-data-with-documents-admin`,
      requestPayload,
      {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response?.data;
  } catch (error) {
    throw new Error('Error occurred while updating Aval data.');
  }
}
