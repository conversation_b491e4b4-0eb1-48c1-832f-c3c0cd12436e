/* eslint-disable prettier/prettier */
/* eslint-disable react-hooks/exhaustive-deps */
'use client';
/* eslint-disable react-hooks/rules-of-hooks */
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import PrimaryButton from '@/components/PrimaryButton';
import { URL_API } from '@/constants';
import AdendumPDF, { AdendumDocument } from '@/pdfComponents/Adendum/adendumPDF';
import { useToast } from '@chakra-ui/react';
import { pdf } from '@react-pdf/renderer';
import axios from 'axios';
import { useParams, useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { parse, format } from 'date-fns';
import { AssociatePaymentType, PaymentsArray } from './types';
import { paymentsArray } from '@/pdfComponents/contract/data/Lunes';
import moment from 'moment';
import PaymentsTable from './components/paymentsTable';
import Spinner from '@/components/Loading/Spinner';
import ModalContainer from '../../components/Modals/ModalContainer';

export type TAllPayments = {
  day: string;
  number: string;
  fee?: number;
  // contractValue?: number;
  // amountOutstanding?: number;
};

export type PDFPayments = { day: string; weeklyRent: number }[];

export type WeeksChanged = { day: string; number: string; weeklyRent: number; index: number; fee?: number }[];

export type LocalStorageData = {
  allPayments: TAllPayments[];
  allPDFPayments: PDFPayments[];
  dateFinished: string;
  dateIn: string;
  fullName: string;
  address: string;
  contract: string;
  differenceWeeks: number;
  weeklyRent: number;
  deliveredDate: string;
  city: string;
  divideWeeks?: number | null;
  method: 'add-weeks' | 'divide-weeks';
  contractId: string;
  addAmount: number;
};

export default function AdendumPageClient({ fetchData }: { fetchData: AssociatePaymentType }) {
  const { vehicleId } = useParams();
  const [data, setData] = useState<LocalStorageData | null>(
    JSON.parse(localStorage.getItem(`adendumData-${vehicleId}`) || 'null')
  );
  const { user } = useCurrentUser();
  const toast = useToast();

  const router = useRouter();
  if (!data) return <div>No adendum</div>;

  const firstDateIndex = data.allPayments.find(
    (payment: TAllPayments) =>
      format(parse(payment.day, 'dd-MM-yyyy', new Date()), 'yyyy-MM-dd') === data.dateIn
  );
  const finishServiceIndex = data.allPayments.find(
    (payment: TAllPayments) =>
      format(parse(payment.day, 'dd-MM-yyyy', new Date()), 'yyyy-MM-dd') === data.dateFinished
  );
  const pastAdendumWhenIsNotFinishedProcessYet = fetchData.lengthAddedBefore
    ? fetchData.lengthAddedBefore > 0
    : false;

  const past = pastAdendumWhenIsNotFinishedProcessYet;

  const totalAllPayments = data.allPayments.slice(
    0,
    past ? data.allPayments.length - fetchData.lengthAddedBefore : undefined
  );

  const totalPaymentsArray = fetchData.paymentsArray.slice(
    0,
    past ? fetchData.paymentsArray.length - fetchData.lengthAddedBefore : undefined
  );

  const dataAdded = {
    ...data,
    allPayments: totalAllPayments,
    paymentsArray: totalPaymentsArray,
  };

  const info =
    data.method === 'add-weeks'
      ? useCalculateDataAddWeeks({ data: dataAdded, firstDateIndex, finishServiceIndex })
      : useCalculateDataDivideWeeks({ data: dataAdded, firstDateIndex, finishServiceIndex });

  if (!info) return <div>No información calculada </div>;

  const [infoState, setInfoState] = useState({
    allPayments: info.allPayments,
    allDataPayments: info.allDataPayments,
    allPDFPayments: info.allPDFPayments,
    weeksChanged: info.weeksChanged,
    weeklyRent: data.weeklyRent,
    weeklyRentCalculate: info.weeksChanged.slice(info.weeksChanged.length - 1)[0]?.weeklyRent,
  });

  // console.log('allDataPayments', infoState.allDataPayments);
  // console.log('allPDFPayments', infoState.allPDFPayments);

  const [isLoding, setIsLoading] = useState(false);

  const [stateChange, setStateChange] = useState(false);
  const adendumData = {
    ...data,
    allPDFPayments: infoState?.allPDFPayments,
  };
  const [isOpen, setIsOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  return (
    <>
      <div className="p-4 flex flex-col gap-3 relative">
        {isLoding && <Spinner zIndex={55} />}

        <PrimaryButton
          onClick={() => setIsOpen(true)}
          disabled={isLoding}
        // className="w-1/2 self-center"
        >
          Enviar adendum a firma
        </PrimaryButton>

        {
          isOpen && <ModalContainer title="Enviar adendum a firma" onClose={() => setIsOpen(false)}>

            {/* Check or select for selecting if its edit or new */}
            <div>

              <p>Editar ultimo adendum (Opcional) </p>
            <div className="flex gap-3 items-center">
              <input
                type="checkbox"
                id="edit"
                name="edit"
                checked={isEdit}
                onChange={() => setIsEdit(!isEdit)}
                />
              <div className='flex flex-col gap-1'>

                <label htmlFor="edit">Selecciona si deseas reenviar el ultimo adendum</label>
                <p className='text-sm text-gray-500' >(Para cuando se necesita reenviar para hacer correcciones)</p>
              </div>

            </div>
            </div>


            <PrimaryButton
              disabled={isLoding}
              onClick={async () => {
                setIsLoading(true);

                const totalAddweeks = infoState?.weeksChanged.filter(
                  (p) => p.weeklyRent !== 0 && p.weeklyRent !== data.weeklyRent
                ).length;

                if (data.divideWeeks && totalAddweeks > data.divideWeeks) {
                  setIsLoading(false);
                  return toast({
                    // title: 'Error',
                    // description: `No puedes tener mas de ${data.differenceWeeks} semanas sin renta`,
                    title: `No puedes tener mas de ${data.divideWeeks} semanas con monto adicional`,
                    // description: 'Ya que esta cantidad de semanas se calculó previamente en el formulario',
                    description:
                      'Por favor revisa las semanas, ya que estas se calcularon previamente en el formulario',
                    status: 'error',
                    position: 'top',
                    duration: 9000,
                    isClosable: true,
                  });
                }
                /* if (infoState?.weeksChanged) {
                return null;
              } */
                const firstIndexWeekChanged = infoState?.weeksChanged[0];
                const lastIndexWeekChanged = infoState?.weeksChanged[infoState?.weeksChanged.length - 1];

                const changed = infoState?.allDataPayments.slice(
                  firstIndexWeekChanged.index,
                  lastIndexWeekChanged.index + 1
                );

                // validate if a week changed is between 0 weeks

                let isValid = true;
                let itemError: any = null;

                for (let i = 1; i < changed.length - 1; i++) {
                  // Explanation: if the previous week is 0 and the next week is 0, the current week can't be changed
                  // because it will be a week without rent
                  if (
                    changed[i - 1].weeklyRent === 0 &&
                    changed[i].weeklyRent !== 0 &&
                    changed[i + 1].weeklyRent === 0
                  ) {
                    isValid = false;
                    itemError = changed[i];
                    break;
                  }
                }

                if (!isValid) {
                  return toast({
                    title: 'Error al cambiar las semanas, en la semana: ' + itemError.number,
                    description:
                      'No se puede cambiar una semana si las semanas anteriores y posteriores de un pago son 0',
                    status: 'error',
                    duration: 12000,
                    position: 'top',
                    isClosable: true,
                  });
                }

                const feePerWeek = data.divideWeeks && data.addAmount / data.divideWeeks;
                // console.log('feePerWeek', feePerWeek);
                const weeksChanged = changed.map((week) => {
                  week.weeklyRent = +week.weeklyRent.toFixed(2);
                  // console.log('weeklyRent', week.weeklyRent);
                  const week0 = week.weeklyRent === 0;
                  if (week0) return week;

                  if (data.addAmount) {
                    if (!feePerWeek) return week;

                    const feeRounded = +feePerWeek.toFixed(2);
                    return {
                      ...week,
                      fee: feeRounded,
                      weeklyRent: +(week.weeklyRent - feePerWeek).toFixed(2),
                    };
                  }

                  return week;
                });

                const dataToSend = {
                  allPayments: infoState?.allPayments,
                  weeksChanged,
                  contractId: data.contractId,
                  listChange: infoState?.weeksChanged,
                  addAmount: data.addAmount,
                  divideWeeks: data.divideWeeks,
                  method: data.method,
                  totalAddedWeeks: data.differenceWeeks,
                };

                const config = {
                  headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${user.accessToken}`,
                  },
                };
                // console.log('week changed', changed);

                toast({
                  title: 'Enviando adendum',
                  description: 'Por favor espere, puede tardar varios segundos',
                  status: 'info',
                  duration: 4000,
                  position: 'top',
                  isClosable: true,
                })

                try {
                  if (data.method === 'add-weeks') {
                  /* const response = */ await axios.patch(
                    `${URL_API}/mainContract/update/${data.contractId}`,
                    dataToSend,
                    config
                  );
                    // console.log('🚀 ~ onClick={ ~ response:', response.data);
                  }
                // console.log('🚀 ~ onClick={ ~ dataToSend:', {
                //   ...dataToSend,
                //   method: data.method,
                //   paymentsArray: info.allPayments,
                // });
                /* const response2 = */ await axios.patch(
                    `${URL_API}/associatePayments/updateByRelationId`,
                    { ...dataToSend, method: data.method, paymentsArray: info.allPayments },
                    config
                  );

                  /* download the pdf */

                  const blob = await pdf(<AdendumDocument data={adendumData} />).toBlob();

                  // Send the blobl to process the pdf

                  const formData = new FormData();
                  const hasFee = data.addAmount > 0;
                  formData.append('adendum', blob, `Adendum-${adendumData.contract}.pdf`);
                  formData.append('associateId', fetchData.associateId);
                  formData.append("vehicleId", vehicleId as string);
                  if (hasFee) {
                    console.log('hasfee again', hasFee);
                    formData.append('hasFee', hasFee.toString());
                  }

                  if (isEdit) {
                    formData.append('isEdit', 'true');
                  }

                  await axios.post(`${URL_API}/associate/signature/adendum`, formData, {
                    headers: {
                      'Content-Type': 'multipart/form-data',
                      Authorization: `Bearer ${user.accessToken}`,
                    },
                  });

                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `Adendum-${adendumData.contract}.pdf`; // Nombre del archivo para la descarga
                  a.style.display = 'none';

                  // Agregar el enlace al cuerpo del documento y simular un clic
                  document.body.appendChild(a);
                  a.click();

                  // delete a after download
                  window.URL.revokeObjectURL(url);

                  toast({
                    title: 'Adendum descargado',
                    description: 'Cerrando pagina',
                    status: 'success',
                    duration: 3000,
                    position: 'top',
                    isClosable: true,
                  });

                  setTimeout(() => {
                    localStorage.removeItem(`adendumData-${vehicleId}`);
                    setData(null);
                    window.close();
                    return router.refresh();
                  }, 3000);
                } catch (error) {
                  toast({
                    title: 'Hubo un error al descargar el adendum',
                    description: 'Comunicarse con TI',
                    status: 'error',
                    duration: 3000,
                    position: 'top',
                    isClosable: true,
                  });
                  console.log(error);
                } finally {
                  setIsLoading(false);
                  setIsOpen(false);
                }
                return undefined;
              }}
            >
              {/* Descargar PDF */}
              Confirmar enviar adendum
            </PrimaryButton>
          </ModalContainer>
        }

        {/* {data.method === 'divide-weeks' && ( */}
        <PaymentsTable
          data={infoState}
          localData={data}
          setChange={setInfoState}
          setStateChange={setStateChange}
          stateChange={stateChange}
        />
        {/* )} */}

        <AdendumPDF data={adendumData} stateChange={stateChange} />
      </div>
    </>
  );
}

interface UseHook {
  data: LocalStorageData & { paymentsArray: PaymentsArray[] };
  firstDateIndex: TAllPayments | undefined;
  finishServiceIndex: TAllPayments | undefined;
}

function useCalculateDataAddWeeks({ data, firstDateIndex, finishServiceIndex }: UseHook) {
  // console.log('allPayments length', data.allPayments.length);
  const newPayments = useMemo(() => {
    if (!firstDateIndex || !finishServiceIndex) return;

    const firstIndex = Number(firstDateIndex.number) - 1;
    const finishIndex = firstIndex + Number(data.differenceWeeks);
    const listChange = data.allPayments.slice(firstIndex, finishIndex);
    // console.log('last index', firstIndex, finishIndex);
    // console.log('data', data.allPayments[firstIndex], data.allPayments[finishIndex]);
    // console.log('firstIndex', firstIndex);
    // console.log('finishIndex', finishIndex);
    // console.log('listChange', listChange.length);
    const weeksChanged: WeeksChanged = [];
    const payments = data.allPayments.map((payment, i) => {
      // if (i > 11 && i < 15)
      //   console.log('🚀 ~ useCalculateDataDivideWeeks ~ payment', data.paymentsArray[i].weeklyCost);

      if (i >= firstIndex && i < finishIndex) {
        weeksChanged.push({ day: payment.day, weeklyRent: 0, number: payment.number, index: i });
        return {
          day: payment.day,
          weeklyRent: 0,
        };
      } else {
        return {
          day: payment.day,
          weeklyRent: data.weeklyRent,
        };
      }
    });

    /* array of weeks to add to the payments table array to display in PDF */
    const add = paymentsArray(
      moment(payments[payments.length - 1].day, 'DD-MM-YYYY'),
      listChange.length
    ).slice(1); // slice to remove the first element because the first element is the last day of the payments array, so it's removed beacuse it is not needed

    const addPDFPayments = add.map((payment) => ({
      day: payment.day,
      weeklyRent: data.weeklyRent,
    }));
    /* array of weeks to add to the payments table array but to change it on backend */
    const addPayments = add.map((payment, i) => ({
      day: payment.day,
      number: (data.allPayments.length + i + 1).toString(),
    }));
    /* totalChange is the array to display in de PDF */
    const totalChange = [...payments, ...addPDFPayments];

    /* allPayments is the array to send to backend and change the array of payments in mainContract */
    const allPayments = [...data.allPayments, ...addPayments];

    const allDataPayments = allPayments.map((payment, i) => {
      return {
        ...payment,
        weeklyRent: totalChange[i].weeklyRent,
        index: i,
      };
    });
    // eslint-disable-next-line consistent-return
    return {
      allPayments,
      allPDFPayments: totalChange,
      weeks2Add: listChange.length,
      arrayChange: listChange,
      weeksChanged,
      allDataPayments,
    };
  }, [data.allPayments, firstDateIndex, finishServiceIndex, data.weeklyRent]);
  return newPayments;
}

function useCalculateDataDivideWeeks({ data, firstDateIndex, finishServiceIndex }: UseHook) {
  // console.log('firstDateIndex', firstDateIndex);
  // console.log('finishServiceIndex', finishServiceIndex);
  // console.log('total payments array', data.paymentsArray.length);
  const totalPrice = +(data.allPayments.length * data.weeklyRent).toFixed(2);
  // console.log('totalPrice', totalPrice);

  const newPayments = useMemo(() => {
    const divideWeeks = data.divideWeeks as number;
    if (!firstDateIndex || !finishServiceIndex) return;

    const firstIndex = Number(firstDateIndex.number) - 1;
    const finishIndex = Number(finishServiceIndex.number) - 1;

    const totalAmount2Pay = data.differenceWeeks * Number(data.weeklyRent) + Number(data.addAmount);
    const weeklyRentChange = totalAmount2Pay / divideWeeks + Number(data.weeklyRent);

    const stopChange = finishIndex + Number(divideWeeks);

    const weeksChanged: WeeksChanged = [];

    const feePerWeek = data.addAmount / divideWeeks;
    let totalPriceRest = totalPrice;
    let amountOutstanding = totalPrice;

    /* updated array of payments  */
    const payments = data.allPayments.map((payment, i) => {
      // totalPriceRest - weeklyRentChange;

      if (i > 0) {
        totalPriceRest -= data.weeklyRent;
      }
      // console.log('PAYMENT NUMBER AND PRICE', i, data.paymentsArray[i].weeklyCost);

      const contractValue = totalPriceRest;

      if (i >= finishIndex && i < stopChange) {
        weeksChanged.push({ ...payment, weeklyRent: Number(weeklyRentChange), index: i });

        amountOutstanding -= weeklyRentChange - feePerWeek;

        return {
          day: payment.day,
          weeklyRent: weeklyRentChange,
          fee: feePerWeek,
          contractValue,
          amountOutstanding,
        };
      } else if (i >= firstIndex && i < finishIndex) {
        weeksChanged.push({ day: payment.day, weeklyRent: 0, number: payment.number, index: i });

        return {
          day: payment.day,
          weeklyRent: 0,
          fee: 0,
          contractValue,
          amountOutstanding,
        };
      } else {
        const weeklyRent =
          data.paymentsArray[i]?.weeklyCost === 0
            ? (data.paymentsArray[i].weeklyCost as number)
            : data.weeklyRent;

        amountOutstanding -= weeklyRent;

        return {
          day: payment.day,
          weeklyRent,
          fee: 0,
          contractValue,
          amountOutstanding,
        };
      }
    });
    const allDataPayments = data.allPayments.map((payment, i) => {
      return {
        ...payment,
        weeklyRent: payments[i].weeklyRent,
        index: i,
      };
    });

    // console.log('weeksChanged', weeksChanged);
    // console.log('payments', payments);
    // eslint-disable-next-line consistent-return
    return {
      allPDFPayments: payments,
      allPayments: data.allPayments,
      weeksChanged,
      allDataPayments,
    };
  }, [firstDateIndex, finishServiceIndex, data]);
  return newPayments;
}
