/* eslint-disable prettier/prettier */
'use client';
import { ColumnDef } from '@tanstack/react-table';
import TableInDetail from '../TableInDetail';
import CellAction from '../CellAction';
import { VehicleResponse } from '@/actions/getVehicleData';
import { useMemo } from 'react';
import { useHandleDriverData } from '@/hooks/useDriverData';
import DriverDataHistoryModal from '@flotilla/components/DriverHistoryModal';
import { useHandleReadmissionData } from '@/hooks/useReadmissionDetail';
import DetailReadmissionModal from '../Modals/DetailReadmissionModal';
import { format, isValid, parseISO } from 'date-fns';

interface ColumnType {
  id: string;
  name: string;
  contract: string;
  reason: string;
  date: string;
}

interface DriversBoxProps {
  drivers: VehicleResponse['drivers'];
  vehicleData: VehicleResponse;
}

export default function DriversBox({ vehicleData }: DriversBoxProps) {
  const parsedDrivers = useMemo(() => {
    const contractNumber = vehicleData.carNumber;
    // const extensionCarNumber = vehicleData.extensionCarNumber;
    return vehicleData?.readmissions?.reverse().map((read, i) => {
      const extension = read.extensionContractNumber;
      const deliveredDate = read.deliveredDate ? parseISO(read.deliveredDate) : null;
      const readmissionDate = read.readmissionDate ? parseISO(read.readmissionDate) : null;
  
      const date = deliveredDate && isValid(deliveredDate) && readmissionDate && isValid(readmissionDate)
        ? `${format(deliveredDate, 'dd/MM/yyyy')} - ${format(readmissionDate, 'dd/MM/yyyy')}`
        : 'Fecha no disponible';
      return {
      id: i + 1,
      name: `${read.associate.firstName} ${read.associate.lastName}`,
      driverId: read.associateId,
      contract: extension
        ? `${contractNumber}${extension === 1 ? '' : ` - ${extension}`}`
        : contractNumber,
      reason: read.description,
      date: date,
    }
});
  }, [vehicleData]);
  const driverHook = useHandleDriverData();
  const readmissionHook = useHandleReadmissionData();
  const actions = [
    {
      label: () => `Ver Perfil`,
      onClick: (data: any) => {
        const selectedDriver2 = vehicleData.readmissions.find((d) => d.associateId === data.driverId);
        let driver: any;
        if (selectedDriver2) {
          driver = {
            ...selectedDriver2.associate,
            _id: selectedDriver2.associateId,
            isDriverBox: true,
          };
        }
        driverHook.setDriver(driver);
        driverHook.setCarNumber(vehicleData.carNumber);
        driverHook.onOpen();
      },
      
    },
    {
      label: () => `Ver detalle`,
      onClick: (data: any) => {
       
        const readmission = 
          vehicleData.readmissions.find((d) => d.associateId === data.driverId) as NonNullable<VehicleResponse['readmissions'][number]>;
        
        // let driver: any;
        // if (selectedDriver2) {
        //   driver = selectedDriver2.associate;
        // }
        // // console.log(selectedDriver);
        readmissionHook.setReadmission(readmission);
        // driverHook.setDriver(driver);
        readmissionHook.onOpen();
      },
      
    },
  ];

  const columns: ColumnDef<ColumnType>[] = [
    {
      header: 'No',
      accessorKey: 'id',
    },
    {
      header: 'Conductor',
      accessorKey: 'name',
    },
    {
      header: 'Contrato',
      accessorKey: 'contract',
    },
    {
      header: 'Motivo de término',
      accessorKey: 'reason',
    },
    {
      header: 'Fecha de inicio y finalización',
      accessorKey: 'date',
    },
    {
      header: 'Acciones',
      id: 'actions',
      cell: ({ row }) => {
        return <CellAction data={row.original} actions={actions} row={row} />;
      },
    },
  ];

  return (
    <>
      <DriverDataHistoryModal />
      {
        readmissionHook.isOpen && <DetailReadmissionModal />
      }
      <div
        className="
        w-full
        bg-white 
        border-[#EAECEE] 
        font-bold 
        rounded 
        min-h-[300px] 
        py-[25px]
        pl-[20px]
        pr-[15px]
        border-[1px]
        flex
        flex-col
        overflow-y-auto
      "
      >
        <p className="text-[24px] mb-[5px]">Historial de conductores</p>
        <TableInDetail data={parsedDrivers} columns={columns} />
      </div>
    </>
  );
}

// import CellAction from './CellAction';
