/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
import { VehicleResponse } from '@/actions/getVehicleData';
import { Region } from './vehiclesContext';
import { VehicleCard } from '@/actions/getAllVehicles';

export type Filter = {
  filterName: string;
  filterValue: string;
};

type State = {
  originalData: VehicleCard[];
  filteredData: VehicleCard[];
  filters: Filter[] | [];
};

type Action =
  | { type: 'REMOVE FILTERS' }
  | { type: 'FILTER_BY_REGION'; region: string }
  | { type: 'ADD_FILTERS'; filter: Filter[] }
  | { type: 'REMOVE_FILTER_BY_NAME'; filter: Filter }
  | { type: 'CLEAR_FILTER'; filter: Filter }
  | { type: 'CLEAR_FILTER'; data: VehicleCard[] }
  | { type: 'SET_ORIGINAL_DATA'; data: VehicleCard[]; region: Region };
// | { type: 'CLEAR_FILTER'; filterType: 'region' | 'step' };

const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case 'SET_ORIGINAL_DATA':
      return {
        ...state,
        originalData: action.data,
        filteredData: action.data.filter((item) => item.carNumber.split('')[0] === action.region),
      };
    case 'FILTER_BY_REGION':
      return {
        ...state,
        filteredData: state.originalData.filter((item) => item.carNumber.split('')[0] === action.region),
      };
    case 'ADD_FILTERS':
      const filters = action.filter;
      const results1 = filterByFilterArray(state, filters);

      return {
        ...state,
        filters: filters,
        filteredData: results1,
      };
    case 'REMOVE_FILTER_BY_NAME':
      const filteredFilters = state.filters.filter((item) => item.filterName !== action.filter.filterName);
      const results2 = filterByFilterArray(state, filteredFilters);
      return {
        ...state,
        filters: filteredFilters,
        filteredData: results2,
      };
    case 'REMOVE FILTERS':
      return {
        ...state,
        filteredData: state.originalData,
      };
    default:
      return state;
  }
};

export default reducer;

const filterObj: { [key: string]: (vehicle: any, filterValue: string) => boolean } = {
  region: (vehicle: VehicleCard, filterValue: Filter['filterValue']) => vehicle.vehicleState === filterValue,
  step: (vehicle: any, filterValue: Filter['filterValue']) => vehicle.step.stepNumber == filterValue,
  status: (vehicle: any, filterValue: Filter['filterValue']) => vehicle.status === filterValue,
  new: (vehicle: VehicleResponse, filterValue: Filter['filterValue']) =>
    vehicle.newCar === JSON.parse(filterValue || 'null'),
};

function filterByFilterArray(state: State, filters: Filter[]) {
  return state.originalData.filter((item: any) => {
    return filters.every((filter) => {
      return filterObj[filter.filterName](item, filter.filterValue);
      // if (filter.filterName === 'region') {
      //   return item.carNumber.split('')[0] === filter.filterValue;
      // }
      // if (filter.filterName === 'step') {
      //   return item.step.stepNumber == filter.filterValue;
      // }
    });
  });
}
