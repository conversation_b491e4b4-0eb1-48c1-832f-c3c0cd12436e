import 'moment/locale/es';
import moment from 'moment';
import { NumerosALetras } from 'numero-a-letras';
import { Text, View } from '@react-pdf/renderer';
import { readmissionDocStyles } from '../ReadmissionDocument';

// make spanish

interface ClausesProps {
  terminationDate: string;
  promissoryPayment: number;
  contractNumber: string;
}

export default function Clauses(props: ClausesProps) {
  const parsedTerminationDate = moment(props.terminationDate).format('DD [de] MMMM [de] YYYY');

  const priceParsed = new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(props.promissoryPayment);

  const textPrice = NumerosALetras(props.promissoryPayment);

  return (
    <>
      <View style={readmissionDocStyles.viewMain}>
        <Text
          style={{
            ...readmissionDocStyles.headerTitle,
            textAlign: 'center',
            marginTop: '40px',
            fontWeight: 'bold',
            letterSpacing: 1.5,
          }}
        >
          CLÁUSULAS
        </Text>
      </View>
      <View style={{ rowGap: 12 }}>
        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>PRIMERA: - </Text> Acuerdan Las Partes dar por
          terminado el contrato del día {parsedTerminationDate}, al cual se le asignó el número{' '}
          {props.contractNumber} respecto del vehículo, por así convenir a sus intereses.
        </Text>

        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>SEGUNDA: - </Text> Las partes acuerdan que no se
          reserva ninguna acción ni derecho presente o futuro respecto del contrato que dan por terminado de
          manera anticipada.
        </Text>

        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>TERCERA: - </Text> Que el Conductor hace entrega
          física y material al arrendador del vehículo materia del contrato de marras y que en este acto signa
          un pagaré por la cantidad de {priceParsed} M.N. ({textPrice}), a efecto de garantizar los daños que
          pudiera presentar el vehículo que entrega.
        </Text>

        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>CUARTA: - </Text> El arrendador, recibe el vehículo
          objeto del contrato de mérito, así como el documento mercantil exhibido, para en su caso hacerlo
          valer por la vía legal conducente.
        </Text>

        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>QUINTA: - </Text> Las Partes se someten a la
          jurisdicción de los Tribunales competentes en la Ciudad de México sean locales y/o federales,
          renunciando expresamente a cualquier otra jurisdicción que pudiera corresponderles, por razón de sus
          domicilios presentes o futuros o por cualquier otra razón.
        </Text>

        <Text style={{ fontSize: 12 }}>
          <Text style={{ fontFamily: 'Helvetica-Bold' }}>SEXTA.- ACUERDO TOTAL. </Text> El presente convenio
          contiene la totalidad de los acuerdos entre Las Partes con relación al presente acto contractual;
          ningún cambio, renuncia o modificación será exigible a menos que conste por escrito y firmado por
          Las Partes.
        </Text>

        <Text style={{ fontSize: 12 }}>
          Leído que fue el presente contrato por las partes y enteradas del alcance legal de todo el contenido
          de este, lo firman al calce y al margen para constancia, en Ciudad de México a 22 de agosto de 2024.
        </Text>
      </View>
    </>
  );
}
