// components/links-visitors.tsx
import { TableCell, TableRow } from '@/components/ui/table';
import { Check, X } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import PaymentTransactionTable from './PaymentTransactionTable';

export default function PaymentsTableRow({ payments }: { payments: any[] }) {
  return (
    <>
      <TableRow className="bg-slate-500 hover:bg-slate-500">
        <TableCell>{'Payment ID'}</TableCell>
        <TableCell>{'Concept'}</TableCell>
        <TableCell>{'Created At'}</TableCell>
        <TableCell>{'Invoice ID'}</TableCell>
        <TableCell>{'Is Paid'}</TableCell>
        <TableCell>{'Receipt ID'}</TableCell>
        <TableCell>{'Status'}</TableCell>
        <TableCell>{'Sub Total'}</TableCell>
        <TableCell>{'Tax'}</TableCell>
        <TableCell>{'Total'}</TableCell>
        <TableCell>{'Type'}</TableCell>
      </TableRow>
      {payments
        ? payments.map((payment) => (
            <TableRow className="bg-slate-300 hover:bg-slate-200" key={payment.id}>
              <Popover>
                <PopoverTrigger>
                  <TableCell>{payment.id}</TableCell>
                </PopoverTrigger>
                <PopoverContent className="w-max">
                  <PaymentTransactionTable
                    paymentTransactions={payment.paymentTransactions}
                  ></PaymentTransactionTable>
                </PopoverContent>
              </Popover>

              <TableCell>{payment.concept}</TableCell>
              <TableCell>{payment.createdAt}</TableCell>
              <TableCell>{payment.invoiceId}</TableCell>
              <TableCell>{payment.isPaid ? <Check /> : <X />}</TableCell>
              <TableCell>{payment.receiptId}</TableCell>
              <TableCell>{payment.status}</TableCell>
              <TableCell>{payment.subTotal}</TableCell>
              <TableCell>{payment.tax}</TableCell>
              <TableCell>{payment.total}</TableCell>
              <TableCell>{payment.type}</TableCell>
            </TableRow>
          ))
        : null}
    </>
  );
}
