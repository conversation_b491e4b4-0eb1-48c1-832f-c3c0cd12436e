'use server';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';

export async function removePayment(paymentId: string) {
  try {
    const url = new URL(`${PAYMENTS_API_URL}/payments/${paymentId}`);

    url.searchParams.append('completeDelete', 'true');

    const res = await axios.delete(`${url}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    return {
      success: true,
      message: res.data.data?.message || 'La información fiscal es correcta',
      errors: res.data?.errors,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'La información fiscal no es correcta',
      errors: error.response?.data?.errors,
    };
  }
}

export async function removeTemporalProducts(paymentid: string) {
  try {
    const url = new URL(`${PAYMENTS_API_URL}/payments/${paymentid}/remove-temporal-products`);

    // url.searchParams.append('completeDelete', 'true');

    const res = await axios.delete(`${url}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    return {
      success: true,
      message: res.data.data?.message || 'La información fiscal es correcta',
      errors: res.data?.errors,
    };
  } catch (error: any) {
    return {
      success: false,
      message: error.response?.data?.message || 'La información fiscal no es correcta',
      errors: error.response?.data?.errors,
    };
  }
}
