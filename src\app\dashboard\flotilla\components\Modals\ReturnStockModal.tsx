/* eslint-disable consistent-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
// import CustomModal from '@/components/Modals/CustomModal';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import AddMoreFiles from '@/components/Inputs/AddMoreFiles';
import CustomInput from '@/components/Inputs/CustomInput';
import InputFile from '@/components/Inputs/InputFile';
import SelectInput from '@/components/Inputs/SelectInput';
import Spinner from '@/components/Loading/Spinner';
import { getFullAddressStringV2, URL_API } from '@/constants';
import useStepper from '@/hooks/useStepper';
import generateFormData from '@/utils/generateFormData';
import { returnToStockSchemaStep1, returnToStockSchemaStep2 } from '@/validatorSchemas/returnToStockSchema';
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import axios from 'axios';
import { Form, Formik, FormikValues } from 'formik';
import Image from 'next/image';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useMemo, useState } from 'react';
import Swal from 'sweetalert2';
import { useVehicleDetailData } from '../Providers/VehicleDetailDataProvider';
import DocumentDisplay from '@/components/DocumentDisplay';
import PrimaryButton from '@/components/PrimaryButton';
import { getRecessionTerminationFile } from '@/pdfComponents/Termination/RecessionTermination/utils/getRecessionTerminationFile';

const initialStep = 1;
const totalSteps = 2;

const validations: { [key: string]: any } = {
  1: returnToStockSchemaStep1,
  2: returnToStockSchemaStep2,
};

export default function ReturnToStockModal() {
  const { currentStep, nextStep, prevStep, setStep } = useStepper(initialStep, totalSteps);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [loading, setLoading] = useState(false);
  const { id } = useParams();
  const { user } = useCurrentUser();
  const toast = useToast();
  const router = useRouter();
  const updateSideData = useUpdateSideData();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  async function onSubmit(values: FormikValues) {
    const formData = generateFormData(values);
    try {
      setLoading(true);
      const result = await axios.patch(`${URL_API}/stock/returnToStock/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      toast({
        title: result.data.message || 'Reingreso exitoso',
        status: 'success',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      await updateSideData(user);
      return router.push(
        window.location.pathname.includes('inactive')
          ? `/dashboard/flotilla/inactive/stock/${id}${country ? `?country=${encodeURI(country)}` : ''}`
          : `/dashboard/flotilla/stock/${id}${country ? `?country=${encodeURI(country)}` : ''}`
      );
    } catch (error) {
      return await Swal.fire({
        title: 'Hubo un error',
        // text: 'El vehículo ha sido reingresado',
        icon: 'error',
        // confirmButtonColor: '#5800F7',
      });
    } finally {
      setLoading(false);
    }
  }

  async function confirmSubmit(values: FormikValues, resetForm: () => void) {
    return Swal.fire({
      title: '¿Estás seguro?',
      text: 'No podrás revertir esta acción',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, continuar',
      cancelButtonText: 'Cancelar',
      confirmButtonColor: '#5800F7',
      cancelButtonColor: '#F3F4F6',
    }).then(async (result) => {
      if (result.isConfirmed) {
        onClose();
        await onSubmit(values);
        resetForm();
        setStep(initialStep);
      }
      resetForm();
      setStep(initialStep);
    });
  }
  async function handleStepChange(values: FormikValues, { resetForm }: { resetForm: () => void }) {
    if (currentStep !== totalSteps) {
      return nextStep();
    } else {
      onClose();
      confirmSubmit(values, resetForm);
    }
  }

  const actionLabel = useMemo(() => {
    if (currentStep === totalSteps) {
      return 'Terminar';
    }
    return 'Siguiente';
  }, [currentStep]);

  return (
    <>
      {loading && <Spinner />}
      <div className="relative">
        <button
          className="bg-[#5800F7] text-white text-start px-3 h-[40px] rounded w-auto"
          onClick={onOpen}
          data-cy="contract"
        >
          Completar reingreso
        </button>
      </div>
      <Modal closeOnOverlayClick={false} size="3xl" isOpen={isOpen} onClose={onClose}>
        <ModalOverlay zIndex={20} />
        <ModalContent>
          <ModalHeader>Detalle del reingreso</ModalHeader>
          <ModalCloseButton />
          <Formik
            initialValues={{
              km: '',
              kmImgs: '',
              evidenceImgs: '',
              promissoryNote: '',
              contractCanceled: '',
              readmissionDoc: '',
              isSignedDoc: '',
              comments: '',
              agreementSigned: '',
              readmissionDocSigned: '',
            }}
            onSubmit={handleStepChange}
            validationSchema={validations[currentStep] || null}
            validateOnMount={false}
            validateOnChange={currentStep !== 2}
          >
            {(props) => {
              const validate = props.dirty && props.isValid;
              return (
                <Form>
                  <ModalBody>
                    <ProgressBar steps={totalSteps} currentStep={currentStep} />
                    {/* <>{body}</> */}
                    <Body currentStep={currentStep} />
                  </ModalBody>
                  <ModalFooter className="flex flex-end gap-3 ">
                    {currentStep !== 1 && (
                      <Button
                        sx={{
                          color: '#5800F7',
                          borderColor: '#5800F7 !important',
                          border: '2px',
                          h: '40px',
                        }}
                        onClick={() => {
                          // onBack();
                          prevStep();
                        }}
                      >
                        Anterior
                      </Button>
                    )}

                    <Button
                      data-cy="next"
                      sx={{
                        color: 'white',
                        h: '40px',
                      }}
                      className={`
                      ${!validate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                      text-white rounded-md  h-[40px] cursor-pointer`}
                      type="submit"
                      disabled={!validate || loading}
                    >
                      {actionLabel}
                    </Button>
                  </ModalFooter>
                </Form>
              );
            }}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}

// interface StateFiles {
//   kmImgs: File[];
//   evidenceImgs: File[];
// }

type StateProps = 'kmImgs' | 'evidenceImgs';

interface StateImagesProps {
  kmImgs: {
    url: string;
    name: string;
  }[];
  evidenceImgs: {
    url: string;
    name: string;
  }[];
}

/* BODY */

function Body({ currentStep }: { currentStep: number }) {
  const [files, setFiles] = useState<Record<StateProps, FileList>>({
    kmImgs: {} as FileList,
    evidenceImgs: {} as FileList,
  });

  const [imgs, setImgs] = useState<StateImagesProps>({
    kmImgs: [],
    evidenceImgs: [],
  });

  const onChange = (fileList: FileList | null, name: string) => {
    if (fileList) {
      setFiles({
        ...files,
        [name]: fileList,
      });

      // Convierte las imagenes a url blob para preview
      const fls2 = Object.values(fileList);
      const imgs2 = fls2?.map((file) => {
        return {
          url: URL.createObjectURL(file),
          name: file.name,
        };
      });
      setImgs({
        ...imgs,
        [name]: imgs2,
      });
    }
  };

  const { vehicleData, associateData } = useVehicleDetailData();

  const promissoryNoteUrl =
    vehicleData.readmissions?.[vehicleData.readmissions.length - 1].terminationFiles?.promissoryNote?.url;

  const agreementSignedUrl =
    vehicleData.readmissions?.[vehicleData.readmissions.length - 1].terminationFiles?.agreement?.url;

  const [nameFiles, setNameFiles] = useState({
    promissoryNote: '',
    contractCanceled: '',
    readmissionDoc: '',
    agreementSigned: '',
    recessionSigned: '',
  });

  const handleSetName = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const onChangeImages = (fls: FileList | null, name: string) => {
    if (fls) {
      setFiles({
        ...files,
        [name]: fls,
      });
      // Convierte las imagenes a url blob para preview
      const fls2 = Object.values(fls);
      const imgs2 = fls2?.map((file) => {
        return {
          url: URL.createObjectURL(file),
          name: file.name,
        };
      });
      setImgs({
        ...imgs,
        [name]: imgs2,
      });
    }
  };

  const [isSigned, setIsSigned] = useState('');

  let body = (
    <div className="flex flex-col gap-4">
      <p className="text-[#262D33] font-bold ">Evidencia</p>
      {promissoryNoteUrl && agreementSignedUrl && (
        <div className="flex flex-col gap-2">
          <p>Documentos de terminación anticipada (Machote)</p>
          <div className="flex items-center gap-2">
            <p className="mr-4">Pagaré:</p>
            <DocumentDisplay
              url={promissoryNoteUrl}
              docName={
                vehicleData.readmissions?.[vehicleData.readmissions.length - 1].terminationFiles
                  ?.promissoryNote?.originalName!
              }
            />
          </div>
          <div className="flex items-center gap-2">
            <p>Convenio:</p>
            <DocumentDisplay
              url={agreementSignedUrl}
              docName={
                vehicleData.readmissions?.[vehicleData.readmissions.length - 1].terminationFiles?.agreement
                  ?.originalName!
              }
            />
          </div>
        </div>
      )}
      <CustomInput name="km" label="KM actual" type="text" />
      {files.kmImgs.length > 0 ? (
        <div className={` flex flex-col gap-2`}>
          <label htmlFor="evidenceImgs">Subir evidencia de KM actual</label>
          <div
            className={`
              flex gap-3 items-center 
              ${files.kmImgs.length > 6 && 'pb-[10px] overflow-y-none overflow-x-scroll custom-scroll'} 
            `}
          >
            {imgs.kmImgs.map((img, index) => {
              return (
                <Image
                  width="1000"
                  height="1000"
                  src={img.url}
                  alt={img.name}
                  key={index}
                  className="w-[80px] h-[80px] object-cover "
                />
              );
            })}
            <AddMoreFiles
              name="kmImgs"
              accept="all-images"
              multiple
              currentImages={files.kmImgs}
              onChange={(fls) => onChangeImages(fls, 'kmImgs')}
            />
          </div>
        </div>
      ) : (
        <>
          <InputFile
            name="kmImgs"
            label="Subir evidencia de KM actual"
            placeholder="Imágenes no mayores a 2 MB"
            buttonText="Subir evidencia"
            nameFile=""
            handleSetName={handleSetName}
            accept="all-images"
            multiple
            onChange={(fls) => {
              onChange(fls, 'kmImgs');
            }}
          />
        </>
      )}

      {files.evidenceImgs.length > 0 ? (
        <div className={`flex flex-col gap-2`}>
          <label htmlFor="evidenceImgs">Subir evidencia de reingreso de vehículo</label>
          <div
            className={`
              flex gap-3 items-center 
              ${files.evidenceImgs.length > 6 && 'pb-[10px] overflow-y-none overflow-x-scroll custom-scroll'} 
            `}
          >
            {imgs.evidenceImgs.map((img, index) => {
              return (
                <Image
                  width="1000"
                  height="1000"
                  src={img.url}
                  alt={img.name}
                  key={index}
                  className="w-[80px] h-[80px] object-cover "
                />
              );
            })}
            <>
              <AddMoreFiles
                name="evidenceImgs"
                accept="all-images"
                multiple
                currentImages={files.evidenceImgs}
                onChange={(fls) => onChangeImages(fls, 'evidenceImgs')}
              />
            </>
          </div>
        </div>
      ) : (
        <>
          <InputFile
            name="evidenceImgs"
            label="Subir evidencia de reingreso de vehículo"
            placeholder="Imágenes no mayores a 2 MB"
            buttonText="Subir evidencia"
            nameFile=""
            handleSetName={handleSetName}
            accept="all-images"
            multiple
            onChange={(fls) => {
              onChange(fls, 'evidenceImgs');
            }}
          />
        </>
      )}
      <SelectInput
        name="isSignedDoc"
        label="¿Firmó documentos de terminación anticipada?"
        options={[
          { value: 'Si', label: 'Si' },
          { value: 'No', label: 'No' },
        ]}
        onChange={(option) => {
          setIsSigned(option.value);
        }}
      />
      {isSigned === 'Si' && (
        <>
          <InputFile
            name="promissoryNote"
            label="Pagaré de terminación firmado"
            placeholder="Imágenes no mayores a 2 MB"
            buttonText="Subir evidencia"
            nameFile={nameFiles.promissoryNote}
            handleSetName={handleSetName}
            accept="pdf"
          />
          <InputFile
            name="agreementSigned"
            label="Convenio de terminación firmado"
            placeholder="Imágenes no mayores a 2 MB"
            buttonText="Subir evidencia"
            nameFile={nameFiles.agreementSigned}
            handleSetName={handleSetName}
            accept="pdf"
          />
        </>
      )}
      {isSigned === 'No' && (
        <>
          <PrimaryButton
            onClick={async () => {
              const extensionCarNumber = vehicleData.extensionCarNumber;
              const carNumber = vehicleData.carNumber;
              const contractNumber = extensionCarNumber ? `${carNumber}-${extensionCarNumber}` : carNumber;

              const data = {
                city: associateData.city,
                firstName: associateData.firstName,
                lastName: associateData.lastName,
                contractNumber,
                brand: vehicleData.brand,
                model: vehicleData.model,
                year: vehicleData.year,
                color: vehicleData.color,
                vin: vehicleData.vin,
                contractTerminationDate:
                  vehicleData.readmissions?.[vehicleData.readmissions.length - 1].readmissionDate,
                deliveryDate:
                  associateData.contractData.deliveryData?.isoStringRealDate ||
                  associateData.contractData.deliveredDate,
                fullAddress: getFullAddressStringV2(associateData),
                plates: vehicleData.carPlates.plates,
              };
              const recessionFile = await getRecessionTerminationFile(data);

              // download the file instance generated by the function, on the browser
              const url = URL.createObjectURL(recessionFile);
              const a = document.createElement('a');
              a.href = url;

              a.download = recessionFile.name;
              a.click();
              URL.revokeObjectURL(url);
            }}
          >
            Generar recesión de contrato
          </PrimaryButton>
          <InputFile
            name="recessionSigned"
            label="Recisión de contrato firmado"
            placeholder="Imágenes no mayores a 2 MB"
            buttonText="Subir evidencia"
            nameFile={nameFiles.recessionSigned}
            handleSetName={handleSetName}
            accept="pdf"
          />
          <CustomInput name="comments" label="Comentarios" type="text" />
        </>
      )}
    </div>
  );

  if (currentStep === 2)
    body = (
      <div className="flex flex-col gap-3">
        <p className="text-[#262D33] font-bold ">Firma de documentos</p>

        <InputFile
          name="contractCanceled"
          label="Cancelación de contrato"
          placeholder="Imágenes no mayores a 2 MB"
          buttonText="Subir evidencia"
          nameFile={nameFiles.contractCanceled}
          handleSetName={handleSetName}
          accept="pdf"
        />
        <InputFile
          name="readmissionDoc"
          label="Documento de reingreso"
          placeholder="Imágenes no mayores a 2 MB"
          buttonText="Subir evidencia"
          nameFile={nameFiles.readmissionDoc}
          handleSetName={handleSetName}
          accept="pdf"
        />
      </div>
    );
  return body;
}

function Check({ number }: { number: number }) {
  return (
    <div
      className={`
        w-[26px] 
        h-[26px] 
        flex justify-center 
        items-center 
        bg-[#5800F7]
        rounded-full 
        text-white
        `}
    >
      {number}
      {/* {number} */}
    </div>
  );
}

function CircleGray() {
  return (
    <div className="w-[24px] h-[24px] flex justify-center items-center bg-[white] border-[3px] border-[#9CA3AF] rounded-full ">
      <div className="w-[6px] h-[6px] bg-[#9CA3AF] rounded-full " />
    </div>
  );
}

const ProgressBar = ({ steps, currentStep }: any) => {
  const progressMemo = useMemo(() => {
    const result = (currentStep / (steps - 1)) * 100;
    if (result > 100) return 100;
    return result;
  }, [currentStep, steps]);

  return (
    <>
      <div className="w-full h-[40px] relative flex items-center place-content-between mb-[25px]">
        <div
          id="word-steps"
          className="w-full flex place-content-between absolute border-[2px] text-[14px] border-[#9CA3AF] h-[1px]"
        ></div>
        <div
          className=" h-[1px] border-[2px] absolute border-purple-500 "
          style={{ width: `${progressMemo}%` }}
        ></div>
        <div id="circle-steps" className="absolute z-10 w-full flex justify-between ">
          {Array.from({ length: steps }, (_, i) => {
            return <div key={i}>{currentStep >= i + 1 ? <Check number={i + 1} /> : <CircleGray />}</div>;
          })}
        </div>
      </div>
    </>
  );
};
