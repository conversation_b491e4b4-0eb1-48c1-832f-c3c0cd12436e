'use client';
/* eslint-disable @typescript-eslint/no-use-before-define */
import { VehicleResponse } from '@/actions/getVehicleData';
import updateStatus from '@/actions/updateStockStatusVehicles';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { BsCheck } from 'react-icons/bs';
import ModalContainer from '../Modals/ModalContainer';
import { Formik, Form } from 'formik';
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import Spinner from '@/components/Loading/Spinner';
import { allCategory, ContractRegionCode, vehicleSteps } from '@/constants';
import CustomDatePicker from '@/components/Inputs/CustomDatePicker';
import { receptionDateSchema } from '@/validatorSchemas/createStockSchema';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import Swal from 'sweetalert2';

interface VehicleStepsProps {
  data: VehicleResponse;
  driver: VehicleResponse['drivers'][number];
}

export default function VehicleSteps({ data }: VehicleStepsProps) {
  const updateSideData = useUpdateSideData();
  const toast = useToast();
  const router = useRouter();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';
  // const stepName = data.step.stepName;
  const stepNumber = data.step.stepNumber;
  const { user } = useCurrentUser();
  const [isLoading, setIsLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);

  const urlSegments = window.location.pathname.split('?')[0].split('/');

  async function update(status: 'Vehiculo listo', receptionDate?: string) {
    setIsLoading(true);
    try {
      const response = await updateStatus({
        id: data._id,
        message: status,
        user: user,
        receptionDate: receptionDate,
      });
      setIsLoading(false);
      toast({
        title: response?.data.message,
        description: 'Actualizando pagina...',
        duration: 4000,
        status: 'success',
        position: 'top',
      });
      await updateSideData(user);
      router.refresh();
    } catch (error: any) {
      setIsLoading(false);
      toast({
        title: error?.response?.data?.message || 'Error al actualizar el vehículo',
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    }
  }

  let hasAllDocs = Boolean(
    data.carPlates &&
      data.circulationCard &&
      data.gpsInstalled &&
      data.tenancy.length > 0 &&
      data.policiesArray.length > 0
  );
  if (data.vehicleState === ContractRegionCode.CDMX) {
    hasAllDocs = Boolean(hasAllDocs && data.circulationCard?.frontImg && data.circulationCard?.backImg);
  }
  const isPolicyValid =
    data.policiesArray?.length > 0
      ? new Date(data.policiesArray[data.policiesArray.length - 1]?.validity) > new Date()
      : false;

  const isTenancyValid =
    data.tenancy?.length > 0 ? new Date(data.tenancy[data.tenancy.length - 1]?.validity) > new Date() : false;

  const isCirculationValid =
    data.circulationCard && data.circulationCard?.validity
      ? new Date(data.circulationCard.validity) > new Date()
      : false;

  useEffect(() => {
    if (
      stepNumber === vehicleSteps.stock.number &&
      hasAllDocs &&
      data.receptionDate &&
      (!isPolicyValid || !isTenancyValid || !isCirculationValid)
    ) {
      const missingConditions = [];

      if (!isPolicyValid && data.policiesArray?.length > 0)
        missingConditions.push('Póliza de seguro vencida');
      if (!isTenancyValid && data.tenancy?.length > 0) missingConditions.push('Tenencia vencida');
      if (!isCirculationValid && data.circulationCard)
        missingConditions.push('Tarjeta de circulación vencida');

      Swal.fire({
        title: 'Condiciones pendientes',
        html: `<div class="text-left">
          <p class="mb-2">El vehículo no puede pasar a stock debido a las siguientes condiciones:</p>
          <ul class="list-disc pl-5">
            ${missingConditions.map((condition) => `<li>${condition}</li>`).join('')}
          </ul>
        </div>`,
        icon: 'warning',
        confirmButtonText: 'Entendido',
        confirmButtonColor: '#5800F7',
      });
    }
  }, []);

  function handleSubmit(values?: { receptionDate: string }) {
    update('Vehiculo listo', values?.receptionDate);
    setModalOpen(false);
  }

  useEffect(() => {
    if (
      stepNumber === vehicleSteps.stock.number &&
      hasAllDocs &&
      isPolicyValid &&
      isTenancyValid &&
      isCirculationValid
    ) {
      if (!data.receptionDate) {
        setModalOpen(true);
      } else {
        update('Vehiculo listo');
      }
    }
  }, [data?.gpsInstalled]);

  useEffect(() => {
    if (data?.category === allCategory.stock && urlSegments && !urlSegments.includes(allCategory.stock)) {
      const newPath = urlSegments.includes('inactive')
        ? `/dashboard/flotilla/inactive/stock/${data._id}${country ? `?country=${encodeURI(country)}` : ''}`
        : `/dashboard/flotilla/stock/${data._id}${country ? `?country=${encodeURI(country)}` : ''}`;
      router.push(newPath);
    }
    if (
      data?.category === allCategory['in-preparation'] &&
      urlSegments &&
      !urlSegments.includes(allCategory['in-preparation'])
    ) {
      const newPath = urlSegments.includes('inactive')
        ? `/dashboard/flotilla/inactive/in-preparation/${data._id}${
            country ? `?country=${encodeURI(country)}` : ''
          }`
        : `/dashboard/flotilla/stock/${data._id}${country ? `?country=${encodeURI(country)}` : ''}`;
      router.push(newPath);
    }
  }, [data?.category, urlSegments]);

  if (isLoading) return <Spinner />;

  return (
    <div>
      <div
        id="vehicle-steps"
        className="w-full h-[40px] relative flex items-center place-content-between mb-[25px] "
      >
        <div
          id="word-steps"
          className="w-full flex place-content-between relative border-[2px] text-[14px] border-[#9CA3AF] h-[1px]"
        >
          <p className="pt-4 text-[#29CC97] ">En stock</p>
          <p
            className={
              stepNumber > vehicleSteps.stock.number ? 'pt-4 text-[#29CC97] ml-[30px]' : 'pt-4 ml-[30px]'
            }
          >
            Vehiculo listo
          </p>
          <p className={`pt-4 ${stepNumber > vehicleSteps.vehicleReady.number ? 'text-[#29CC97]' : ''}  `}>
            Conductor asignado
          </p>
          <p className={stepNumber > vehicleSteps.driverAssigned.number ? 'pt-4 text-[#29CC97]' : 'pt-4'}>
            Contrato generado
          </p>
          <p className={stepNumber > vehicleSteps.contractCreated.number ? 'pt-4 text-[#29CC97]' : 'pt-4'}>
            Entregado
          </p>
        </div>
        <div id="circle-steps" className="absolute  flex w-full place-content-between ">
          <Check />
          {stepNumber > vehicleSteps.stock.number ? <Check /> : <CircleGray />}
          {stepNumber > vehicleSteps.vehicleReady.number ? <Check /> : <CircleGray />}
          {stepNumber > vehicleSteps.driverAssigned.number ? <Check /> : <CircleGray />}{' '}
          {stepNumber > vehicleSteps.contractCreated.number ? <Check /> : <CircleGray />}
        </div>
      </div>
      {modalOpen && (
        <ModalContainer title="Enviar a stock" onClose={() => setModalOpen(false)}>
          <Formik
            initialValues={{ receptionDate: '' }}
            enableReinitialize
            validationSchema={receptionDateSchema('', '')}
            onSubmit={handleSubmit}
          >
            {({ values, dirty }) => (
              <Form>
                <div className="flex flex-col gap-3">
                  <CustomDatePicker label="Fecha de recepción" name="receptionDate" />
                  <div className="flex gap-3 pt-[20px] justify-end">
                    <button
                      className="w-[100px] h-[40px] bg-white border-2 font-[600] text-primaryPurple border-primaryBtn rounded-[5px] text-[14px] hover:bg-[#EAEAEA] transition-colors duration-200"
                      onClick={() => setModalOpen(false)}
                      type="button"
                    >
                      Cancelar
                    </button>
                    <button
                      disabled={!dirty && !values.receptionDate}
                      type="submit"
                      className={`
                      w-[max-content] min-w-[100px] 
                      h-[40px] 
                      text-white rounded-[5px] 
                      cursor-pointer
                      ${!dirty && !values.receptionDate ? 'bg-[#9CA3AF]' : 'bg-[#5800F7]'}
                      text-[14px] font-[500] 
                      transition-colors duration-200 px-2
                    `}
                    >
                      Confirmar
                    </button>
                  </div>
                </div>
              </Form>
            )}
          </Formik>
        </ModalContainer>
      )}
    </div>
  );
}

function CircleGray() {
  return (
    <div className="w-[32px] h-[32px] flex justify-center items-center bg-[white] border-[3px] border-[#9CA3AF] rounded-full ">
      <div className="w-[10px] h-[10px] bg-[#9CA3AF] rounded-full " />
    </div>
  );
}

function Check() {
  return (
    <div className="w-[32px] h-[32px] flex justify-center items-center bg-[#29CC97] rounded-full ">
      <BsCheck size={28} color="white" />
    </div>
  );
}
