'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import { useOpenOverHaulingModal } from '@/zustand/modalStates';
import InputDate from '@/components/Inputs/InputDate';
import InputMultipleFiles from '@/components/Inputs/InputMultipleFiles';
import CustomInput from '@/components/Inputs/CustomInput';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { useParams, useSearchParams } from 'next/navigation';
import InputFile from '@/components/Inputs/InputFile';
import { useState } from 'react';
import { sendOverHaulingSchema } from '@/validatorSchemas/overhaulingSchema';
import { useRouter } from 'next/navigation';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import { getTranslationMap } from '@/constants/status';
import SelectInput from '@/components/Inputs/SelectInput';
import { VehicleSubCategoryTranslations } from '../translations/statusTranslations';

export default function OverHaulingModal() {
  const overHaulingModal = useOpenOverHaulingModal();
  type SubCategoryKey = keyof typeof VehicleSubCategoryTranslations;

  const updateSideData = useUpdateSideData();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  const { user } = useCurrentUser();
  const { id } = useParams();
  const router = useRouter();

  const toast = useToast();

  const [nameFile, setNameFile] = useState('');

  const subCategoryOptions = [
    { value: 'aesthetic-repair', label: 'Reparación Estética' },
    { value: 'duplicate-key-missing', label: 'Falta Duplicado de Llave' },
    { value: 'mechanical-repair', label: 'Reparación Mecánica' },
    { value: 'electrical-repair', label: 'Reparación Eléctrica' },
    { value: 'engine-repair', label: 'Reparación de Motor' },
    { value: 'waiting-for-parts', label: 'En Espera de Refacciones' },
    { value: 'corrective-maintenance', label: 'Mantenimiento Correctivo' },
    { value: 'management', label: 'Gestión' },
    { value: 'gps', label: 'GPS' },
  ].map((option) => ({
    value: option.value,
    label: getTranslationMap(country)?.subCategories[option.value as SubCategoryKey] || option.label,
  }));

  return (
    <ModalContainer
      title="Mandar a revisión"
      removeScrollBar
      onClose={() => {
        overHaulingModal.onClose();
      }}
    >
      <FormikContainer
        initialValues={{
          dateIn: '',
          comments: '',
          quotationDoc: '',
          inImgs: '',
          subCategory: {
            value: '',
            label: 'Selecciona',
          },
        }}
        confirmBtnText="Mandar a revisión"
        validatorSchema={sendOverHaulingSchema}
        onSubmit={async (values) => {
          try {
            await axios.patch(
              `${URL_API}/stock/sendOverHauling/${id}`,
              {
                ...values,
                subCategory: values.subCategory.value,
              },
              {
                headers: {
                  Authorization: `Bearer ${user.accessToken}`,
                  'Content-Type': 'multipart/form-data',
                },
              }
            );
            overHaulingModal.onClose();

            toast({
              title: 'Se mando a revisión correctamente',
              description: '',
              status: 'success',
              position: 'top',
              duration: 3000,
              isClosable: true,
            });

            setTimeout(() => {
              router.refresh();
              if (window.location.pathname.includes('inactive')) {
                router.push(
                  `/dashboard/flotilla/inactive/revision/${id}${
                    country ? `?country=${encodeURI(country)}` : ''
                  }`
                );
              }
            }, 3000);
            await updateSideData(user);
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Ocurrio un error al mandar a revisión',
              status: 'error',
              position: 'top',
              duration: 3000,
              isClosable: true,
            });
          }
        }}
      >
        <div className="flex flex-col gap-3">
          <InputDate name="dateIn" label="Fecha de entrada a Revisión" />

          <SelectInput label="Motivo de revisión" name="subCategory" options={subCategoryOptions} />

          <InputFile
            name="quotationDoc"
            label="Cotización"
            nameFile={nameFile}
            handleSetName={setNameFile}
            accept="pdf"
            buttonText="Subir archivo"
            placeholder="No mayor a 3mb"
          />

          <InputMultipleFiles
            name="inImgs"
            label="Fotografias (minimo 5)"
            buttonText="Subir archivos"
            accept="all-images"
            placeholder="Arrastra o selecciona archivos"
          />

          <CustomInput name="comments" label="Comentarios (opcional)" type="textarea" />
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
