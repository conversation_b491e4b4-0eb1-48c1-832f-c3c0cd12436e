import { useState, useEffect } from 'react';
import {
  Modal,
  Modal<PERSON>ody,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>ooter,
  Modal<PERSON>eader,
  ModalOverlay,
  ModalCloseButton,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { URL_API, Countries } from '@/constants';
import { getAllowedFileExtensions } from '@/utils/fileExtensionHelpers';
import { PresignedUrlResponseItem, UploadedDocumentInfo } from '@/types';
import { Button } from '@/components/ui/button';
import FileUploader from './UploadZone';
import { Formik, Form as FormikForm } from 'formik';
import LocationSelector from './steps/LocationSelector';
import DocumentCategorySelector from './steps/DocumentCategorySelector';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { documentUploadTranslationsMX, documentUploadTranslationsUS } from '@/constants/translations';
import { getAllDocumentInfoMessages, getDefaultDocumentInfoMessage } from '@/utils/documentInfoMessages';
import * as Yup from 'yup';
import { CONTRACT_REGIONS_IATA } from '@/constants';
import { useSearchParams } from 'next/navigation';
import { useConnectionStatus } from '@/hooks/useConnectionStatus';

enum STEPS {
  LOCATION_SELECTION = 0,
  CATEGORY_SELECTION = 1,
  FILE_UPLOAD = 2,
}

// Simple validation schema
const documentUploadSchema = Yup.object().shape({
  country: Yup.object().shape({
    value: Yup.string().required('Country is required'),
  }),
  documentCategory: Yup.string().when('_', {
    is: () => true,
    then: (schema) => schema.required('Document category is required'),
  }),
});

export default function BulkUploadVehicleDocuments() {
  const [files, setFiles] = useState<File[]>([]);
  const [step, setStep] = useState<STEPS>(STEPS.LOCATION_SELECTION);
  const [documentCategory, setDocumentCategory] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [country, setCountry] = useState<Countries | null>(null);
  const [region, setRegion] = useState<string | null>(null);
  const [contractNumber, setContractNumber] = useState<string>('');
  const { data: session } = useSession();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const { user: currentUser, isSuperAdminOrAdmin } = useCurrentUser();
  const search = useSearchParams();
  const paramCountry = search ? search.get('country') : '';

  const allowedRegions = CONTRACT_REGIONS_IATA.filter((r) => {
    const code = r.code as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'pbc';
    return currentUser?.settings?.allowedRegions?.includes(code);
  });

  // Initialize country from paramCountry if provided, otherwise default to Mexico
  useEffect(() => {
    if (paramCountry) {
      setCountry(paramCountry as Countries);
    } else if (!country) {
      // Default to Mexico if no country is set
      setCountry(Countries.Mexico);
    }
  }, [paramCountry, country]);

  // This ensures translations update whenever country changes
  // Get translations based on current country selection and memoize them
  const translations =
    country === Countries['United States'] ? documentUploadTranslationsUS : documentUploadTranslationsMX;

  useConnectionStatus(country as Countries);

  // Get all info messages using centralized utility
  const infoMessages = getAllDocumentInfoMessages(country === Countries['United States']);
  const defaultInfoMessage = getDefaultDocumentInfoMessage(country === Countries['United States']);

  // Get modal header based on current step
  const modalHeader =
    step === STEPS.LOCATION_SELECTION
      ? translations.selectLocation
      : step === STEPS.CATEGORY_SELECTION
      ? translations.selectDocumentCategory
      : translations.uploadDocuments;

  // Reset bulk upload state for fresh modal each open
  const openModal = () => {
    setStep(STEPS.LOCATION_SELECTION);
    setFiles([]);
    setDocumentCategory('');
    // Default to Mexico if no country is selected
    setCountry(Countries.Mexico);
    setRegion(null);
    setContractNumber('');
    setUploadProgress(0);
    onOpen();
  };

  const handleSubmit = async () => {
    if (!session?.user?.accessToken) {
      toast({ title: translations.userNotAuthenticated, status: 'error' });
      return;
    }

    setIsLoading(true);
    toast({ title: translations.startingUpload, status: 'info' });

    try {
      const filesToUpload = files.map((file) => ({
        fileName: file.name,
        contentType: file.type,
        documentCategory,
      }));

      const presignResponse = await fetch(`${URL_API}/stock/documents/generate-upload-urls`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.user.accessToken}`,
        },
        body: JSON.stringify({ filesToUpload }),
      });

      if (!presignResponse.ok) {
        throw new Error(translations.errorProcessing);
      }

      const presignData = (await presignResponse.json()) as { data: PresignedUrlResponseItem[] };
      const uploads: PresignedUrlResponseItem[] = presignData.data;

      let completedUploads = 0;
      for (let i = 0; i < uploads.length; i++) {
        const uploadInfo = uploads[i];
        const file = files.find((f) => f.name === uploadInfo.originalFileName);
        if (!file) continue;
        let uploaded = false;
        while (!uploaded) {
          try {
            if (!navigator.onLine) {
              await new Promise((resolve) => window.addEventListener('online', resolve, { once: true }));
            }
            const response = await fetch(uploadInfo.presignedUrl, {
              method: 'PUT',
              headers: { 'Content-Type': uploadInfo.contentType },
              body: file,
            });
            if (!response.ok) {
              throw new Error(`Upload failed with status ${response.status}`);
            }
            uploaded = true;
            completedUploads++;
            setUploadProgress(Math.round((completedUploads / files.length) * 100));
          } catch (error: any) {
            if (!navigator.onLine) {
              // Wait for connection to be restored, then retry, but dont forward the error to be thrown as will already be prompted by the useConnectionStatus hook
            } else {
              throw error;
            }
          }
        }
      }

      toast({ title: translations.uploadSuccess, status: 'success' });

      // Trigger backend processing of uploaded documents
      const documentsToProcess: UploadedDocumentInfo[] = uploads.map((u) => ({
        s3Key: u.s3Key,
        originalFileName: u.originalFileName,
        contentType: u.contentType,
        documentCategory: u.documentCategory,
      }));
      const processResponse = await fetch(`${URL_API}/stock/documents/process-bulk-upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session?.user?.accessToken}`,
        },
        body: JSON.stringify({
          documents: documentsToProcess,
          userName: session?.user?.name,
          userEmail: session?.user?.email,
          country: country,
          region: region,
          contractNumber: contractNumber,
        }),
      });
      if (!processResponse.ok) {
        throw new Error(translations.errorProcessing);
      }
      toast({
        title: translations.uploadSuccess,
        status: 'success',
      });

      setFiles([]);
      setDocumentCategory('');
      setStep(STEPS.LOCATION_SELECTION);
      setCountry(null);
      setRegion(null);
      setContractNumber('');
      setUploadProgress(0);
      onClose();
    } catch (error: any) {
      toast({
        title: translations.uploadError,
        description: error.message,
        status: 'error',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleModalClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const handleCountrySelect = (selectedCountry: Countries) => {
    // Immediately update the country to trigger UI refresh
    setCountry(selectedCountry);
    // Reset related state to ensure clean UI update
    setRegion(null);
    setContractNumber('');
    // Reset document category when changing countries as options might differ
    setDocumentCategory('');
  };

  const handleContractNumberUpdate = (number: string) => {
    setContractNumber(number);
  };

  const initialValues = {
    country: {
      label: '',
      value: '',
    },
    state: {
      label: '',
      value: '',
    },
    vehicleState: {
      value: '',
      label: translations.select,
      code: '',
    },
    documentCategory: '',
  };

  const handleNext = () => {
    if (step === STEPS.LOCATION_SELECTION && country) {
      setStep(STEPS.CATEGORY_SELECTION);
    } else if (step === STEPS.CATEGORY_SELECTION && documentCategory) {
      setStep(STEPS.FILE_UPLOAD);
    }
  };

  const handleBack = () => {
    if (step === STEPS.FILE_UPLOAD) {
      setStep(STEPS.CATEGORY_SELECTION);
    } else if (step === STEPS.CATEGORY_SELECTION) {
      setStep(STEPS.LOCATION_SELECTION);
    }
  };

  // Form is ready for rendering

  return (
    <>
      <Button onClick={openModal} className="bg-[#5800F7] px-4 py-2 rounded">
        {translations.bulkUploadTitle}
      </Button>

      <Modal
        closeOnOverlayClick={false}
        size={step === STEPS.FILE_UPLOAD ? 'xl' : 'md'}
        isOpen={isOpen}
        onClose={handleModalClose}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader className="font-inter font-[600] text-[18px]">{modalHeader}</ModalHeader>
          <ModalCloseButton />
          <Formik
            initialValues={initialValues}
            validationSchema={documentUploadSchema}
            onSubmit={() => {}}
            enableReinitialize
          >
            {({ setFieldValue }) => (
              <FormikForm>
                <ModalBody pb={6}>
                  <div className="flex flex-col gap-[20px]">
                    {step === STEPS.LOCATION_SELECTION && (
                      <LocationSelector
                        onCountrySelect={(selectedCountry) => {
                          // Force immediate update of country to trigger UI refresh
                          handleCountrySelect(selectedCountry);
                          // Update form values
                          setFieldValue('country', {
                            label: selectedCountry === Countries['United States'] ? 'USA' : 'MX',
                            value: selectedCountry,
                          });
                        }}
                        isSuperAdminOrAdmin={isSuperAdminOrAdmin}
                        allowedRegions={allowedRegions}
                        setRegion={(selectedRegion) => {
                          setRegion(selectedRegion);
                        }}
                        onContractNumberUpdate={handleContractNumberUpdate}
                        translations={{
                          selectRegion: translations.selectRegion || 'Select Region',
                        }}
                      />
                    )}

                    {step === STEPS.CATEGORY_SELECTION && (
                      <DocumentCategorySelector
                        documentCategory={documentCategory}
                        setDocumentCategory={setDocumentCategory}
                        isLoading={isLoading}
                        infoMessages={infoMessages}
                        defaultInfoMessage={defaultInfoMessage}
                        translations={translations}
                        country={country}
                        region={region}
                      />
                    )}

                    {step === STEPS.FILE_UPLOAD && (
                      <FileUploader
                        translations={translations}
                        accept={getAllowedFileExtensions(documentCategory, 'string') as string}
                        allowedExtensions={getAllowedFileExtensions(documentCategory, 'array') as string[]}
                        maxFiles={50}
                        maxFileSize={5 * 1024 * 1024} // 5MB in bytes
                        isLoading={isLoading}
                        progress={uploadProgress}
                        onFilesChange={(filesArr) => setFiles(filesArr)}
                        labels={{
                          uploadingText: translations.uploadingFiles,
                          selectedFilesTitle: translations.selectedFiles,
                          moreLabel: translations.showMore,
                          lessLabel: translations.showLess,
                          emptyPlaceholder: (
                            <p className="mt-4">
                              <span className="font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#6210FF] to-[#A74DF9]">
                                {translations.selectFilesMessage?.split(' ')[0]}
                              </span>{' '}
                              {translations.selectFilesMessage?.split(' ').slice(1).join(' ')}
                            </p>
                          ),
                        }}
                      />
                    )}
                  </div>
                </ModalBody>

                <ModalFooter gap={3}>
                  {step > STEPS.LOCATION_SELECTION && (
                    <Button
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      onClick={handleBack}
                    >
                      {translations.back || 'Back'}
                    </Button>
                  )}

                  {step < STEPS.FILE_UPLOAD && (
                    <Button
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      onClick={handleNext}
                      disabled={
                        (step === STEPS.LOCATION_SELECTION && (!country || !region)) ||
                        (step === STEPS.CATEGORY_SELECTION && !documentCategory)
                      }
                    >
                      {translations.next || 'Next'}
                    </Button>
                  )}

                  {step === STEPS.FILE_UPLOAD && (
                    <Button
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      onClick={handleSubmit}
                      disabled={isLoading || files.length === 0}
                    >
                      {isLoading ? translations.uploading : translations.upload}
                    </Button>
                  )}
                </ModalFooter>
              </FormikForm>
            )}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
}
