'use client';
import { StyleSheet } from '@react-pdf/renderer';
import dynamic from 'next/dynamic';
import React from 'react';
import PagareTerminationDocumentPDF from './PagareTermination-DocumentPDF';

const PDFViewer = dynamic(() => import('@react-pdf/renderer').then((mod) => mod.PDFViewer), {
  ssr: false,
});

const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
    width: '100%',
  },
  body: {
    marginHorizontal: '10%',
    marginVertical: '7%',
    // rowGap: '30px' as any,
  },
  body2: {
    marginVertical: '10%',
  },
  viewer: {
    width: '80%',
    height: '100vh',
  },
});

export default function PagareTerminationPDFViewer() {
  return (
    <>
      <PDFViewer style={styles.viewer}>
        <PagareTerminationDocumentPDF
          city="mty"
          firstName="Pedro"
          lastName="Martinez"
          weeklyRent={3450}
          totalPays={0}
          date="2021-12-12"
          fullAddress="Calle 123"
        />
      </PDFViewer>
    </>
  );
}
