import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
export enum AuthErrors {
  Id_Token_Invalid = 'Id_Token_Invalid',
  Google_Registered_User_Not_Found = 'Google_Registered_User_Not_Found',
  Server_Is_Down = 'Server_Is_Down',
  Internal_Server_Error = 'Internal_Server_Error',
  Bad_Gateway_Status_Code = 502,
}

export const authErrors = {
  [AuthErrors.Id_Token_Invalid]: 'Invalid Id Token',
  [AuthErrors.Google_Registered_User_Not_Found]: 'User Not Found',
  [AuthErrors.Server_Is_Down]: 'Server Is Down, unable to connect to the api server',
  [AuthErrors.Internal_Server_Error]: 'Internal Server Error',
};
