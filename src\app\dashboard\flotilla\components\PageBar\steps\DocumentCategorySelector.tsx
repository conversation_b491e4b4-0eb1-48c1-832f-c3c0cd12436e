import { FormControl, Select, Alert, AlertIcon, Text } from '@chakra-ui/react';
import { useFormikContext } from 'formik';
import { DocumentCategory, Countries, ContractRegionCode } from '@/constants';

interface DocumentCategorySelectorProps {
  documentCategory: string;
  setDocumentCategory: (category: string) => void;
  isLoading: boolean;
  infoMessages: Record<string, string>;
  defaultInfoMessage: string;
  translations: any;
  country?: Countries | null;
  region?: string | null;
}

const DocumentCategorySelector = ({
  documentCategory,
  setDocumentCategory,
  isLoading,
  infoMessages,
  defaultInfoMessage,
  translations,
  country,
  region,
}: DocumentCategorySelectorProps) => {
  const { setFieldValue } = useFormikContext();

  const handleCategorySelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedCategory = e.target.value;
    setDocumentCategory(selectedCategory);
    setFieldValue('documentCategory', selectedCategory);
  };

  return (
    <FormControl>
      <div className="flex flex-col gap-4">
        <Select
          name="documentCategory"
          placeholder={translations.selectDocumentCategory}
          value={documentCategory}
          onChange={handleCategorySelect}
          isDisabled={isLoading}
        >
          <option value={DocumentCategory.INSURANCE_POLICY}>{translations.insurancePolicy}</option>
          <option value={DocumentCategory.TENENCIA}>{translations.tenencia}</option>
          <option value={DocumentCategory.CIRCULATION_CARD_FRONT}>{translations.circulationCardFront}</option>
          {country === Countries.Mexico && region === ContractRegionCode.CDMX && (
            <option value={DocumentCategory.CIRCULATION_CARD_BACK}>{translations.circulationCardBack}</option>
          )}
          <option value={DocumentCategory.PLATES_ALTA_PLACAS}>{translations.platesAltaPlacas}</option>
          <option value={DocumentCategory.PLATES_FRONT}>{translations.platesFront}</option>
          <option value={DocumentCategory.PLATES_BACK}>{translations.platesBack}</option>
          <option value={DocumentCategory.FACTURE}>{translations.facture}</option>
        </Select>
        {documentCategory && (
          <Alert status="info" variant="subtle" mt={2} borderRadius="md">
            <AlertIcon />
            <Text as="span" color="blue.600" fontSize="sm">
              {infoMessages[documentCategory] || defaultInfoMessage}
            </Text>
          </Alert>
        )}
      </div>
    </FormControl>
  );
};

export default DocumentCategorySelector;
