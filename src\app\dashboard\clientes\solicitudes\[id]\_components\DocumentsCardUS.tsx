'use client';
import { QueryLink } from '@/components/QueryLink';
import { URL_API } from '@/constants';

import {
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  TableContainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Box,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
} from '@chakra-ui/react';
import { useTransition } from 'react';

import { truncateFileName } from '@/utils/text';
import { FaEllipsisH } from 'react-icons/fa';
import Swal from 'sweetalert2';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import {
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestStatus,
  RequestDocumentsAnalysisStatus,
  RequestDocumentStatus,
} from '../../enums';
import { RequestDocumentTypeTranslationsUS } from '../../data';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { getSuggestedStatus } from '../../utils/documentAnalysisUtils';

const RequestDocumentStatusTranslations = {
  [RequestDocumentStatus.pending]: 'Pending',
  [RequestDocumentStatus.approved]: 'Approved',
  [RequestDocumentStatus.pending_review]: 'Pending review',
  [RequestDocumentStatus.rejected]: 'Rejected',
};

interface RequestDocument {
  mediaId: string | null;
  status: RequestDocumentStatus;
  type: AdmissionRequestDocumentTypeUS;
  media?: {
    url: string;
    fileName: string;
    mimeType: string;
  };
  analysisResult?: {
    status: 'valid' | 'invalid' | 'pending' | 'error';
    extractedData?: any;
    validationErrors: string[];
    validationWarnings: string[];
    confidence: number;
  };
}

interface DocumentsAnalysis {
  status: RequestDocumentsAnalysisStatus;
  documents: RequestDocument[];
}

function formatDocumentStatus(status: RequestDocumentStatus) {
  const colorScheme = {
    [RequestDocumentStatus.approved]: 'green',
    [RequestDocumentStatus.pending]: 'yellow',
    [RequestDocumentStatus.pending_review]: 'purple',
    [RequestDocumentStatus.rejected]: 'red',
  }[status];
  return <Text color={`${colorScheme}.500`}>{RequestDocumentStatusTranslations[status] || status}</Text>;
}

function formatDocumentType(type: AdmissionRequestDocumentTypeUS) {
  return RequestDocumentTypeTranslationsUS[type] || type;
}

const DocumentBadge = ({ fileName }: { fileName: string }) => {
  const truncated = truncateFileName(fileName, 25);
  return (
    <Box
      display="inline-block"
      px={2}
      py={1}
      borderWidth="1px"
      bg="purple.50"
      borderColor="purple.500"
      color="purple.500"
      borderRadius="md"
      fontSize="xs"
      fontWeight="normal"
      _hover={{ bg: 'purple.100' }}
    >
      {truncated}
    </Box>
  );
};

// function canApproveDocument(docStatus: RequestDocumentStatus) {
//   return docStatus == RequestDocumentStatus.pending_review;
// }

// function canRejectDocument(docStatus: RequestDocumentStatus) {
//   return docStatus == RequestDocumentStatus.pending_review;
// }

// function canReplaceDocument(docStatus: RequestDocumentStatus) {
//   return docStatus == RequestDocumentStatus.pending_review;
// }

function canUploadDocument(docStatus: RequestDocumentStatus) {
  return docStatus == RequestDocumentStatus.pending;
}

function isDocumentApproved(docStatus: RequestDocumentStatus) {
  return docStatus == RequestDocumentStatus.approved;
}

export default function DocumentsCardUS({
  requestId,
  documentsAnalysis,
}: {
  requestId: string;
  documentsAnalysis: DocumentsAnalysis;
  status: AdmissionRequestStatus;
}) {
  const router = useRouter();
  const toast = useToast();
  const [isTransitioning, startTransition] = useTransition();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const { isSuperAdminOrAdmin } = useCurrentUser();

  const isDocumentsAnalysisApproved = documentsAnalysis?.status === RequestDocumentsAnalysisStatus.approved;
  const isDocumentsAnalysisRejected = documentsAnalysis?.status === RequestDocumentsAnalysisStatus.rejected;

  function handleReject(type: AdmissionRequestDocumentTypeUS) {
    Swal.fire({
      animation: false,
      title: 'Are you sure you want to reject this document?',
      text: 'This action cannot be undone',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, reject',
      cancelButtonText: 'Cancel',
      preConfirm: async () => {
        try {
          await fetch(`${URL_API}/admission/requests/${requestId}/documents/${type}/reject`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
        } catch (error) {
          Swal.showValidationMessage(`Request failed: ${error}`);
        }
      },
    }).then((result) => {
      if (result.isConfirmed) {
        startTransition(() => {
          toast({
            title: 'Updated document',
            status: 'success',
          });
          router.refresh();
        });
      }
    });
  }

  async function handleApproveDocument(type: AdmissionRequestDocumentTypeUS) {
    try {
      await fetch(`${URL_API}/admission/requests/${requestId}/documents/${type}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      startTransition(() => {
        toast({
          title: 'Updated document',
          status: 'success',
        });
        router.refresh();
      });
    } catch (error) {
      Swal.showValidationMessage(`Request failed: ${error}`);
    }
    return false;
  }

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {'Document analysis'}
          </Heading>
          {isDocumentsAnalysisApproved && (
            <Text color="green.500" fontSize="sm">
              {'Pre-approved'}
            </Text>
          )}

          {isDocumentsAnalysisRejected && (
            <Text color="red.500" fontSize="sm">
              {'Refused'}
            </Text>
          )}

          {isTransitioning && (
            <div className="spinner-border spinner-border-sm" role="status">
              <span className="visually-hidden">{'Updating...'}</span>
            </div>
          )}
        </Flex>
      </CardHeader>
      {documentsAnalysis && (
        <CardBody>
          <TableContainer fontSize="sm">
            <Table variant="striped">
              <Thead>
                <Tr>
                  <Th>{'Name'}</Th>
                  <Th>{'Document'}</Th>
                  <Th>{'Status'}</Th>
                  <Th textAlign="center">{'Suggested Status'}</Th>
                  <Th textAlign="right">{'Acciones'}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {documentsAnalysis.documents.map((document: RequestDocument) => (
                  <Tr key={document.type}>
                    <Td p={2}>{formatDocumentType(document.type)}</Td>
                    <Td p={2}>
                      {/* trim the name and add elipsis */}
                      {document.media && (
                        <a href={document.media.url} target="_blank" rel="noopener noreferrer">
                          <DocumentBadge fileName={document.media.fileName} />
                        </a>
                      )}
                    </Td>
                    <Td p={2}>{formatDocumentStatus(document.status)}</Td>
                    <Td p={2} textAlign="center">
                      {getSuggestedStatus(document.analysisResult?.status) && (
                        <Text color={getSuggestedStatus(document.analysisResult?.status)?.color}>
                          {getSuggestedStatus(document.analysisResult?.status)?.text}
                        </Text>
                      )}
                    </Td>
                    <Td p={2} textAlign="right" pr={8}>
                      {
                        <Menu>
                          <MenuButton disabled={!isSuperAdminOrAdmin}>
                            <FaEllipsisH />
                          </MenuButton>
                          <MenuList>
                            {!canUploadDocument(document.status) && !isDocumentApproved(document.status) && (
                              <MenuItem
                                onClick={() => {
                                  handleApproveDocument(document.type);
                                }}
                                sx={{
                                  _hover: {
                                    bg: 'gray.50',
                                  },
                                }}
                              >
                                {'Approve'}
                              </MenuItem>
                            )}
                            {!canUploadDocument(document.status) && (
                              <QueryLink
                                query={{
                                  dialog: 'upload-document',
                                  documentType: document.type,
                                  actionType: 'replace',
                                }}
                              >
                                <MenuItem
                                  sx={{
                                    _hover: {
                                      bg: 'gray.50',
                                    },
                                  }}
                                >
                                  {'Replace document'}
                                </MenuItem>
                              </QueryLink>
                            )}
                            {!canUploadDocument(document.status) && (
                              <MenuItem
                                onClick={() => {
                                  handleReject(document.type);
                                }}
                                color="red.500"
                                sx={{
                                  _hover: {
                                    bg: 'gray.50',
                                  },
                                }}
                              >
                                {'Decline'}
                              </MenuItem>
                            )}
                            {canUploadDocument(document.status) && (
                              <QueryLink
                                query={{
                                  dialog: 'upload-document',
                                  documentType: document.type,
                                  actionType: 'upload',
                                }}
                              >
                                <MenuItem
                                  sx={{
                                    _hover: {
                                      bg: 'gray.50',
                                    },
                                  }}
                                >
                                  {'Upload document'}
                                </MenuItem>
                              </QueryLink>
                            )}
                          </MenuList>
                        </Menu>
                      }
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        </CardBody>
      )}
    </Card>
  );
}
