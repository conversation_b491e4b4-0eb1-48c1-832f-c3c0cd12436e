/* eslint-disable prettier/prettier */
import { Contact } from '@/actions/getVehicleData';
import { Text, View, StyleSheet } from '@react-pdf/renderer';

const styles = StyleSheet.create({
  body: {
    flexDirection: 'column',
    rowGap: 1,
    marginBottom: '20px',
  },

  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    marginTop: '70px',
    opacity: 0.2,
    height: '500px',
    width: '100%',
  },

  anexoSubTitle: {
    color: '#6210FF',
    textAlign: 'center',
    fontSize: 12,
    marginBottom: 10,
  },

  date: {
    alignItems: 'flex-end',
    textAlign: 'right',
    fontSize: 11,
  },

  read: {
    color: 'black',
    textAlign: 'center',
    fontSize: 8,
    marginTop: 15,
    fontFamily: 'Helvetica-Oblique',
  },

  text: {
    fontSize: 12,
    color: '#6210FF',
    marginBottom: 5,
    lineHeight: 1,
  },

  textContainer: {
    flexDirection: 'column',
  },

  address: {
    fontSize: 12,
    color: '#6210FF',
    lineHeight: 1,
  },

  lines: {
    color: '#6210FF',
    fontSize: 12,
    lineHeight: 2,
    marginTop: 5,
  },

  content: {
    gap: '10px',
  },
});

interface Props {
  contacts: Contact[];
}

export default function DatosContacto({ contacts }: Props) {
  return (
    <View style={styles.body} break>
      <Text style={styles.anexoSubTitle}>Datos de contacto</Text>

      {contacts.length === 0 && (
        <>
          {Array.from({ length: 3 }).map((_, index) => (
            <View key={index}>
              <Text style={styles.anexoSubTitle}>Referencia {index + 1}*</Text>
              <Text style={styles.text}>
                Nombre: __________________________________________________________
              </Text>
              <Text style={styles.text}>
                Parentesco: _______________________________________________________
              </Text>
              <Text style={styles.text}>Celular: ______________________ </Text>
              <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
                <Text style={styles.address}>Dirección:</Text>
                <Text style={styles.lines}>
                  _________________________________________________________________
                  _________________________________________________________________
                </Text>
              </View>
            </View>
          ))}
        </>
      )}

      {contacts.length > 2 ? (
        <>
          {contacts.map((contact, index) => (
            <View key={index}>
              <Text style={styles.anexoSubTitle}>Referencia {index + 1}*</Text>
              <Text style={styles.text}>Nombre: {contact.name}</Text>
              <Text style={styles.text}>Parentesco: {contact.kinship}</Text>
              <Text style={styles.text}>
                Celular: {contact.phone} {/* Teléfono: {contact.phone} */}
              </Text>
              <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
                <Text style={styles.address}>
                  Dirección:
                  <Text style={styles.address}>{contact.address}</Text>
                </Text>
                {/* <Text style={styles.lines}>{contact.address}</Text> */}
              </View>
            </View>
          ))}
        </>
      ) : (
        <>
          {contacts.map((contact, index) => (
            <View key={index}>
              <Text style={styles.anexoSubTitle}>Referencia {index + 1}*</Text>
              <Text style={styles.text}>Nombre: {contact.name}</Text>
              <Text style={styles.text}>Parentesco: {contact.kinship}</Text>
              <Text style={styles.text}>
                Celular: {contact.phone} {/* Teléfono: {contact.phone} */}
              </Text>
              <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
                <Text style={styles.address}>
                  Dirección: <Text style={styles.address}>{contact.address}</Text>
                </Text>
              </View>
            </View>
          ))}

            {contacts.length !== 0 && contacts.length < 3 && (
              <>
                <Text style={styles.anexoSubTitle}>Referencia 3*</Text>

                <Text style={styles.text}>
                  Nombre: __________________________________________________________
                </Text>
                <Text style={styles.text}>
                  Parentesco: _______________________________________________________
                </Text>

                <Text style={styles.text}>Celular: ______________________ </Text>

                <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
                  <Text style={styles.address}>Dirección:</Text>
                  <Text style={styles.lines}>
                    _________________________________________________________________
                    _________________________________________________________________
                  </Text>
                </View>
              </>
            )}
        </>
      )}

      {/* <Text style={styles.anexoSubTitle}>Referencia 1*</Text>

      <Text style={styles.text}>Nombre: _____________________________________________________</Text>
      <Text style={styles.text}>Parentesco: __________________________________________________</Text>

      <Text style={styles.text}>Celular: ______________________ Teléfono: ______________________</Text>
      <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
        <Text style={styles.address}>Dirección:</Text>
        <Text style={styles.lines}>
          _________________________________________________________________
          _________________________________________________________________
        </Text>
      </View>

      <Text style={styles.anexoSubTitle}>Referencia 2*</Text>

      <Text style={styles.text}>Nombre: _____________________________________________________</Text>
      <Text style={styles.text}>Parentesco: __________________________________________________</Text>

      <Text style={styles.text}>Celular: ______________________ Teléfono: ______________________</Text>

      <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
        <Text style={styles.address}>Dirección:</Text>
        <Text style={styles.lines}>
          _________________________________________________________________
          _________________________________________________________________
        </Text>
      </View>

      <Text style={styles.anexoSubTitle}>Referencia 3*</Text>

      <Text style={styles.text}>Nombre: _____________________________________________________</Text>
      <Text style={styles.text}>Parentesco: __________________________________________________</Text>

      <Text style={styles.text}>Celular: ______________________ Teléfono: ______________________</Text>

      <View style={{ flexDirection: 'column', gap: 0, marginBottom: 20 }}>
        <Text style={styles.address}>Dirección:</Text>
        <Text style={styles.lines}>
          _________________________________________________________________
          _________________________________________________________________
        </Text>
      </View> */}
    </View>
  );
}
