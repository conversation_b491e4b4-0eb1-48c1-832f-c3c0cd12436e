import { DRIVERS_APP_URL } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { AiOutlineLink } from 'react-icons/ai';
import { useCountry } from './detail';

export default function CopyLinkButton({ requestId }: { requestId: string }) {
  const toast = useToast();
  const url = `${DRIVERS_APP_URL}/?id=${requestId}`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(url);
    toast({
      description: 'Enlace se ha copiado al portapapeles',
      status: 'success',
    });
  };

  const { isCountryUSA } = useCountry();
  const btnText = isCountryUSA ? 'Copy Link' : 'Copiar enlace';

  return (
    <button className="bg-[#5800F7] text-white pr-1 h-[40px] rounded w-[180px]" onClick={copyToClipboard}>
      <AiOutlineLink className="inline-block mr-2" size={18} />
      {btnText}
    </button>
  );
}
