import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>lay,
  <PERSON>dal<PERSON><PERSON>nt,
  Modal<PERSON>eader,
  ModalBody,
  ModalCloseButton,
  <PERSON><PERSON><PERSON>ooter,
  VStack,
  HStack,
  Text,
  Box,
  Flex,
} from '@chakra-ui/react';
import { FiPrinter } from 'react-icons/fi';
import { VehicleWithQR } from '@/actions/getVehiclesWithQR';

interface VehiclePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedVehiclesData: VehicleWithQR[];
  generatingPDF: boolean;
  setShowPreview: (show: boolean) => void;
  handlePrint: () => void;
  selectedVehicles: Set<string>;
}

const VehiclePreviewModal: React.FC<VehiclePreviewModalProps> = ({
  isOpen,
  onClose,
  selectedVehiclesData,
  generatingPDF,
  setShowPreview,
  handlePrint,
  selectedVehicles,
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} size="4xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader bg="#5800F7" color="white" borderTopRadius="md">
          <Flex justify="space-between" align="center">
            <Text>Previsualización - {selectedVehicles.size} vehículos seleccionados</Text>
            <button
              onClick={() => setShowPreview(false)}
              className="text-white hover:text-gray-200 text-sm underline"
            >
              Volver a la selección
            </button>
          </Flex>
        </ModalHeader>
        <ModalCloseButton color="white" onClick={onClose} />
        <ModalBody p={6} maxH="500px" overflowY="auto">
          <VStack spacing={4} align="stretch">
            {selectedVehiclesData.map((vehicle, index) => (
              <Box key={vehicle._id} p={4} borderWidth={1} borderRadius="md" borderColor="#E2E8F0">
                <HStack spacing={4}>
                  <Box
                    bg="#5800F7"
                    color="white"
                    px={3}
                    py={1}
                    borderRadius="full"
                    fontSize="sm"
                    fontWeight="bold"
                  >
                    {index + 1}
                  </Box>
                  <VStack align="start" spacing={1} flex={1}>
                    <Text fontWeight="bold" fontSize="lg">
                      {vehicle.brand} {vehicle.model}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      VIN: {vehicle.vin}
                    </Text>
                    <Text fontSize="sm" color="gray.600">
                      Vehículo #: {vehicle.carNumber}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            ))}
          </VStack>
        </ModalBody>
        <ModalFooter bg="gray.50" borderBottomRadius="md">
          <button
            onClick={() => setShowPreview(false)}
            className="border-2 border-[#5800F7] text-[#5800F7] px-4 py-2 rounded mr-3 hover:bg-[#5800F7] hover:text-white transition-colors"
          >
            Volver
          </button>
          <button
            onClick={handlePrint}
            disabled={generatingPDF}
            className={`
              ${generatingPDF ? 'bg-gray-400' : 'bg-[#5800F7] hover:bg-purple-800'} 
              text-white px-4 py-2 rounded transition-colors flex items-center gap-2
              ${generatingPDF ? 'cursor-not-allowed' : 'cursor-pointer'}
            `}
          >
            <FiPrinter size={16} />
            {generatingPDF ? 'Generando PDF...' : `Imprimir ${selectedVehicles.size} QR`}
          </button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default VehiclePreviewModal;
