import React from 'react';
import { Box, HStack, Icon, Text, Avatar, Tooltip, Link } from '@chakra-ui/react';
import { Clock, Image as ImageIcon } from 'lucide-react';
import { getStatusLabel, getModalOptionLabel } from '../Modals/PhysicalStatusUpdateModal';
import { PhysicalVehicleStatus } from '@/constants';

interface QRScanHistoryItem {
  _id: string;
  vehicleId: string;
  userId: {
    _id: string;
    name: string;
    email: string;
  };
  scanTime: string;
  statusChangedFrom: string;
  statusChangedTo: string;
  deviceInfo?: string;
  actionType: string;
  notes?: string;
  location?: string;
  photo?: {
    _id: string;
    path: string;
    originalName: string;
    createdAt: string;
    updatedAt: string;
    vehicleId: string;
    url?: string;
  };
  vendorWorkshopName?: string;
  vendorRegion?: string;
  isAdminCorrection?: boolean;
  vendorUserName?: string;
}

interface StatusHistoryItemProps {
  entry: QRScanHistoryItem;
  countryValue: string;
  primaryColor: string;
  timelineItemBg: string;
  timelineBorder: string;
  repairStatusesWithVendorInfo: string[];
  translatedText: any;
  handleViewPhoto: (photoPathOrUrl: string) => void;
  viewPhotoText: string;
  formatDate: (dateString: string) => string;
  isAdminCorrection?: boolean;
}

const StatusHistoryItem: React.FC<StatusHistoryItemProps> = ({
  entry,
  countryValue,
  primaryColor,
  timelineItemBg,
  timelineBorder,
  repairStatusesWithVendorInfo,
  translatedText,
  handleViewPhoto,
  viewPhotoText,
  formatDate,
  isAdminCorrection,
}) => {
  return (
    <Box
      borderWidth="1px"
      borderRadius="md"
      p={3}
      bg={timelineItemBg}
      borderColor={timelineBorder}
      position="relative"
      _hover={{ boxShadow: 'sm' }}
    >
      <HStack mb={2} spacing={2} wrap="wrap">
        <HStack spacing={1}>
          <Icon as={Clock} size={14} color="gray.500" />
          <Text fontSize="xs" color="gray.500">
            {formatDate(entry.scanTime)}
          </Text>
        </HStack>
        {isAdminCorrection && (
          <Box
            bg="orange.100"
            color="orange.800"
            px={2}
            py={1}
            borderRadius="md"
            fontSize="xs"
            fontWeight="medium"
          >
            {translatedText.correction}
          </Box>
        )}
      </HStack>

      <HStack spacing={2} mb={2}>
        <Text fontSize="sm" fontWeight="bold" color={primaryColor}>
          {entry.statusChangedFrom === PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT &&
          (entry.statusChangedTo === PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER ||
            entry.statusChangedTo === PhysicalVehicleStatus.COLLECTED_FROM_STOCK)
            ? getModalOptionLabel(
                entry.statusChangedTo,
                countryValue,
                PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT
              )
            : getStatusLabel(entry.statusChangedTo, countryValue)}
        </Text>
      </HStack>

      {/* Display Vendor Info in History Item if applicable */}
      {repairStatusesWithVendorInfo.includes(entry.statusChangedTo as PhysicalVehicleStatus) &&
        entry.vendorWorkshopName &&
        entry.vendorRegion && (
          <Text fontSize="xs" color="gray.600" mt={1}>
            {`${translatedText.workshopName}: ${entry.vendorWorkshopName} (${entry.vendorRegion})`}
          </Text>
        )}

      <HStack spacing={2} mt={1} justifyContent="space-between">
        <Tooltip
          label={`${entry.userId?.name || entry.vendorUserName || translatedText.user}`}
          placement="top"
        >
          <HStack spacing={1}>
            <Avatar
              size="2xs"
              name={`${entry.userId?.name || entry.vendorUserName || 'U'}`}
              bg={primaryColor}
              color="white"
            />
            <Text fontSize="xs" color="gray.500">
              {entry.userId?.name || entry.vendorUserName || translatedText.user}
            </Text>
          </HStack>
        </Tooltip>

        {entry.photo?.path && (
          <Link
            fontSize="xs"
            color={primaryColor}
            onClick={() => handleViewPhoto(entry.photo!.url || entry.photo!.path)}
            display="flex"
            alignItems="center"
            cursor="pointer"
          >
            <Icon as={ImageIcon} size={12} mr={1} />
            {viewPhotoText}
          </Link>
        )}
      </HStack>
    </Box>
  );
};

export default StatusHistoryItem;
