'use client';

import { ColumnDef } from '@tanstack/react-table';

// eslint-disable-next-line import/no-extraneous-dependencies
import { MoreHorizontal, ArrowUpDown, Search, Copy } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { User } from '../types';
import { toast } from 'sonner';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';

export const columns: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">CLIENTE</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Searh by name</h4>
                <Input
                  className="mt-4"
                  value={(column?.getFilterValue() as string) ?? ''}
                  onChange={(event) => column?.setFilterValue(event.target.value)}
                />
              </div>
            </PopoverContent>
          </Popover>
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          <Link href={`clientDetails?id=${row.original.id}`}>
            <div className="font-medium">{row.getValue('name')}</div>
          </Link>
          <br />
          <div>
            <p className="text-xs">{row.original.id}</p>
            <Copy
              className="h-4 w-4 pr-1 hover:cursor-pointer"
              onClick={() => {
                navigator.clipboard.writeText(row.original.id);
                toast('ID copiado al portapapeles');
              }}
            />
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'contractNumber',
    header: ({ column }) => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">COMPAÑÍA</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Buscar Company</h4>
                <Input
                  className="mt-4"
                  value={(column?.getFilterValue() as string) ?? ''}
                  onChange={(event) => column?.setFilterValue(event.target.value)}
                />
              </div>
            </PopoverContent>
          </Popover>
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'EMAIL',
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('email')}</div>;
    },
  },
  {
    accessorKey: 'tax_system',
    header: 'PERFIL FISCAL',
    cell: ({ row }) => {
      return <Badge>{row.getValue('tax_system') ? row.getValue('tax_system') : 'Sin Info. fiscal'}</Badge>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'CREACIÓN',
    cell: ({ row }) => {
      const formattedDate = new Date(row.getValue('createdAt')).toLocaleDateString();
      return <div className="font-medium">{formattedDate}</div>;
    },
  },
  {
    accessorKey: 'country',
    header: 'Country',
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('country')}</div>;
    },
  },
  {
    accessorKey: 'actions',
    header: 'ACCIONES',
    cell: () => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const ability = usePermissions();
      const canEdit = canPerform(ability, Capabilities.Edit, Sections.Payments, Subsections.Clients);
      const canDelete = canPerform(ability, Capabilities.Delete, Sections.Payments, Subsections.Clients);
      const canRequestPayment = canPerform(
        ability,
        Capabilities.RequestPayment,
        Sections.Payments,
        Subsections.Clients
      );
      const canCreateInvoice = canPerform(
        ability,
        Capabilities.CreateInvoice,
        Sections.Payments,
        Subsections.Clients
      );
      const canCreateReceipt = canPerform(
        ability,
        Capabilities.CreateReceipt,
        Sections.Payments,
        Subsections.Clients
      );

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            {canEdit && <DropdownMenuItem>Edit</DropdownMenuItem>}
            {canDelete && <DropdownMenuItem>Delete</DropdownMenuItem>}
            {canRequestPayment && <DropdownMenuItem>Request Payment</DropdownMenuItem>}
            {canCreateInvoice && <DropdownMenuItem>Create Invoice</DropdownMenuItem>}
            {canCreateReceipt && <DropdownMenuItem>Create Receipt</DropdownMenuItem>}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];

export const columnsUS: ColumnDef<User>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">Customer</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Searh by name</h4>
                <Input
                  className="mt-4"
                  value={(column?.getFilterValue() as string) ?? ''}
                  onChange={(event) => column?.setFilterValue(event.target.value)}
                />
              </div>
            </PopoverContent>
          </Popover>
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      return (
        <>
          <Link href={`clientDetails?id=${row.original.id}`}>
            <div className="font-medium">{row.getValue('name')}</div>
          </Link>
          <br />
          <div>
            <p className="text-xs">{row.original.id}</p>
            <Copy
              className="h-4 w-4 pr-1 hover:cursor-pointer"
              onClick={() => {
                navigator.clipboard.writeText(row.original.id);
                toast('ID copiado al portapapeles');
              }}
            />
          </div>
        </>
      );
    },
  },
  {
    accessorKey: 'contractNumber',
    header: ({ column }) => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">Company</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Search by company</h4>
                <Input
                  className="mt-4"
                  value={(column?.getFilterValue() as string) ?? ''}
                  onChange={(event) => column?.setFilterValue(event.target.value)}
                />
              </div>
            </PopoverContent>
          </Popover>
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: 'EMAIL',
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('email')}</div>;
    },
  },
  {
    accessorKey: 'stripeCustomer',
    header: 'Back Account Linked',
    cell: ({ row }) => {
      const stripeCustomer: User['stripeCustomer'] = row.getValue('stripeCustomer');
      let isCustomerBankAccountLinked = false;
      if (stripeCustomer && stripeCustomer.isCustomerBankAccountLinked) {
        isCustomerBankAccountLinked = stripeCustomer.isCustomerBankAccountLinked;
      }
      return <div className="font-medium">{isCustomerBankAccountLinked ? '✅' : '❌'}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: 'Creation',
    cell: ({ row }) => {
      const formattedDate = new Date(row.getValue('createdAt')).toLocaleDateString();
      return <div className="font-medium">{formattedDate}</div>;
    },
  },
  {
    accessorKey: 'country',
    header: 'Country',
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('country')}</div>;
    },
  },

  {
    accessorKey: 'actions',
    header: 'Actions',
    cell: () => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const ability = usePermissions();
      const canEdit = canPerform(ability, Capabilities.Edit, Sections.Payments, Subsections.Clients);
      const canDelete = canPerform(ability, Capabilities.Delete, Sections.Payments, Subsections.Clients);
      const canRequestPayment = canPerform(
        ability,
        Capabilities.RequestPayment,
        Sections.Payments,
        Subsections.Clients
      );
      const canCreateInvoice = canPerform(
        ability,
        Capabilities.CreateInvoice,
        Sections.Payments,
        Subsections.Clients
      );
      const canCreateReceipt = canPerform(
        ability,
        Capabilities.CreateReceipt,
        Sections.Payments,
        Subsections.Clients
      );

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            {canEdit && <DropdownMenuItem>Edit</DropdownMenuItem>}
            {canDelete && <DropdownMenuItem>Delete</DropdownMenuItem>}
            {canRequestPayment && <DropdownMenuItem>Request Payment</DropdownMenuItem>}
            {canCreateInvoice && <DropdownMenuItem>Create Invoice</DropdownMenuItem>}
            {canCreateReceipt && <DropdownMenuItem>Create Receipt</DropdownMenuItem>}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
