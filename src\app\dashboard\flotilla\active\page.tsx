import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';

export const metadata = {
  title: 'Flotilla  | Active',
  description: 'Esto es la flotilla',
};

interface ActivosProps {
  searchParams: Record<string, string>;
}

export default async function Activos({ searchParams }: ActivosProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({
    searchParams,
    filters,
  });

  if (!user) return null;
  const result = await getStockVehicles({
    limit: 50,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'active',
      country: definitiveFilters?.country,
    },
  });
  if (!result) return null;

  return (
    <VehicleInfiniteList route="active" page="Active" data={result.stock} totalCount={result.totalCount} />
  );
}
