import { columns } from './columns';
import { DataTable } from '@/components/DataTable';
import { Product } from '../types';
import axios from 'axios';
import { PAYMENTS_API_URL, PAYMENT_API_SECRET } from '@/constants';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { ClientSectionHeader } from './_components/ProductsHeader';
import { CountryProvider } from '../../providers/CountryProvider';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';

async function getProducts(url: string): Promise<Product[]> {
  let res = null;
  try {
    res = await axios(url, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

interface ProductosProps {
  searchParams: Record<string, string>;
}

export default async function Productos({ searchParams }: ProductosProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');
  const url = new URL(`${PAYMENTS_API_URL}/products`);
  for (const [key, value] of Object.entries(searchParams)) {
    url.searchParams.append(key, value);
  }

  const data = await getProducts(url.toString());

  return (
    <CountryProvider>
      <section className="py-8">
        <div className="container relative">
          <ClientSectionHeader />
          <Suspense fallback={<Skeleton className="w-full h-[300px]"></Skeleton>}>
            <DataTable columns={columns} data={data} />
          </Suspense>
        </div>
      </section>
    </CountryProvider>
  );
}
