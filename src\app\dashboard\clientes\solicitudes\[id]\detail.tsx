'use client';
import { Box, Container, Flex, Heading, Spacer, Stack } from '@chakra-ui/react';
import PersonalDataCard from './PersonalDataCard';
import ScorecardCard from './ScorecardCard';
import PlatformsCard from './PlatformsCard';
import DocumentsCard from './DocumentsCard';
import HomeVisitDataCard from './HomeVisitCard';
import EventsCard from './EventsCard';
import StepperCard from './StepperCard';
import MainActions from './MainActions';
import { useContext, createContext } from 'react';
import DocumentsCardUS from './_components/DocumentsCardUS';
import StepperCardUS from './_components/StepperCardUS';
import AdditionalDocumentsCard from './AdditionalDocumentsCard';
import { ModelScores } from '../types';
import SolidarityObligatorDetailsCard from './SolidarityObligatorDetailsCard';
import { RequestDocument } from '@/components/DriverDetails/DriverDocumentsCard';
import { CreditCheckUS } from './_components/CreditCheckUS';
import { MotorVehicleRecordCheck } from './_components/VehicleRecordCheckUS';
import { approvalOptionsMap } from '@/constants';
import { cn } from '@/lib/utils';
import QuickNotesCard from './QuickNotesCard';

interface ICountriesContext {
  isCountryUSA: boolean;
}

export const CountryContext = createContext(null as unknown as ICountriesContext);
export const useCountry = () => {
  return useContext(CountryContext);
};

export default function Detail({
  request,
  documentsAnalysis,
  additionalDocumentsAnalysis,
  screenshots,
  isCountryUSA,
}: {
  request: any;
  documentsAnalysis: any;
  additionalDocumentsAnalysis: any;
  screenshots: any;
  isCountryUSA: boolean;
}) {
  const applications = isCountryUSA ? 'Application' : 'Solicitud';

  const typeOfPreapproval = approvalOptionsMap[request.typeOfPreapproval as string];

  return (
    <CountryContext.Provider
      value={{
        isCountryUSA: isCountryUSA,
      }}
    >
      <Container maxW="7xl">
        <Box w="full">
          <Flex className="flex items-center gap-3">
            <Heading fontSize="32px" fontWeight="bold" colorScheme="gray">
              {applications}{' '}
            </Heading>
            {typeOfPreapproval && (
              <span
                className={cn(
                  'text-sm py-1 px-3 rounded border-2  mt-[4px] font-bold',
                  approvalOptionsMap[request.typeOfPreapproval as string]?.bgColor,
                  approvalOptionsMap[request.typeOfPreapproval as string]?.textColor,
                  approvalOptionsMap[request.typeOfPreapproval as string]?.borderColor
                )}
              >
                {typeOfPreapproval.shortLabel}
              </span>
            )}

            <Spacer />
            <MainActions
              requestId={request.id}
              status={request.status}
              documentsAnalysis={request.documentsAnalysis}
              modelScores={request.modelScores as ModelScores}
            />
          </Flex>
        </Box>
        <Box flex={1}>
          {isCountryUSA ? (
            <StepperCardUS status={request.status} documentsAnalysis={request.documentsAnalysis} />
          ) : (
            <StepperCard status={request.status} documentsAnalysis={request.documentsAnalysis} />
          )}
        </Box>
        <Flex direction="row" py={4} gap={4}>
          <Box flex={1}>
            <Stack spacing={4}>
              <PersonalDataCard
                data={request.personalData}
                hubspot={request?.hubspot}
                hilos={request.hilos}
                source={request.source}
                clientIpAddress={request.clientIpAddress}
              />
              <PlatformsCard
                palenca={request.palenca}
                screenshots={screenshots}
                earningsAnalysis={request.earningsAnalysis}
                requestId={request.id}
              />

              {isCountryUSA ? (
                <>
                  <MotorVehicleRecordCheck admissionRequest={request} />
                  <CreditCheckUS admissionRequest={request} />
                  <DocumentsCardUS
                    requestId={request.id}
                    documentsAnalysis={documentsAnalysis}
                    status={request.status}
                  />
                </>
              ) : (
                <DocumentsCard
                  requestId={request.id}
                  documentsAnalysis={documentsAnalysis}
                  status={request.status}
                />
              )}
              {!isCountryUSA && (
                <>
                  <HomeVisitDataCard
                    homeVisit={request.homeVisit}
                    status={request.status}
                    requestId={request.id}
                    request={request}
                  />
                  <AdditionalDocumentsCard
                    requestId={request.id}
                    additionalDocumentsAnalysis={additionalDocumentsAnalysis}
                    status={request.status}
                  />
                  <SolidarityObligatorDetailsCard
                    solidarityObligatorDetails={request?.avalData as SolidarityObligatorDetails}
                    avalDocs={[
                      additionalDocumentsAnalysis?.documents?.filter(
                        (doc: RequestDocument) => doc?.type?.startsWith('solidarity_obligor') && doc?.mediaId
                      ),
                    ]}
                  />
                </>
              )}
              <EventsCard requestId={request.id} />
            </Stack>
          </Box>
          <Box flex={0.5}>
            <Box mb={4}>
              <ScorecardCard request={request} requestId={request.id} />
            </Box>
            <Box mb={4}>
              <QuickNotesCard admissionRequestId={request.id}></QuickNotesCard>
            </Box>
          </Box>
        </Flex>
      </Container>
    </CountryContext.Provider>
  );
}
