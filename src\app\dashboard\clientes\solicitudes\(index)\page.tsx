import queryAdmissionRequests from '@/actions/searchAdmissionRequests';
import { Suspense } from 'react';
import Requests from '../../_components/requests';
import { getPageParam, getSearchParam } from '@/utils/params';
import { Countries, CountriesShortNames } from '@/constants';
import { CargandoMX, LoadingUS } from '../../_components/translations';
import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';

interface PageProps {
  searchParams: {
    page?: string;
    search?: string;
    country?: string;
  };
}

export function generateMetadata(props: PageProps) {
  const { searchParams } = props;
  const page = getPageParam(searchParams.page);
  const search = getSearchParam(searchParams.search);
  if (search && search.length > 0) {
    return {
      title: `Solicitudes resultados de búsqueda para "${search}" (Página ${page})`,
    };
  }

  return { title: `Solicitudes (Página ${page})` };
}

const Page = async (props: PageProps) => {
  const { searchParams } = props;

  const user = await getCurrentUser();
  if (!user) return redirect('/');

  let country = searchParams.country;
  if (!country) {
    country = Countries.Mexico;
  }
  const countryShortName = CountriesShortNames[country as keyof typeof CountriesShortNames];
  const page = getPageParam(searchParams.page);
  const search = getSearchParam(searchParams.search);

  const data = await queryAdmissionRequests({ country: countryShortName, search, page });
  const isCountryUSA = country === Countries['United States'];
  return (
    <>
      <Suspense fallback={<div>{isCountryUSA ? `${LoadingUS}` : `${CargandoMX}`}.</div>}>
        <Requests requests={data} />
      </Suspense>
    </>
  );
};

export default Page;
