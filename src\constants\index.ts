export const isLocal = process.env.NEXT_PUBLIC_IS_LOCAL!;

// API URLS

export const URL_API_DEV = 'https://dev-api.onecarnow.com';
export const URL_API_PROD = 'https://api.onecarnow.com';
export const URL_API = process.env.NEXT_PUBLIC_API_URL as string;
export const PAYMENTS_API_URL = process.env.NEXT_PUBLIC_PAYMENTS_API_URL as string;
export const PAYMENT_API_SECRET = process.env.NEXT_PUBLIC_PAYMENT_API_SECRET as string;

// FRONT URLS

export const PROD_URL = 'https://administrador.onecarnow.com';
export const DEV_URL = 'https://develop.administrador.onecarnow.com';

// drivers app
export const DRIVERS_APP_URL = process.env.NEXT_PUBLIC_DRIVERS_APP_URL;

//contract regions

export const teamLeads = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
export const emailUsersAllowed = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
export const emailUsersAllowedRegion = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];
export const adminsPayFlow = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const isProd = process.env.NODE_ENV === 'production';
export const IS_PROD = process.env.NEXT_PUBLIC_IS_PROD === 'true';

export const PAGOS_PAYMENT_URL = 'https://pagos.onecarnow.com/pago/';

export enum ContractRegionCode {
  CDMX = 'cdmx',
  GDL = 'gdl',
  MTY = 'mty',
  QRO = 'qro',
  TIJ = 'tij',
  MOKA = 'moka',
  PBE = 'pbe',
  PTV = 'ptv',
  TEP = 'tep',
  COL = 'col',
  SAL = 'sal',
  TORR = 'torr',
  DUR = 'dur',
  MXLI = 'mxli',
  HER = 'her',
  CHI = 'chi',
  LEO = 'leo',
  AGS = 'ags',
  SLP = 'slp',
  MER = 'mer',
}

export const CONTRACT_REGIONS: {
  value: string;
  label: string;
  code: string;
  number: number;
}[] = [
  {
    value: ContractRegionCode.CDMX,
    label: 'CDMX/EDOMEX',
    code: ContractRegionCode.CDMX,
    number: 1,
  },
  {
    value: ContractRegionCode.GDL,
    label: 'Guadalajara',
    code: ContractRegionCode.GDL,
    number: 2,
  },
  {
    value: ContractRegionCode.MTY,
    label: 'Monterrey',
    code: ContractRegionCode.MTY,
    number: 3,
  },
  {
    value: ContractRegionCode.QRO,
    label: 'Querétaro',
    code: ContractRegionCode.QRO,
    number: 4,
  },
  {
    value: ContractRegionCode.TIJ,
    label: 'Tijuana',
    code: ContractRegionCode.TIJ,
    number: 5,
  },
  {
    value: ContractRegionCode.MOKA,
    label: 'MOKA',
    code: ContractRegionCode.MOKA,
    number: 6,
  },
  {
    value: ContractRegionCode.PBE,
    label: 'Puebla',
    code: ContractRegionCode.PBE,
    number: 7,
  },
  // {
  //   value: 'tol',
  //   label: 'Toluca',
  //   code: 'tol',
  //   number: 8,
  // },
  {
    value: ContractRegionCode.PTV,
    label: 'Puerto Vallarta',
    code: ContractRegionCode.PTV,
    number: 9,
  },
  {
    value: ContractRegionCode.TEP,
    label: 'Tepic',
    code: ContractRegionCode.TEP,
    number: 10,
  },
  {
    value: ContractRegionCode.COL,
    label: 'Colima',
    code: ContractRegionCode.COL,
    number: 11,
  },
  {
    value: ContractRegionCode.SAL,
    label: 'Saltillo',
    code: ContractRegionCode.SAL,
    number: 12,
  },
  {
    value: ContractRegionCode.TORR,
    label: 'Torreon',
    code: ContractRegionCode.TORR,
    number: 13,
  },
  {
    value: ContractRegionCode.DUR,
    label: 'Durango',
    code: ContractRegionCode.DUR,
    number: 14,
  },
  {
    value: ContractRegionCode.MXLI,
    label: 'Mexicali',
    code: ContractRegionCode.MXLI,
    number: 15,
  },
  {
    value: ContractRegionCode.HER,
    label: 'Hermosillo',
    code: ContractRegionCode.HER,
    number: 16,
  },
  {
    value: ContractRegionCode.CHI,
    label: 'Chihuahua',
    code: ContractRegionCode.CHI,
    number: 17,
  },
  {
    value: ContractRegionCode.LEO,
    label: 'León',
    code: ContractRegionCode.LEO,
    number: 18,
  },
  {
    value: ContractRegionCode.AGS,
    label: 'Aguas Calientes',
    code: ContractRegionCode.AGS,
    number: 19,
  },
  {
    value: ContractRegionCode.SLP,
    label: 'San Luis Potosí',
    code: ContractRegionCode.SLP,
    number: 20,
  },
  {
    value: ContractRegionCode.MER,
    label: 'Mérida',
    code: ContractRegionCode.MER,
    number: 21,
  },
];

export const CONTRACT_REGIONS_IATA: {
  value: string;
  label: string;
  code: string;
}[] = [
  {
    value: ContractRegionCode.CDMX,
    label: 'CDMX',
    code: ContractRegionCode.CDMX,
  },
  {
    value: ContractRegionCode.GDL,
    label: 'Guadalajara',
    code: ContractRegionCode.GDL,
  },
  {
    value: ContractRegionCode.MTY,
    label: 'Monterrey',
    code: ContractRegionCode.MTY,
  },
  // {
  //   value: 'tol',
  //   label: 'Toluca',
  //   code: 'tol',
  // },
  {
    value: ContractRegionCode.QRO,
    label: 'Querétaro',
    code: ContractRegionCode.QRO,
  },
  {
    value: ContractRegionCode.TIJ,
    label: 'Tijuana',
    code: ContractRegionCode.TIJ,
  },
  {
    value: ContractRegionCode.MOKA,
    label: 'MOKA',
    code: ContractRegionCode.MOKA,
  },
  {
    value: ContractRegionCode.PBE,
    label: 'Puebla',
    code: ContractRegionCode.PBE,
  },
  {
    value: ContractRegionCode.PTV,
    label: 'Puerto Vallarta',
    code: ContractRegionCode.PTV,
  },
  {
    value: ContractRegionCode.TEP,
    label: 'Tepic',
    code: ContractRegionCode.TEP,
  },
  {
    value: ContractRegionCode.COL,
    label: 'Colima',
    code: ContractRegionCode.COL,
  },
  {
    value: ContractRegionCode.SAL,
    label: 'Saltillo',
    code: ContractRegionCode.SAL,
  },
  {
    value: ContractRegionCode.TORR,
    label: 'Torreon',
    code: ContractRegionCode.TORR,
  },
  {
    value: ContractRegionCode.DUR,
    label: 'Durango',
    code: ContractRegionCode.DUR,
  },
  {
    value: ContractRegionCode.MXLI,
    label: 'Mexicali',
    code: ContractRegionCode.MXLI,
  },
  {
    value: ContractRegionCode.HER,
    label: 'Hermosillo',
    code: ContractRegionCode.HER,
  },
  {
    value: ContractRegionCode.CHI,
    label: 'Chihuahua',
    code: ContractRegionCode.CHI,
  },
  {
    value: ContractRegionCode.LEO,
    label: 'León',
    code: ContractRegionCode.LEO,
  },
  {
    value: ContractRegionCode.AGS,
    label: 'Aguas Calientes',
    code: ContractRegionCode.AGS,
  },
  {
    value: ContractRegionCode.SLP,
    label: 'San Luis Potosí',
    code: ContractRegionCode.SLP,
  },
  {
    value: ContractRegionCode.MER,
    label: 'Mérida',
    code: ContractRegionCode.MER,
  },
];
/**
 * This timezones are for the regions in Mexico
 * Is used to send the timezone to contract generation API call and set the correct timezone for the contract,
 * to then get the correct date and time for the contract
 */
export const TIMEZONES_FOR_REGIONS_MX: { [key: string]: string } = {
  cdmx: 'America/Mexico_City',
  gdl: 'America/Mexico_City',
  mty: 'America/Monterrey',
  qro: 'America/Mexico_City',
  tij: 'America/Tijuana',
  moka: 'America/Mexico_City',
  pbe: 'America/Mexico_City',
  tol: 'America/Mexico_City',
  ptv: 'America/Mexico_City',
  tep: 'America/Mazatlan',
  col: 'America/Mexico_City',
  sal: 'America/Monterrey',
  torr: 'America/Monterrey',
  dur: 'America/Monterrey',
  mxli: 'America/Tijuana',
  her: 'America/Hermosillo',
  chi: 'America/Chihuahua',
  leo: 'America/Mexico_City',
  ags: 'America/Mexico_City',
  slp: 'America/Mexico_City',
  mer: 'America/Merida',
};

export const editTextBtn = 'Guardar';

export const regions: { [key: string]: string } = {
  1: 'CDMX',
  2: 'Guadalajara',
  3: 'Monterrey',
  4: 'Querétaro',
  5: 'Tijuana',
  6: 'MOKA',
  7: 'Puebla',
  // 8: 'Toluca',
  9: 'Puerto Vallarta',
  10: 'Tepic',
  11: 'Colima',
  12: 'Saltillo',
  13: 'Torreon',
  14: 'Durango',
  15: 'Mexicali',
  16: 'Hermosillo',
  17: 'Chihuahua',
  18: 'León',
  19: 'Aguas Calientes',
  20: 'San Luis Potosí',
  21: 'Mérida',
};

export const cities: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'Ciudad De México', value: 'cdmx' },
  edomx: { label: 'Estado De México', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const cities2: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'CDMX', value: 'cdmx' },
  edomx: { label: 'EDOMX', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const citiesSelect = Object.keys(cities).map((key) => cities[key]);

// export const citiesSelect = [
//   { label: 'Ciudad De México', value: 'cdmx' },
//   { label: 'Estado De México', value: 'edomx' },
//   { label: 'Guadalajara', value: 'gdl' },
//   { label: 'Monterrey', value: 'mty' },
//   { label: 'Querétaro', value: 'qro' },
//   { label: 'Tijuana', value: 'tij' },
//   { label: 'Moka', value: 'moka' },
//   { label: 'Puebla', value: 'pbe' },
//   { label: 'Toluca', value: 'tol' },
//   { label: 'Puerto Vallarta', value: 'ptv' },
//   { label: 'Tepic', value: 'tep' },
//   { label: 'Colima', value: 'col' },
//   { label: 'Saltillo', value: 'sal' },
//   { label: 'Torreon', value: 'torr' },
//   { label: 'Durango', value: 'dur' },
//   { label: 'Mexicali', value: 'mxli' },
//   { label: 'Hermosillo', value: 'her' },
//   { label: 'Chihuahua', value: 'chi' },
//   { label: 'León', value: 'leo' },
//   { label: 'Aguas Calientes', value: 'ags' },
//   { label: 'San Luis Potosí', value: 'slp' },
//   { label: 'Mérida', value: 'mer' },
// ];

export type StateOption = {
  label: string;
  value: string;
};

export const statesSelect: StateOption[] = [
  { label: 'Ciudad de México', value: 'Ciudad de México' },
  { label: 'Estado de México', value: 'Estado de México' },
  { label: 'Jalisco', value: 'Jalisco' },
  { label: 'Nuevo León', value: 'Nuevo León' },
  { label: 'Querétaro', value: 'Querétaro' },
  { label: 'Baja California', value: 'Baja California' },
  { label: 'Moka', value: 'Moka' },
  { label: 'Puebla', value: 'Puebla' },
  { label: 'Toluca', value: 'Toluca' },
  { label: 'Puerto Vallarta', value: 'Puerto Vallarta' },
  { label: 'Tepic', value: 'Tepic' },
  { label: 'Colima', value: 'Colima' },
  { label: 'Saltillo', value: 'Saltillo' },
  { label: 'Torreon', value: 'Torreon' },
  { label: 'Durango', value: 'Durango' },
  { label: 'Mexicali', value: 'Mexicali' },
  { label: 'Hermosillo', value: 'Hermosillo' },
  { label: 'Chihuahua', value: 'Chihuahua' },
  { label: 'León', value: 'León' },
  { label: 'Aguas Calientes', value: 'Aguas Calientes' },
  { label: 'San Luis Potosí', value: 'San Luis Potosí' },
  { label: 'Mérida', value: 'Mérida' },
];

export const citiesWithMoka: { [key: string]: { label: string; value: string } } = {
  cdmx: { label: 'Ciudad De México', value: 'cdmx' },
  edomx: { label: 'Estado De México', value: 'edomx' },
  gdl: { label: 'Guadalajara', value: 'gdl' },
  mty: { label: 'Monterrey', value: 'mty' },
  qro: { label: 'Querétaro', value: 'qro' },
  tij: { label: 'Tijuana', value: 'tij' },
  moka: { label: 'Moka', value: 'moka' },
  pbe: { label: 'Puebla', value: 'pbe' },
  tol: { label: 'Toluca', value: 'tol' },
  ptv: { label: 'Puerto Vallarta', value: 'ptv' },
  tep: { label: 'Tepic', value: 'tep' },
  col: { label: 'Colima', value: 'col' },
  sal: { label: 'Saltillo', value: 'sal' },
  torr: { label: 'Torreon', value: 'torr' },
  dur: { label: 'Durango', value: 'dur' },
  mxli: { label: 'Mexicali', value: 'mxli' },
  her: { label: 'Hermosillo', value: 'her' },
  chi: { label: 'Chihuahua', value: 'chi' },
  leo: { label: 'León', value: 'leo' },
  ags: { label: 'Aguas Calientes', value: 'ags' },
  slp: { label: 'San Luis Potosí', value: 'slp' },
  mer: { label: 'Mérida', value: 'mer' },
};

export const federalEntities: { [key: string]: { label: string; value: string } } = {
  'Ciudad de México': { label: 'Ciudad de México', value: 'Ciudad de México' },
  'Estado de México': { label: 'Estado de México', value: 'Estado de México' },
  Jalisco: { label: 'Jalisco', value: 'Jalisco' },
  'Nuevo León': { label: 'Nuevo León', value: 'Nuevo León' },
  Puebla: { label: 'Puebla', value: 'Puebla' },
  Querétaro: { label: 'Querétaro', value: 'Querétaro' },
  'Baja California': { label: 'Baja California', value: 'Baja California' },
  Moka: { label: 'Moka', value: 'Moka' },
  Toluca: { label: 'Toluca', value: 'Toluca' },
  'Puerto Vallarta': { label: 'Puerto Vallarta', value: 'Puerto Vallarta' },
  Tepic: { label: 'Tepic', value: 'Tepic' },
  Colima: { label: 'Colima', value: 'Colima' },
  Saltillo: { label: 'Saltillo', value: 'Saltillo' },
  Torreon: { label: 'Torreon', value: 'Torreon' },
  Durango: { label: 'Durango', value: 'Durango' },
  Mexicali: { label: 'Mexicali', value: 'Mexicali' },
  Hermosillo: { label: 'Hermosillo', value: 'Hermosillo' },
  Chihuahua: { label: 'Chihuahua', value: 'Chihuahua' },
  León: { label: 'León', value: 'León' },
  'Aguas Calientes': { label: 'Aguas Calientes', value: 'Aguas Calientes' },
  'San Luis Potosí': { label: 'San Luis Potosí', value: 'San Luis Potosí' },
  Mérida: { label: 'Mérida', value: 'Mérida' },
};

export const ferderalEntitiesSelect = Object.keys(federalEntities).map((key) => federalEntities[key]);

export const citiesDependingState: { [key: string]: { label: string; value: string }[] } = {
  'Ciudad de México': [{ label: 'Ciudad de México', value: 'cdmx' }],
  'Estado de México': [{ label: 'Estado de México', value: 'edomx' }],
  Jalisco: [{ label: 'Guadalajara', value: 'gdl' }],
  'Nuevo León': [{ label: 'Monterrey', value: 'mty' }],
  Querétaro: [{ label: 'Querétaro', value: 'qro' }],
  'Baja California': [{ label: 'Tijuana', value: 'tij' }],
  Moka: [{ label: 'Moka', value: 'moka' }],
  Puebla: [{ label: 'Puebla', value: 'pbe' }],
  Toluca: [{ label: 'Toluca', value: 'tol' }],
  'Puerto Vallarta': [{ label: 'Puerto Vallarta', value: 'ptv' }],
  Tepic: [{ label: 'Tepic', value: 'tep' }],
  Colima: [{ label: 'Colima', value: 'col' }],
  Saltillo: [{ label: 'Saltillo', value: 'sal' }],
  Torreon: [{ label: 'Torreon', value: 'torr' }],
  Durango: [{ label: 'Durango', value: 'dur' }],
  Mexicali: [{ label: 'Mexicali', value: 'mxli' }],
  Hermosillo: [{ label: 'Hermosillo', value: 'her' }],
  Chihuahua: [{ label: 'Chihuahua', value: 'chi' }],
  León: [{ label: 'León', value: 'leo' }],
  'Aguas Calientes': [{ label: 'Aguas Calientes', value: 'ags' }],
  'San Luis Potosí': [{ label: 'San Luis Potosí', value: 'slp' }],
  Mérida: [{ label: 'Mérida', value: 'mer' }],
};

export const stateDependingCity: { [key: string]: string } = {
  mty: 'Nuevo León',
  gdl: 'Jalisco',
  cdmx: 'Ciudad de México',
  edomx: 'México',
  qro: 'Querétaro',
  tij: 'Baja California',
  moka: 'Moka',
  pbe: 'Puebla',
  tol: 'Toluca',
  ptv: 'Puerto Vallarta',
  tep: 'Nayarit',
  col: 'Colima',
  sal: 'Coahuila',
  torr: 'Coahuila',
  dur: 'Durango',
  mxli: 'Baja California',
  her: 'Sonora',
  chi: 'Chihuahua',
  leo: 'Guanajuato',
  ags: 'Aguascalientes',
  slp: 'San Luis Potosí',
  mer: 'Yucatán',
};

export const stepsSelect = [
  { value: '1', label: 'Stock' },
  { value: '2', label: 'Vehiculo listo' },
  { value: '3', label: 'Conductor asignado' },
  { value: '4', label: 'Contrato generado' },
  { value: '5', label: 'Entregado' },
];

export const statusSelected = [
  { value: 'active', label: 'Activos' },
  { value: 'activo', label: 'Activos' },
  { value: 'in-service', label: 'Taller' },
  { value: 'legal-process', label: 'Proceso legal' },
  { value: 'awaiting-insurance', label: 'Espera de seguro' },
  { value: 'overhauling', label: 'Revisión' },
  { value: 'stock', label: 'Stock' },
];

export const svgColors: { [key: string]: string } = {
  BLANCO: '#f2f2f2',
  NEGRO: 'black',
  GRIS: '#808080',
  // ROJO: '#c0392b',
  ROJO: '#dc143c' /* #dc143c */,
  BEIGE: '#E1C699',
  // BEIGE: '#E1C699',
  AZUL: '#0c0cdaf9',
  ESCARLATA: '#FD2D1C',
  PLATA: '#BEBEBE',
  ROSA: '#e98899',
  VERDE: '#117457',
};

export const colors = Object.keys(svgColors).map((key) => ({ label: key, value: key }));

export const platforms = ['Uber', 'Didi'];

export const platformOptions = platforms.map((p) => ({ label: p, value: p.toLowerCase() }));

export const allStatus = {
  stock: 'stock',
  inactive: 'inactive',
  active: 'active',
  activo: 'active',
  readmissions: 'readmissions',
  discharged: 'discharged',
  'in-service': 'in-service',
  'legal-process': 'legal-process',
  'awaiting-insurance': 'awaiting-insurance',
  overhauling: 'overhauling',
};

export const allCategory = {
  withdrawn: 'withdrawn',
  sold: 'sold',
  insurance: 'insurance',
  collection: 'collection',
  legal: 'legal',
  workshop: 'workshop',
  revision: 'revision',
  adendum: 'adendum',
  'in-preparation': 'in-preparation',
  stock: 'stock',
  assigned: 'assigned',
  delivered: 'delivered',
  utilitary: 'utilitary',
  default: '',
};

export const allSubCategory = {
  'damage-payment': 'damage-payment',
  valuation: 'valuation',
  repair: 'repair',
  'payment-commitment': 'payment-commitment',
  'payment-extension': 'payment-extension',
  'non-payment': 'non-payment',
  'incomplete-payment': 'incomplete-payment',
  'in-recovery': 'in-recovery',
  demand: 'demand',
  'public-ministry': 'public-ministry',
  complaint: 'complaint',
  impounded: 'impounded',
  'aesthetic-repair': 'aesthetic-repair',
  'duplicate-key-missing': 'duplicate-key-missing',
  'mechanical-repair': 'mechanical-repair',
  'electrical-repair': 'electrical-repair',
  'engine-repair': 'engine-repair',
  'waiting-for-parts': 'waiting-for-parts',
  'corrective-maintenance': 'corrective-maintenance',
  management: 'management',
  gps: 'gps',
  'total-loss': 'total-loss',
  'operational-loss': 'operational-loss',
  default: '',
};

export type StepName =
  | 'Stock'
  | 'Vehiculo listo'
  | 'Conductor asignado'
  | 'Contrato generado'
  | 'Entregado'
  | 'Solicitud de reingreso';

export type StepNumber = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

export type StepProperties =
  | 'stock'
  | 'vehicleReady'
  | 'driverAssigned'
  | 'contractCreated'
  | 'delivered'
  | 'readmissions';

export const vehicleSteps: Record<StepProperties, { name: StepName; number: StepNumber }> = {
  stock: {
    name: 'Stock',
    number: 1,
  },
  vehicleReady: {
    name: 'Vehiculo listo',
    number: 2,
  },
  driverAssigned: {
    name: 'Conductor asignado',
    number: 3,
  },
  contractCreated: {
    name: 'Contrato generado',
    number: 4,
  },
  delivered: {
    name: 'Entregado',
    number: 5,
  },
  readmissions: {
    name: 'Solicitud de reingreso',
    number: 6,
  },
};

export const pagesSearchRedirect: { [key: string]: string } = {
  stock: 'stock',
  active: 'activos',
  activo: 'activos',
  discharged: 'bajas',
  readmissions: 'reingresos',
  bloqueo: 'activos',
  desbloqueo: 'activos',
  'in-service': 'activos',
  'legal-process': 'activos',
  'awaiting-insurance': 'activos',
  overhauling: 'stock',
  invoiced: 'invoiced',
  inactive: 'inactive',
};

export const areaOptions = [
  { value: 'sales', label: 'Ventas' },
  { value: 'audit', label: 'Audit' },
  { value: 'collection', label: 'Cobranza' },
  { value: 'fleet', label: 'Fleet' },
  { value: 'backoffice', label: 'Backoffice' },
  { value: 'legal', label: 'Legal' },
  { value: 'cx', label: 'CX' },
  { value: 'kyc', label: 'KYC' },
  { value: 'homeVisit', label: 'Home Visit' },
  { value: 'finance', label: 'Finance' },
  { value: 'superadmin', label: 'Super Admin' },
];

export enum Areas {
  Sales = 'sales',
  Audit = 'audit',
  Collection = 'collection',
  Fleet = 'fleet',
  Backoffice = 'backoffice',
  Legal = 'legal',
  CX = 'cx',
  KYC = 'kyc',
  HomeVisit = 'homeVisit',
  Finance = 'finance',
  Superadmin = 'superadmin',
}

export const roleOptions = [
  { value: 'administrador', label: 'Administrator' },
  { value: 'superadmin', label: 'Super Admin' },
  { value: 'agent', label: 'Agente' },
  { value: 'lead', label: 'Lead' },
  { value: 'auditor', label: 'Auditor' },
  { value: 'visitor', label: 'Visitador' },
  { value: 'recoveryAgent', label: 'Agente de recuperación' },
  { value: 'recoveryManager', label: 'Gerente de Recuperación' },
];

export const salesRoleOptions = [
  { value: 'administrador', label: 'Administrator' },
  { value: 'agent', label: 'Agente' },
  { value: 'lead', label: 'Lead' },
];

export const collectionRoleOptions = [
  { value: 'administrador', label: 'Administrator' },
  { value: 'agent', label: 'Agente' },
  { value: 'lead', label: 'Lead' },
  { value: 'recoveryAgent', label: 'Agente de recuperación' },
  { value: 'recoveryManager', label: 'Gerente de Recuperación' },
];
export const fleetRoleOptions = salesRoleOptions;
export const backOfficeRoleOptions = salesRoleOptions;
export const cxRoleOptions = salesRoleOptions;
export const kycRoleOptions = salesRoleOptions;

export const auditRoleOptions = [{ value: 'auditor', label: 'Auditor' }];

export const legalRoleOptions = [
  { value: 'administrador', label: 'Administrator' },
  { value: 'lead', label: 'Lead' },
];

export const financeRoleOptions = legalRoleOptions;

export const visitorRoleOptions = [{ value: 'visitor', label: 'Visitador' }];

export const superAdminRoleOptions = [{ value: 'superadmin', label: 'Super Administrator' }];

export enum Roles {
  Administrator = 'administrador',
  Superadmin = 'superadmin',
  Agent = 'agent',
  Lead = 'lead',
  Auditor = 'auditor',
  Visitor = 'visitor',
  RecoveryAgent = 'recoveryAgent',
  RecoveryManager = 'recoveryManager',
}

export enum Paths {
  dashboard_default = '/dashboard',
  userManagement_users = '/dashboard/usuarios',
  userManagement_permissions = '/dashboard/permisos',
  calendar_appointmentScheduler = '/dashboard/calendar/appointment-scheduler',
  calendar_calendarPreview = '/dashboard/calendar/calendar-preview',
  clients_admissions = '/dashboard/clientes/solicitudes',
  clients_waitlist = '/dashboard/clientes/lista-de-espera',
  fleet_active = '/dashboard/flotilla/active',
  fleet_inactive = '/dashboard/flotilla/inactive',
  payments_clients = '/dashboard/pagos/clientes',
  payments_products = '/dashboard/pagos/productos',
  payments_payments = '/dashboard/pagos/payments',
  payments_paymentSchedule = '/dashboard/pagos/schedule',
  payments_paymentVerification = '/dashboard/pagos/unlock',
  noPermisos = '/dashboard/noPermisos',
  clients_leads = '/dashboard/clientes/clientes-potenciales',
  clients_assignedLeads = '/dashboard/clientes/clientes-asignados',
}

export enum Sections {
  Dashboard = 'dashboard',
  UserManagement = 'userManagement',
  Calendar = 'calendar',
  Clients = 'clients',
  Fleet = 'fleet',
  Payments = 'payments',
  GlobalSearch = 'globalSearch',
}

export enum Subsections {
  Users = 'users',
  Permissions = 'permissions',
  Admissions = 'admissions',
  Waitlist = 'waitlist',
  Inactive = 'inactive',
  Active = 'active',
  General = 'general',
  Clients = 'clients',
  Products = 'products',
  Payments = 'payments',
  PaymentSchedule = 'paymentSchedule',
  PaymentVerification = 'paymentVerification',
  AppointmentScheduler = 'appointmentScheduler',
  CalendarPreview = 'calendarPreview',
  Empty = 'default', //if subsection is empty
  QRScan = 'qrScan',
  Leads = 'leads',
  AssignedLeads = 'assignedLeads',
}

export enum Capabilities {
  View = 'view',
  Edit = 'edit',
  MakeHomeVisitor = 'makeHomeVisitor',
  Delete = 'delete',
  AssignPermissions = 'assignPermissions',
  Add = 'add',
  AddEarnings = 'addEarnings',
  EditEarnings = 'editEarnings',
  AssignDriver = 'assignDriver',
  GenerateContract = 'generateContract',
  RegenarateContract = 'regenarateContract',
  SendContractToSignature = 'sendContractToSignature',
  ChangeStatus = 'changeStatus',
  ViewAll = 'viewAll',
  EditProfile = 'editProfile',
  UploadDocument = 'uploadDocument',
  ReplaceDocument = 'replaceDocument',
  ApproveDocument = 'approveDocument',
  DeclineDocument = 'declineDocument',
  AddGuarantee = 'addGuarantee',
  EditGuarantee = 'editGuarantee',
  ToRecovery = 'toRecovery',
  EditVehicle = 'editVehicle',
  AddPolicy = 'addPolicy',
  EditPolicy = 'editPolicy',
  AddHolding = 'addHolding',
  EditHolding = 'editHolding',
  AddPlate = 'addPlate',
  EditPlate = 'editPlate',
  AddCirculationCard = 'addCirculationCard',
  EditCirculationCard = 'editCirculationCard',
  RequestPayment = 'requestPayment',
  CreateInvoice = 'createInvoice',
  CreateReceipt = 'createReceipt',
  ViewDriverProfile = 'viewDriverProfile',
  EditDriverProfile = 'editDriverProfile',
  ViewContractDetails = 'viewContractDetails',
  ReturnStep = 'returnStep',
  Search = 'search',
  InPreparation = 'in-preparation',
  Stock = 'stock',
  Assigned = 'assigned',
  Delivered = 'delivered',
  Insurance = 'insurance',
  Workshop = 'workshop',
  Revision = 'revision',
  Legal = 'legal',
  Collection = 'collection',
  Withdrawn = 'withdrawn',
  Sold = 'sold',
  Adendum = 'adendum',
  Utilitary = 'utilitary',
  MyTeam = 'myTeam',
  AllTeam = 'allTeam',
  OcnSignature = 'ocnSignature',
  EditGPS = 'editGPS',
  ViewMyCalendar = 'viewMyCalendar',
  ViewAllCalendar = 'viewAllCalendar',
  EditMyCalendar = 'editMyCalendar',
  EditAllCalendar = 'editAllCalendar',
  TerminateContract = 'terminateContract',
}

export enum Countries {
  'United States' = 'United States',
  Mexico = 'Mexico',
  'Brazil' = 'Brazil',
}

export const CountriesOptions = {
  [Countries['United States']]: {
    value: Countries['United States'],
    label: Countries['United States'],
  },
  [Countries.Mexico]: {
    value: Countries.Mexico,
    label: Countries.Mexico,
  },
  [Countries.Brazil]: {
    value: Countries.Brazil,
    label: Countries.Brazil,
  },
};

export const countries = [
  CountriesOptions[Countries['United States']],
  CountriesOptions[Countries.Mexico],
  CountriesOptions[Countries.Brazil],
];

export enum USSTATES {
  Alabama = 'Alabama',
  Alaska = 'Alaska',
  Arizona = 'Arizona',
  Arkansas = 'Arkansas',
  California = 'California',
  Colorado = 'Colorado',
  Connecticut = 'Connecticut',
  Delaware = 'Delaware',
  Florida = 'Florida',
  Georgia = 'Georgia',
  Hawaii = 'Hawaii',
  Idaho = 'Idaho',
  Illinois = 'Illinois',
  Indiana = 'Indiana',
  Iowa = 'Iowa',
  Kansas = 'Kansas',
  Kentucky = 'Kentucky',
  Louisiana = 'Louisiana',
  Maine = 'Maine',
  Maryland = 'Maryland',
  Massachusetts = 'Massachusetts',
  Michigan = 'Michigan',
  Minnesota = 'Minnesota',
  Mississippi = 'Mississippi',
  Missouri = 'Missouri',
  Montana = 'Montana',
  Nebraska = 'Nebraska',
  Nevada = 'Nevada',
  NewHampshire = 'New Hampshire',
  NewJersey = 'New Jersey',
  NewMexico = 'New Mexico',
  NewYork = 'New York',
  NorthCarolina = 'North Carolina',
  NorthDakota = 'North Dakota',
  Ohio = 'Ohio',
  Oklahoma = 'Oklahoma',
  Oregon = 'Oregon',
  Pennsylvania = 'Pennsylvania',
  RhodeIsland = 'Rhode Island',
  SouthCarolina = 'South Carolina',
  SouthDakota = 'South Dakota',
  Tennessee = 'Tennessee',
  Texas = 'Texas',
  Utah = 'Utah',
  Vermont = 'Vermont',
  Virginia = 'Virginia',
  Washington = 'Washington',
  WestVirginia = 'West Virginia',
  Wisconsin = 'Wisconsin',
  Wyoming = 'Wyoming',
}

export const US_STATES_OBJ = {
  // [USSTATES.Florida]: {
  //   value: USSTATES.Florida,
  //   label: USSTATES.Florida,
  // },
  [USSTATES.Texas]: {
    value: USSTATES.Texas,
    label: USSTATES.Texas,
  },
};

export const US_STATES_OPTIONS = [US_STATES_OBJ[USSTATES.Texas]];

export const US_CITIES = [
  {
    value: 'DAL',
    label: 'Dallas',
    code: 'DAL',
    number: 22,
  },
];

export const US_COLORS = [
  { label: 'RED', value: 'RED' },
  { label: 'WHITE', value: 'WHITE' },
  { label: 'BLACK', value: 'BLACK' },
  { label: 'GRAY', value: 'GRAY' },
  { label: 'BEIGE', value: 'BEIGE' },
  { label: 'BLUE', value: 'BLUE' },
  { label: 'SCARLET', value: 'SCARLET' },
];

export enum USCITIESNAMES {
  // Miami = 'Miami',
  Dallas = 'Dallas',
}

export const US_CITIES_NAMES_SHORT_CODES = {
  // [USCITIESNAMES.Miami]: 'MIA',
  [USCITIESNAMES.Dallas]: 'DAL',
};

export const US_CITIES_OPTIONS = {
  // [USCITIESNAMES.Miami]: {
  //   value: USCITIESNAMES.Miami,
  //   label: USCITIESNAMES.Miami,
  // },
  [USCITIESNAMES.Dallas]: {
    value: USCITIESNAMES.Dallas,
    label: USCITIESNAMES.Dallas,
  },
};

export const US_STATES_CITIES = {
  'New York': [
    'New York',
    'Buffalo',
    'Rochester',
    'Yonkers',
    'Syracuse',
    'Albany',
    'New Rochelle',
    'Mount Vernon',
    'Schenectady',
    'Utica',
    'White Plains',
    'Hempstead',
    'Troy',
    'Niagara Falls',
    'Binghamton',
    'Freeport',
    'Valley Stream',
  ],
  California: [
    'Los Angeles',
    'San Diego',
    'San Jose',
    'San Francisco',
    'Fresno',
    'Sacramento',
    'Long Beach',
    'Oakland',
    'Bakersfield',
    'Anaheim',
    'Santa Ana',
    'Riverside',
    'Stockton',
    'Chula Vista',
    'Irvine',
    'Fremont',
    'San Bernardino',
    'Modesto',
    'Fontana',
    'Oxnard',
    'Moreno Valley',
    'Huntington Beach',
    'Glendale',
    'Santa Clarita',
    'Garden Grove',
    'Oceanside',
    'Rancho Cucamonga',
    'Santa Rosa',
    'Ontario',
    'Lancaster',
    'Elk Grove',
    'Corona',
    'Palmdale',
    'Salinas',
    'Pomona',
    'Hayward',
    'Escondido',
    'Torrance',
    'Sunnyvale',
    'Orange',
    'Fullerton',
    'Pasadena',
    'Thousand Oaks',
    'Visalia',
    'Simi Valley',
    'Concord',
    'Roseville',
    'Victorville',
    'Santa Clara',
    'Vallejo',
    'Berkeley',
    'El Monte',
    'Downey',
    'Costa Mesa',
    'Inglewood',
    'Carlsbad',
    'San Buenaventura (Ventura)',
    'Fairfield',
    'West Covina',
    'Murrieta',
    'Richmond',
    'Norwalk',
    'Antioch',
    'Temecula',
    'Burbank',
    'Daly City',
    'Rialto',
    'Santa Maria',
    'El Cajon',
    'San Mateo',
    'Clovis',
    'Compton',
    'Jurupa Valley',
    'Vista',
    'South Gate',
    'Mission Viejo',
    'Vacaville',
    'Carson',
    'Hesperia',
    'Santa Monica',
    'Westminster',
    'Redding',
    'Santa Barbara',
    'Chico',
    'Newport Beach',
    'San Leandro',
    'San Marcos',
    'Whittier',
    'Hawthorne',
    'Citrus Heights',
    'Tracy',
    'Alhambra',
    'Livermore',
    'Buena Park',
    'Menifee',
    'Hemet',
    'Lakewood',
    'Merced',
    'Chino',
    'Indio',
    'Redwood City',
    'Lake Forest',
    'Napa',
    'Tustin',
    'Bellflower',
    'Mountain View',
    'Chino Hills',
    'Baldwin Park',
    'Alameda',
    'Upland',
    'San Ramon',
    'Folsom',
    'Pleasanton',
    'Union City',
    'Perris',
    'Manteca',
    'Lynwood',
    'Apple Valley',
    'Redlands',
    'Turlock',
    'Milpitas',
    'Redondo Beach',
    'Rancho Cordova',
    'Yorba Linda',
    'Palo Alto',
    'Davis',
    'Camarillo',
    'Walnut Creek',
    'Pittsburg',
    'South San Francisco',
    'Yuba City',
    'San Clemente',
    'Laguna Niguel',
    'Pico Rivera',
    'Montebello',
    'Lodi',
    'Madera',
    'Santa Cruz',
    'La Habra',
    'Encinitas',
    'Monterey Park',
    'Tulare',
    'Cupertino',
    'Gardena',
    'National City',
    'Rocklin',
    'Petaluma',
    'Huntington Park',
    'San Rafael',
    'La Mesa',
    'Arcadia',
    'Fountain Valley',
    'Diamond Bar',
    'Woodland',
    'Santee',
    'Lake Elsinore',
    'Porterville',
    'Paramount',
    'Eastvale',
    'Rosemead',
    'Hanford',
    'Highland',
    'Brentwood',
    'Novato',
    'Colton',
    'Cathedral City',
    'Delano',
    'Yucaipa',
    'Watsonville',
    'Placentia',
    'Glendora',
    'Gilroy',
    'Palm Desert',
    'Cerritos',
    'West Sacramento',
    'Aliso Viejo',
    'Poway',
    'La Mirada',
    'Rancho Santa Margarita',
    'Cypress',
    'Dublin',
    'Covina',
    'Azusa',
    'Palm Springs',
    'San Luis Obispo',
    'Ceres',
    'San Jacinto',
    'Lincoln',
    'Newark',
    'Lompoc',
    'El Centro',
    'Danville',
    'Bell Gardens',
    'Coachella',
    'Rancho Palos Verdes',
    'San Bruno',
    'Rohnert Park',
    'Brea',
    'La Puente',
    'Campbell',
    'San Gabriel',
    'Beaumont',
    'Morgan Hill',
    'Culver City',
    'Calexico',
    'Stanton',
    'La Quinta',
    'Pacifica',
    'Montclair',
    'Oakley',
    'Monrovia',
    'Los Banos',
    'Martinez',
  ],
  Illinois: [
    'Chicago',
    'Aurora',
    'Rockford',
    'Joliet',
    'Naperville',
    'Springfield',
    'Peoria',
    'Elgin',
    'Waukegan',
    'Cicero',
    'Champaign',
    'Bloomington',
    'Arlington Heights',
    'Evanston',
    'Decatur',
    'Schaumburg',
    'Bolingbrook',
    'Palatine',
    'Skokie',
    'Des Plaines',
    'Orland Park',
    'Tinley Park',
    'Oak Lawn',
    'Berwyn',
    'Mount Prospect',
    'Normal',
    'Wheaton',
    'Hoffman Estates',
    'Oak Park',
    'Downers Grove',
    'Elmhurst',
    'Glenview',
    'DeKalb',
    'Lombard',
    'Belleville',
    'Moline',
    'Buffalo Grove',
    'Bartlett',
    'Urbana',
    'Quincy',
    'Crystal Lake',
    'Plainfield',
    'Streamwood',
    'Carol Stream',
    'Romeoville',
    'Rock Island',
    'Hanover Park',
    'Carpentersville',
    'Wheeling',
    'Park Ridge',
    'Addison',
    'Calumet City',
  ],
  Texas: [
    'Houston',
    'San Antonio',
    'Dallas',
    'Austin',
    'Fort Worth',
    'El Paso',
    'Arlington',
    'Corpus Christi',
    'Plano',
    'Laredo',
    'Lubbock',
    'Garland',
    'Irving',
    'Amarillo',
    'Grand Prairie',
    'Brownsville',
    'Pasadena',
    'McKinney',
    'Mesquite',
    'McAllen',
    'Killeen',
    'Frisco',
    'Waco',
    'Carrollton',
    'Denton',
    'Midland',
    'Abilene',
    'Beaumont',
    'Round Rock',
    'Odessa',
    'Wichita Falls',
    'Richardson',
    'Lewisville',
    'Tyler',
    'College Station',
    'Pearland',
    'San Angelo',
    'Allen',
    'League City',
    'Sugar Land',
    'Longview',
    'Edinburg',
    'Mission',
    'Bryan',
    'Baytown',
    'Pharr',
    'Temple',
    'Missouri City',
    'Flower Mound',
    'Harlingen',
    'North Richland Hills',
    'Victoria',
    'Conroe',
    'New Braunfels',
    'Mansfield',
    'Cedar Park',
    'Rowlett',
    'Port Arthur',
    'Euless',
    'Georgetown',
    'Pflugerville',
    'DeSoto',
    'San Marcos',
    'Grapevine',
    'Bedford',
    'Galveston',
    'Cedar Hill',
    'Texas City',
    'Wylie',
    'Haltom City',
    'Keller',
    'Coppell',
    'Rockwall',
    'Huntsville',
    'Duncanville',
    'Sherman',
    'The Colony',
    'Burleson',
    'Hurst',
    'Lancaster',
    'Texarkana',
    'Friendswood',
    'Weslaco',
  ],
  Pennsylvania: [
    'Philadelphia',
    'Pittsburgh',
    'Allentown',
    'Erie',
    'Reading',
    'Scranton',
    'Bethlehem',
    'Lancaster',
    'Harrisburg',
    'Altoona',
    'York',
    'State College',
    'Wilkes-Barre',
  ],
  Arizona: [
    'Phoenix',
    'Tucson',
    'Mesa',
    'Chandler',
    'Glendale',
    'Scottsdale',
    'Gilbert',
    'Tempe',
    'Peoria',
    'Surprise',
    'Yuma',
    'Avondale',
    'Goodyear',
    'Flagstaff',
    'Buckeye',
    'Lake Havasu City',
    'Casa Grande',
    'Sierra Vista',
    'Maricopa',
    'Oro Valley',
    'Prescott',
    'Bullhead City',
    'Prescott Valley',
    'Marana',
    'Apache Junction',
  ],
  Florida: [
    'Jacksonville',
    'Miami',
    'Tampa',
    'Orlando',
    'St. Petersburg',
    'Hialeah',
    'Tallahassee',
    'Fort Lauderdale',
    'Port St. Lucie',
    'Cape Coral',
    'Pembroke Pines',
    'Hollywood',
    'Miramar',
    'Gainesville',
    'Coral Springs',
    'Miami Gardens',
    'Clearwater',
    'Palm Bay',
    'Pompano Beach',
    'West Palm Beach',
    'Lakeland',
    'Davie',
    'Miami Beach',
    'Sunrise',
    'Plantation',
    'Boca Raton',
    'Deltona',
    'Largo',
    'Deerfield Beach',
    'Palm Coast',
    'Melbourne',
    'Boynton Beach',
    'Lauderhill',
    'Weston',
    'Fort Myers',
    'Kissimmee',
    'Homestead',
    'Tamarac',
    'Delray Beach',
    'Daytona Beach',
    'North Miami',
    'Wellington',
    'North Port',
    'Jupiter',
    'Ocala',
    'Port Orange',
    'Margate',
    'Coconut Creek',
    'Sanford',
    'Sarasota',
    'Pensacola',
    'Bradenton',
    'Palm Beach Gardens',
    'Pinellas Park',
    'Coral Gables',
    'Doral',
    'Bonita Springs',
    'Apopka',
    'Titusville',
    'North Miami Beach',
    'Oakland Park',
    'Fort Pierce',
    'North Lauderdale',
    'Cutler Bay',
    'Altamonte Springs',
    'St. Cloud',
    'Greenacres',
    'Ormond Beach',
    'Ocoee',
    'Hallandale Beach',
    'Winter Garden',
    'Aventura',
  ],
  Indiana: [
    'Indianapolis',
    'Fort Wayne',
    'Evansville',
    'South Bend',
    'Carmel',
    'Bloomington',
    'Fishers',
    'Hammond',
    'Gary',
    'Muncie',
    'Lafayette',
    'Terre Haute',
    'Kokomo',
    'Anderson',
    'Noblesville',
    'Greenwood',
    'Elkhart',
    'Mishawaka',
    'Lawrence',
    'Jeffersonville',
    'Columbus',
    'Portage',
  ],
  Ohio: [
    'Columbus',
    'Cleveland',
    'Cincinnati',
    'Toledo',
    'Akron',
    'Dayton',
    'Parma',
    'Canton',
    'Youngstown',
    'Lorain',
    'Hamilton',
    'Springfield',
    'Kettering',
    'Elyria',
    'Lakewood',
    'Cuyahoga Falls',
    'Middletown',
    'Euclid',
    'Newark',
    'Mansfield',
    'Mentor',
    'Beavercreek',
    'Cleveland Heights',
    'Strongsville',
    'Dublin',
    'Fairfield',
    'Findlay',
    'Warren',
    'Lancaster',
    'Lima',
    'Huber Heights',
    'Westerville',
    'Marion',
    'Grove City',
  ],
  'North Carolina': [
    'Charlotte',
    'Raleigh',
    'Greensboro',
    'Durham',
    'Winston-Salem',
    'Fayetteville',
    'Cary',
    'Wilmington',
    'High Point',
    'Greenville',
    'Asheville',
    'Concord',
    'Gastonia',
    'Jacksonville',
    'Chapel Hill',
    'Rocky Mount',
    'Burlington',
    'Wilson',
    'Huntersville',
    'Kannapolis',
    'Apex',
    'Hickory',
    'Goldsboro',
  ],
  Michigan: [
    'Detroit',
    'Grand Rapids',
    'Warren',
    'Sterling Heights',
    'Ann Arbor',
    'Lansing',
    'Flint',
    'Dearborn',
    'Livonia',
    'Westland',
    'Troy',
    'Farmington Hills',
    'Kalamazoo',
    'Wyoming',
    'Southfield',
    'Rochester Hills',
    'Taylor',
    'Pontiac',
    'St. Clair Shores',
    'Royal Oak',
    'Novi',
    'Dearborn Heights',
    'Battle Creek',
    'Saginaw',
    'Kentwood',
    'East Lansing',
    'Roseville',
    'Portage',
    'Midland',
    'Lincoln Park',
    'Muskegon',
  ],
  Tennessee: [
    'Memphis',
    'Nashville-Davidson',
    'Knoxville',
    'Chattanooga',
    'Clarksville',
    'Murfreesboro',
    'Jackson',
    'Franklin',
    'Johnson City',
    'Bartlett',
    'Hendersonville',
    'Kingsport',
    'Collierville',
    'Cleveland',
    'Smyrna',
    'Germantown',
    'Brentwood',
  ],
  Massachusetts: [
    'Boston',
    'Worcester',
    'Springfield',
    'Lowell',
    'Cambridge',
    'New Bedford',
    'Brockton',
    'Quincy',
    'Lynn',
    'Fall River',
    'Newton',
    'Lawrence',
    'Somerville',
    'Waltham',
    'Haverhill',
    'Malden',
    'Medford',
    'Taunton',
    'Chicopee',
    'Weymouth Town',
    'Revere',
    'Peabody',
    'Methuen',
    'Barnstable Town',
    'Pittsfield',
    'Attleboro',
    'Everett',
    'Salem',
    'Westfield',
    'Leominster',
    'Fitchburg',
    'Beverly',
    'Holyoke',
    'Marlborough',
    'Woburn',
    'Chelsea',
  ],
  Washington: [
    'Seattle',
    'Spokane',
    'Tacoma',
    'Vancouver',
    'Bellevue',
    'Kent',
    'Everett',
    'Renton',
    'Yakima',
    'Federal Way',
    'Spokane Valley',
    'Bellingham',
    'Kennewick',
    'Auburn',
    'Pasco',
    'Marysville',
    'Lakewood',
    'Redmond',
    'Shoreline',
    'Richland',
    'Kirkland',
    'Burien',
    'Sammamish',
    'Olympia',
    'Lacey',
    'Edmonds',
    'Bremerton',
    'Puyallup',
  ],
  Colorado: [
    'Denver',
    'Colorado Springs',
    'Aurora',
    'Fort Collins',
    'Lakewood',
    'Thornton',
    'Arvada',
    'Westminster',
    'Pueblo',
    'Centennial',
    'Boulder',
    'Greeley',
    'Longmont',
    'Loveland',
    'Grand Junction',
    'Broomfield',
    'Castle Rock',
    'Commerce City',
    'Parker',
    'Littleton',
    'Northglenn',
  ],
  'District of Columbia': ['Washington'],
  Maryland: ['Baltimore', 'Frederick', 'Rockville', 'Gaithersburg', 'Bowie', 'Hagerstown', 'Annapolis'],
  Kentucky: ['Louisville/Jefferson County', 'Lexington-Fayette', 'Bowling Green', 'Owensboro', 'Covington'],
  Oregon: [
    'Portland',
    'Eugene',
    'Salem',
    'Gresham',
    'Hillsboro',
    'Beaverton',
    'Bend',
    'Medford',
    'Springfield',
    'Corvallis',
    'Albany',
    'Tigard',
    'Lake Oswego',
    'Keizer',
  ],
  Oklahoma: [
    'Oklahoma City',
    'Tulsa',
    'Norman',
    'Broken Arrow',
    'Lawton',
    'Edmond',
    'Moore',
    'Midwest City',
    'Enid',
    'Stillwater',
    'Muskogee',
  ],
  Wisconsin: [
    'Milwaukee',
    'Madison',
    'Green Bay',
    'Kenosha',
    'Racine',
    'Appleton',
    'Waukesha',
    'Eau Claire',
    'Oshkosh',
    'Janesville',
    'West Allis',
    'La Crosse',
    'Sheboygan',
    'Wauwatosa',
    'Fond du Lac',
    'New Berlin',
    'Wausau',
    'Brookfield',
    'Greenfield',
    'Beloit',
  ],
  Nevada: ['Las Vegas', 'Henderson', 'Reno', 'North Las Vegas', 'Sparks', 'Carson City'],
  'New Mexico': ['Albuquerque', 'Las Cruces', 'Rio Rancho', 'Santa Fe', 'Roswell', 'Farmington', 'Clovis'],
  Missouri: [
    'Kansas City',
    'St. Louis',
    'Springfield',
    'Independence',
    'Columbia',
    "Lee's Summit",
    "O'Fallon",
    'St. Joseph',
    'St. Charles',
    'St. Peters',
    'Blue Springs',
    'Florissant',
    'Joplin',
    'Chesterfield',
    'Jefferson City',
    'Cape Girardeau',
  ],
  Virginia: [
    'Virginia Beach',
    'Norfolk',
    'Chesapeake',
    'Richmond',
    'Newport News',
    'Alexandria',
    'Hampton',
    'Roanoke',
    'Portsmouth',
    'Suffolk',
    'Lynchburg',
    'Harrisonburg',
    'Leesburg',
    'Charlottesville',
    'Danville',
    'Blacksburg',
    'Manassas',
  ],
  Georgia: [
    'Atlanta',
    'Columbus',
    'Augusta-Richmond County',
    'Savannah',
    'Athens-Clarke County',
    'Sandy Springs',
    'Roswell',
    'Macon',
    'Johns Creek',
    'Albany',
    'Warner Robins',
    'Alpharetta',
    'Marietta',
    'Valdosta',
    'Smyrna',
    'Dunwoody',
  ],
  Nebraska: ['Omaha', 'Lincoln', 'Bellevue', 'Grand Island'],
  Minnesota: [
    'Minneapolis',
    'St. Paul',
    'Rochester',
    'Duluth',
    'Bloomington',
    'Brooklyn Park',
    'Plymouth',
    'St. Cloud',
    'Eagan',
    'Woodbury',
    'Maple Grove',
    'Eden Prairie',
    'Coon Rapids',
    'Burnsville',
    'Blaine',
    'Lakeville',
    'Minnetonka',
    'Apple Valley',
    'Edina',
    'St. Louis Park',
    'Mankato',
    'Maplewood',
    'Moorhead',
    'Shakopee',
  ],
  Kansas: [
    'Wichita',
    'Overland Park',
    'Kansas City',
    'Olathe',
    'Topeka',
    'Lawrence',
    'Shawnee',
    'Manhattan',
    'Lenexa',
    'Salina',
    'Hutchinson',
  ],
  Louisiana: [
    'New Orleans',
    'Baton Rouge',
    'Shreveport',
    'Lafayette',
    'Lake Charles',
    'Kenner',
    'Bossier City',
    'Monroe',
    'Alexandria',
  ],
  Hawaii: ['Honolulu'],
  Alaska: ['Anchorage'],
  'New Jersey': [
    'Newark',
    'Jersey City',
    'Paterson',
    'Elizabeth',
    'Clifton',
    'Trenton',
    'Camden',
    'Passaic',
    'Union City',
    'Bayonne',
    'East Orange',
    'Vineland',
    'New Brunswick',
    'Hoboken',
    'Perth Amboy',
    'West New York',
    'Plainfield',
    'Hackensack',
    'Sayreville',
    'Kearny',
    'Linden',
    'Atlantic City',
  ],
  Idaho: [
    'Boise City',
    'Nampa',
    'Meridian',
    'Idaho Falls',
    'Pocatello',
    'Caldwell',
    "Coeur d'Alene",
    'Twin Falls',
  ],
  Alabama: [
    'Birmingham',
    'Montgomery',
    'Mobile',
    'Huntsville',
    'Tuscaloosa',
    'Hoover',
    'Dothan',
    'Auburn',
    'Decatur',
    'Madison',
    'Florence',
    'Gadsden',
  ],
  Iowa: [
    'Des Moines',
    'Cedar Rapids',
    'Davenport',
    'Sioux City',
    'Iowa City',
    'Waterloo',
    'Council Bluffs',
    'Ames',
    'West Des Moines',
    'Dubuque',
    'Ankeny',
    'Urbandale',
    'Cedar Falls',
  ],
  Arkansas: [
    'Little Rock',
    'Fort Smith',
    'Fayetteville',
    'Springdale',
    'Jonesboro',
    'North Little Rock',
    'Conway',
    'Rogers',
    'Pine Bluff',
    'Bentonville',
  ],
  Utah: [
    'Salt Lake City',
    'West Valley City',
    'Provo',
    'West Jordan',
    'Orem',
    'Sandy',
    'Ogden',
    'St. George',
    'Layton',
    'Taylorsville',
    'South Jordan',
    'Lehi',
    'Logan',
    'Murray',
    'Draper',
    'Bountiful',
    'Riverton',
    'Roy',
  ],
  'Rhode Island': ['Providence', 'Warwick', 'Cranston', 'Pawtucket', 'East Providence', 'Woonsocket'],
  Mississippi: ['Jackson', 'Gulfport', 'Southaven', 'Hattiesburg', 'Biloxi', 'Meridian'],
  'South Dakota': ['Sioux Falls', 'Rapid City'],
  Connecticut: [
    'Bridgeport',
    'New Haven',
    'Stamford',
    'Hartford',
    'Waterbury',
    'Norwalk',
    'Danbury',
    'New Britain',
    'Meriden',
    'Bristol',
    'West Haven',
    'Milford',
    'Middletown',
    'Norwich',
    'Shelton',
  ],
  'South Carolina': [
    'Columbia',
    'Charleston',
    'North Charleston',
    'Mount Pleasant',
    'Rock Hill',
    'Greenville',
    'Summerville',
    'Sumter',
    'Goose Creek',
    'Hilton Head Island',
    'Florence',
    'Spartanburg',
  ],
  'New Hampshire': ['Manchester', 'Nashua', 'Concord'],
  'North Dakota': ['Fargo', 'Bismarck', 'Grand Forks', 'Minot'],
  Montana: ['Billings', 'Missoula', 'Great Falls', 'Bozeman'],
  Delaware: ['Wilmington', 'Dover'],
  Maine: ['Portland'],
  Wyoming: ['Cheyenne', 'Casper'],
  'West Virginia': ['Charleston', 'Huntington'],
  Vermont: ['Burlington'],
};

export const US_COUNTRY_CODE = '+1';
export const US_CURRENCY = 'USD';
export const MEXICAN_CURRENCY = 'MXN';

// export const SevenPercentTaxInMiami = 7; // 7% tax in Miami
export const SevenPercentTaxInDallas = 0.07; // 7% tax in Dallas

export const statusListByPage: { [key: string]: string[] } = {
  stock: ['stock', 'overhauling'],
  activos: ['active', 'activo', 'in-service', 'legal-process', 'awaiting-insurance'],
  discharged: ['discharged'],
  bajas: ['readmissions'],
  invoiced: ['invoiced'],
};

export const CountriesShortNames = Object.freeze({
  [Countries['United States']]: 'us',
  [Countries.Mexico]: 'mx',
  [Countries.Brazil]: 'br',
});

export const US_DEFAULT_STATE_OPTIONS = [US_STATES_OBJ[USSTATES.Texas]];
export const US_STATES_DEFAULT_CITIES = {
  // [USSTATES.Florida]: [US_CITIES_OPTIONS[USCITIESNAMES.Miami]],
  [USSTATES.Texas]: [US_CITIES_OPTIONS[USCITIESNAMES.Dallas]],
};

export const getUSStatesOptions = () => {
  const US_STATES_OPTIONS_FOR_CUSTOMER_REGISTERATION = Object.values(USSTATES).reduce((acc, state) => {
    const usStateOption = {
      label: state,
      value: state,
    };
    acc.push(usStateOption);
    return acc;
  }, [] as Array<{ label: string; value: string }>);
  return US_STATES_OPTIONS_FOR_CUSTOMER_REGISTERATION;
};

export const getUSCitiesBasedOnState = (state: string) => {
  if (!state) {
    state = USSTATES.Texas;
  }
  const US_CITIES_OPTION_FOR_CUSTOMERS = US_STATES_CITIES[state as keyof typeof US_STATES_CITIES].reduce(
    (acc, city) => {
      const cityOption = {
        label: city,
        value: city,
      };
      acc.push(cityOption);
      return acc;
    },
    [] as Array<{ label: string; value: string }>
  );
  return US_CITIES_OPTION_FOR_CUSTOMERS;
};

export function getFullAddressString(associate: any) {
  const street = associate?.address.addressStreet || '';
  const exterior = associate?.address.exterior || '';
  const interior = associate?.address.interior || '';
  const delegation = associate?.address.delegation || '';
  const city = cities[associate?.address.city]?.value || '';
  const state = federalEntities[associate?.address.state]?.value || '';
  const postalCode = associate?.address.postalCode || '';
  const colony = associate?.address.colony || '';

  // return `${street} ${exterior} ${interior} ${colony} ${delegation} ${state} ${postalCode}`
  const fullAddress = `${street} ${exterior} ${interior} ${colony} ${delegation} ${city}, ${state} C.P. ${postalCode}`;

  // remove extra spaces or double spaces

  return fullAddress.replace(/\s+/g, ' ').trim();
}

export function getFullAddressStringV2(associate: any) {
  const addressStreet = associate.address.addressStreet;
  const exterior = associate.address.exterior.toString();
  const interior = associate.address.interior ? associate.address.interior.toString() : '';

  const city = cities[associate.city] ? cities[associate.city].label : associate.city;

  if (addressStreet.includes(exterior) && addressStreet.includes(interior)) {
    return `${addressStreet}, ${associate.colony}, ${city}, C.P. ${associate.postalCode}`;
  } else {
    return `${addressStreet} ${exterior} ${interior}, ${associate.colony}, ${city}, C.P. ${associate.postalCode}`;
  }
}

export const usersPaymentActionsAllowed = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
];

export const combineSearchParamsAndFilters = ({
  searchParams,
  filters,
  paramCategory,
}: {
  searchParams: Record<string, any>;
  filters: Record<string, any>;
  paramCategory?: string | null;
}) => {
  let definitiveFilters;
  const doesSearchParamsExist = Object.keys(searchParams).length > 0;
  if (doesSearchParamsExist && filters) {
    Object.keys(searchParams).forEach((key) => {
      filters[key] = searchParams[key];
    });

    if (paramCategory === 'withdrawn') {
      definitiveFilters = filters;
    } else {
      definitiveFilters = { ...filters };
      delete definitiveFilters.reason;
    }
  } else if (doesSearchParamsExist) {
    definitiveFilters = paramCategory === 'withdrawn' ? searchParams : { ...searchParams };
    if (paramCategory !== 'withdrawn') {
      delete definitiveFilters.reason;
    }
  }
  return definitiveFilters;
};

export const MexicoRegions = [
  { label: 'CDMX', value: 'CDMX' },
  { label: 'GDL', value: 'GDL' },
  { label: 'MTY', value: 'MTY' },
  { label: 'TIJ', value: 'TIJ' },
  { label: 'QRO', value: 'QRO' },
  { label: 'PBE', value: 'PBE' },
  { label: 'TOL', value: 'TOL' },
  { label: 'PTV', value: 'PTV' },
  { label: 'TEP', value: 'TEP' },
  { label: 'COL', value: 'COL' },
  { label: 'SAL', value: 'SAL' },
  { label: 'TORR', value: 'TORR' },
  { label: 'DUR', value: 'DUR' },
  { label: 'MXLI', value: 'MXLI' },
  { label: 'HER', value: 'HER' },
  { label: 'CHI', value: 'CHI' },
  { label: 'LEO', value: 'LEO' },
  { label: 'AGS', value: 'AGS' },
  { label: 'SLP', value: 'SLP' },
  { label: 'MER', value: 'MER' },
];

export const ONE_CAR_NOW_EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@onecarnow\.com$/;

export const MAXIMUM_GOOGLE_SINGIN_FAILURES_ALLOWED = 3;

const CountriesNationality = [
  'Mexico',
  'Afghanistan',
  'Albania',
  'Algeria',
  'Andorra',
  'Angola',
  'Antigua & Deps',
  'Argentina',
  'Armenia',
  'Australia',
  'Austria',
  'Azerbaijan',
  'Bahamas',
  'Bahrain',
  'Bangladesh',
  'Barbados',
  'Belarus',
  'Belgium',
  'Belize',
  'Benin',
  'Bermuda',
  'Bhutan',
  'Bolivia',
  'Bosnia Herzegovina',
  'Botswana',
  'Brazil',
  'Brunei',
  'Bulgaria',
  'Burkina',
  'Burundi',
  'Cambodia',
  'Cameroon',
  'Canada',
  'Cape Verde',
  'Central African Rep',
  'Chad',
  'Chile',
  'China',
  'Colombia',
  'Comoros',
  'Congo',
  'Congo (Democratic Rep)',
  'Costa Rica',
  'Croatia',
  'Cuba',
  'Cyprus',
  'Czech Republic',
  'Denmark',
  'Djibouti',
  'Dominica',
  'Dominican Republic',
  'East Timor',
  'Ecuador',
  'Egypt',
  'El Salvador',
  'Equatorial Guinea',
  'Eritrea',
  'Estonia',
  'Eswatini',
  'Ethiopia',
  'Fiji',
  'Finland',
  'France',
  'Gabon',
  'Gambia',
  'Georgia',
  'Germany',
  'Ghana',
  'Greece',
  'Grenada',
  'Guatemala',
  'Guinea',
  'Guinea-Bissau',
  'Guyana',
  'Haiti',
  'Honduras',
  'Hungary',
  'Iceland',
  'India',
  'Indonesia',
  'Iran',
  'Iraq',
  'Ireland (Republic)',
  'Israel',
  'Italy',
  'Ivory Coast',
  'Jamaica',
  'Japan',
  'Jordan',
  'Kazakhstan',
  'Kenya',
  'Kiribati',
  'Korea North',
  'Korea South',
  'Kosovo',
  'Kuwait',
  'Kyrgyzstan',
  'Laos',
  'Latvia',
  'Lebanon',
  'Lesotho',
  'Liberia',
  'Libya',
  'Liechtenstein',
  'Lithuania',
  'Luxembourg',
  'Macedonia',
  'Madagascar',
  'Malawi',
  'Malaysia',
  'Maldives',
  'Mali',
  'Malta',
  'Marshall Islands',
  'Mauritania',
  'Mauritius',
  'Micronesia',
  'Moldova',
  'Monaco',
  'Mongolia',
  'Montenegro',
  'Morocco',
  'Mozambique',
  'Myanmar',
  'Namibia',
  'Nauru',
  'Nepal',
  'Netherlands',
  'New Zealand',
  'Nicaragua',
  'Niger',
  'Nigeria',
  'Norway',
  'Oman',
  'Pakistan',
  'Palau',
  'Palestine',
  'Panama',
  'Papua New Guinea',
  'Paraguay',
  'Peru',
  'Philippines',
  'Poland',
  'Portugal',
  'Qatar',
  'Romania',
  'Russian Federation',
  'Rwanda',
  'St Kitts & Nevis',
  'St Lucia',
  'Saint Vincent & the Grenadines',
  'Samoa',
  'San Marino',
  'Sao Tome & Principe',
  'Saudi Arabia',
  'Senegal',
  'Serbia',
  'Seychelles',
  'Sierra Leone',
  'Singapore',
  'Slovakia',
  'Slovenia',
  'Solomon Islands',
  'Somalia',
  'South Africa',
  'South Sudan',
  'Spain',
  'Sri Lanka',
  'Sudan',
  'Suriname',
  'Sweden',
  'Switzerland',
  'Syria',
  'Taiwan',
  'Tajikistan',
  'Tanzania',
  'Thailand',
  'Togo',
  'Tonga',
  'Trinidad & Tobago',
  'Tunisia',
  'Turkey',
  'Turkmenistan',
  'Tuvalu',
  'Uganda',
  'Ukraine',
  'United Arab Emirates',
  'United Kingdom',
  'United States',
  'Uruguay',
  'Uzbekistan',
  'Vanuatu',
  'Vatican City',
  'Venezuela',
  'Vietnam',
  'Yemen',
  'Zambia',
  'Zimbabwe',
];

export const getNationalities = () => {
  const nationalities = CountriesNationality.map((country) => {
    return {
      label: country,
      value: country,
    };
  });
  return nationalities;
};

export enum YesOrNoOptions {
  Yes = 'Yes',
  No = 'No',
  Si = 'Si',
}

export const yesOrNoOptions = [
  { label: YesOrNoOptions.Si, value: YesOrNoOptions.Yes },
  { label: YesOrNoOptions.No, value: YesOrNoOptions.No },
];

export enum OWNORRENTED {
  OWNEDMX = 'Propia',
  RENTEDMX = 'Rentada',
}

export const ownedOrRentedOptions = [
  { label: OWNORRENTED.OWNEDMX, value: OWNORRENTED.OWNEDMX },
  { label: OWNORRENTED.RENTEDMX, value: OWNORRENTED.RENTEDMX },
];

export const ownerAndRentedOptionsMapping = {
  [OWNORRENTED.OWNEDMX]: YesOrNoOptions.Yes,
  [OWNORRENTED.RENTEDMX]: YesOrNoOptions.No,
  [YesOrNoOptions.Yes]: OWNORRENTED.OWNEDMX,
  [YesOrNoOptions.No]: OWNORRENTED.RENTEDMX,
};

export const capitalizeFirstLetter = (string: string) => {
  return string.charAt(0).toUpperCase() + string.slice(1);
};

export const smallFirstLetter = (string: string) => {
  return string.charAt(0).toLowerCase() + string.slice(1);
};

export enum HomeVisitStepsStatus {
  complete = 'complete',
  incomplete = 'incomplete',
}

export const calculateAverageEarningsOfLastTwelveWeeks = (totalEarnings: number) => {
  const lastTwelveWeek = 12;
  return Math.round(totalEarnings / lastTwelveWeek);
};

export const emailsCanMarkAsPaidProd = ['<EMAIL>', '<EMAIL>'];
export const paymentActionsEmailList = [
  '<EMAIL>',
  '<EMAIL>',
  ,
  '<EMAIL>',
];

export function canPerformPaymentActions(email: string) {
  if (IS_PROD) {
    return paymentActionsEmailList.includes(email);
  }
  return true;
}

enum USVehicles {
  'Hyundai Ioniq5 Long Range' = 'Hyundai Ioniq5 Long Range',
  'Genesis GV60 Long Range' = 'Genesis GV60 Long Range',
  '2025 Hyundai Kona Electric' = '2025 Hyundai Kona Electric',
  // we don't have this car in our fleet for now, but we will have it in the future.
  // 'GM Equinox' = 'gm-equinox',
}

export const USVehiclesList = Object.values(USVehicles).map((vehicle) => ({
  label: vehicle,
  value: vehicle,
}));

export enum ApprovalTypes {
  PREAPPROVED = 'preapproved',
  PRE_OWNED = 'pre-owned',
  // MG3 = 'MG3',
  DOWN_PAYMENT = 'down payment',
  DEPOSIT = 'deposit',
}

export enum ExtendApproval {
  APPROVED = 'approved',
}

// Central enum for document categories
export enum DocumentCategory {
  INSURANCE_POLICY = 'INSURANCE_POLICY',
  TENENCIA = 'TENENCIA',
  CIRCULATION_CARD_FRONT = 'CIRCULATION_CARD_FRONT',
  CIRCULATION_CARD_BACK = 'CIRCULATION_CARD_BACK',
  PLATES_ALTA_PLACAS = 'PLATES_ALTA_PLACAS',
  PLATES_FRONT = 'PLATES_FRONT',
  PLATES_BACK = 'PLATES_BACK',
  FACTURE = 'FACTURE',
}

export const approvalOptionsMap: Record<
  any,
  {
    label: string;
    value: ApprovalTypes;
    bgColor: string;
    textColor: string;
    borderColor: string;
    shortLabel: string;
  }
> = {
  [ExtendApproval.APPROVED]: {
    label: 'Aprobado (sin enganche ni deposito)',
    shortLabel: 'Aprobado',
    value: ExtendApproval.APPROVED as any,
    bgColor: 'bg-[#E8FAEB]',
    textColor: 'text-[#067F20]',
    borderColor: 'border-[#08AA29]',
  },
  [ApprovalTypes.PREAPPROVED]: {
    label: 'Preaprobado (sin enganche ni deposito)',
    shortLabel: 'Preaprobado',
    value: ApprovalTypes.PREAPPROVED,
    bgColor: 'bg-[#E8FAEB]',
    textColor: 'text-[#067F20]',
    borderColor: 'border-[#08AA29]',
  },
  [ApprovalTypes.PRE_OWNED]: {
    label: 'Preaprobado Seminuevo',
    shortLabel: 'Seminuevo',
    value: ApprovalTypes.PRE_OWNED,
    bgColor: 'bg-[#F6F5E2]',
    textColor: 'text-[#C9CC00]',
    borderColor: 'border-[#D7CD0C]',
  },
  [ApprovalTypes.DOWN_PAYMENT]: {
    label: 'Aprobado con Enganche',
    shortLabel: 'Aprobado con Enganche',
    value: ApprovalTypes.DOWN_PAYMENT,
    bgColor: 'bg-[#FFEFDB]',
    textColor: 'text-[#B95000]',
    borderColor: 'border-[#FBAE50]',
  },
  [ApprovalTypes.DEPOSIT]: {
    label: 'Aprobado con Deposito',
    shortLabel: 'Aprobado con Depósito',
    value: ApprovalTypes.DEPOSIT,
    bgColor: 'bg-[#EEF0FA]',
    textColor: 'text-[#5078FB]',
    borderColor: 'border-[#5078FB]',
  },
};

export const PermissionModuleMexicanLabels: Record<string, string> = {
  [Sections.Dashboard]: 'Dashboard',
  [Sections.UserManagement]: 'Gestión de Usuarios',
  [Sections.Calendar]: 'Calendario',
  [Sections.Clients]: 'Clientes',
  [Sections.Fleet]: 'Flotillia',
  [Sections.Payments]: 'Pagos',
  [Sections.GlobalSearch]: 'Global search',
  [Subsections.Active]: 'Activos',
  [Subsections.Admissions]: 'Solicitudes',
  [Subsections.AppointmentScheduler]: 'Programación de visitas',
  [Subsections.CalendarPreview]: 'Vista de calendario',
  [Subsections.General]: 'General',
  [Subsections.Inactive]: 'Inactivos',
  [Subsections.PaymentSchedule]: 'Calendario de pagos',
  [Subsections.PaymentVerification]: 'Verificación de pagos',
  [Subsections.Permissions]: 'Permisos',
  [Subsections.Products]: 'Productos',
  [Subsections.Users]: 'Usuario',
  [Subsections.Waitlist]: 'Lista de espera',
  [Subsections.QRScan]: 'Administración de Códigos QR',
  [Capabilities.Add]: 'Agregar',
  [Capabilities.AddCirculationCard]: 'Agregar tarjeta de circulación',
  [Capabilities.AddEarnings]: 'Agregar ganancias',
  [Capabilities.AddGuarantee]: 'Agregar datos de obligado solidario',
  [Capabilities.AddHolding]: 'Agregar tenencia',
  [Capabilities.AddPlate]: 'Agregar placas',
  [Capabilities.AddPolicy]: 'Agregar póliza',
  [Capabilities.ApproveDocument]: 'Aprobar documento',
  [Capabilities.AssignDriver]: 'Asignar conductor',
  [Capabilities.AssignPermissions]: 'Asignar Permisos',
  [Capabilities.ChangeStatus]: 'Cambiar status',
  [Capabilities.CreateInvoice]: 'Crear factura',
  [Capabilities.CreateReceipt]: 'Crear recibo',
  [Capabilities.DeclineDocument]: 'Rechazar documento',
  [Capabilities.Delete]: 'Eliminar',
  [Capabilities.Edit]: 'Editar',
  [Capabilities.EditCirculationCard]: 'Editar tarjeta de circulación',
  [Capabilities.EditDriverProfile]: 'Editar perfil de conductor',
  [Capabilities.EditEarnings]: 'Editar ganancias',
  [Capabilities.EditGuarantee]: 'Editar datos de obligado solidario',
  [Capabilities.EditHolding]: 'Editar tenencia',
  [Capabilities.EditPlate]: 'Editar placas',
  [Capabilities.EditPolicy]: 'Editar póliza',
  [Capabilities.EditProfile]: 'Editar perfil',
  [Capabilities.EditVehicle]: 'Editar vehículo',
  [Capabilities.GenerateContract]: 'Generar contratos',
  [Capabilities.MakeHomeVisitor]: 'Hacer visitador',
  [Capabilities.RegenarateContract]: 'Regenerar contratos',
  [Capabilities.ReplaceDocument]: 'Reemplazar documento',
  [Capabilities.RequestPayment]: 'Request Payment',
  [Capabilities.SendContractToSignature]: 'Enviar contrato a firma',
  [Capabilities.ToRecovery]: 'Enviar a recuperación',
  [Capabilities.UploadDocument]: 'Subir documento',
  [Capabilities.View]: 'Ver',
  [Capabilities.ViewAll]: 'View All',
  [Capabilities.ViewContractDetails]: 'Ver detalles de contrato',
  [Capabilities.ViewDriverProfile]: 'Ver perfil de conductor',
  [Capabilities.ReturnStep]: 'Regresar paso',
  [Capabilities.Search]: 'Buscar',
  [Capabilities.InPreparation]: 'En preparación',
  [Capabilities.Stock]: 'Stock',
  [Capabilities.Assigned]: 'Asignados',
  [Capabilities.Delivered]: 'Delivered',
  [Capabilities.Insurance]: 'Seguro',
  [Capabilities.Workshop]: 'Taller',
  [Capabilities.Revision]: 'Revisión',
  [Capabilities.Legal]: 'Legal',
  [Capabilities.Collection]: 'Cobranza',
  [Capabilities.Withdrawn]: 'Baja',
  [Capabilities.Sold]: 'Vendido',
  [Capabilities.Adendum]: 'Addendum',
  [Capabilities.Utilitary]: 'Utilitarios',
  [Capabilities.MyTeam]: 'Mi equipo',
  [Capabilities.AllTeam]: 'Todos los equipos',
  [Capabilities.OcnSignature]: 'OCN Firma',
  [Capabilities.EditGPS]: 'Editar GPS',
  [Capabilities.ViewMyCalendar]: 'Ver Mi Calenderio',
  [Capabilities.ViewAllCalendar]: 'Ver All Calenderio',
  [Capabilities.EditMyCalendar]: 'Editar Mi Calenderio',
  [Capabilities.EditAllCalendar]: 'Editar All Calenderio',
  [Subsections.Leads]: 'Mis Solicitudes',
  [Subsections.AssignedLeads]: 'Asignación de solicitudes',
  [Capabilities.TerminateContract]: 'Proceso de rescisión del contrato',
};

export enum PhysicalVehicleStatus {
  AWAITING_RECEIPT = 'AWAITING_RECEIPT',
  RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP = 'RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP',
  AVAILABLE_IN_STOCK = 'AVAILABLE_IN_STOCK',
  VEHICLE_TO_BE_REPAIRED = 'VEHICLE_TO_BE_REPAIRED',
  COLLECTED_FROM_STOCK = 'COLLECTED_FROM_STOCK',
  DELIVERED_TO_CUSTOMER = 'DELIVERED_TO_CUSTOMER',

  // New statuses for vendor workshop flow
  IN_TRANSIT_TO_VENDOR_WORKSHOP = 'IN_TRANSIT_TO_VENDOR_WORKSHOP',
  RECEIVED_BY_VENDOR_WORKSHOP = 'RECEIVED_BY_VENDOR_WORKSHOP',
  UNDER_REPAIR_AT_VENDOR_WORKSHOP = 'UNDER_REPAIR_AT_VENDOR_WORKSHOP',
  REPAIR_COMPLETE_BY_VENDOR = 'REPAIR_COMPLETE_BY_VENDOR',
  COLLECTED_BY_CUSTOMER = 'COLLECTED_BY_CUSTOMER',
  RETURNED_TO_OCN_AND_REAVAILABLE = 'RETURNED_TO_OCN_AND_REAVAILABLE',
  RETURNED_AND_AVAILABLE_FOR_REDELIVERY = 'RETURNED_AND_AVAILABLE_FOR_REDELIVERY',
  COLLECTED_FROM_STOCK_TO_BE_REDELIVERED = 'COLLECTED_FROM_STOCK_TO_BE_REDELIVERED',
  REDELIVERED_TO_CUSTOMER = 'REDELIVERED_TO_CUSTOMER',
  COLLECTED_FROM_VENDOR_BY_OCN_AGENT = 'COLLECTED_FROM_VENDOR_BY_OCN_AGENT',

  // Repossession Flow Statuses
  REPOSSESSION_COMPLETE = 'REPOSSESSION_COMPLETE',
  RETURNED_TO_OCN_WAREHOUSE = 'RETURNED_TO_OCN_WAREHOUSE',
}

// User-defined physical status labels and values
export const PHYSICAL_STATUS_MAP: Record<string, string> = {
  [PhysicalVehicleStatus.AWAITING_RECEIPT]: 'Awaiting Receipt',
  [PhysicalVehicleStatus.RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP]:
    'Received at OCN Warehouse from Dealership',
  [PhysicalVehicleStatus.AVAILABLE_IN_STOCK]: 'Vehicle Available in Stock',
  [PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED]: 'Vehicle to be repaired',
  [PhysicalVehicleStatus.COLLECTED_FROM_STOCK]: 'Vehicle Collected from Stock For Delivery to Customer',
  [PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER]: 'Vehicle Delivered to Customer',
  [PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP]: 'Vehicle In Transit to Vendor Workshop for Repairs',
  [PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP]: 'Vehicle Received by Vendor Workshop',
  [PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP]: 'Vehicle Under Repair at Vendor Workshop',
  [PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR]: 'Repair Complete by Vendor',
  [PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT]: 'Vehicle Collected from Vendor by OCN Agent',
  [PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER]: 'Vehicle Collected by Customer',
  [PhysicalVehicleStatus.RETURNED_TO_OCN_AND_REAVAILABLE]:
    'Vehicle Returned to OCN Warehouse and Re-available in Stock',
  [PhysicalVehicleStatus.RETURNED_AND_AVAILABLE_FOR_REDELIVERY]:
    'Vehicle Returned to OCN Warehouse to be Redelivered to Customer',
  [PhysicalVehicleStatus.COLLECTED_FROM_STOCK_TO_BE_REDELIVERED]:
    'Vehicle Collected from Stock to be Redelivered to Customer',
  [PhysicalVehicleStatus.REDELIVERED_TO_CUSTOMER]: 'Vehicle Redelivered to Customer',
  [PhysicalVehicleStatus.REPOSSESSION_COMPLETE]: 'Vehicle Repossession Complete',
  [PhysicalVehicleStatus.RETURNED_TO_OCN_WAREHOUSE]: 'Vehicle Returned to OCN Warehouse',
};

export const PHYSICAL_STATUS_MAP_ES: Record<string, string> = {
  [PhysicalVehicleStatus.AWAITING_RECEIPT]: 'Pendiente de Recepción',
  [PhysicalVehicleStatus.RECEIVED_AT_OCN_WAREHOUSE_FROM_DEALERSHIP]:
    'Recibido en Almacén OCN desde Concesionario',
  [PhysicalVehicleStatus.AVAILABLE_IN_STOCK]: 'Vehículo Disponible en Stock',
  [PhysicalVehicleStatus.VEHICLE_TO_BE_REPAIRED]: 'Vehículo para Reparar',
  [PhysicalVehicleStatus.COLLECTED_FROM_STOCK]: 'Vehículo Retirado de Stock para Entrega al Cliente',
  [PhysicalVehicleStatus.DELIVERED_TO_CUSTOMER]: 'Vehículo Entregado al Cliente',
  [PhysicalVehicleStatus.IN_TRANSIT_TO_VENDOR_WORKSHOP]:
    'Vehículo en Tránsito al Taller del Proveedor para Reparaciones',
  [PhysicalVehicleStatus.RECEIVED_BY_VENDOR_WORKSHOP]: 'Vehículo Recibido por el Taller del Proveedor',
  [PhysicalVehicleStatus.UNDER_REPAIR_AT_VENDOR_WORKSHOP]:
    'Vehículo en Reparación en el Taller del Proveedor',
  [PhysicalVehicleStatus.REPAIR_COMPLETE_BY_VENDOR]: 'Reparación Completada por el Proveedor',
  [PhysicalVehicleStatus.COLLECTED_FROM_VENDOR_BY_OCN_AGENT]:
    'Vehículo Recogido del Proveedor por Agente OCN',
  [PhysicalVehicleStatus.COLLECTED_BY_CUSTOMER]: 'Vehículo Recogido por el Cliente',
  [PhysicalVehicleStatus.RETURNED_TO_OCN_AND_REAVAILABLE]:
    'Vehículo Devuelto al Almacén OCN y Re-disponible en Stock',
  [PhysicalVehicleStatus.RETURNED_AND_AVAILABLE_FOR_REDELIVERY]:
    'Vehículo Devuelto al Almacén OCN para Ser Re-entregado al Cliente',
  [PhysicalVehicleStatus.COLLECTED_FROM_STOCK_TO_BE_REDELIVERED]:
    'Vehículo Retirado de Stock para Ser Re-entregado al Cliente',
  [PhysicalVehicleStatus.REDELIVERED_TO_CUSTOMER]: 'Vehículo Re-entregado al Cliente',
  [PhysicalVehicleStatus.REPOSSESSION_COMPLETE]: 'Reposesión Completada',
  [PhysicalVehicleStatus.RETURNED_TO_OCN_WAREHOUSE]: 'Vehículo Devuelto al Almacén OCN',
};

export enum BlockLeadAssignationReason {
  Vacaciones = 'Vacaciones',
  BajaPorEnfermedad = 'Baja por enfermedad',
  Ausente = 'Ausente',
  PermisoDeEmergencia = 'Permiso de emergencia',
  AsignaciónEspecial = 'Asignación Especial',
  Rendimiento = 'Rendimiento',
  Terminación = 'Terminación',
}

export enum ReassignLeadsReason {
  SystemOverrideByLead = 'Anulación del sistema por el líder',
  SalesAssociateNoLongerWithTheCompany = 'El asociado de ventas ya no trabaja con la empresa',
  SickLeave = 'Incapacidad',
  Vacations = 'Vacaciones',
  Performance = 'Desempeño',
  EmergencyLeave = 'Permiso urgente',
  SpecialAssignment = 'Asignación especial',
  MissingFollowUpFromAssociate = 'El asociado no ha dado seguimiento',
  IncorrectlyAssignedByTheSystem = 'Asignado incorrectamente por el sistema',
}

export const VENDOR_WORKSHOPS = [
  {
    region: 'cdmx',
    label: 'CDMX/EDOMEX',
    workshops: [
      {
        name: 'Ruedda',
        address: 'address',
        phone: '',
      },
    ],
  },
  {
    region: 'mty',
    label: 'Monterrey',
    workshops: [
      {
        name: 'Ruedda',
        address: 'address',
        phone: '',
      },
    ],
  },
];

export type Agent = {
  _id: string;
  email: string;
  name: string;
  city: string;
  isVerified: boolean;
  role: string;
  area: string;
};

export type AgentOption = {
  value: string;
  label: string;
};
