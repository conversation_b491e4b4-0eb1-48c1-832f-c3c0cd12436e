'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import InputFile from '@/components/Inputs/InputFile';
import CustomInput from '@/components/Inputs/CustomInput';
import { useState } from 'react';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { dischargeVehicleSchema } from '@/validatorSchemas/dischargeVehicleSchema';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import InputDate from '@/components/Inputs/InputDate';
import { useOpenDischargeModal } from '@/zustand/modalStates';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
// import { FormikHelpers } from 'formik';

// type Helpers = FormikHelpers<{
//   reason: {
//     label: string;
//     value: string;
//   };
//   comments: string;
//   platesDischargedDoc: string;
//   dictamenDoc: string;
//   reportDoc: string;
// }>;

const initialValues = {
  reason: '',
  comments: '',
  platesDischargedDoc: '',
  dictamenDoc: '',
  reportDoc: '',
  date: '',
};

type InitialValuesonSubmit = typeof initialValues & { reason: { label: string; value: string } };

export default function DischargeModal() {
  const updateSideData = useUpdateSideData();
  const dischargeModal = useOpenDischargeModal();
  const [categorySelected, setCategorySelected] = useState('');
  const [optionSelected, setOptionSelected] = useState('');
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  const categoryOptions = [
    { value: 'total-loss', label: country === 'Mexico' ? 'Pérdida total' : 'Total loss' },
    { value: 'operational-loss', label: country === 'Mexico' ? 'Pérdida operacional' : 'Operational loss' },
  ];

  const options = [
    { value: 'Robo', label: country === 'Mexico' ? 'Robo' : 'Theft' },
    { value: 'Accidente', label: country === 'Mexico' ? 'Accidente' : 'Accident' },
  ];

  const [nameFiles, setNameFiles] = useState({
    reportDoc: '',
    dictamenDoc: '',
    platesDischargedDoc: '',
  });

  const handleSetNames = (name: string, value: string) => {
    setNameFiles({
      ...nameFiles,
      [name]: value,
    });
  };

  const { id } = useParams();
  const router = useRouter();
  const { user } = useCurrentUser();
  const toast = useToast();

  const onSubmit = async (values: typeof initialValues) => {
    const values2 = values as InitialValuesonSubmit;

    const data = {
      ...values,
      reason: optionSelected
        ? values2.reason.label
        : country === 'Mexico'
        ? 'Pérdida operacional'
        : 'Operational loss',
      subCategory: categorySelected,
    };
    try {
      const response = await axios.patch(`${URL_API}/stock/sendDischarged/${id}`, data, {
        headers: {
          'Content-type': 'multipart/form-data',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      toast({
        title: response.data.message,
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      await updateSideData(user);
      router.refresh();
      router.push(
        window.location.pathname.includes('active') || window.location.pathname.includes('inactive')
          ? `/dashboard/flotilla/inactive/withdrawn/${id}${country ? `?country=${encodeURI(country)}` : ''}`
          : `/dashboard/flotilla/bajas/${id}${country ? `?country=${encodeURI(country)}` : ''}`
      );
    } catch (error: any) {
      toast({
        title: error.response.data.message,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    } finally {
      // helpers.setSubmitting(false);
      dischargeModal.onClose();
    }
  };

  const confirmSubmit = async (values: typeof initialValues) => {
    return Swal.fire({
      title: '¿Estás seguro?',
      text: 'Una vez que se reporte la baja, no se podrá revertir',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Si, reportar',
      cancelButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  return (
    <ModalContainer title="Reportar baja" onClose={dischargeModal.onClose} classAnimation="animate__fadeIn">
      <FormikContainer
        onSubmit={confirmSubmit}
        onClose={dischargeModal.onClose}
        initialValues={initialValues}
        footerClassName="flex gap-3 pt-[20px] justify-center"
        validatorSchema={dischargeVehicleSchema}
      >
        <div className="flex flex-col gap-[20px]">
          <SelectInput
            label="Motivo"
            name="category"
            options={categoryOptions}
            onChange={(option) => {
              setCategorySelected(option.value);
              if (option.value === 'operational-loss') {
                setOptionSelected('');
              }
            }}
          />
          {categorySelected === 'total-loss' ? (
            <>
              <SelectInput
                label={`Razon de pérdida total`}
                name="reason"
                options={options}
                onChange={(option) => {
                  setOptionSelected(option.value);
                }}
              />
              {optionSelected === 'Robo' && (
                <InputFile
                  name="reportDoc"
                  label="Denuncia del robo"
                  accept="pdf"
                  nameFile={nameFiles.reportDoc}
                  handleSetName={handleSetNames}
                  buttonText="Subir documento"
                  placeholder="No mayor a 2mb"
                />
              )}
              {optionSelected === 'Accidente' && (
                <InputFile
                  name="dictamenDoc"
                  label="Dictamen de perdida total"
                  accept="pdf"
                  nameFile={nameFiles.dictamenDoc}
                  handleSetName={handleSetNames}
                  buttonText="Subir documento"
                  placeholder="No mayor a 2mb"
                />
              )}
              <InputFile
                name="platesDischargedDoc"
                label="Baja de placas"
                accept="pdf"
                nameFile={nameFiles.platesDischargedDoc}
                handleSetName={handleSetNames}
                buttonText="Subir documento"
                placeholder="No mayor a 2mb"
              />
              <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
            </>
          ) : categorySelected === 'operational-loss' ? (
            <>
              <InputFile
                name="platesDischargedDoc"
                label="Baja de placas"
                accept="pdf"
                nameFile={nameFiles.platesDischargedDoc}
                handleSetName={handleSetNames}
                buttonText="Subir documento"
                placeholder="No mayor a 2mb"
              />
              <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
            </>
          ) : null}
          <InputDate name="date" label="Fecha de la baja" />
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
