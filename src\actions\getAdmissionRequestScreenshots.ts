import { URL_API } from '@/constants';
import getCurrentUser from './getCurrentUser';

interface GetAdmissionRequestProps {
  id: string;
}

export default async function getAdmissionRequestScreenshots({ id }: GetAdmissionRequestProps) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const res = await fetch(`${URL_API}/admission/requests/screenshots/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const response = await res.json();

    return response;
  } catch (error) {
    console.error(error);
    return null;
  }
}
