import { useField } from 'formik';
import { RadioGroup as ChakraRadioGroup, RadioGroupProps as ChakraRadioGroupProps } from '@chakra-ui/react';
import * as React from 'react';

type Props = ChakraRadioGroupProps;

const RadioGroup = ({ name = '', children, ...props }: Props) => {
  const [field, , { setValue }] = useField({ name: name, value: props.value });

  const namedChildren = React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) return null;

    return React.cloneElement(child as React.ReactElement<any>, {
      name,
    });
  });

  return (
    <ChakraRadioGroup {...props} {...field} onChange={setValue}>
      {namedChildren}
    </ChakraRadioGroup>
  );
};

export default RadioGroup;
