import { StockService } from '@/actions/getStockServices';
import { create } from 'zustand';

interface Modals {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
}

export const createModalState = () =>
  create<Modals>((set) => ({
    isOpen: false,
    onOpen: () => set(() => ({ isOpen: true })),
    onClose: () => set(() => ({ isOpen: false })),
  }));

export const useOpenDetailDischargeModal = createModalState();

export const useOpenDischargeModal = createModalState();

export const useOpenChangeStatusModal = createModalState();

export const useOpenOverHaulingModal = createModalState();
export const useOpenFinishOverHaulingModal = createModalState();

export const useOpenFinishTaller = createModalState();

export const useAdendumGenerator = createModalState();

export const useReturnStepModal = createModalState();

export const useReturnChangeRegion = createModalState();

export const useCirculationCardTijModal = createModalState();

export const useAppointmentModal = createModalState();

// export const useOpenVendorServiceModal = createModalState();

interface ServiceModalProps extends Modals {
  serviceData: StockService;
  setServiceData: (data: StockService) => void;
}

export const useOpenServiceDetail = create<ServiceModalProps>((set) => ({
  isOpen: false,
  serviceData: {} as StockService,
  setServiceData: (data: StockService) => set(() => ({ serviceData: data })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));

interface VendorServiceModalProps extends Modals {
  serviceData: any;
  setServiceData: (data: any) => void;
}

export const useOpenVendorServiceModal = create<VendorServiceModalProps>((set) => ({
  isOpen: false,
  serviceData: {} as any,
  setServiceData: (data: any) => set(() => ({ serviceData: data })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));

interface GestoresModalProps extends Modals {
  gestorData: any;
  setGestorData: (data: any) => void;
}

export const useOpenGestoresModal = create<GestoresModalProps>((set) => ({
  isOpen: false,
  gestorData: {} as any,
  setGestorData: (data: any) => set(() => ({ gestorData: data })),
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
}));

interface AppointmentDetailModalProps extends Modals {
  appointmentData: any;
  setAppointmentData: (data: any) => void;
}

export const useOpenAppointmentDetailModal = create<AppointmentDetailModalProps>((set) => ({
  isOpen: false,
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
  appointmentData: {} as any,
  setAppointmentData: (data: any) => set(() => ({ appointmentData: data })),
}));
