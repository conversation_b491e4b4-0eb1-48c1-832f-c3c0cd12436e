'use server';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';

export async function validateTaxInformation(clientId: string, facturapiId?: string | null) {
  try {
    const url = new URL(`${PAYMENTS_API_URL}/clients/${clientId}/validate-tax-information`);

    if (facturapiId) {
      url.searchParams.append('facturapiId', facturapiId);
    }

    const res = await axios.get(`${url}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });

    console.log('data', res.data);
    return {
      success: res.data.is_valid,
      message: res.data.data?.message || 'La información fiscal es correcta',
      errors: res.data?.errors,
    };
  } catch (error: any) {
    console.log(error.response.data);
    return {
      success: false,
      message: error.response?.data?.message || 'La información fiscal no es correcta',
      errors: error.response?.data?.errors,
    };
  }
}
