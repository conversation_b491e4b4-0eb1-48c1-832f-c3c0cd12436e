'use client';

import { Countries, CountriesOptions } from '@/constants';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { useContext, createContext, useState, ReactNode, useEffect } from 'react';
import { deleteCookie } from 'cookies-next';
type Country = { value: Countries; label: Countries };

interface ICountriesContext {
  country: Country;
  handleCountryChange: (option: Country) => void;
  isCountryUSA: boolean;
  isCountryMexico: boolean;
}

const CountryContext = createContext(null as unknown as ICountriesContext);

export const CountryProvider = ({ children }: { children: ReactNode }) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const countryValue = searchParams.get('country');
  let selectedCountry = CountriesOptions[Countries.Mexico];

  if (countryValue !== null) {
    // Case-insensitive matching for country parameter
    const countryKey = Object.keys(Countries).find(
      (key) => key.toLowerCase() === countryValue.toLowerCase()
    ) as Countries;

    if (countryKey) {
      selectedCountry = CountriesOptions[countryKey];
    } else if (countryValue.toLowerCase() === Countries.Mexico.toLowerCase()) {
      selectedCountry = CountriesOptions[Countries.Mexico];
    } else if (countryValue.toLowerCase() === Countries['United States'].toLowerCase()) {
      selectedCountry = CountriesOptions[Countries['United States']];
    }
  }

  const [country, setCountry] = useState(selectedCountry);
  const handleCountryChange = (option: Country) => {
    setCountry(option);
    deleteCookie('filters');
  };

  useEffect(() => {
    if (country) {
      const params = new URLSearchParams(searchParams);
      params.set('country', country.value);
      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    }
  }, [country, pathname, router, searchParams]);

  return (
    <CountryContext.Provider
      value={{
        country,
        handleCountryChange,
        isCountryUSA: country?.value === Countries['United States'],
        isCountryMexico: country?.value === Countries.Mexico,
      }}
    >
      {children}
    </CountryContext.Provider>
  );
};

export const useCountry = () => {
  return useContext(CountryContext);
};
