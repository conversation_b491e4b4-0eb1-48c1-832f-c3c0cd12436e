import { Dispatch, SetStateAction } from 'react';
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuList, Table, Tbody, Td, Th, Thead, Tr } from '@chakra-ui/react';
import { LocalStorageData, PDFPayments, TAllPayments, WeeksChanged } from '../client';
import { BsThreeDots } from 'react-icons/bs';

export interface AllDataPayment {
  day: string;
  number: string;
  weeklyRent: number;
  index: number;
}

export interface DataPayment {
  allPayments: TAllPayments[];
  allDataPayments: AllDataPayment[];
  allPDFPayments: PDFPayments;
  weeksChanged: WeeksChanged;
  weeklyRent: number;
  weeklyRentCalculate: number;
}

interface PaymentsTableProps {
  data: DataPayment;
  localData: LocalStorageData;
  setChange: Dispatch<SetStateAction<DataPayment>>;
  setStateChange: Dispatch<SetStateAction<boolean>>;
  stateChange: boolean;
  // columns: ColumnDef[];
}

export default function PaymentsTable({
  data,
  localData,
  setChange,
  setStateChange,
  stateChange,
}: PaymentsTableProps) {
  const weeks = data.weeksChanged;
  const options = [0, data.weeklyRent, data.weeklyRentCalculate];

  const firstIndex = weeks[0].index;

  const isNearBegging = firstIndex - 5 <= 0;

  const from = isNearBegging ? 0 : firstIndex - 5;

  // console.log('allDataPayments', data.allDataPayments.length);
  const shortPayments =
    localData.method === 'divide-weeks'
      ? data.allDataPayments.slice(from, weeks[weeks.length - 1].index + 6)
      : data.allDataPayments;

  return (
    <div>
      <h1 className="text-black font-bold text-[24px]">Editar pagos de adendum</h1>
      <Table className="bg-white w-3/4 rounded " variant="striped" colorScheme="gray">
        <Thead>
          <Tr>
            <Th>Numero de pago</Th>
            <Th>Dia</Th>
            <Th>Renta semanal</Th>
            <Th>Acciones</Th>
          </Tr>
        </Thead>
        <Tbody>
          {shortPayments.map((payment, index) => (
            <Tr key={index}>
              <Td className="pr-1 py-1 ">{index + 1}</Td>
              <Td className="pr-1 py-1">{payment.day}</Td>
              <Td className="pr-1 py-1">{payment.weeklyRent.toFixed(2)}</Td>
              <Td className="pr-1 py-1">
                {/* <button className="bg-blue-500 text-white p-1 rounded">Editar</button> */}
                <Menu>
                  <MenuButton /* rightIcon={<ChevronDownIcon />} */>
                    {/* <p>Actions</p> */}
                    <BsThreeDots />
                  </MenuButton>
                  <MenuList>
                    {options
                      .filter((option) => option !== payment.weeklyRent)
                      .map((option, i) => (
                        <MenuItem
                          key={i}
                          onClick={() => {
                            setStateChange(!stateChange);
                            const newPDFPayments = data.allDataPayments.map((p) => {
                              if (p.number === payment.number) {
                                return { day: payment.day, weeklyRent: option };
                              }
                              return p;
                            });

                            const newDataPayments = data.allDataPayments.map((p) => {
                              if (p.number === payment.number) {
                                return { ...p, weeklyRent: option };
                              }
                              return p;
                            });

                            const weeksChanged = newDataPayments.filter((p) => {
                              // const lastPayment = p.index - 1;
                              // console.log('lastPayment', lastPayment, p.index);
                              if (
                                p.weeklyRent !== options[1] /* || */
                                // (p.index > 0 && data.allDataPayments[lastPayment].weeklyRent === 0)
                              )
                                return true;
                              // if(data.allDataPayments[lastPayment].weeklyRent === 0) return true;
                              return false;
                            });
                            // console.log('weeksChanged', weeksChanged);
                            setChange((prev) => {
                              return {
                                ...prev,
                                weeksChanged,
                                allDataPayments: newDataPayments,
                                allPDFPayments: newPDFPayments,
                              };
                            });
                          }}
                        >
                          Cambiar a: {option.toFixed(2)}
                        </MenuItem>
                      ))}
                  </MenuList>
                </Menu>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </div>
  );
}
