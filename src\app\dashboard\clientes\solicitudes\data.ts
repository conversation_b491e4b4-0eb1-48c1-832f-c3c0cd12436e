import {
  Gig<PERSON><PERSON>form,
  PalencaAccountStatus,
  AdmissionRequestStatus,
  RequestDocumentStatus,
  AdmissionRequestDocumentType,
  MediaType,
  ResidentOwnershipStatus,
  HomeVisitStatus,
  RiskCategory,
  EarningsAnalysisStatus,
  AdmissionRequestDocumentTypeUS,
  AdmissionRequestAdditionalDocumentType,
} from './enums';

export const RequestStatusTranslations = {
  [AdmissionRequestStatus.created]: 'Creada',
  [AdmissionRequestStatus.earnings_analysis]: 'Análisis de ingresos',
  [AdmissionRequestStatus.documents_analysis]: 'Análisis de documentos',
  [AdmissionRequestStatus.judicial_analysis]: 'Análisis judicial',
  [AdmissionRequestStatus.risk_analysis]: 'Aná<PERSON><PERSON> de riesgo',
  [AdmissionRequestStatus.home_visit]: 'Visita domiciliaria',
  [AdmissionRequestStatus.social_analysis]: 'Análisis social',
  [AdmissionRequestStatus.approved]: 'Aprobada',
  [AdmissionRequestStatus.rejected]: 'Rechazada',
};

export const RequestStatusTranslationsUS = {
  [AdmissionRequestStatus.created]: 'Created',
  [AdmissionRequestStatus.earnings_analysis]: 'Revenue analysis',
  [AdmissionRequestStatus.documents_analysis]: 'Document analysis',
  [AdmissionRequestStatus.judicial_analysis]: 'Judicial analysis',
  [AdmissionRequestStatus.risk_analysis]: 'Risk analysis',
  [AdmissionRequestStatus.home_visit]: 'Home visit',
  [AdmissionRequestStatus.social_analysis]: 'Social analysis',
  [AdmissionRequestStatus.approved]: 'Approved',
  [AdmissionRequestStatus.rejected]: 'Rejected',
};

export const PalencaAccountStatusTranslations = {
  [PalencaAccountStatus.success]: 'Completado',
  [PalencaAccountStatus.incomplete]: 'Incompleto',
  [PalencaAccountStatus.pending]: 'Pendiente',
  [PalencaAccountStatus.retry]: 'Reintentar',
  [PalencaAccountStatus.error]: 'Error',
  [PalencaAccountStatus.failure]: 'Failed',
};

export const RequestDocumentStatusTranslations = {
  [RequestDocumentStatus.pending]: 'Pendiente',
  [RequestDocumentStatus.approved]: 'Preaprobado',
  [RequestDocumentStatus.pending_review]: 'Pendiente de revisión',
  [RequestDocumentStatus.rejected]: 'Rechazado',
};

export const RequestDocumentStatusTranslationsUS = {
  [RequestDocumentStatus.pending]: 'Pending',
  [RequestDocumentStatus.approved]: 'Pre-approved',
  [RequestDocumentStatus.pending_review]: 'Pending review',
  [RequestDocumentStatus.rejected]: 'Rejected',
};

export const RequestDocumentTypeTranslations = {
  [AdmissionRequestAdditionalDocumentType.proof_of_tax_situation]: 'Constancia de situación fiscal',
  [AdmissionRequestDocumentType.identity_card_front]: 'Identificación (frente)',
  [AdmissionRequestDocumentType.identity_card_back]: 'Identificación (reverso)',
  [AdmissionRequestAdditionalDocumentType.drivers_license_front]: 'Licencia de conducir (frente)',
  [AdmissionRequestAdditionalDocumentType.drivers_license_back]: 'Licencia de conducir (reverso)',
  [AdmissionRequestDocumentType.proof_of_address]: 'Comprobante de domicilio',
  [AdmissionRequestDocumentType.bank_statement_month_1]: 'Estado de cuenta bancario (1)',
  [AdmissionRequestDocumentType.bank_statement_month_2]: 'Estado de cuenta bancario (2)',
  [AdmissionRequestDocumentType.bank_statement_month_3]: 'Estado de cuenta bancario (3)',
  [AdmissionRequestAdditionalDocumentType.selfie_photo]: 'Foto Selfie',
  [AdmissionRequestAdditionalDocumentType.garage_photo]: 'Foto Garage',
  [AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front]:
    'INE del Obligado Solidario (Frontal)',
  [AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back]:
    'INE del Obligado Solidario (Reverso)',
  [AdmissionRequestAdditionalDocumentType.curp]: 'CURP',
};

export const RequestDocumentTypeTranslationsUS = {
  [AdmissionRequestDocumentTypeUS.drivers_license_front]: `Driver's license (front)`,
  [AdmissionRequestDocumentTypeUS.drivers_license_back]: `Driver's license (reverse)`,
  [AdmissionRequestDocumentTypeUS.proof_of_address]: 'Proof of address',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_1]: 'Bank statement (1)',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_2]: 'Bank statement (2)',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_3]: 'Bank statement (3)',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_4]: 'Bank statement (4)',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_5]: 'Bank statement (5)',
  [AdmissionRequestDocumentTypeUS.bank_statement_month_6]: 'Bank statement (6)',
  [AdmissionRequestAdditionalDocumentType.garage_photo]: 'Garage Photo',
};

type HomeVisitStatusTranslationsType = {
  [key in HomeVisitStatus]?: string;
};

export const HomeVisitStatusTranslations: HomeVisitStatusTranslationsType = {
  [HomeVisitStatus.pending]: 'Pendiente',
  [HomeVisitStatus.approved]: 'Aprobada',
  [HomeVisitStatus.rejected]: 'Rechazada',
};

type ResidentOwnershipStatusTranslationsType = {
  [key in ResidentOwnershipStatus]?: string;
};

export const ResidentOwnershipStatusTranslations: ResidentOwnershipStatusTranslationsType = {
  [ResidentOwnershipStatus.rented]: 'Rentada',
  [ResidentOwnershipStatus.owned]: 'Propia',
};

export const GigPlatformCatalog = [
  {
    platform: GigPlatform.uber,
    name: 'Uber',
    color: '#000000',
  },
  {
    platform: GigPlatform.didi,
    name: 'Didi',
    color: '#000000',
  },
  {
    platform: GigPlatform.didi_food,
    name: 'Didi Food',
    color: '#000000',
  },
  {
    platform: GigPlatform.cornershop,
    name: 'Cornershop',
    color: '#000000',
  },
  {
    platform: GigPlatform.indriver,
    name: 'InDriver',
    color: '#000000',
  },
  {
    platform: GigPlatform.uber_eats,
    name: 'Uber Eats',
    color: '#000000',
  },
  {
    platform: GigPlatform.rappi,
    name: 'Rappi',
    color: '#000000',
  },
  {
    platform: GigPlatform.lyft,
    name: 'Lyft',
    color: '#000000',
  },
  {
    platform: GigPlatform.other,
    name: 'Other',
    color: '#000000',
  },
];

export const MediaTypeCatalog = {
  [AdmissionRequestAdditionalDocumentType.proof_of_tax_situation]: MediaType.proof_of_tax_situation,
  [AdmissionRequestDocumentType.identity_card_front]: MediaType.identity_card_front,
  [AdmissionRequestDocumentType.identity_card_back]: MediaType.identity_card_back,
  [AdmissionRequestAdditionalDocumentType.drivers_license_front]: MediaType.drivers_license_front,
  [AdmissionRequestAdditionalDocumentType.drivers_license_back]: MediaType.drivers_license_back,
  [AdmissionRequestDocumentType.proof_of_address]: MediaType.proof_of_address,
  [AdmissionRequestDocumentType.bank_statement_month_1]: MediaType.bank_statement,
  [AdmissionRequestDocumentType.bank_statement_month_2]: MediaType.bank_statement,
  [AdmissionRequestDocumentType.bank_statement_month_3]: MediaType.bank_statement,
  [AdmissionRequestAdditionalDocumentType.selfie_photo]: MediaType.selfie_photo,
  [AdmissionRequestAdditionalDocumentType.garage_photo]: MediaType.garage_photo,
  [AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_front]:
    MediaType.identity_card_front,
  [AdmissionRequestAdditionalDocumentType.solidarity_obligor_identity_card_back]:
    MediaType.identity_card_back,
  [AdmissionRequestAdditionalDocumentType.curp]: MediaType.curp,
};

export const findPlatform = (platform: GigPlatform) => {
  const platformEntry = GigPlatformCatalog.find((entry) => entry.platform === platform);

  return platformEntry ?? { platform: '', name: 'Desconocida', color: '#00000' };
};

type FileUploadAcceptTypes = {
  [key in MediaType]?: string;
};
export const FileUploadAcceptByMediaTypeCatalog: FileUploadAcceptTypes = {
  [MediaType.proof_of_tax_situation]: 'application/pdf',
  [MediaType.identity_card_front]: 'image/*',
  [MediaType.identity_card_back]: 'image/*',
  [MediaType.drivers_license_front]: 'image/*',
  [MediaType.drivers_license_back]: 'image/*',
  [MediaType.proof_of_address]: 'image/*,application/pdf',
  [MediaType.bank_statement]: 'application/pdf',
  [MediaType.selfie_photo]: 'image/*',
  [MediaType.garage_photo]: 'image/*',
  [MediaType.curp]: 'application/pdf',
};

type FileUploadMaxSize = {
  [key in MediaType]?: number;
};

export const FileUploadMaxSizeByMediaTypeCatalog: FileUploadMaxSize = {
  [MediaType.proof_of_tax_situation]: 5,
  [MediaType.identity_card_front]: 5,
  [MediaType.identity_card_back]: 5,
  [MediaType.drivers_license_front]: 5,
  [MediaType.drivers_license_back]: 5,
  [MediaType.proof_of_address]: 5,
  [MediaType.bank_statement]: 5,
  [MediaType.curp]: 5,
};

type FileUploadTotalFiles = {
  [key in MediaType]?: number;
};

export const FileUploadTotalFilesByMediaTypeCatalog: FileUploadTotalFiles = {
  [MediaType.proof_of_tax_situation]: 1,
  [MediaType.identity_card_front]: 1,
  [MediaType.identity_card_back]: 1,
  [MediaType.drivers_license_front]: 1,
  [MediaType.drivers_license_back]: 1,
  [MediaType.proof_of_address]: 1,
  [MediaType.bank_statement]: 1,
  [MediaType.curp]: 1,
};

type RiskCategoryTranslationsType = {
  category: RiskCategory;
  name: string;
  color: string;
};

export const RiskCategoryTranslations: RiskCategoryTranslationsType[] = [
  {
    category: RiskCategory.low,
    name: 'Bajo',
    color: '#29CC4D',
  },
  {
    category: RiskCategory.medium,
    name: 'Medio',
    color: '#FFAB00',
  },
  {
    category: RiskCategory.high,
    name: 'Alto',
    color: '#E14942',
  },
];

export const ScorecardVariableNameTranslations = {
  age: 'Edad',
  gig_platforms: 'Plataformas',
  life_time_completed_trips: 'Viajes completados',
  days_since_first_trip: 'Días desde primer viaje',
  percentage_acceptance_rate: 'Tasa de aceptación',
  percentage_cancellation_rate: 'Tasa de cancelación',
  average_rating: 'Calificación promedio',
  earnings_last_12_weeks: 'Ganancias últimos 12 semanas',
  vehicle_condition: 'Condición del vehículo',
  vehicle_type: 'Tipo de vehículo',
};

export const EarningsAnalysisStatusTranslations = {
  [EarningsAnalysisStatus.pending]: 'Pendiente',
  [EarningsAnalysisStatus.approved]: 'Preaprobado',
  [EarningsAnalysisStatus.rejected]: 'Rechazado',
  [EarningsAnalysisStatus.approved_with_conditions]: 'Preaprobado con condiciones',
};
