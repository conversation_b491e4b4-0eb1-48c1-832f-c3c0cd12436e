'use client';
import { QueryLink } from '@/components/QueryLink';
import EmptyState from '@/components/EmptyState';
import {
  Card,
  CardBody,
  CardHeader,
  Flex,
  Heading,
  TableContainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Button,
  useToast,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useDisclosure,
} from '@chakra-ui/react';
import { EarningsAnalysisStatus, GigPlatform, PalencaAccountStatus } from '../enums';
import { findPlatform, PalencaAccountStatusTranslations } from '../data';
import { Capabilities, Sections, Subsections, URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { FaEllipsisH, FaDownload, FaEdit } from 'react-icons/fa';
import { useCountry } from './detail';
import { PlatformDetailDialog } from './PlatformDetailDialog';
import DownloadPopover from './DownloadPopover';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';

interface Account {
  accountId: string;
  platform: GigPlatform;
  status: PalencaAccountStatus;
}

interface RequestPalenca {
  accounts: Account[];
}

interface EarningsAnalysis {
  totalEarnings?: number | null;
  status: EarningsAnalysisStatus;
}

interface Screenshot {
  name: string;
  platform: string;
  path: string;
}

function formatStatus(status: PalencaAccountStatus) {
  const colorScheme = {
    [PalencaAccountStatus.success]: 'green',
    [PalencaAccountStatus.incomplete]: 'yellow',
    [PalencaAccountStatus.pending]: 'yellow',
    [PalencaAccountStatus.retry]: 'yellow',
    [PalencaAccountStatus.error]: 'red',
    [PalencaAccountStatus.failure]: 'red',
  }[status];

  return <Text color={`${colorScheme}.500`}>{PalencaAccountStatusTranslations[status]}</Text>;
}

function formatTotalEarnings(totalEarnings?: number | null) {
  if (totalEarnings === null || totalEarnings === undefined) {
    return '';
  }
  return `(${new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(totalEarnings)})`;
}

function formatToPercent(totalEarnings?: number | null) {
  if (totalEarnings === null || totalEarnings === undefined) {
    return '';
  }
  return `${new Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  }).format(totalEarnings)}`;
}
export default function PlatformsCard({
  palenca,
  screenshots,
  earningsAnalysis,
  requestId,
}: {
  palenca: RequestPalenca;
  screenshots: Screenshot[];
  earningsAnalysis: EarningsAnalysis;
  requestId: string;
}) {
  const toast = useToast();
  const { user } = useCurrentUser();
  function saveEarnings() {
    try {
      fetch(`${URL_API}/ocr/get-earnings-analysis/${requestId}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      setTimeout(() => {
        window.location.reload();
      }, 2500);

      toast({
        title: 'Precalificación exitosa',
        status: 'success',
      });
      setTimeout(() => {
        window.location.reload();
      }, 2500);
    } catch (error) {
      console.error(error);
      toast({
        title: 'Error al precalificar',
        status: 'error',
      });
    }
  }
  const accounts: Account[] = palenca.accounts;
  const hasAccounts = accounts.length > 0;
  const isPending = earningsAnalysis.status === EarningsAnalysisStatus.pending;
  const isRejected = earningsAnalysis.status === EarningsAnalysisStatus.rejected;
  const isApproved = earningsAnalysis.status === EarningsAnalysisStatus.approved;
  const isApprovedWithConditions =
    earningsAnalysis.status === EarningsAnalysisStatus.approved_with_conditions;
  const isPendingApproval = earningsAnalysis.status === EarningsAnalysisStatus.pending;
  const isReady = accounts.every((account) => account.status === PalencaAccountStatus.success);

  const { isCountryUSA } = useCountry();

  const headingText = isCountryUSA ? 'Revenue analysis' : 'Análisis de ingresos';
  const addPlatformbuttonText = isCountryUSA ? 'Add platform' : 'Agregar plataforma';
  const preQualifyButtonText = isCountryUSA ? 'Prequalify' : 'Precalificar';
  const preApprovedText = isCountryUSA ? 'Preapproved' : 'Preaprobado';
  const preApprovedWithConditionsText = isCountryUSA
    ? 'Preapproved with conditions'
    : 'Preaprobado con condiciones';
  const refusedText = isCountryUSA ? 'Refused' : 'Rechazado';
  const platformText = isCountryUSA ? 'Platform' : 'Plataforma';
  const stateText = isCountryUSA ? 'State' : 'Estado';
  const accionesText = isCountryUSA ? 'Actions' : 'Acciones';
  const downloadVideoText = isCountryUSA ? 'Download video' : 'Descargar video';
  const editPlatformInfoText = isCountryUSA ? 'Edit Platform Info' : 'Editar información de la plataforma';
  const editEarningsText = isCountryUSA ? 'Edit earnings' : 'Editar ganancias';
  const downloadScreenshot = isCountryUSA ? 'Screenshots' : 'Capturas de pantalla';

  let uberScreenshots = null;
  let didiScreenshots = null;
  let lyftScreenshots = null;
  let otherScreenshots = null;
  if (screenshots && screenshots.length > 0) {
    uberScreenshots = (screenshots as Array<any>).filter((val) => {
      return val.platform === 'uber';
    });
    didiScreenshots = (screenshots as Array<any>).filter((val) => {
      return val.platform === 'didi';
    });
    lyftScreenshots = (screenshots as Array<any>).filter((val) => {
      return val.platform === 'lyft';
    });
    otherScreenshots = (screenshots as Array<any>).filter((val) => {
      return val.platform === 'other';
    });
  }
  const ability = usePermissions();
  const canEditEarnings = canPerform(
    ability,
    Capabilities.EditEarnings,
    Sections.Clients,
    Subsections.Admissions
  );

  const canAddEarnings = canPerform(
    ability,
    Capabilities.AddEarnings,
    Sections.Clients,
    Subsections.Admissions
  );

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {headingText}{' '}
            <Text as="span" color="gray.500" fontSize="md" fontWeight="medium">
              {formatTotalEarnings(earningsAnalysis.totalEarnings)}{' '}
            </Text>
          </Heading>

          {isPendingApproval && (
            <QueryLink query={{ dialog: 'earnings-analysis' }}>
              {canAddEarnings && (
                <Button className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100">
                  {addPlatformbuttonText}
                </Button>
              )}
            </QueryLink>
          )}
          {accounts.length > 0 && isPendingApproval && isReady && (
            <Button
              className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
              onClick={saveEarnings}
            >
              {preQualifyButtonText}
            </Button>
          )}
          {isApproved ? (
            <Text color="green.500" fontSize="md">
              {earningsAnalysis.totalEarnings &&
                formatToPercent(Math.round(earningsAnalysis.totalEarnings / 12))}
              %
            </Text>
          ) : null}
          {isApprovedWithConditions ? (
            <Text color="yellow.500" fontSize="md">
              {earningsAnalysis.totalEarnings &&
                formatToPercent(Math.round(earningsAnalysis.totalEarnings / 12))}
              %
            </Text>
          ) : null}
          {isRejected ? (
            <Text color="red.500" fontSize="md">
              {earningsAnalysis.totalEarnings &&
                formatToPercent(Math.round(earningsAnalysis.totalEarnings / 12))}
              %
            </Text>
          ) : null}
          {isApproved ? (
            <Text color="green.500" fontSize="sm">
              {preApprovedText}
            </Text>
          ) : null}
          {isApprovedWithConditions ? (
            <Text color="yellow.500" fontSize="sm">
              {preApprovedWithConditionsText}
            </Text>
          ) : null}
          {isRejected ? (
            <Text color="red.500" fontSize="sm">
              {refusedText}
            </Text>
          ) : null}
          {isPending ? <Text color="gray.500" fontSize="sm"></Text> : null}
        </Flex>
      </CardHeader>
      <CardBody minH={150}>
        {!hasAccounts && (
          <EmptyState
            title="No hay plataformas"
            description="El cliente aún no ha conectado ninguna plataforma"
          />
        )}
        {hasAccounts && (
          <TableContainer>
            <Table size="sm" variant="striped">
              <Thead>
                <Tr>
                  <Th>{platformText}</Th>
                  <Th>{stateText}</Th>
                  <Th>{downloadScreenshot}</Th>
                  <Th isNumeric>{accionesText}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {accounts.map((account: Account) => (
                  <Tr key={account.accountId}>
                    <Td>
                      <Text as="span">{findPlatform(account.platform)?.name}</Text>
                    </Td>
                    <Td>
                      <Text as="span">{formatStatus(account.status)}</Text>
                    </Td>
                    <Td>
                      {account.platform === 'uber' && (
                        <DownloadPopover fileList={uberScreenshots}></DownloadPopover>
                      )}
                      {account.platform === 'didi' && (
                        <DownloadPopover fileList={didiScreenshots}></DownloadPopover>
                      )}
                      {account.platform === 'lyft' && (
                        <DownloadPopover fileList={lyftScreenshots}></DownloadPopover>
                      )}
                      {account.platform === 'other' && (
                        <DownloadPopover fileList={otherScreenshots}></DownloadPopover>
                      )}
                    </Td>
                    <Td isNumeric>
                      <Menu>
                        <MenuButton>
                          <FaEllipsisH />
                        </MenuButton>
                        <MenuList>
                          {canEditEarnings && (
                            <>
                              {(account.status === PalencaAccountStatus.success ||
                                account.status === PalencaAccountStatus.failure ||
                                account.status === PalencaAccountStatus.pending) && (
                                <EditMenuItem
                                  requestId={requestId}
                                  platform={account.platform}
                                  menuItemText={editPlatformInfoText}
                                />
                              )}
                              {
                                <QueryLink query={{ dialog: 'earnings-analysis' }}>
                                  <MenuItem
                                    sx={{
                                      _hover: {
                                        bg: 'gray.50',
                                      },
                                    }}
                                  >
                                    <FaEdit style={{ marginRight: '0.5rem' }} />
                                    {editEarningsText}
                                  </MenuItem>
                                </QueryLink>
                              }
                            </>
                          )}

                          {/* {earningsAnalysis.status === EarningsAnalysisStatus.pending && (
                            <QueryLink query={{ dialog: 'earnings-analysis' }}>
                              <MenuItem
                                sx={{
                                  _hover: {
                                    bg: 'gray.50',
                                  },
                                }}
                              >
                                <FaEdit style={{ marginRight: '0.5rem' }} />
                                {editEarningsText}
                              </MenuItem>
                            </QueryLink>
                          )} */}
                          <MenuItem
                            onClick={async () => {
                              try {
                                const response = await fetch(
                                  `${URL_API}/ocr/get-video/${requestId}/${account.platform}`,
                                  {
                                    headers: {
                                      Authorization: `Bearer ${user.accessToken}`,
                                    },
                                  }
                                );

                                if (!response.ok) {
                                  throw new Error(`HTTP error! Status: ${response.status}`);
                                }

                                const data = await response.json();

                                if (data.url) {
                                  window.open(data.url, '_blank');
                                  toast({
                                    title: 'Descarga exitosa',
                                    status: 'success',
                                  });
                                } else {
                                  console.error('No se encontraron URLs de video en la respuesta.');
                                  // Manejar el caso en que no hay videoUrls
                                  toast({
                                    title: 'Error al obtener el video',
                                    status: 'error',
                                  });
                                }
                              } catch (error) {
                                console.error('Error al obtener la URL del video:', error);
                                // Manejar el error de fetch
                                toast({
                                  title: 'Error al obtener el video',
                                  status: 'error',
                                });
                              }
                            }}
                            sx={{
                              _hover: {
                                bg: 'gray.50',
                              },
                            }}
                          >
                            <FaDownload style={{ marginRight: '0.5rem' }} />
                            {downloadVideoText}
                          </MenuItem>
                        </MenuList>
                      </Menu>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </TableContainer>
        )}
      </CardBody>
    </Card>
  );
}

const EditMenuItem = ({
  requestId,
  platform,
  menuItemText,
}: {
  requestId: string;
  platform: string;
  menuItemText: string;
}) => {
  const { onClose, onOpen, isOpen } = useDisclosure();
  return (
    <>
      <MenuItem
        onClick={onOpen}
        sx={{
          _hover: {
            bg: 'gray.50',
          },
        }}
      >
        <FaEdit style={{ marginRight: '0.5rem' }} />
        {menuItemText}
      </MenuItem>

      {isOpen ? (
        <PlatformDetailDialog requestId={requestId} isOpen={isOpen} onClose={onClose} platform={platform} />
      ) : null}
    </>
  );
};
