import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BlockAgentAssignmentDataTable } from './BlockAgentDataTable';
import { AssignedClientsDataTable } from './AssignedClientsDataTable';
import { ReassignLeadsTab } from './ReassignLeadsTab';

export function LeadAssignationTabs() {
  return (
    <Tabs defaultValue="leadAssignation">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="leadAssignation">Asignación clientes potenciales</TabsTrigger>
        <TabsTrigger value="blockAgent">Agente de bloque</TabsTrigger>
        <TabsTrigger value="reassignLeads">Reasignar clientes potenciales</TabsTrigger>
      </TabsList>
      <TabsContent value="leadAssignation">
        <AssignedClientsDataTable></AssignedClientsDataTable>
      </TabsContent>
      <TabsContent value="blockAgent">
        <BlockAgentAssignmentDataTable></BlockAgentAssignmentDataTable>
      </TabsContent>
      <TabsContent value="reassignLeads">
        <ReassignLeadsTab></ReassignLeadsTab>
      </TabsContent>
    </Tabs>
  );
}
