import * as Yup from 'yup';

export const bulkUploadSchema = Yup.object().shape({
  files: Yup.mixed()
    .required('Por favor, sube al menos un archivo')
    .test('fileType', 'Solo se permiten archivos XML', (value) => {
      if (!value) return true;
      const files = value as FileList;
      return Array.from(files).every((file) => file.name.toLowerCase().endsWith('.xml'));
    })
    .test('fileCount', 'No puedes subir más de 200 archivos a la vez', (value) => {
      if (!value) return true;
      const files = value as FileList;
      return files.length <= 200;
    }),
});
