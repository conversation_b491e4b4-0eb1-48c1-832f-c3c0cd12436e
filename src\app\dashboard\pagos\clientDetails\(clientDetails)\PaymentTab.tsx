/* eslint-disable react-hooks/rules-of-hooks */
'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { Payment } from '../../types';
import {
  canPerformPaymentActions,
  PAGOS_PAYMENT_URL,
  PAYMENT_API_SECRET,
  PAYMENTS_API_URL,
} from '@/constants';
import axios from 'axios';
import { DataTable } from '@/components/DataTable';
import { Skeleton } from '@/components/ui/skeleton';

import { ColumnDef } from '@tanstack/react-table';

// eslint-disable-next-line import/no-extraneous-dependencies
import { Check, X, CirclePlus, BookCheck, RotateCw, Trash } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Copy /* , Mail, Link */ } from 'lucide-react';
import InvoiceReceiptSheet from '@/components/Sheets/InviceReceiptSheet/InvoiceReceiptSheet';
import Swal from 'sweetalert2';
import Spinner from '@/components/Loading/Spinner';
import { useToast } from '@chakra-ui/react';
import { retryInvoice } from './Bar/actions/retry-invoice';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { removePayment, removeTemporalProducts } from './Bar/actions/remove-payment';
import { usePaymentTransactionState } from '@/hooks/usePaymentTransaction';
import PaymentTransactionModal from '../../payments/_components/PaymentTransactionModal';

async function getClientPayments(id: string): Promise<Payment[]> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/payments?clientId=${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}

type Props = {
  id?: string;
};

export default function PaymentTab({ id }: Props) {
  const [paymentsData, setPaymentsData] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const toast = useToast();
  const router = useRouter();

  const { user } = useCurrentUser();
  const { isOpen, onOpen, setPaymentId, setPaymentAmount, updatePayments } = usePaymentTransactionState();
  const shouldDisplay = canPerformPaymentActions(user.email);

  const confirmMarkAsPaid = async (paymentId: string, total: number) => {
    console.log('confirmMarkAsPaid');
    setPaymentId(paymentId);
    setPaymentAmount(total);
    onOpen();
  };

  const confirmDelete = useCallback(
    async (paymentId: string) => {
      return Swal.fire({
        title: '¿Estás seguro?',
        text: '¿Quieres eliminar este pago?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sí',
        cancelButtonText: 'No',
      }).then(async (result) => {
        if (result.isConfirmed) {
          setSending(true);
          const res = await removePayment(paymentId);
          setSending(false);

          if (res.success) {
            // router.refresh();
            const newData = await getClientPayments(id as string);
            setPaymentsData(newData);
            return toast({
              title: 'Pago eliminado',
              status: 'success',
              duration: 4000,
              isClosable: true,
              position: 'top',
            });
          }

          return toast({
            title: 'Error al eliminar el pago',
            description: res.message || 'Ocurrió un error al eliminar el pago',
            status: 'error',
            position: 'top',
            duration: 5000,
            isClosable: true,
          });
        }
        return false;
      });
    },
    [toast, id]
  );

  const confirmDeleteTemporalProducts = async (paymentId: string) => {
    return Swal.fire({
      title: '¿Estás seguro?',
      text: '¿Quieres eliminar los productos temporales?',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí',
      cancelButtonText: 'No',
    }).then(async (result) => {
      if (result.isConfirmed) {
        setSending(true);
        const res = await removeTemporalProducts(paymentId);
        setSending(false);
        if (res.success) {
          toast({
            title: 'Productos temporales eliminados',
            status: 'success',
            duration: 4000,
            isClosable: true,
            position: 'top',
          });

          const newData = await getClientPayments(id as string);
          setPaymentsData(newData);
          return true;
        }

        return toast({
          title: 'Error al eliminar los productos temporales',
          description: res.message || 'Ocurrió un error al eliminar los productos temporales',
          status: 'error',
          position: 'top',
          duration: 5000,
          isClosable: true,
        });
      }
      return false;
    });
  };

  const paymentColumns: ColumnDef<Payment>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            <div className="font-medium">{row?.original?.id}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'client.name',
      header: 'CLIENTE',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            <div className="font-medium">{row?.original?.client?.name}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'ESTADO',
      cell: ({ row }) => {
        return (
          <div className="flex flex-row items-center">
            <div className="font-medium">{row.getValue('status')}</div>
          </div>
        );
      },
    },
    {
      accessorKey: 'total',
      header: 'MONTO',
      cell: ({ row }) => {
        return <div className="font-medium">{row.getValue('total')}</div>;
      },
    },
    {
      accessorKey: 'createdAt',
      header: 'CREADO',
      cell: ({ row }) => {
        const date = new Date(row.getValue('createdAt'));
        const formatted = date.toLocaleDateString();
        return <div className="font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: 'isPaid',
      header: 'PAGADO',
      cell: ({ row }) => {
        //const user = row.original;
        const isPaid = row.getValue('isPaid');
        if (isPaid) {
          return <Check />;
        } else {
          return <X />;
        }
      },
    },
    {
      accessorKey: 'paidAt',
      header: 'Paid At',
      cell: ({ row }) => {
        const paidAtDateStr = row?.original?.paidAt;
        let formatted = '';
        if (paidAtDateStr) {
          const date = new Date(row.getValue('paidAt'));
          formatted = date.toLocaleString();
        }
        return <div className="font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: 'subscription.isActive',
      header: 'FUENTE',
      cell: ({ row }) => {
        // const isActive = row?.original?.subscription?.isActive;
        // console.log('payment', row?.original);
        const isSubscription = row?.original?.type === 'subscription';

        if (isSubscription) {
          return <Badge className="bg-green-300">Recurrencia</Badge>;
        } else {
          return <Badge className="bg-blue-300">Una Vez</Badge>;
        }
      },
    },
    {
      accessorKey: 'relations',
      header: 'RELACIONES',
      cell: ({ row }) => {
        const { invoiceId, receiptId } = row?.original;
        return <InvoiceReceiptSheet invoiceId={invoiceId} receiptId={receiptId} />;
      },
    },
    {
      accessorKey: 'actions',
      header: 'ACCIONES',
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      cell: ({ row }) => {
        const shouldDisplayMarkAsPaidOnly = canPerformPaymentActions(user.email);

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <CirclePlus />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {!row.original.isPaid && shouldDisplayMarkAsPaidOnly && (
                <DropdownMenuItem
                  onClick={async () => {
                    await confirmMarkAsPaid(row.original.id, row.getValue('total'));
                  }}
                >
                  <BookCheck className="h-4 w-4 pr-1" />
                  Marcar como pagado
                </DropdownMenuItem>
              )}

              <DropdownMenuItem
                onClick={() => {
                  navigator.clipboard.writeText(`${PAGOS_PAYMENT_URL}` + row.original.id);
                  // toast('URL de pago copiada al portapapeles');
                  toast({
                    title: 'URL de pago copiada al portapapeles',
                    position: 'top',
                    status: 'success',
                    duration: 3000,
                    isClosable: true,
                  });
                }}
              >
                <Copy className="h-4 w-4 pr-1" />
                Copiar al portapapeles
              </DropdownMenuItem>

              {row.original.isPaid && !row.original.invoiceId && (
                <DropdownMenuItem
                  onClick={async () => {
                    setSending(true);

                    const response = await retryInvoice(row.original.id);
                    setSending(false);

                    if (response.success) {
                      router.refresh();
                      return toast({
                        title: 'Factura creada correctamente',
                        status: 'success',
                        duration: 4000,
                        isClosable: true,
                        position: 'top',
                      });
                    }

                    return toast({
                      title: 'Error al crear la factura',
                      description: response.message || 'Ocurrió un error al crear la factura',
                      status: 'error',
                      position: 'top',
                      duration: 5000,
                      isClosable: true,
                    });
                  }}
                >
                  <RotateCw className="h-4 w-4 pr-1" />
                  Reintentar factura
                </DropdownMenuItem>
              )}
              {!row.original.isPaid && row.original.status === 'pending' && shouldDisplay && (
                <>
                  <DropdownMenuItem
                    onClick={async () => {
                      await confirmDelete(row.original.id);
                    }}
                  >
                    <Trash className="h-4 w-4 pr-1" />
                    Eliminar
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={async () => {
                      await confirmDeleteTemporalProducts(row.original.id);
                    }}
                  >
                    <Trash className="h-4 w-4 pr-1" />
                    Eliminar productos temporales
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  useEffect(() => {
    try {
      if (id) {
        setLoading(true);
        getClientPayments(id as string).then((res) => {
          setPaymentsData(res);
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  }, [id, updatePayments]);

  if (loading) {
    return <Skeleton className="w-full h-28"></Skeleton>;
  } else {
    return (
      <>
        {sending && <Spinner />}
        {isOpen && <PaymentTransactionModal />}
        <DataTable columns={paymentColumns} data={paymentsData}></DataTable>
      </>
    );
  }
}
