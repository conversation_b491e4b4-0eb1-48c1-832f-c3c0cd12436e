'use client';

import { ColumnDef } from '@tanstack/react-table';

// eslint-disable-next-line import/no-extraneous-dependencies
import { MoreHorizontal, ArrowUpDown, Search } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Product } from '../types';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import { Capabilities, Sections, Subsections } from '@/constants';

export const columns: ColumnDef<Product>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) => {
      return <div className="font-medium">{row?.original?.id}</div>;
    },
  },
  {
    accessorKey: 'name',
    header: ({ column }) => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">CONCEPTO</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Search className="h-4 w-4 hover:bg-grey" />
            </PopoverTrigger>
            <PopoverContent>
              <div className="flex flex-col justify-evenly">
                <h4 className="font-medium leading-none">Searh by name</h4>
                <Input
                  className="mt-4"
                  value={(column?.getFilterValue() as string) ?? ''}
                  onChange={(event) => column?.setFilterValue(event.target.value)}
                />
              </div>
            </PopoverContent>
          </Popover>
          <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}>
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('name')}</div>;
    },
  },
  {
    accessorKey: 'price',
    header: 'PRECIO',
    cell: ({ row }) => {
      return <div className="font-medium">{'$' + row.getValue('price')}</div>;
    },
  },
  {
    accessorKey: 'taxType',
    header: 'IMPUESTOS',
    cell: ({ row }) => {
      return (
        <>
          {row.original?.taxType && (
            <div className="font-medium">
              {row.original?.taxType +
                ' ' +
                row.original?.taxRate +
                ' ' +
                `${row.original.ivaIncluded ? '(Inc.)' : ''}`}
            </div>
          )}
        </>
      );
    },
  },
  {
    accessorKey: 'region',
    header: () => {
      return (
        <div className="flex flex-row items-center">
          <Label className="pr-2">REGION</Label>
        </div>
      );
    },
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('region')}</div>;
    },
  },
  {
    accessorKey: 'actions',
    header: 'ACCIONES',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    cell: ({ row }) => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const ability = usePermissions();
      const canEdit = canPerform(ability, Capabilities.Edit, Sections.Payments, Subsections.Products);

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            {canEdit && <DropdownMenuItem>Editar</DropdownMenuItem>}
            <DropdownMenuItem>Duplicar</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
