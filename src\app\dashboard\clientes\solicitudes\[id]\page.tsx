import getAdmissionRequest from '@/actions/getAdmissionRequest';
import { Suspense } from 'react';
import Detail from './detail';
import getAdmissionRequestDocAnalysis from '@/actions/getAdmissionRequestDocAnalysis';
import getAdmissionRequestScreenshots from '@/actions/getAdmissionRequestScreenshots';
import { DocumentClassification } from '../enums';
import { CountriesShortNames } from '@/constants';

interface PageProps {
  params: {
    id: string;
  };
}

const Page = async (props: PageProps) => {
  const { id } = props.params;

  const admissionRequest = await getAdmissionRequest({ id });
  const isCountryUSA = admissionRequest?.data?.personalData?.country === CountriesShortNames['United States'];

  const documentsWithMedia = await getAdmissionRequestDocAnalysis({
    id,
    documentClassification: isCountryUSA ? DocumentClassification.all : DocumentClassification.mandatory,
  });
  const additionalDocumentsWithMedia = isCountryUSA
    ? []
    : await getAdmissionRequestDocAnalysis({
        id,
        documentClassification: DocumentClassification.additional,
      });
  const screenshots = await getAdmissionRequestScreenshots({ id });

  return (
    <>
      <Suspense fallback={<div>Cargando...</div>}>
        <Detail
          request={admissionRequest?.data}
          documentsAnalysis={documentsWithMedia?.data}
          additionalDocumentsAnalysis={additionalDocumentsWithMedia?.data}
          screenshots={screenshots}
          isCountryUSA={isCountryUSA}
        />
      </Suspense>
    </>
  );
};

export default Page;
