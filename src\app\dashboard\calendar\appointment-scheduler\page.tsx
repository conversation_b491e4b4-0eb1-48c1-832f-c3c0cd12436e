import getCurrentUser from '@/actions/getCurrentUser';
import { redirect } from 'next/navigation';
import AppointmentScheduler from './_components/Appointment';
import { getCalendarSchedule } from '@/actions/getAdmissionRequest';
import { Suspense } from 'react';
import { translations } from '../../clientes/solicitudes/[id]/home-visit/_components/translations';

const Page = async () => {
  const user = await getCurrentUser();
  if (!user) return redirect('/');
  const calendarSchedule = await getCalendarSchedule();

  return (
    <>
      <div>
        <Suspense fallback={<div>Cargando...</div>}>
          <h2 className="text-lg font-bold">{translations.es.Appointments}</h2>
          <div className="py-2 bg-gray-50">
            <AppointmentScheduler calendarSchedule={calendarSchedule} />
          </div>
        </Suspense>
      </div>
    </>
  );
};

export default Page;
