import { useState } from 'react';
import { CONTRACT_REGIONS, Countries, URL_API, US_CITIES } from '@/constants';
import { VehicleRegisteration } from './VehicleRegisteration';
import { Region } from '../vehiclesContext';
import {
  Button,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalFooter,
  ModalHeader,
  ModalOverlay,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { Form as FormikForm, Formik, FormikValues } from 'formik';
// import CountrySelector from './steps/CountrySelector';
import RegistrationMethodSelector from './steps/RegistrationMethodSelector';
import BulkUploadStep from './steps/BulkUploadStep';
import { createStockSchema, createStockSchemaUS } from '@/validatorSchemas/createStockSchema';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import {
  vehicleRegistrationTranslationsMX,
  vehicleRegistrationTranslationsUS,
} from '@/constants/translations';
import { bulkUploadSchema } from '@/validatorSchemas/xmlUploadSchema';
import LocationSelector from './steps/LocationSelector';

enum STEPS {
  COUNTRY = 0,
  REGISTRATION_TYPE = 1,
  NORMAL_FLOW = 2,
  BULK_UPLOAD = 3,
}

interface VehicleRegistrationFlowProps {
  paramCountry: string | null;
  allowedRegions: any[];
  handleSetName: (name: string, file: any) => void;
  nameFiles: any;
}

const VehicleRegistrationFlow = ({
  paramCountry,
  allowedRegions,
  handleSetName,
  nameFiles,
}: VehicleRegistrationFlowProps) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  const { user: currentUser, isSuperAdminOrAdmin } = useCurrentUser();
  const updateSideData = useUpdateSideData();

  const [step, setStep] = useState(STEPS.COUNTRY);
  const [country, setCountry] = useState<Countries | null>(null);
  const [region, setRegion] = useState<Region | null>(null);
  const [contractNumber, setContractNumber] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [formFiles, setFormFiles] = useState<FileList | null>(null);
  const [loaderDuration, setLoaderDuration] = useState(3000);

  const translations =
    country === Countries['United States']
      ? vehicleRegistrationTranslationsUS
      : vehicleRegistrationTranslationsMX;

  const modalHeader =
    step === STEPS.COUNTRY
      ? translations.selectCountry
      : step === STEPS.REGISTRATION_TYPE
      ? translations.selectMethod
      : step === STEPS.NORMAL_FLOW
      ? translations.addVehicle
      : translations.bulkUpload;

  const initialValues = {
    bill: '',
    brand: { value: '', label: 'Selecciona' },
    model: { value: '', label: 'Selecciona' },
    version: { value: '', label: 'Selecciona' },
    year: { value: '', label: 'Selecciona' },
    color: { value: '', label: 'Selecciona' },
    vin: '',
    owner: '',
    billAmount: '',
    billNumber: '',
    billDate: '',
    receptionDate: '',
    km: '',
    vehicleState: {
      value: '',
      label: 'Selecciona',
      code: '',
    },
    country: {
      label: country,
      value: country,
    },
    state: {
      label: '',
      value: '',
    }, // will be used in US Vehicle Form Registeration
    mi: '',
    isElectric: { value: 'false', label: 'No' },
  };

  const initialValuesUS = {
    bill: '',
    brand: '',
    model: '',
    version: '',
    year: '',
    color: { value: '', label: 'Selecciona' },
    vin: '',
    owner: '',
    billAmount: '',
    billNumber: '',
    billDate: '',
    receptionDate: '',
    km: '',
    vehicleState: {
      value: '',
      label: 'Selecciona',
      code: '',
    },
    country: {
      label: country,
      value: country,
    },
    state: {
      label: '',
      value: '',
    }, // will be used in US Vehicle Form Registeration
    mi: '',
    isElectric: { value: 'false', label: 'No' },
  };

  const validatePreSubmit = async (values: FormikValues) => {
    try {
      await axios.post(
        `${URL_API}/stock/validate-fields`,
        {
          vin: values.vin,
        },
        {
          headers: {
            Authorization: `Bearer ${currentUser.accessToken}`,
          },
        }
      );
      return true;
    } catch (error: any) {
      toast({
        title: 'VIN o Serie registrado en el vehiculo : ' + error.response.data.carNumber,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
      return false;
    }
  };

  const router = useRouter();

  const handleCountrySelect = (selectedCountry: Countries) => {
    setCountry(selectedCountry);
    setRegion(null);
    setContractNumber('');
  };

  const handleContractNumberUpdate = (number: string) => {
    setContractNumber(number);
  };

  const handleRegistrationTypeSelect = (type: STEPS.NORMAL_FLOW | STEPS.BULK_UPLOAD) => {
    setStep(type);
  };

  const handleFileChange = (files: FileList) => {
    setFormFiles(files);
  };

  const handleClose = () => {
    onClose();
    setStep(STEPS.COUNTRY);
    setCountry(null);
    setRegion(null);
    setContractNumber('');
    setFormFiles(null);
  };

  const onBack = () => {
    if (country === Countries.Mexico && (step === STEPS.NORMAL_FLOW || step === STEPS.BULK_UPLOAD)) {
      setStep(STEPS.REGISTRATION_TYPE);
      setFormFiles(null);
    }
    if (country === Countries['United States'] && step === STEPS.NORMAL_FLOW) {
      setStep(STEPS.COUNTRY);
    }
    if (step === STEPS.REGISTRATION_TYPE) {
      setStep(STEPS.COUNTRY);
    }
  };

  const handleSubmit = async (values: typeof initialValues, _: any, onCloseModal: () => void) => {
    const isValid = await validatePreSubmit(values);
    if (!isValid) return null;
    setIsLoading(true);

    const data = {
      ...values,
      brand: values.brand.value,
      model: values.model.value,
      version: values.version.value,
      year: values.year.value,
      vehicleState: values.vehicleState.code,
      isElectric: values.isElectric.value === 'true',
      color: values.color.value,
      km: parseInt(values.km, 10),
      status: 'stock',
      region: region,
      carNumber: contractNumber,
      country: values.country.value,
      state: values.state.value,
      mi: values.mi,
    };

    try {
      const response = await axios.post(`${URL_API}/stock/add`, data, {
        headers: {
          Authorization: `Bearer ${currentUser.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      toast({
        title: 'Vehiculo agregado al inventario correctamente',
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      setIsLoading(false);
      onCloseModal();
      router.refresh();
      router.push(
        window.location.pathname.includes('active')
          ? `/dashboard/flotilla/inactive/in-preparation/${response.data.stock._id}${
              paramCountry ? `?country=${encodeURI(paramCountry)}` : ''
            }`
          : `/dashboard/flotilla/invoiced/${response.data.stock._id}${
              paramCountry ? `?country=${encodeURI(paramCountry)}` : ''
            }`
      );
    } catch (error: any) {
      toast({
        title: error.response?.data?.message || 'Error al agregar el vehículo',
        duration: 6000,
        status: 'error',
        position: 'top',
      });
      setIsLoading(false);
    }
  };

  const handleSubmitUSVehicleRegisterationForm = async (
    values: typeof initialValuesUS,
    _: any,
    onCloseModal: () => void
  ) => {
    const isValid = await validatePreSubmit(values);
    if (!isValid) return null;
    setIsLoading(true);

    const data = {
      ...values,
      vehicleState: values.vehicleState.code,
      color: values.color.value,
      km: values.km,
      status: 'stock',
      region: region, // Use the region number directly
      carNumber: contractNumber,
      country: values.country.value,
      state: values.state.value,
      mi: parseInt(values.mi, 10),
    };

    try {
      const response = await axios.post(`${URL_API}/stock/add`, data, {
        headers: {
          Authorization: `Bearer ${currentUser.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      });
      toast({
        title: 'Vehiculo agregado al inventario correctamente',
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      setIsLoading(false);
      onCloseModal();
      router.refresh();
      router.push(
        window.location.pathname.includes('active')
          ? `/dashboard/flotilla/inactive/in-preparation/${response.data.stock._id}${
              paramCountry ? `?country=${encodeURI(paramCountry)}` : ''
            }`
          : `/dashboard/flotilla/invoiced/${response.data.stock._id}${
              paramCountry ? `?country=${encodeURI(paramCountry)}` : ''
            }`
      );
    } catch (error: any) {
      toast({
        title: error.response?.data?.message || 'Error al agregar el vehículo',
        duration: 6000,
        status: 'error',
        position: 'top',
      });
      setIsLoading(false);
    }
  };

  const handleUpdateRegion = (regionCode: string) => {
    const regionNumber =
      country === Countries['United States']
        ? US_CITIES.find((c) => c.value === regionCode)?.number
        : CONTRACT_REGIONS.find((r) => r.value === regionCode)?.number;
    setRegion(regionNumber as unknown as Region);
  };

  const getInitialValues = () => {
    if (country === Countries['United States']) {
      return initialValuesUS;
    }
    return initialValues;
  };

  const getValidationSchema = () => {
    if (country === Countries['United States'] && step === STEPS.NORMAL_FLOW) {
      return createStockSchemaUS;
    }
    if (country === Countries.Mexico && step === STEPS.NORMAL_FLOW) {
      return createStockSchema;
    }
    if (step === STEPS.BULK_UPLOAD) {
      return bulkUploadSchema;
    }
    return undefined;
  };

  const onSubmit = async (
    values: typeof initialValues | typeof initialValuesUS,
    setFieldError: (field: string, message: string) => void
  ) => {
    if (step === STEPS.NORMAL_FLOW) {
      try {
        if (country === Countries['United States']) {
          await handleSubmitUSVehicleRegisterationForm(values as typeof initialValuesUS, null, handleClose);
        } else {
          await handleSubmit(values as typeof initialValues, null, handleClose);
        }
      } catch (error: any) {
        toast({
          title: 'Error',
          description: error.message || 'Failed to register vehicle',
          status: 'error',
          duration: 3000,
          position: 'top',
        });
        setIsLoading(false);
      }
    }

    if (step === STEPS.BULK_UPLOAD) {
      if (!formFiles || formFiles.length === 0) {
        setFieldError('files', translations.pleaseUploadFile);
        return;
      }

      try {
        const fileCount = formFiles.length;
        let minLoadingTime = 3000;

        if (fileCount > 100) {
          minLoadingTime = 7000;
        } else if (fileCount > 50) {
          minLoadingTime = 5000;
        }

        setLoaderDuration(minLoadingTime);
        setIsLoading(true);

        // Wait for the loader to complete first
        await new Promise((resolve) => setTimeout(resolve, minLoadingTime));

        const formData = new FormData();
        Array.from(formFiles).forEach((file) => {
          formData.append(`files`, file);
        });

        const userName = currentUser.name;
        const userEmail = currentUser.email;
        formData.append('country', country || '');
        formData.append('region', region || '');
        formData.append('userName', userName);
        formData.append('userEmail', userEmail);

        await axios.post(`${URL_API}/stock/bulk-upload`, formData, {
          headers: {
            Authorization: `Bearer ${currentUser.accessToken}`,
            'Content-Type': 'multipart/form-data',
          },
        });

        toast({
          title: translations.uploadInProgress,
          description: translations.uploadNotification,
          status: 'success',
          duration: 3000,
          position: 'top',
        });

        await updateSideData(currentUser);
        setIsLoading(false);
        handleClose();
        router.refresh();
      } catch (error: any) {
        toast({
          title: translations.error,
          description: error.response?.data?.message || translations.uploadFailed,
          status: 'error',
          duration: 3000,
          position: 'top',
        });
        setIsLoading(false);
      }
    }
  };

  // Action button label based on current step
  const actionLabel = (() => {
    if (step === STEPS.REGISTRATION_TYPE) {
      return translations.continue;
    }
    return translations.submit;
  })();

  // Modal content based on current step
  const getBodyContent = (setFieldValue: (field: string, value: any) => void) => {
    if (step === STEPS.COUNTRY) {
      return (
        <LocationSelector
          onCountrySelect={(selectedCountry) => {
            handleCountrySelect(selectedCountry);
            setFieldValue('country', {
              label: selectedCountry === Countries['United States'] ? 'USA' : 'MX',
              value: selectedCountry,
            });
          }}
          isSuperAdminOrAdmin={isSuperAdminOrAdmin}
          allowedRegions={allowedRegions}
          setRegion={handleUpdateRegion}
          onContractNumberUpdate={handleContractNumberUpdate}
          translations={translations}
        />
      );
    }

    if (step === STEPS.REGISTRATION_TYPE) {
      return (
        <RegistrationMethodSelector
          onMethodSelect={handleRegistrationTypeSelect}
          translations={translations}
        />
      );
    }

    if (step === STEPS.NORMAL_FLOW && country) {
      return (
        <div>
          <VehicleRegisteration
            country={country}
            contractNumber={contractNumber}
            handleSetName={handleSetName}
            nameFiles={nameFiles}
          />
        </div>
      );
    }

    if (step === STEPS.BULK_UPLOAD && country) {
      return (
        <BulkUploadStep
          onFileChange={handleFileChange}
          isLoading={isLoading}
          loaderDuration={loaderDuration}
        />
      );
    }

    return null;
  };

  return (
    <div>
      <Button onClick={onOpen} colorScheme="blue" className="bg-[#5800F7] text-white px-4 py-2 rounded">
        Agregar
      </Button>

      <Modal
        closeOnOverlayClick={false}
        size={step === STEPS.NORMAL_FLOW || step === STEPS.BULK_UPLOAD ? 'xl' : 'md'}
        isOpen={isOpen}
        onClose={handleClose}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader className="font-inter font-[600] text-[18px]">{modalHeader}</ModalHeader>
          <ModalCloseButton />

          <Formik
            initialValues={getInitialValues()}
            validationSchema={getValidationSchema()}
            onSubmit={(values, { setFieldError }) => {
              onSubmit(values, setFieldError);
            }}
            enableReinitialize
          >
            {({ setFieldValue, values }) => (
              <FormikForm>
                <ModalBody pb={6}>
                  <div className="flex flex-col gap-[20px]">{getBodyContent(setFieldValue)}</div>
                </ModalBody>
                <ModalFooter gap={3}>
                  {step > STEPS.COUNTRY && (
                    <Button
                      sx={{
                        color: '#5800F7',
                        borderColor: '#5800F7 !important',
                        border: '2px',
                        h: '40px',
                      }}
                      onClick={onBack}
                    >
                      {translations.back}
                    </Button>
                  )}

                  {/* Submit button for steps that require form submission */}
                  {(step === STEPS.NORMAL_FLOW || step === STEPS.BULK_UPLOAD) && (
                    <Button
                      sx={{
                        color: 'white',
                        h: '40px',
                      }}
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      type="submit"
                      isLoading={isLoading}
                      isDisabled={step === STEPS.BULK_UPLOAD && (!formFiles || formFiles.length === 0)}
                    >
                      {actionLabel}
                    </Button>
                  )}

                  {step === STEPS.COUNTRY && values.country.value && (
                    <Button
                      sx={{
                        color: 'white',
                        h: '40px',
                      }}
                      className="bg-[#5800F7] text-white rounded-md h-[40px] cursor-pointer"
                      onClick={() => {
                        if (country === Countries['United States']) {
                          setStep(STEPS.NORMAL_FLOW);
                        } else {
                          setStep(STEPS.REGISTRATION_TYPE);
                        }
                      }}
                      isDisabled={
                        !values.country ||
                        (values.country.value === Countries['United States'] &&
                          (!values.state?.value || !values.vehicleState?.value)) ||
                        (values.country.value === Countries.Mexico && !values.vehicleState?.value)
                      }
                    >
                      {translations.continue}
                    </Button>
                  )}
                </ModalFooter>
              </FormikForm>
            )}
          </Formik>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default VehicleRegistrationFlow;
