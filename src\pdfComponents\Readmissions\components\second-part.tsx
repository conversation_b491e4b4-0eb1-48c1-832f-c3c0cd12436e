import { Text, View } from '@react-pdf/renderer';
import React from 'react';
import { readmissionDocStyles } from '../ReadmissionDocument';

interface SecondPartProps {
  address: string;
}

export default function SecondPart(props: SecondPartProps) {
  return (
    <>
      <View style={readmissionDocStyles.viewMain}>
        <Text style={{ ...readmissionDocStyles.headerTitle, marginTop: '20px' }}>
          SEGUNDA.- DECLARA “EL CONDUCTOR”:
        </Text>
      </View>

      <View style={{ rowGap: 12 }}>
        <Text style={{ fontSize: 12 }}>
          a) Llamarse según lo anotado en el proemio de este convenio, así como contar con la capacidad legal
          para cumplir con las obligaciones contenidas en este instrumento.
        </Text>
        <Text style={{ fontSize: 12 }}>
          b) Que cuenta con la capacidad legal, en términos de las leyes aplicables, para obligarse bajo los
          términos y condiciones contenidos en este convenio.
        </Text>
        <Text style={{ fontSize: 12 }}>
          c) Que es su deseo firmar el presente acuerdo de voluntades, en los términos y condiciones que se
          establecen en este documento.
        </Text>
        <Text style={{ fontSize: 12 }}>
          d) Que señala como domicilio para oír y recibir notificaciones el ubicado en :{' '}
          {/* CLL NORTE 66 A 3526
          DEP INT 1 CUCHILLA LA JOYA Ciudad de México GUSTAVO A MADERO 07890. */}
          {props.address}
        </Text>
      </View>
    </>
  );
}
