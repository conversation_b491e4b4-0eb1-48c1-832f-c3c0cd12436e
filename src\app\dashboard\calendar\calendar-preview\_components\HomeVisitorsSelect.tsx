'use client';
import Select from 'react-select';
import { useEffect, useState } from 'react';
import { StepperButton } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/StepperButtons';
import { useToast } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { ApiPath } from '@/constants/api.endpoints';
import { URL_API } from '@/constants';
import { AppointmentEvent } from '../../types';
import { appointmentHomeVisitorChange } from '@/actions/calendar';
import { translations } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/translations';

const NO_HOME_VISITOR_AVAILABLE = 'no-home-visitor-available';

interface IHomeVisitorsWithFreeSlots {
  event: AppointmentEvent;
  onClose: () => void;
}

export const HomeVisitorsWithFreeSlots = (props: IHomeVisitorsWithFreeSlots) => {
  const { event, onClose } = props;
  const [selectedHomeVisitor, setSelectedHomeVisitor] = useState<any>('');
  const [availableHomeVisitorsOptions, setAvailableHomeVisitorsOptions] = useState<any[]>([]);
  const [availableHomeVisitors, setAvailableHomeVisitors] = useState<any[]>([]);

  const toast = useToast();
  const router = useRouter();
  const { data: session } = useSession();

  const user = session?.user as unknown as MyUser;
  useEffect(() => {
    async function getAvailableSlotsUsers({ accessToken }: { accessToken: string }) {
      try {
        const apiUrl = new URL(`${URL_API}${ApiPath.AVAILABLE_SLOTS_USERS}/${event.slot}`);
        const res = await fetch(apiUrl.toString(), {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        const response = await res.json();
        if (response.success && Array.isArray(response.data)) {
          let availableSlotUsers = [];
          const MIN_AVAILABLE_USERS = 0;
          if (response.data.length > MIN_AVAILABLE_USERS) {
            availableSlotUsers = response.data.map((slot: any) => {
              return {
                label: slot?.user?.name,
                value: slot?.user?.id,
              };
            });
          }
          availableSlotUsers.push({
            label: translations.es.NoVisitorAvailable,
            value: NO_HOME_VISITOR_AVAILABLE,
          });
          setAvailableHomeVisitorsOptions(availableSlotUsers);
          setAvailableHomeVisitors(response.data);
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Se produjo un error al buscar espacios disponibles para visitantes del hogar',
          status: 'error',
          duration: 2000,
        });
      }
    }

    if (user?.accessToken) {
      getAvailableSlotsUsers({
        accessToken: user.accessToken,
      });
    }
  }, [event.slot, toast, user.accessToken]);

  const handleChange = (selectedOptions: any) => {
    setSelectedHomeVisitor(selectedOptions);
  };
  const [isLoading, setIsLoading] = useState(false);

  const assignAppointmentToSelectedHomeVisitor = async () => {
    try {
      setIsLoading(true);
      const payload = {
        slotId:
          availableHomeVisitors.find((option) => option.user.id === selectedHomeVisitor.value)?.id ||
          NO_HOME_VISITOR_AVAILABLE,
        homeVisitorId: selectedHomeVisitor.value,
        appointmentId: event.id,
      };
      const response = await appointmentHomeVisitorChange(payload);
      if (response?.success) {
        toast({
          title: translations.es.Success,
          description: translations.es.AppointmentHomeVisitorUpdatedSuccessfully,
          status: 'success',
          duration: 3000,
        });
        onClose();
        router.refresh();
      }
    } catch (err) {
      toast({
        title: translations.es.Error,
        description: translations.es.ErrorUpdatingAppointmentHomeVisitor,
        status: 'error',
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="py-2">
      <Select
        value={selectedHomeVisitor}
        onChange={handleChange}
        name="homeVisitors"
        options={availableHomeVisitorsOptions}
        className="basic-multi-select"
        classNamePrefix="select"
        placeholder={translations.es.AllHomeVisitors}
      />
      <div className="flex justify-center pt-10">
        <StepperButton
          text={'Confirmar'}
          variant={'solid'}
          onClick={assignAppointmentToSelectedHomeVisitor}
          className={'text-white bg-primaryPurple py-2 my-2 rounded-md  hover:!bg-primaryPurple w-3/6'}
          isDisabled={isLoading || !selectedHomeVisitor}
        />
      </div>
    </div>
  );
};
