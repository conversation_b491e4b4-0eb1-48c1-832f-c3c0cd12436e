'use client';
import ZoomImage from '@/app/dashboard/flotilla/components/others/ZoomImage';
import { useState } from 'react';
import { useSession } from 'next-auth/react';
import axios from 'axios';
import Spinner from '@/components/Loading/Spinner';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';

interface Document {
  url: string;
  originalName: string;
}

interface SalesDocumentsProps {
  data: {
    purchaseAgreement?: Document;
    soldInvoicePdf?: Document;
    soldInvoiceXml?: Document;
    platesCancelation?: Document;
    _id: string;
  };
}

export default function SalesDocumentsBox({ data: initialData }: SalesDocumentsProps) {
  const { user } = useCurrentUser();
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(initialData); // Estado local para los datos
  const { data: session } = useSession();
  const toast = useToast();

  const handleUpload = async (
    file: File,
    documentType: 'purchaseAgreement' | 'soldInvoicePdf' | 'soldInvoiceXml' | 'platesCancelation'
  ) => {
    setIsLoading(true);

    const formData = new FormData();
    formData.append('soldDocument', file);
    formData.append('type', documentType);
    formData.append('historyData[userId]', user._id);
    formData.append('historyData[step]', 'VENTA');
    formData.append('historyData[description]', `Documento de venta (${documentType}) actualizado`);

    try {
      const response = await axios.patch(`${URL_API}/stock/update/salesDocuments/${data._id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `bearer ${session ? user.accessToken : null}`,
        },
      });

      // Actualizar el estado local con los nuevos datos
      setData((prev) => ({
        ...prev,
        [documentType]: {
          url: response.data.documentUrl,
          originalName: file.name,
        },
      }));

      toast({
        title: 'Documento actualizado',
        status: 'success',
        position: 'top',
      });
      return true;
    } catch (error) {
      toast({
        title: 'Error al actualizar documento',
        status: 'error',
        position: 'top',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) return <Spinner />;

  return (
    <div className="w-full bg-white flex flex-col gap-3 py-[25px] px-[20px] border border-[#EAECEE] font-bold rounded">
      <p className="font-bold text-[24px]">Documentos de venta</p>
      <div className="flex flex-row flex-wrap gap-4">
        <div className="min-w-[250px] flex-1">
          <DocumentUploader
            label="Contrato de compraventa"
            document={data.purchaseAgreement}
            accept=".pdf,.doc,.docx"
            onUpload={(file) => handleUpload(file, 'purchaseAgreement')}
            disabled={isLoading}
          />
        </div>

        <div className="min-w-[250px] flex-1">
          <DocumentUploader
            label="Factura de venta (PDF)"
            document={data.soldInvoicePdf}
            accept=".pdf"
            onUpload={(file) => handleUpload(file, 'soldInvoicePdf')}
            disabled={isLoading}
          />
        </div>

        <div className="min-w-[250px] flex-1">
          <DocumentUploader
            label="Factura de venta (XML)"
            document={data.soldInvoiceXml}
            accept=".xml"
            onUpload={(file) => handleUpload(file, 'soldInvoiceXml')}
            disabled={isLoading}
          />
        </div>
        <div className="min-w-[250px] flex-1">
          <DocumentUploader
            label="Baja de placas"
            document={data.platesCancelation}
            accept=".pdf"
            onUpload={(file) => handleUpload(file, 'platesCancelation')}
            disabled={isLoading}
          />
        </div>
      </div>
    </div>
  );
}

interface DocumentUploaderProps {
  label: string;
  document?: { url: string; originalName: string };
  accept: string;
  onUpload: (file: File) => Promise<boolean>;
  disabled?: boolean;
}

function DocumentUploader({ label, document, accept, onUpload, disabled }: DocumentUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0] && !disabled) {
      setIsUploading(true);
      const success = await onUpload(e.target.files[0]);
      setIsUploading(false);
      if (success) {
        e.target.value = '';
      }
    }
  };

  return (
    <div className="flex flex-col gap-1">
      <p className="font-bold">{label}</p>

      {document && (
        <div className="flex items-center gap-2 mb-2">
          <a
            href={document.url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
          >
            {document.originalName.substring(0, 20) + (document.originalName.length > 20 ? '...' : '')}
          </a>
          <ZoomImage imageUrl={document.url} name={document.originalName} />
        </div>
      )}

      <div className="relative">
        <input
          type="file"
          id={`upload-${label}`}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          accept={accept}
          onChange={handleFileChange}
          disabled={disabled || isUploading}
        />
        <label
          htmlFor={`upload-${label}`}
          className={`inline-block px-4 py-2 text-sm font-medium rounded-md ${
            disabled || isUploading
              ? 'bg-gray-300 cursor-not-allowed'
              : 'bg-blue-100 text-blue-700 hover:bg-blue-200 cursor-pointer'
          }`}
        >
          {isUploading ? 'Subiendo...' : document ? 'Reemplazar documento' : 'Subir documento'}
        </label>
      </div>
    </div>
  );
}
