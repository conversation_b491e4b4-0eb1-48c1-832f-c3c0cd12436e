import * as Yup from 'yup';
import { fileListValidator, fileValidator } from './filesValidators';
import createSelectInputValidator from './selectInputValidator';

export const sendOverHaulingSchema = Yup.object().shape({
  dateIn: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  inImgs: fileListValidator('Debe seleccionar al menos 5 imagenes', 'all-images', 5),
  quotationDoc: fileValidator('Debe seleccionar un archivo', 'pdf'),
  comments: Yup.string(),
});

export const finishOverHaulingSchema = Yup.object().shape({
  dateOut: Yup.string().min(9, 'Fecha incompleta').required('Fecha requerida'),
  outImgs: fileListValidator('Debe seleccionar al menos 5 imagenes', 'all-images', 5),
  hasInvoice: createSelectInputValidator('Selecciona una opción'),
  invoiceFile: Yup.mixed().when('hasInvoice', {
    is: (option: any) => option.value && option.value === 'Si', // If hasInvoice is 'Si' then it's required
    then: fileValidator('Debe seleccionar un archivo', 'pdf'),
    otherwise: Yup.mixed(),
  }),
  invoiceAmount: Yup.mixed().when('hasInvoice', {
    is: (option: any) => option.value && option.value === 'Si',
    then: Yup.number().required('Monto requerido'),
    otherwise: Yup.mixed(),
  }),
});
