'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
// eslint-disable-next-line import/no-extraneous-dependencies
import { z } from 'zod';
// eslint-disable-next-line import/no-extraneous-dependencies
import { zodResolver } from '@hookform/resolvers/zod';
// eslint-disable-next-line import/no-extraneous-dependencies
import { useForm, useFieldArray } from 'react-hook-form';

import { Input } from '@/components/ui/input';
import { ChevronsUpDown, SquarePlus, CircleMinus, Check, X } from 'lucide-react';
import axios from 'axios';
import { Countries, PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { cn } from '@/lib/utils';
import { Product } from '@/app/dashboard/pagos/types';
import { useEffect, useState } from 'react';
import SelectRegion from '@/app/dashboard/pagos/productos/(productos)/SelectRegion';
import { CustomPopoverContent, CustomPopover, CustomPopoverTrigger } from '../common/ReactPopover';

const ProductSchema = z.object({
  id: z.string({ required_error: 'please select a product' }).min(1, 'please select a product'),
  quantity: z.coerce.number(),
});

const FormSchema = z.object({
  clientId: z.string(),
  products: z.array(ProductSchema),
  payment_form: z.string(),
});

type Props = {
  open: boolean;
  setOpen: (val: boolean) => void;
};

type Client = {
  id: string;
  contractNumber: string;
  email: string;
  legal_name: string;
};

async function getClients(search?: string): Promise<Client[]> {
  let res = null;
  try {
    let url = `${PAYMENTS_API_URL}/clients`;

    if (search) {
      url += '/q?search=' + search;
    }

    res = await axios(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

async function getProducts(region?: string): Promise<Product[]> {
  let res = null;
  try {
    const url = new URL(`${PAYMENTS_API_URL}/products`);
    if (region && region !== 'none') url.searchParams.append('region', region);

    res = await axios(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

type LineItem = {
  product: Product | null;
  quantity: number;
};

export function PaymentModal({ open, setOpen }: Props) {
  const [region, setRegion] = useState('');
  const [productsData, setProductsData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [clientData, setClientData] = useState<Client[]>([]);
  const [lineItemData, setLineItemData] = useState<LineItem[]>([]);
  const [total, setTotal] = useState(0);
  const [disableSubmit, setDisableSubmit] = useState(false);

  useEffect(() => {
    if (searchText) {
      setLoading(true);
      getClients(searchText).then((res) => {
        setLoading(false);
        setClientData(res);
      });
    }
  }, [searchText]);

  const addLineItem = (lineItem: LineItem) => {
    setLineItemData([...lineItemData, lineItem]);
  };

  const removeLineItem = (index: number) => {
    const updatedLineItems = [...lineItemData];
    updatedLineItems.splice(index, 1);
    setLineItemData(updatedLineItems);
  };

  const updateProduct = (product: Product, index: number) => {
    if (product) {
      const updateLineItems = [...lineItemData];
      updateLineItems[index].product = product;
      setLineItemData(updateLineItems);
    }
  };

  const updateProductQuantity = (quantity: number, index: number) => {
    const updateLineItems = [...lineItemData];
    updateLineItems[index].quantity = quantity;
    setLineItemData(updateLineItems);
  };

  useEffect(() => {
    let sum = 0;
    lineItemData.map((lineItem) => {
      if (lineItem.product?.ivaIncluded) {
        sum += (lineItem.quantity ? lineItem.quantity : 0) * (lineItem?.product?.price ?? 0);
      } else {
        sum +=
          (lineItem.quantity ? lineItem.quantity : 0) *
          (lineItem?.product?.price ?? 0) *
          (1 + (lineItem?.product?.taxRate ?? 0));
      }
    });
    setTotal(sum);
  }, [lineItemData]);

  useEffect(() => {
    setLoading(true);
    getProducts(region).then((res) => {
      setLoading(false);
      setProductsData(res);
    });
  }, [region]);

  const form = useForm<z.infer<typeof FormSchema>>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      clientId: '',
      products: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'products',
  });

  function onSubmit(data: z.infer<typeof FormSchema>) {
    console.log(data);
    setDisableSubmit(true);
    try {
      axios
        .post(`${PAYMENTS_API_URL}/payments/isolated`, data, {
          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
            'Content-Type': 'application/json',
          },
        })
        .then((response) => {
          setDisableSubmit(false);
          if (response?.status === 200) {
            form.reset();
            setOpen(false);
          }
        });
    } catch (error) {
      setDisableSubmit(false);
      console.log(error);
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          onClick={() => {
            setOpen(true);
          }}
        >
          <SquarePlus className="h-4 w-4 pr-1" />
          Crear pago aislado
        </Button>
      </DialogTrigger>
      <DialogContent className="w-4/5 max-h-screen overflow-y-auto">
        {loading || form.formState.isSubmitting ? (
          <div role="status" className="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
            <svg
              aria-hidden="true"
              className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        ) : (
          ''
        )}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <DialogHeader>
              <DialogTitle>Crear pago aislado</DialogTitle>
            </DialogHeader>
            <div className="flex flex-row items-center">
              <FormLabel className="basis-1/5">Region</FormLabel>
              <SelectRegion region={region} setRegion={setRegion} country={Countries.Mexico} />
            </div>
            <FormField
              control={form.control}
              name="payment_form"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center">
                  <FormLabel className="basis-1/5">Payment Form</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl className="basis-4/5">
                      <SelectTrigger>
                        <SelectValue placeholder="Select Payment Form" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="01">EFECTIVO</SelectItem>
                      <SelectItem value="02">CHEQUE NOMINATIVO</SelectItem>
                      <SelectItem value="03">TRANSFERENCIA ELECTRONICA DE FONDOS</SelectItem>
                      <SelectItem value="04">TARJETA DECREDITO</SelectItem>
                      <SelectItem value="05">MONEDERO ELECTRONICO</SelectItem>
                      <SelectItem value="06">DINERO ELECTRONICO</SelectItem>
                      <SelectItem value="08">VALES DE DESPENSA</SelectItem>
                      <SelectItem value="12">DACION EN PAGO</SelectItem>
                      <SelectItem value="13">PAGO POR SUBROGACION</SelectItem>
                      <SelectItem value="14">PAGO POR CONSIGNACION</SelectItem>
                      <SelectItem value="17">COMPENSACION</SelectItem>
                      <SelectItem value="23">NOVACION</SelectItem>
                      <SelectItem value="24">CONFUSION</SelectItem>
                      <SelectItem value="25">REMISIÓN DE DEUDA</SelectItem>
                      <SelectItem value="26">PRESCRIPCION O CADUCIDAD</SelectItem>
                      <SelectItem value="27">A SATISFACCION DEL ACREEDOR</SelectItem>
                      <SelectItem value="28">TARJETA DE DEBITO</SelectItem>
                      <SelectItem value="29">TARJETA DE SERVICIOS</SelectItem>
                      <SelectItem value="30">APLICACION DE ANTICIPOS</SelectItem>
                      <SelectItem value="31">INTERMEDIARIO DE PAGOS</SelectItem>
                      <SelectItem value="99">POR DEFINIR</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name={`clientId`}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center">
                  <FormLabel className="basis-1/5">Client</FormLabel>
                  <CustomPopover modal={true}>
                    <CustomPopoverTrigger asChild>
                      <FormControl className="basis-4/5">
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn('w-[200px] justify-between', !field.value && 'text-muted-foreground')}
                        >
                          {field.value
                            ? clientData.find((client: Client) => client.id === field.value)?.contractNumber +
                              ' : ' +
                              clientData.find((client: Client) => client.id === field.value)?.legal_name
                            : 'Select Client'}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </CustomPopoverTrigger>
                    <CustomPopoverContent className="basis-4/5 p-0">
                      <Command>
                        <CommandInput
                          onValueChange={(e) => {
                            setSearchText(e);
                          }}
                          placeholder="Search Client..."
                        />
                        <CommandEmpty>
                          {searchText ? 'No client found' : 'Please enter client contract number or name'}
                        </CommandEmpty>
                        <CommandList>
                          <CommandGroup>
                            {clientData.map((client: Client) => (
                              <CommandItem
                                value={client.contractNumber + ' : ' + client.legal_name}
                                key={client.id}
                                onSelect={() => {
                                  form.setValue('clientId', client.id);
                                }}
                              >
                                {client.contractNumber + ' : ' + client.legal_name}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </CustomPopoverContent>
                  </CustomPopover>
                  <FormMessage />
                </FormItem>
              )}
            />
            {form.formState?.errors.clientId && (
              <FormMessage>{form?.formState?.errors?.clientId?.message}</FormMessage>
            )}

            <div className="mt-8 border rounded-lg overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead className="w-[80px]">Cantidad</TableHead>
                    <TableHead className="w-[120px]">Price</TableHead>
                    <TableHead>Tax Rate</TableHead>
                    <TableHead className="w-[120px]">Tax</TableHead>
                    <TableHead className="w-[120px]">Subtotal</TableHead>
                    <TableHead>IVA Included</TableHead>
                    <TableHead className="w-[120px]">Amount</TableHead>
                    <TableHead className="w-[60px]" />
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fields.map((fld, index) => (
                    <TableRow key={fld.id}>
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`products.${index}.id`}
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <CustomPopover>
                                <CustomPopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant="outline"
                                      role="combobox"
                                      className={cn(
                                        'w-[300px] justify-between truncate',
                                        !field.value && 'text-muted-foreground'
                                      )}
                                    >
                                      {field.value
                                        ? productsData.find((product: Product) => product.id === field.value)
                                            ?.name
                                        : 'Select Product'}
                                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </CustomPopoverTrigger>
                                <CustomPopoverContent className="w-[200px] p-0">
                                  <Command>
                                    <CommandInput placeholder="Search Product..." />
                                    <CommandEmpty>No products found.</CommandEmpty>
                                    <CommandList>
                                      <CommandGroup>
                                        {productsData.map((product: Product) => (
                                          <CommandItem
                                            value={product.name}
                                            key={product.id}
                                            onSelect={() => {
                                              form.setValue(`products.${index}`, {
                                                id: product.id,
                                                quantity: 1,
                                              });
                                              updateProduct(product, index);
                                            }}
                                          >
                                            {product.name}
                                          </CommandItem>
                                        ))}
                                      </CommandGroup>
                                    </CommandList>
                                  </Command>
                                </CustomPopoverContent>
                              </CustomPopover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {form.formState?.errors.products?.[index] && (
                          <FormMessage>{form?.formState?.errors?.products[index]?.message}</FormMessage>
                        )}
                      </TableCell>
                      <TableCell>
                        <FormField
                          control={form.control}
                          name={`products.${index}.quantity`}
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormControl>
                                <Input
                                  placeholder="Cantidad"
                                  type="number"
                                  min={1}
                                  {...field}
                                  value={lineItemData[index].quantity}
                                  onChange={(e) => {
                                    updateProductQuantity(parseInt(e.target.value), index);
                                    form.setValue(`products.${index}`, {
                                      id: lineItemData[index]?.product?.id || '',
                                      quantity: parseInt(e.target.value) || 1,
                                    });
                                  }}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <Input value={lineItemData[index].product?.price ?? 0} disabled={true}></Input>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <Input value={lineItemData[index].product?.taxRate ?? 0} disabled={true}></Input>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <Input value={lineItemData[index].product?.tax ?? 0} disabled={true}></Input>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <Input value={lineItemData[index].product?.subTotal ?? 0} disabled={true}></Input>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          {lineItemData[index].product?.ivaIncluded ? <Check /> : <X />}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <Input
                            value={(lineItemData[index].product?.ivaIncluded
                              ? (lineItemData[index].product?.price ?? 0) *
                                (lineItemData[index].quantity ? lineItemData[index].quantity : 1)
                              : (lineItemData[index].product?.price ?? 0) *
                                (lineItemData[index].quantity ? lineItemData[index].quantity : 1) *
                                (1 + (lineItemData[index].product?.taxRate ?? 0))
                            ).toFixed(2)}
                            disabled={true}
                          ></Input>
                        </div>
                      </TableCell>
                      <TableCell>
                        <CircleMinus
                          color="#EE4B2B"
                          onClick={() => {
                            remove(index);
                            removeLineItem(index);
                          }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                append({ id: '', quantity: 1 });
                addLineItem({ product: null, quantity: 1 });
              }}
            >
              <SquarePlus />
              Add Product
            </Button>
            <div className="mt-4 text-right font-medium">Total: ${total.toFixed(2)}</div>
            <DialogFooter>
              <Button
                type="button"
                onClick={() => {
                  form.reset();
                  setRegion('');
                  setOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={disableSubmit}>
                Crear
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
