'use client';

import React, { useEffect, useState } from 'react';
import { Receipt } from '../../../app/dashboard/pagos/types';
import axios from 'axios';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import { DataTable } from '@/components/DataTable';
import { receiptColumns } from '../../../app/dashboard/pagos/clientDetails/(clientDetails)/receiptColumns';
import { Skeleton } from '@/components/ui/skeleton';

async function getReceipts(id: string): Promise<Receipt> {
  let res = null;
  try {
    res = await axios(`${PAYMENTS_API_URL}/payments/receipts/${id}`, {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res?.data?.data : res;
}

type Props = {
  id?: string;
};

function ReceiptTable({ id }: Props) {
  const [receiptData, setReceiptData] = useState<Receipt[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    try {
      if (id) {
        setLoading(true);
        getReceipts(id as string).then((res) => {
          const receiptArr: Receipt[] = [res];
          setReceiptData(receiptArr);
          setLoading(false);
        });
      }
    } catch (error) {
      console.log(error);
    }
  }, [id]);

  if (loading) {
    return <Skeleton className="w-full h-28"></Skeleton>;
  } else {
    return (
      <div className="flex flex-col">
        <h2 className="text-3xl">Detalles del recibo</h2>
        <DataTable columns={receiptColumns} data={receiptData} showPagination={false}></DataTable>
      </div>
    );
  }
}

export default ReceiptTable;
