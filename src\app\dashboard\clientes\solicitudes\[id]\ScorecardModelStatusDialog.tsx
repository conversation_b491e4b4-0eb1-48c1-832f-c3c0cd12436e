'use client';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>lose<PERSON>utton,
  ModalHeader,
  ModalOverlay,
  ModalContent,
  Table,
  TableContainer,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Text,
} from '@chakra-ui/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AnalysisStatus, MLModels } from '../enums';
import { AdmissionRequest } from './ScorecardCard';
import { ModelScores } from '../types';
// import { useCountry } from '@/app/dashboard/providers/CountryProvider';

// Natural language model names with an optional Mexican translation
const modelNamesTranslation: {
  [key in MLModels]: { en: string; mx: string };
} = {
  [MLModels.RIDESHARE_PERFORMANCE]: {
    en: 'Rideshare Performance',
    mx: 'Desempeño Transporte Compartido',
  },
  [MLModels.FINANCIAL_ASSESSMENT]: {
    en: 'Financial Assessment',
    mx: 'Evaluación Financiera',
  },
  [MLModels.PERSONAL_INFORMATION]: {
    en: 'Personal Information',
    mx: 'Información Personal',
  },
  [MLModels.HOMEVISIT_INFORMATION]: {
    en: 'Home Visit Information',
    mx: 'Información de Visita Domiciliaria',
  },
  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: {
  //   en: 'Driving and Legal History',
  //   mx: 'Historial de Conducción y Legal',
  // },
};

const tableTranslations = {
  title: {
    en: 'Model Scores Details',
    mx: 'Detalles de Puntajes de Modelos',
  },
  headers: {
    model: { en: 'Model', mx: 'Modelo' },
    status: { en: 'Status', mx: 'Estado' },
    score: { en: 'Score', mx: 'Puntaje' },
  },
  status: {
    completed: { en: 'Completed', mx: 'Completado' },
    pending: { en: 'Pending', mx: 'Pendiente' },
    error: { en: 'Error', mx: 'Error' },
    notFound: { en: 'Not Found', mx: 'No Encontrado' },
  },
};

interface ModelScoresDialogProps {
  admissionRequest: AdmissionRequest;
}

export function ModelScoresDialog({ admissionRequest }: ModelScoresDialogProps) {
  const modelScores = admissionRequest.modelScores;
  const router = useRouter();
  const searchParams = useSearchParams();
  // HINT: CANT USE THIS AS THE PROVIDER DOESNT WRAP IN LAYOUT.TSX BUT RATHER PAGE.TSX
  // const { isCountryUSA } = useCountry();
  const lang = 'mx';

  const isOpen = searchParams.get('dialog') === 'view-model-scores';

  function onClose() {
    router.back();
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent minWidth="fit-content">
        <ModalHeader>{tableTranslations.title[lang]}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <TableContainer fontSize="sm">
            <Table variant="striped">
              <Thead>
                <Tr>
                  <Th>{tableTranslations.headers.model[lang]}</Th>
                  <Th>{tableTranslations.headers.status[lang]}</Th>
                  <Th>{tableTranslations.headers.score[lang]}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {Object.values(MLModels).map((modelName) => {
                  const model = modelScores?.[modelName as keyof ModelScores];
                  return (
                    <Tr key={modelName}>
                      <Td>{modelNamesTranslation[modelName][lang]}</Td>
                      <Td>
                        {model?.status === AnalysisStatus.COMPLETED && (
                          <Text color="green.500">{tableTranslations.status.completed[lang]}</Text>
                        )}
                        {(!model || model?.status === AnalysisStatus.PENDING) && (
                          <Text color="yellow.500">{tableTranslations.status.pending[lang]}</Text>
                        )}
                        {model?.status === AnalysisStatus.ERROR && (
                          <Text color="red.500">{tableTranslations.status.error[lang]}</Text>
                        )}
                      </Td>
                      <Td>
                        {model?.status === AnalysisStatus.COMPLETED ? (
                          <Text>{Number(model.modelScore).toFixed(2)}%</Text>
                        ) : (
                          <Text textAlign={'center'}>-</Text>
                        )}
                      </Td>
                    </Tr>
                  );
                })}
              </Tbody>
            </Table>
          </TableContainer>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
