import { MLModels, AnalysisStatus, AdmissionRequestStatus } from '../enums';
import { useCountry } from './detail';
import { ModelScores } from '../types';
import ScorecardChartFallback from './ScorecardChartFallback';
import AnalysisCard from './AnalysisCard';
import { SocialAnalysisCard } from './SocialAnalysisCard';
import { RiskAnalysisCard } from './RiskAnalysisCard';

// Helper weights – legal history is ignored (0 weight)
const WEIGHTS: Record<MLModels, number> = {
  [MLModels.RIDESHARE_PERFORMANCE]: 1,
  [MLModels.FINANCIAL_ASSESSMENT]: 1,
  [MLModels.PERSONAL_INFORMATION]: 1,
  [MLModels.HOMEVISIT_INFORMATION]: 1,
  // [MLModels.DRIVING_AND_LEGAL_HISTORY]: 0,
};

// Calculate final weighted score from modelscores (only COMPLETED ones are considered)
export function getFinalModelScore(modelScores: ModelScores): number {
  let sumScore = 0;
  let sumWeight = 0;
  (Object.keys(WEIGHTS) as MLModels[]).forEach((modelKey) => {
    const model = modelScores?.[modelKey];
    if (model && model.status === AnalysisStatus.COMPLETED) {
      sumScore += model.modelScore * WEIGHTS[modelKey];
      sumWeight += WEIGHTS[modelKey];
    }
  });
  return sumWeight > 0 ? sumScore / sumWeight : 0;
}

export interface AdmissionRequest {
  modelScores: ModelScores;
  earningsAnalysis?: { status: string };
  documentsAnalysis?: { status: string };
  homeVisit?: { status: string };
  [key: string]: any;
}

export function canRunModel(modelName: MLModels, admissionRequest: AdmissionRequest): boolean {
  if (modelName === MLModels.RIDESHARE_PERFORMANCE || modelName === MLModels.FINANCIAL_ASSESSMENT) {
    return (
      admissionRequest.earningsAnalysis?.status === 'approved' &&
      admissionRequest.documentsAnalysis?.status === 'approved'
    );
  }
  if (modelName === MLModels.PERSONAL_INFORMATION || modelName === MLModels.HOMEVISIT_INFORMATION) {
    return admissionRequest.homeVisit?.status === 'approved';
  }
  // For DRIVING_AND_LEGAL_HISTORY, or any other model, disallow retry for now
  return false;
}

export function showRiskAnalysisFallback(admissionRequest: AdmissionRequest): boolean {
  return (
    canRunModel(MLModels.RIDESHARE_PERFORMANCE, admissionRequest) &&
    canRunModel(MLModels.FINANCIAL_ASSESSMENT, admissionRequest) &&
    !admissionRequest.modelScores?.[MLModels.PERSONAL_INFORMATION]?.status &&
    !admissionRequest.modelScores?.[MLModels.FINANCIAL_ASSESSMENT]?.status
  );
}
export default function ScorecardCard({
  request,
  requestId,
}: {
  request: AdmissionRequest;
  requestId: string;
}) {
  const { modelScores, status: stepStatus } = request;
  const { isCountryUSA } = useCountry();
  const riskAnalysisText = isCountryUSA ? 'Risk analysis' : 'Análisis de riesgo';
  const seeDetailsText = isCountryUSA ? 'See detail' : 'Ver detalle';

  const showfallback = showRiskAnalysisFallback(request);

  // Fallback: if modelscores are not available then use API-based risk analysis
  if (showfallback) {
    return <ScorecardChartFallback requestId={requestId} />;
  }

  // Determine statuses for key models
  const ridesharePerformanceStatus = modelScores?.[MLModels.RIDESHARE_PERFORMANCE]?.status;
  const financialAssessmentStatus = modelScores?.[MLModels.FINANCIAL_ASSESSMENT]?.status;
  const personalInfoStatus = modelScores?.[MLModels.PERSONAL_INFORMATION]?.status;
  const homeVisitStatus = modelScores?.[MLModels.HOMEVISIT_INFORMATION]?.status;

  // -------------------------------------------------------------------------
  // Branch 1: If stepStatus is before risk_analysis, always show risk analysis pending.
  if (
    stepStatus === AdmissionRequestStatus.created ||
    stepStatus === AdmissionRequestStatus.earnings_analysis ||
    stepStatus === AdmissionRequestStatus.documents_analysis
  ) {
    return (
      <AnalysisCard
        title={riskAnalysisText}
        numericScore={0}
        category=""
        seeDetailsText={seeDetailsText}
        isPending={true}
      />
    );
  }

  // -------------------------------------------------------------------------
  // Branch 2: For stepStatus 'risk_analysis' or 'home_visit' → show risk analysis.
  if (
    stepStatus === AdmissionRequestStatus.risk_analysis ||
    stepStatus === AdmissionRequestStatus.home_visit
  ) {
    // Check for errors first
    const hasError =
      ridesharePerformanceStatus === AnalysisStatus.ERROR ||
      financialAssessmentStatus === AnalysisStatus.ERROR;

    // Then check for pending/missing
    const isPending =
      !ridesharePerformanceStatus ||
      ridesharePerformanceStatus === AnalysisStatus.PENDING ||
      !financialAssessmentStatus ||
      financialAssessmentStatus === AnalysisStatus.PENDING;

    return (
      <RiskAnalysisCard
        modelScores={modelScores}
        hasError={hasError}
        isPending={isPending}
        isCountryUSA={isCountryUSA}
      />
    );
  }

  // -------------------------------------------------------------------------
  // Branch 3: For stepStatus 'social_analysis', 'approved', 'rejected' → show social analysis.
  if (
    stepStatus === AdmissionRequestStatus.social_analysis ||
    stepStatus === AdmissionRequestStatus.approved ||
    stepStatus === AdmissionRequestStatus.rejected
  ) {
    // Check for errors first
    const hasError =
      ridesharePerformanceStatus === AnalysisStatus.ERROR ||
      financialAssessmentStatus === AnalysisStatus.ERROR ||
      personalInfoStatus === AnalysisStatus.ERROR ||
      homeVisitStatus === AnalysisStatus.ERROR;

    // Then check for pending/incomplete
    const isPending =
      !ridesharePerformanceStatus ||
      ridesharePerformanceStatus !== AnalysisStatus.COMPLETED ||
      !financialAssessmentStatus ||
      financialAssessmentStatus !== AnalysisStatus.COMPLETED ||
      !personalInfoStatus ||
      personalInfoStatus !== AnalysisStatus.COMPLETED ||
      !homeVisitStatus ||
      homeVisitStatus !== AnalysisStatus.COMPLETED;

    return (
      <SocialAnalysisCard
        modelScores={modelScores}
        hasError={hasError}
        isPending={isPending}
        isCountryUSA={isCountryUSA}
      />
    );
  }
}
