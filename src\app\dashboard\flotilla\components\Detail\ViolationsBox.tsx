'use client';
import { ColumnDef } from '@tanstack/react-table';
import TableInDetail from '../TableInDetail';
import { VehicleResponse } from '@/actions/getVehicleData';
import { useMemo, useState } from 'react';
import axios from 'axios';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import Swal from 'sweetalert2';
import PrimaryButton from '@/components/PrimaryButton';
import {
  UpdateViolationsUS,
  UpdateViolationsMX,
  PleaseWaitMX,
  PleaseWaitUS,
  FailUpdateTitleMX,
  FailUpdateTitleUS,
  SuccessUpdateTextMX,
  SuccessUpdateTextUS,
  SuccessUpdateTitleMX,
  SuccessUpdateTitleUS,
} from '@/app/dashboard/clientes/_components/translations';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';

interface ColumnType {
  id: string;
  name: string;
  contract: string;
  reason: string;
  date: string;
}

interface ViolationsBoxProps {
  vehicleData: VehicleResponse;
}

const statusMap = {
  paid: 'pagado',
  unpaid: 'no pagado',
  unknown: 'desconocido',
};

export default function ViolationsBox({ vehicleData }: ViolationsBoxProps) {
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [isLoading, setIsLoading] = useState(false);
  const { isCountryUSA } = useCountry();

  const parsedViolations = useMemo(() => {
    if (!vehicleData?.violations?.length) return [];
    return vehicleData?.violations?.reverse().map((read, i) => {
      return {
        id: i + 1,
        folio: read.folio,
        violationDate: new Date(read.violationDate).toISOString().split('T')[0],
        status: statusMap[read.status as keyof typeof statusMap] || read.status,
        amount: read.amount,
      };
    });
  }, [vehicleData]);

  const columns: ColumnDef<ColumnType>[] = [
    {
      header: 'No',
      accessorKey: 'id',
    },
    {
      header: 'Folio',
      accessorKey: 'folio',
    },
    {
      header: 'Fecha de Infracción',
      accessorKey: 'violationDate',
    },
    {
      header: 'Situación',
      accessorKey: 'status',
    },
    {
      header: 'Monto',
      accessorKey: 'amount',
    },
  ];

  const fetchViolations = async () => {
    if (isLoading) return;
    setIsLoading(true);

    const carNumber = vehicleData.carNumber;
    const data = { carNumber };

    Swal.fire({
      title: isCountryUSA ? UpdateViolationsUS : UpdateViolationsMX,
      text: isCountryUSA ? PleaseWaitUS : PleaseWaitMX,
      allowOutsideClick: () => false,
      allowEscapeKey: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    try {
      const response = await axios.patch(`${URL_API}/vehicleViolation/updateViolationByCarNumber`, data, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.data.status) {
        Swal.fire({
          title: isCountryUSA ? SuccessUpdateTitleUS : SuccessUpdateTitleMX,
          text: isCountryUSA ? SuccessUpdateTextUS : SuccessUpdateTextMX,
          icon: 'success',
          confirmButtonText: 'Cerrar',
        }).then((result) => {
          if (result.isConfirmed) {
            window.location.reload();
          }
        });
      } else {
        setIsLoading(false);
        Swal.fire({
          title: isCountryUSA ? FailUpdateTitleUS : FailUpdateTitleMX,
          text:
            response?.data?.message ||
            'No se pudieron obtener las infracciones. Por favor, inténtelo de nuevo más tarde.Error desconocido',
          icon: 'error',
          confirmButtonText: 'Cerrar',
        });
      }
    } catch (error: any) {
      setIsLoading(false);
      Swal.fire({
        title: isCountryUSA ? FailUpdateTitleUS : FailUpdateTitleMX,
        text: 'No se pudieron obtener las infracciones. Por favor, inténtelo de nuevo más tarde.Error desconocido',
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    }
  };

  return (
    <>
      <div
        className="
        w-full
        bg-white 
        border-[#EAECEE] 
        font-bold 
        rounded 
        min-h-[300px] 
        py-[25px]
        pl-[20px]
        pr-[15px]
        border-[1px]
        flex
        flex-col
        overflow-y-auto
      "
      >
        <div className="flex justify-between">
          <p className="text-[24px] mb-[5px] items-center">
            <span>
              Infracciones
              <span className="text-sm text-gray-500 ml-2">
                Última actualización:{' '}
                {vehicleData?.lastViolationCheck
                  ? `${new Date(vehicleData.lastViolationCheck).toISOString().split('T')[0]}`
                  : '—'}
              </span>
            </span>
          </p>
          {(vehicleData?.lastViolationCheck == null ||
            (vehicleData?.lastViolationCheck &&
              new Date(vehicleData.lastViolationCheck).toISOString().split('T')[0] !==
                new Date().toISOString().split('T')[0])) && (
            <PrimaryButton onClick={fetchViolations}>Recuperar infracciones</PrimaryButton>
          )}
        </div>
        <TableInDetail data={parsedViolations} columns={columns} isDisplayPagination={true} />
      </div>
    </>
  );
}
