import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import RecurringPaymentTab from './(clientDetails)/RecurringPaymentTab';
import ClientDetailCard from './(clientDetails)/ClientDetailCard';
import PaymentTab from './(clientDetails)/PaymentTab';
import InvoiceTab from './(clientDetails)/InvoiceTab';
import ReceiptTab from './(clientDetails)/ReceiptTab';
import { RiArrowLeftSLine } from 'react-icons/ri';
import Link from 'next/link';
import { getClientDetails } from '@/actions/getClientDetails';
interface PageProps {
  searchParams: {
    id?: string;
  };
}

export const metadata = {
  title: 'Detalles del cliente',
};

export default async function ClientDetails(props: PageProps) {
  const { id } = props.searchParams;

  const clientData = await getClientDetails(id as string);

  if (!clientData) {
    return (
      <>
        <Link href={`/dashboard/pagos/clientes`} prefetch={false}>
          <div className="fixed top-[12px] z-30 text-[#5800F7] flex items-center cursor-pointer">
            <RiArrowLeftSLine color="#5800F7" size={32} />
            <button>Regresar</button>
          </div>
        </Link>

        <div className="flex justify-center items-center h-full text-2xl font-bold">
          <p>Cliente no encontrado</p>
        </div>
      </>
    );
  }

  function renderClientData() {
    return (
      <>
        <Link href={`/dashboard/pagos/clientes`} prefetch={false}>
          <div className="fixed top-[12px] z-30 text-[#5800F7] flex items-center cursor-pointer">
            <RiArrowLeftSLine color="#5800F7" size={32} />
            <button>Regresar</button>
          </div>
        </Link>
        <ClientDetailCard clientData={clientData} />
        <section className="pt-24">
          <Tabs defaultValue="payments" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="payments">Pagos</TabsTrigger>
              <TabsTrigger value="recurring_payments">Pagos Recurrentes</TabsTrigger>
              <TabsTrigger value="sales_receipts">Recibos de venta</TabsTrigger>
              <TabsTrigger value="bills">Facturas</TabsTrigger>
            </TabsList>
            <TabsContent value="payments">
              <PaymentTab id={id} />
            </TabsContent>
            <TabsContent value="recurring_payments">
              <RecurringPaymentTab id={id} />
            </TabsContent>
            <TabsContent value="sales_receipts">
              <ReceiptTab id={id} />
            </TabsContent>
            <TabsContent value="bills">
              <InvoiceTab id={id} />
            </TabsContent>
          </Tabs>
        </section>
      </>
    );
  }

  return (
    <div>
      {id ? (
        renderClientData()
      ) : (
        <div>
          <p>Data not available</p>
        </div>
      )}
    </div>
  );
}
