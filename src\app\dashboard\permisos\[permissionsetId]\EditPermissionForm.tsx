'use client';
import React, { useState } from 'react';
import Swal from 'sweetalert2';
import axios from 'axios';
import CustomInput from '@/components/Inputs/CustomInput';
import SelectInput from '@/components/Inputs/SelectInput';
import { Formik, Form, FormikHelpers } from 'formik';
import { createPermissionSet } from '@/validatorSchemas/createPermissionSet';
import PermissionMatrix from '../PermissionMatrix';
import { areaOptions, roleOptions } from '@/constants';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { MyUser } from '@/actions/getCurrentUser';
import { useSession } from 'next-auth/react';
import { PermissionSetResponse } from '@/actions/getPermissionSetById';
import { Section } from '@/actions/getPermissionMatrix';
import { useRouter } from 'next/navigation';
import { getRolesByArea } from '@/utils/roleHelpers';
interface Props {
  permissionMatrix: Section[];
  permissionSet: PermissionSetResponse;
}

export interface Option {
  value: string;
  label: string;
}

const EditPermissionForm: React.FC<Props> = ({ permissionMatrix, permissionSet }) => {
  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>(() => {
    const initial: Record<string, boolean> = {};
    permissionSet.permissions.forEach((perm) => {
      const key = `${perm.section}.${perm.subSection}.${perm.capability}`;

      const section = permissionMatrix.find((s) => s.section === perm.section);
      const subSection = section?.subSections.find((ss) => ss.subSection === perm.subSection);
      const hasCapability = subSection?.capabilities.includes(perm.capability);

      initial[key] = !!hasCapability;
    });
    return initial;
  });
  const [, setSelectedArea] = useState('');
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [availableRoles, setAvailableRoles] = useState<Option[]>(
    getRolesByArea(permissionSet.area, user?.role)
  );
  const url = useCurrentUrl();
  const router = useRouter();

  const handleAreaChange = (option: Option, formik: any) => {
    const selected = option.value;
    setSelectedArea(selected);

    setAvailableRoles(getRolesByArea(selected, user?.role));

    // Reset the role field in Formik
    formik.setFieldValue('role', '');
  };

  const editPermissionSetAPI = async (body: any) => {
    const response = await axios.patch(`${url}/permissionSet/${permissionSet._id}`, body, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response;
  };

  const handleSubmit = async (
    values: { name: string; role: any; area: any },
    actions: FormikHelpers<{ name: string; role: any; area: any }>
  ) => {
    const permissionData = {
      name: values.name,
      role: values.role.value,
      area: values.area.value,
      permissions: [] as Array<{ section: string; subSection: string; capability: string }>,
    };

    Object.keys(selectedPermissions).forEach((key) => {
      if (selectedPermissions[key]) {
        const [section, subSection, capability] = key.split('.');
        permissionData.permissions.push({ section, subSection, capability });
      }
    });

    try {
      const response = await editPermissionSetAPI(permissionData);

      if (response.status === 200) {
        Swal.fire({
          title: 'Actualización exitosa',
          text: response.data.message,
          icon: 'success',
          confirmButtonText: 'Cerrar',
        }).then(() => {
          router.push('/dashboard/permisos');
        });
      } else {
        Swal.fire({
          title: 'Algo salió mal',
          text: 'Respuesta inesperada del servidor',
          icon: 'warning',
          confirmButtonText: 'Cerrar',
        });
      }
    } catch (error: any) {
      Swal.fire({
        title: 'Algo salió mal',
        text: error?.response?.data?.message || 'Ocurrió un error inesperado',
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    } finally {
      actions.setSubmitting(false);
    }
  };

  if (!permissionSet) return <div>Loading...</div>;

  return (
    <Formik
      initialValues={{
        name: permissionSet.name || '',
        role: roleOptions.find((x) => x.value === permissionSet.role),
        area: areaOptions.find((x) => x.value === permissionSet.area),
      }}
      onSubmit={handleSubmit}
      validationSchema={createPermissionSet}
      validateOnMount
    >
      <Form>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <CustomInput name="name" label="Nombre" type="text" disabled />
          <SelectInput name="area" label="Área" options={areaOptions} onChange={handleAreaChange} disabled />
          <SelectInput name="role" label="Rol" options={availableRoles} disabled />
        </div>

        <div className="mt-6">
          <PermissionMatrix
            permissionSets={permissionMatrix}
            selected={selectedPermissions}
            setSelected={setSelectedPermissions}
            disabledPermissions={{}}
          />
        </div>

        <div className="flex justify-end mt-6">
          <button
            type="submit"
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded"
          >
            Actualizar Permiso
          </button>
        </div>
      </Form>
    </Formik>
  );
};

export default EditPermissionForm;
