// components/links-visitors.tsx
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export default function PaymentTransactionTable({ paymentTransactions }: { paymentTransactions: any[] }) {
  return (
    <div className="w-full sm:p-4">
      <h2 className="p-4">Payment Transactions</h2>
      <div className="rounded-md sm:border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="font-medium">ID</TableHead>
              <TableHead className="font-medium">Created At</TableHead>
              <TableHead className="font-medium">Description</TableHead>
              <TableHead className="font-medium">Amount</TableHead>
              <TableHead className="font-medium">Clave Rastreo</TableHead>
              <TableHead className="font-medium">Depositant Clabe</TableHead>
              <TableHead className="font-medium">Depositant Email</TableHead>
              <TableHead className="font-medium">Monex Transaction ID</TableHead>
              <TableHead className="font-medium">Reference</TableHead>
              <TableHead className="font-medium">Sender Account</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paymentTransactions
              ? paymentTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>{transaction.id}</TableCell>
                    <TableCell>{new Date(transaction.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>{transaction.description}</TableCell>
                    <TableCell>{transaction.amount}</TableCell>
                    <TableCell>{transaction.monexData.clave_rastreo}</TableCell>
                    <TableCell>{transaction.monexData.depositant_clabe}</TableCell>
                    <TableCell>{transaction.monexData.description}</TableCell>
                    <TableCell>{transaction.monexData.monex_transaction_id}</TableCell>
                    <TableCell>{transaction.monexData.reference}</TableCell>
                    <TableCell>{transaction.monexData.sender_account}</TableCell>
                  </TableRow>
                ))
              : null}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
