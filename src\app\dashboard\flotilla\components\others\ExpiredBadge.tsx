import { useCountry } from '@/app/dashboard/providers/CountryProvider';
import React from 'react';
import { BadgeTranslations, BadgeTranslationsMX } from '../translations/statusTranslations';

const ExpiredBadge = () => {
  const { isCountryUSA } = useCountry();
  const expired = isCountryUSA ? BadgeTranslations.expired : BadgeTranslationsMX.expired;
  return (
    <span className="inline-flex items-center text-[14px] bg-[#FFEFDB] border-[1.5px] border-[#FBAE50] px-2 py-1 rounded-sm">
      <span className="w-2 h-2 bg-[#B95000] rounded-full mr-2"></span>
      <span className="font-bold text-[#B95000]">{expired}</span>
    </span>
  );
};

export default ExpiredBadge;
