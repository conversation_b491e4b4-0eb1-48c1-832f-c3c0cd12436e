/* eslint-disable @typescript-eslint/no-use-before-define */
import { VehicleResponse } from '@/actions/getVehicleData';
import updateStatus from '@/actions/updateStockStatusVehicles';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { ContractRegionCode } from '@/constants';
import { BsCheck } from 'react-icons/bs';

interface VehicleStepsProps {
  data: VehicleResponse;
  driver: VehicleResponse['drivers'][number];
}

export default function StepsReadmissions({ data }: VehicleStepsProps) {
  // const stepName = data.step.stepName;
  const stepNumber = data.step.stepNumber;
  const { user } = useCurrentUser();
  async function update(status: 'Vehiculo listo') {
    await updateStatus({ id: data._id, message: status, user: user });
  }
  let hasAllDocs = Boolean(
    data.carPlates &&
      data.circulationCard &&
      data.gpsInstalled &&
      data.tenancy.length > 0 &&
      data.policiesArray.length > 0
  );
  if (data.vehicleState === ContractRegionCode.CDMX) {
    hasAllDocs = Boolean(hasAllDocs && data.circulationCard?.frontImg && data.circulationCard?.backImg);
  }

  if (stepNumber === 1 && hasAllDocs && data?.receptionDate) {
    update('Vehiculo listo');
  }

  return (
    <div
      id="vehicle-steps"
      className="w-full h-[40px] relative flex items-center place-content-between mb-[25px] "
    >
      <div
        id="word-steps"
        className="w-full flex place-content-between relative border-[2px] text-[14px] border-[#9CA3AF] h-[1px]"
      >
        <p className="pt-4 text-[#29CC97] ">Solicitud de reingreso</p>
        <p className={stepNumber > 6 ? 'pt-4 text-[#29CC97] ml-[30px]' : 'pt-4 ml-[30px]'}>Reingresado</p>
        {/* <p className={`pt-4 ${stepNumber > 2 ? 'text-[#29CC97]' : ''}  `}>Conductor asignado</p>
        <p className={stepNumber > 3 ? 'pt-4 text-[#29CC97]' : 'pt-4'}>Contrato generado</p>
        <p className={stepNumber > 4 ? 'pt-4 text-[#29CC97]' : 'pt-4'}>Entregado</p> */}
      </div>
      <div id="circle-steps" className="absolute z-10 flex w-full place-content-between ">
        <Check />
        {stepNumber > 6 ? <Check /> : <CircleGray />} {/* Vehiculo listo */}
      </div>
    </div>
  );
}

function CircleGray() {
  return (
    <div className="w-[32px] h-[32px] flex justify-center items-center bg-[white] border-[3px] border-[#9CA3AF] rounded-full ">
      <div className="w-[10px] h-[10px] bg-[#9CA3AF] rounded-full " />
    </div>
  );
}

function Check() {
  return (
    <div className="w-[32px] h-[32px] flex justify-center items-center bg-[#29CC97] rounded-full ">
      <BsCheck size={28} color="white" />
    </div>
  );
}
