import { VehicleResponse } from '@/actions/getVehicleData';
import ModalContainer from './ModalContainer';
import DocumentDisplay from '@/components/DocumentDisplay';
import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { useOpenDetailDischargeModal } from '@/zustand/modalStates';

interface DetailDischargeModalProps {
  dischargedData: NonNullable<VehicleResponse['dischargedData']>;
}

export default function DetailDischargeModal({ dischargedData }: DetailDischargeModalProps) {
  const modalDetail = useOpenDetailDischargeModal();
  return (
    <ModalContainer title="Detalle de la baja" onClose={modalDetail.onClose}>
      <div className="flex flex-col gap-[30px] text-textGray2 ">
        <div className="flex flex-col gap-2">
          <p className="font-[600] text-textGray1 ">Mo<PERSON><PERSON> de la baja</p>
          <p>{dischargedData.reason}</p>
        </div>
        {dischargedData.date && (
          <div className="flex flex-col gap-2">
            <p className="font-[600] text-textGray1 ">Fecha de la baja</p>
            {format(parseISO(dischargedData.date), "dd 'de' MMMM 'de' yyyy", { locale: es })}
          </div>
        )}
        {dischargedData.dictamenDoc && (
          <div className="flex flex-col gap-2">
            <p className="font-[600] text-textGray1 ">Dictamen del seguro</p>
            <DocumentDisplay
              docName={dischargedData.dictamenDoc.originalName}
              url={dischargedData.dictamenDoc.url}
            />
          </div>
        )}
        {dischargedData.reportDoc && (
          <div className="flex flex-col gap-2">
            <p className="font-[600] text-textGray1 ">Reporte de robo</p>
            <DocumentDisplay
              docName={dischargedData.reportDoc.originalName}
              url={dischargedData.reportDoc.url}
            />
          </div>
        )}
        <div className="flex flex-col gap-2">
          <p className="font-[600] text-textGray1 ">Baja de placas</p>
          <DocumentDisplay
            docName={dischargedData.platesDischargedDoc.originalName}
            url={dischargedData.platesDischargedDoc.url}
          />
        </div>
        {dischargedData.comments && (
          <div className="flex flex-col gap-2">
            <p className="font-[600] text-textGray1 ">Comentarios adicionales</p>
            <p>{dischargedData.comments}</p>
          </div>
        )}
      </div>
      <div className="w-full flex justify-end">
        <button
          className=" py-2 px-3 bg-primaryBtn rounded text-white w-[117px] "
          onClick={modalDetail.onClose}
        >
          Cerrar
        </button>
      </div>
    </ModalContainer>
  );
}
