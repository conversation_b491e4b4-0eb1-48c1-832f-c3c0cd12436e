'use client';
import Spinner from '@/components/Loading/Spinner';
import PrimaryButton from '@/components/PrimaryButton';
import { useToast } from '@chakra-ui/react';
import { useState } from 'react';
import { User } from '../../../../types';
import { validateTaxInformation } from '../actions/validate-tax-info';

interface ValidateTaxInfoProps {
  clientData: User;
}

export default function ValidateTaxInfo({ clientData }: ValidateTaxInfoProps) {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  return (
    <>
      {isLoading && <Spinner />}
      <PrimaryButton
        onClick={async () => {
          setIsLoading(true);
          // await sleep(2000);

          const res = await validateTaxInformation(clientData.id, clientData.facturapiId);
          setIsLoading(false);

          if (res.success) {
            return toast({
              title: res.message || 'La información fiscal es correcta',
              status: 'success',
              duration: 4000,
              isClosable: true,
              position: 'top',
            });
          }

          // const displayErrors = res.errors?.map((error: any) => error.message).join(', ');
          // console.log('displayErrors', displayErrors);

          // errors is an object with message and path string properties, show both on toast

          const displayErrors = res.errors?.map((error: any, i: number) => {
            if (i === res.errors.length - 1) {
              return `${error.message.slice(1)}: ${error.path}`;
            }

            return `${error.message.slice(1)}: ${error.path}, `;
          });

          return toast({
            title: res.message || 'La información fiscal no es correcta',
            description: displayErrors,
            status: 'error',
            duration: 6000,
            isClosable: true,
            position: 'top',
          });
        }}
      >
        Validar información fiscal
      </PrimaryButton>
    </>
  );
}
