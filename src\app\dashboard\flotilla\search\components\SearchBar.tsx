'use client';
import SearchInput from '@/components/Inputs/SearchInput';
import useDebounce from '@/hooks/useDebounce';
// import Link from 'next/link';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
// import { useMemo } from 'react';
// import { RiArrowLeftSLine } from 'react-icons/ri';

export default function SearchBar() {
  const searchParams = useSearchParams();

  const pathname = usePathname();
  const router = useRouter();

  const onChange = useDebounce((term: string, param: string) => {
    const params = new URLSearchParams(searchParams);

    if (term && term.trim().length > 0) {
      params.set(param, term);
    } else {
      params.delete(param);
    }
    router.replace(`${pathname}?${params.toString()}`);
  }, 500);

  return (
    <>
      {/* <Link href={`/dashboard/flotilla/${currentParamPage}`} prefetch={false}>
      <div
        className="fixed top-[12px] z-30 text-[#5800F7] flex items-center cursor-pointer"
        onClick={() => router.back()}
      >
        <RiArrowLeftSLine color="#5800F7" size={32} />
        <button>Regresar</button>
      </div>
      </Link> */}
      <div className="flex justify-between items-center mb-[20px] flex-wrap ">
        <h1 className="font-bold text-interBold32 font-inter ">Busquedas</h1>
        <SearchInput onChange={(term) => onChange(term, 'q')} placeholder="Buscar" param="q" />
      </div>
    </>
  );
}
