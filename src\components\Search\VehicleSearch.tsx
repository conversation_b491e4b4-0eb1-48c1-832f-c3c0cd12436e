'use client';
import React, { useState, useEffect, useRef } from 'react';
import { FiSearch } from 'react-icons/fi';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import axios from 'axios';
import { Countries, URL_API } from '@/constants';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Input } from '@/components/ui/input';
import CustomBadge from './CustomBadge';
import {
  StatusTranslationsMX,
  VehicleCategoryTranslationsMX,
  VehicleSubCategoryTranslationsMX,
} from '@/app/dashboard/flotilla/components/translations/statusTranslations';
import { vehicleSearchTranslationsMX, vehicleSearchTranslationsUS } from '@/constants/translations';
import { ClientPath } from '@/constants/api.endpoints';

interface SearchResult {
  _id: string;
  model: string;
  brand: string;
  vin: string;
  carNumber: string;
  category: string;
  subCategory?: string;
  vehicleStatus: string;
  step?: {
    stepName: string;
    stepNumber: number;
  };
}

const NON_ALPHANUMERIC_REGEX = /[^a-zA-Z0-9]/g;

// Helper function to generate vehicle URL
const getVehicleUrl = (vehicle: SearchResult, countryParam: string | null): string => {
  let url = '';
  const { DASHBOARD, FLOTILLA, ACTIVE } = ClientPath;
  if (vehicle.vehicleStatus === 'active') {
    url = `${DASHBOARD}${FLOTILLA}${ACTIVE}/${vehicle._id}`;
  } else if (vehicle.category) {
    url = `${DASHBOARD}${FLOTILLA}/${vehicle.vehicleStatus}/${vehicle.category}/${vehicle._id}`;
  } else {
    url = `${DASHBOARD}${FLOTILLA}/${vehicle.vehicleStatus}/${vehicle._id}`;
  }

  if (countryParam) {
    url += `?country=${encodeURI(countryParam)}`;
  }

  return url;
};

// Helper function to translate labels based on country
const getTranslatedLabel = (
  value: string,
  country: string | null,
  translationsMX: Record<string, string>
): string => {
  if (country?.toLowerCase() === Countries.Mexico.toLowerCase() && value in translationsMX) {
    return translationsMX[value as keyof typeof translationsMX];
  }
  return value;
};

// Get message based on country and query length
const getSearchMessage = (queryLength: number, country: string | null): string => {
  if (queryLength < 3 && country?.toLowerCase() === Countries.Mexico.toLowerCase()) {
    return vehicleSearchTranslationsMX.queryLength;
  }

  if (queryLength < 3 && country?.toLowerCase() === Countries['United States'].toLowerCase()) {
    return vehicleSearchTranslationsUS.queryLength;
  }

  if (country?.toLowerCase() === Countries.Mexico.toLowerCase()) {
    return vehicleSearchTranslationsMX.noResults;
  }

  if (country?.toLowerCase() === Countries['United States'].toLowerCase()) {
    return vehicleSearchTranslationsUS.noResults;
  }

  return vehicleSearchTranslationsMX.noResults;
};

export default function VehicleSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const searchParams = useSearchParams();
  const countryParam = searchParams.get('country');

  const { user } = useCurrentUser();

  // Get placeholder text based on country
  const getPlaceholderText = () => {
    if (countryParam?.toLowerCase() === Countries.Mexico.toLocaleLowerCase())
      return vehicleSearchTranslationsMX.searchPlaceholder;
    if (countryParam?.toLocaleLowerCase() === Countries['United States'].toLowerCase())
      return vehicleSearchTranslationsUS.searchPlaceholder;
    return 'Search...';
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Search API
  const handleSearch = async (query: string) => {
    // Only allow alphanumeric characters
    const sanitizedQuery = query.replace(NON_ALPHANUMERIC_REGEX, '');

    // Limit to 20 characters
    const trimmedQuery = sanitizedQuery.slice(0, 20);

    setSearchQuery(trimmedQuery);

    if (trimmedQuery.length < 3) {
      setSearchResults([]);
      setShowDropdown(false);
      return;
    }

    setIsSearching(true);
    setShowDropdown(true);

    try {
      const encodedQuery = encodeURI(trimmedQuery);
      let url = `${URL_API}/stock/search?search=${encodedQuery}`;

      // Add country parameter if it exists
      if (countryParam) {
        url += `&country=${encodeURI(countryParam)}`;
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      setSearchResults(response.data);
    } catch (error) {
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Validate input to only allow alphanumeric characters
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    const sanitizedInput = input.replace(NON_ALPHANUMERIC_REGEX, '');

    if (sanitizedInput.length <= 20) {
      handleSearch(sanitizedInput);
    } else {
      handleSearch(sanitizedInput.slice(0, 20));
    }
  };

  return (
    <div
      className="relative hidden sm:block w-[188px] sm:w-[300px] md:w-[413px] lg:w-[525px] ml-auto mr-2 md:mr-4"
      ref={searchRef}
    >
      <div className="relative">
        <FiSearch className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-300" />
        <Input
          placeholder={getPlaceholderText()}
          className="pl-10 h-10 border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500 focus:border-[#5800F7]"
          value={searchQuery}
          onChange={handleInputChange}
          maxLength={20}
          onFocus={() => {
            if (searchResults.length > 0) setShowDropdown(true);
          }}
        />
      </div>

      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white z-50 max-h-[400px] overflow-y-auto border border-gray-200 rounded-md shadow-md w-full">
          {isSearching ? (
            <div className="flex justify-center items-center py-4">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#5800F7] mr-2"></div>
              <p>Searching...</p>
            </div>
          ) : searchResults.length > 0 && !searchResults[0]?._id ? (
            <div className="p-4 text-center">
              <p className="text-gray-600 font-medium text-md">
                {getSearchMessage(searchQuery.length, countryParam)}
              </p>
            </div>
          ) : (
            searchResults.map((vehicle) => {
              const vehicleUrl = getVehicleUrl(vehicle, countryParam);

              return (
                <Link key={vehicle._id} href={vehicleUrl}>
                  <div
                    className="p-4 hover:bg-gray-50 cursor-pointer border-b border-gray-100"
                    onClick={() => setShowDropdown(false)}
                  >
                    {/* Car number with hash in purple - FIRST */}
                    {vehicle.carNumber && (
                      <p className="text-[#5800F7] font-medium text-md">#{vehicle.carNumber}</p>
                    )}

                    {/* Brand Model in bold black - SECOND */}
                    {(vehicle.brand || vehicle.model) && (
                      <p className="font-bold text-black mt-1 text-lg">
                        {vehicle.brand || ''} {vehicle.model || ''}
                      </p>
                    )}

                    {/* VIN in gray (no label) - THIRD */}
                    {vehicle.vin && <p className="text-md text-gray-600 mt-1">{vehicle.vin}</p>}

                    {/* Step Name in green - FOURTH */}
                    {vehicle.step && vehicle.step.stepName && (
                      <p className="text-md text-[#29CC97] font-medium mt-1">{vehicle.step.stepName}</p>
                    )}

                    {/* Status, Category, and Subcategory badges in a single row - FIFTH */}
                    <div className="mt-2 flex gap-2 flex-wrap">
                      {vehicle.vehicleStatus && (
                        <CustomBadge
                          label={getTranslatedLabel(
                            vehicle.vehicleStatus,
                            countryParam,
                            StatusTranslationsMX
                          )}
                        />
                      )}

                      {vehicle.category && (
                        <CustomBadge
                          label={getTranslatedLabel(
                            vehicle.category,
                            countryParam,
                            VehicleCategoryTranslationsMX
                          )}
                        />
                      )}

                      {vehicle.subCategory && (
                        <CustomBadge
                          label={getTranslatedLabel(
                            vehicle.subCategory,
                            countryParam,
                            VehicleSubCategoryTranslationsMX
                          )}
                        />
                      )}
                    </div>
                  </div>
                </Link>
              );
            })
          )}
        </div>
      )}
    </div>
  );
}
