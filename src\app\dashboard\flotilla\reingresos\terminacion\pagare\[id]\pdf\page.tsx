'use client';

import PagareTerminationDocumentPDF from '@/pdfComponents/Termination/PagareTermination/PagareTermination-DocumentPDF';
import AgreementTerminationDocumentPDF from '@/pdfComponents/Termination/AgreementTermination/AgreementTermination-DocumentPDF';
import ViewPDFLoader from '@/pdfComponents/ViewPDFLoader';
import { useParams } from 'next/navigation';
export default function PagareTerminationPage() {
  const { id } = useParams<{ id: string }>();

  const storage = localStorage.getItem(`readmission-${id}`);
  const data = storage ? JSON.parse(storage) : null;
  if (!data) {
    return (
      <div>
        <h1>Información no encontrada</h1>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 py-5 fixed top-0 left-0 w-full h-full overflow-y-auto z-[50] bg-gray-500 scrollbar-hidden ">
      <p className="text-2xl font-bold text-center ">Pagaré de terminación anticipada</p>
      <div className="w-[90%] mx-auto">
        <ViewPDFLoader
          removeDefaultBackground
          additionalClasses="!h-[80vh]"
          frameHeight="80vh"
          documentComponent={
            <PagareTerminationDocumentPDF
              city={data.city}
              firstName={data.firstName}
              lastName={data.lastName}
              weeklyRent={data.weeklyRent}
              totalPays={data.totalPays}
              date={data.date}
              fullAddress={data.fullAddress}
            />
          }
        />
      </div>
      <p className="text-2xl font-bold text-center ">Convenio de terminación anticipada</p>

      <div className="w-[90%] mx-auto">
        <ViewPDFLoader
          removeDefaultBackground
          additionalClasses="!h-[80vh]"
          frameHeight="80vh"
          documentComponent={
            <AgreementTerminationDocumentPDF
              city={data.city}
              firstName={data.firstName}
              lastName={data.lastName}
              fullAddress={data.fullAddress}
              contractTerminationDate={data.date}
              weeklyRent={data.weeklyRent}
              totalPays={data.totalPays}
              contractNumber={data.contractNumber}
              deliveryDate={data.deliveryDate}
            />
          }
        />
      </div>
    </div>
  );
}
