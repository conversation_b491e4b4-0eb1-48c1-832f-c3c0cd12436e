// <reference types="cypress" />

declare namespace Cypress {
  interface Chainable<Subject = any> {
    login(email: string, password: string): Chainable<Subject>;
    postStock(value: string | Blob): Chainable<Subject>;
    resetStock(): Chainable<Subjet>;
    setSelectOption({
      selector,
      name,
      eq,
    }: {
      selector: string;
      name: string;
      eq: number;
    }): Chainable<Subject>;
  }
}
