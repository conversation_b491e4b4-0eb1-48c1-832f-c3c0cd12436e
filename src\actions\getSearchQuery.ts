import { URL_API } from '@/constants';
import { cache } from 'react';
import getCurrentUser from './getCurrentUser';
import axios from 'axios';
import { VehicleCard } from './getAllVehicles';

export const getSearchQuery = cache(async (query: string, param: string) => {
  const user = await getCurrentUser();
  if (!user) return null;
  try {
    const res = await axios(`${URL_API}/stock/search?${param}=${query}`, {
      headers: {
        Authorization: 'Bearer ' + user.accessToken,
      },
    });
    return res.data as VehicleCard[];
  } catch (error) {
    return null;
  }
});
