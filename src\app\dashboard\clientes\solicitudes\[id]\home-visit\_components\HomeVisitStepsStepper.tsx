'use client';
import {
  Box,
  Step,
  StepIndicator,
  StepN<PERSON>ber,
  StepStatus,
  StepTitle,
  Stepper,
  useSteps,
} from '@chakra-ui/react';
import { steps, StepsComponents } from './steps';
import { useEffect } from 'react';
import { HomeVisitStepsStatus } from '@/constants';
import { HomeVisitStatus } from '../../../enums';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { CheckLogo } from '@/svgsComponents/CheckLogo';
import { ExclamationLogo } from '@/svgsComponents/ExclamationLogo';
import { LineLogoGray, LineLogoPink } from '@/svgsComponents/LineLogo';
import { translations } from './translations';

interface IHomeVisitStepStepper {
  admissionRequest: Record<string, any>;
}

export default function HomeVisitStepStepper(props: IHomeVisitStepStepper) {
  const { admissionRequest } = props;

  const { homeVisit } = admissionRequest;

  const { activeStep, isActiveStep, goToNext, goToPrevious, setActiveStep } = useSteps({
    count: steps.length,
  });

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const status = homeVisit?.status;
    if (status === HomeVisitStatus.approved || status === HomeVisitStatus.rejected) {
      setActiveStep(steps.length - 1);
      return;
    }
    const stepIndex = searchParams.get('stepIndex');
    if (stepIndex) {
      const params = new URLSearchParams(searchParams.toString());
      params.delete('stepIndex');
      setActiveStep(+stepIndex);
      router.replace(`${pathname}`);
    }
  }, [homeVisit, setActiveStep, searchParams, pathname, router]);

  const isServerStepComplete = (stepIndex: number) => {
    if (homeVisit?.homeVisitStepsStatus) {
      const stepStatus = Object.values(homeVisit?.homeVisitStepsStatus)[stepIndex];
      if (stepStatus && stepStatus === HomeVisitStepsStatus.complete) {
        return true;
      }
    }
    return false;
  };

  const isStepActive = (stepIndex: number) => {
    return isActiveStep(stepIndex);
  };

  const isStepIncomplete = (stepIndex: number) => {
    if (homeVisit?.homeVisitStepsStatus) {
      const stepStatus = Object.values(homeVisit?.homeVisitStepsStatus)[stepIndex];
      if (stepStatus && stepStatus === HomeVisitStepsStatus.incomplete) {
        return true;
      }
    }
    return false;
  };
  const StepComponent = StepsComponents[activeStep];

  return (
    <div className=" bg-primaryLightGray">
      <Stepper index={activeStep} className="py-4 w-11/12  overflow-x-scroll">
        {steps.map((step, index) => {
          const stepClass = isServerStepComplete(index)
            ? isStepActive(index)
              ? '!border-0 bg-primary-gradient'
              : '!border-2 !bg-white !border-primaryVibrantPurpleGradient'
            : isStepActive(index)
            ? '!border-0 bg-primary-gradient'
            : '!border-2 !bg-primaryLightGray';

          return (
            <Step key={index} className=" ">
              <StepIndicator className={stepClass} onClick={() => setActiveStep(index)}>
                <StepStatus
                  complete={isServerStepComplete(index) ? <CheckLogo /> : <ExclamationLogo />}
                  active={<StepNumber className="text-white" />}
                  incomplete={
                    isServerStepComplete(index) ? (
                      <CheckLogo />
                    ) : isStepIncomplete(index) ? (
                      <ExclamationLogo />
                    ) : (
                      <StepNumber className="text-primaryGray1" />
                    )
                  }
                />
              </StepIndicator>

              <Box flexShrink="0">
                {isServerStepComplete(index) ? (
                  <StepTitle>{translations.es[step.title]}</StepTitle>
                ) : isActiveStep(index) ? (
                  <StepTitle>{translations.es[step.title]}</StepTitle>
                ) : (
                  <StepTitle className=" text-primaryGray3">{translations.es[step.title]}</StepTitle>
                )}
              </Box>

              {index !== steps.length - 1 && (
                <Box flexShrink="0">
                  {isServerStepComplete(index) ? (
                    <LineLogoPink />
                  ) : isActiveStep(index) ? (
                    <LineLogoPink />
                  ) : (
                    <LineLogoGray />
                  )}
                </Box>
              )}
            </Step>
          );
        })}
      </Stepper>

      <section className="rounded-md bg-white px-4 py-2">
        <StepComponent
          goToNext={goToNext}
          goToPrevious={goToPrevious}
          activeStep={activeStep}
          admissionRequest={admissionRequest}
        />
      </section>
    </div>
  );
}
