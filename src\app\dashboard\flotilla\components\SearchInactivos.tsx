'use client';
import { useSearchParams } from 'next/navigation';
import Spinner from '@/components/Loading/Spinner';
import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { URL_API } from '@/constants';
import VehicleTable from './VehicleTable';

const SearchInactivos = ({ page }: { page: string }) => {
  console.log('Page:', page);
  const search = useSearchParams();

  const searchQuery = search ? search.get('q') : null;
  const country = search ? search.get('country') : null;
  const vehicleStatus = search ? search.get('vehicleStatus') : null;
  const category = search ? search.get('category') : null;
  const subCategory = search ? search.get('subCategory') : null;
  //const [isLoading, setIsLoading] = useState(false);
  const [showLoader, setShowLoader] = useState(true);
  const [isError, setIsError] = useState(false);
  const [active, setActive] = useState('');
  const [data, setData] = useState<any>(null);
  const encodedSearchQuery = encodeURI(searchQuery || '');
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  const [tempPage, setPage] = useState();
  const [tempSubPage, setSubpage] = useState();
  const [tempSubOptions, setTempSub] = useState();

  const getQuery2 = useCallback(
    async (
      query: string,
      // eslint-disable-next-line @typescript-eslint/no-shadow
      vehicleStatus: string | null,
      // eslint-disable-next-line @typescript-eslint/no-shadow
      category: string | null,
      // eslint-disable-next-line @typescript-eslint/no-shadow
      subCategory: string | null,
      filterCountry: string | null
      // eslint-disable-next-line max-params
    ) => {
      //setIsLoading(true);
      const loaderTimeout = setTimeout(() => setShowLoader(true), 2000);
      try {
        let endpoint = `${URL_API}/stock/search?search=${query}`;
        if (vehicleStatus) {
          endpoint += `&vehicleStatus=${encodeURI(vehicleStatus)}`;
        }
        if (category) {
          endpoint += `&category=${encodeURI(category)}`;
        }
        if (subCategory) {
          endpoint += `&subCategory=${encodeURI(subCategory)}`;
        }
        if (filterCountry) {
          endpoint += `&country=${encodeURI(filterCountry)}`;
        }
        const res = await axios(endpoint, {
          headers: {
            Authorization: 'Bearar ' + user.accessToken,
          },
        });
        setData(res.data);
      } catch (err: any) {
        console.error(err);
        setIsError(true);
      } finally {
        clearTimeout(loaderTimeout); // Clear timeout if API finishes in less than 2 seconds
        setShowLoader(false); // Ensure loader is hidden after loading
        //setIsLoading(false);
        const savedState = localStorage.getItem('vehicleTableState');
        if (savedState) {
          const { page: savedStatePage, subPage, subOptions, activeTab } = JSON.parse(savedState);
          setPage(savedStatePage);
          setSubpage(subPage);
          setTempSub(subOptions);
          setActive(activeTab);
        }
      }
    },
    [user]
  );

  useEffect(() => {
    if (user?.accessToken) {
      getQuery2(encodedSearchQuery, vehicleStatus, category, subCategory, country);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [encodedSearchQuery, category, subCategory, user, country]);

  if (showLoader) {
    return <Spinner />;
  }
  return (
    <div>
      {/* {isError || !data ? null : (
        <div className="flow-root">
          Mostrando {data.length} de {data.length} resultados para &quot;{searchQuery}&quot;
        </div>
      )} */}
      <div>
        {isError || !data ? (
          <div>
            <h1>No se encontraron vehiculos con el criterio de busqueda</h1>
          </div>
        ) : (
          <>
            <div className="flow-root mb-4">
              Found {data[0]?.categoryCount || 0} results matching &quot;{searchQuery}&quot; in {category}
            </div>
            <VehicleTable
              page={tempPage}
              subPage={tempSubPage}
              data={data}
              totalCount={data.length}
              subOptions={tempSubOptions}
              active={active}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default SearchInactivos;
