import { InputHTMLAttributes } from 'react';

interface CustomRadioProps extends InputHTMLAttributes<HTMLInputElement> {
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

const CustomRadio = ({ checked, onChange }: CustomRadioProps) => {
  return (
    <>
      <input type="radio" className="custom-radio" checked={checked} onChange={onChange} />
      <style jsx>{`
        .custom-radio {
          appearance: none;
          width: 20px;
          height: 20px;
          border: 2px solid #d0d5dd;
          border-radius: 50%;
          outline: none;
          cursor: pointer;
          position: relative;
          background: white;
        }

        .custom-radio:checked {
          border: double 5px transparent;
          border-radius: 50%;
          background-image: linear-gradient(white, white),
            linear-gradient(91.35deg, #6210ff 3.4%, #a74df9 100%);
          background-origin: border-box;
          background-clip: content-box, border-box;
        }

        .custom-radio:checked::after {
          content: '';
          position: absolute;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      `}</style>
    </>
  );
};

export default CustomRadio;
