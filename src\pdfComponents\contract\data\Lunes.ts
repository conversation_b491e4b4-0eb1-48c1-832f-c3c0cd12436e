import moment from 'moment';
import { isMonday, nextMonday, parseISO } from 'date-fns';

export function siguienteLunes(date: moment.Moment) {
  let fechaMoment = moment(date, 'YYYY-MM-DD');
  if (fechaMoment.day() === 1) return fechaMoment;

  if (fechaMoment.day() === 2 || fechaMoment.day() === 3) {
    return fechaMoment.clone().subtract(fechaMoment.day() === 2 ? 1 : 2, 'days');
  }

  let diasHastaLunes;

  if (fechaMoment.day() === 0) diasHastaLunes = 1;
  else diasHastaLunes = 1 + (7 - fechaMoment.day());

  let siguienteLunesv = fechaMoment.clone().add(diasHastaLunes, 'days');
  return siguienteLunesv;
}

export function nextMondayDateFns(date: Date | string) {
  if (typeof date === 'string') date = parseISO(date);
  if (isMonday(date)) return date;

  return nextMonday(date);
}

export const paymentsArray = (date: moment.Moment, totalPayments: number) => {
  let lunes = siguienteLunes(date);

  let pagos = [{ day: lunes.format('DD-MM-YYYY'), number: 1 }];

  const total = totalPayments ? totalPayments : 155;
  for (let i = 1; i <= total; i++) {
    pagos.push({ day: lunes.add(1, 'week').format('DD-MM-YYYY'), number: i + 1 });
  }

  return pagos;
};

export const customPaymentArray = (date: moment.Moment, totalWeeks: number) => {
  let lunes = siguienteLunes(date);

  let pagos = [{ day: lunes.format('DD-MM-YYYY'), number: 1 }];

  for (let i = 1; i < totalWeeks; i++) {
    pagos.push({ day: lunes.add(1, 'week').format('DD-MM-YYYY'), number: i + 1 });
  }

  return pagos;
};

let fechaOriginal = moment('2023-02-21');
fechaOriginal.add(1, 'week');

export const getContractEndDate = (date: moment.Moment) => {
  let dateSetToNextMonday = siguienteLunes(date);

  let contractEndDate = dateSetToNextMonday.format('MM-DD-YYYY');
  const totalNoOfWeeksInThreeYears = 155;
  for (let i = 1; i <= totalNoOfWeeksInThreeYears; i++) {
    contractEndDate = dateSetToNextMonday.add(1, 'week').format('DD-MM-YYYY');
  }
  return contractEndDate;
};
