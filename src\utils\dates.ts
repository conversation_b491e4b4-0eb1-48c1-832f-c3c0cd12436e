import { Countries } from '@/constants';
import { format, parseISO } from 'date-fns';
import { es, enUS } from 'date-fns/locale';

export const formatDate = (date: string): string => {
  const datePlusOneDay = new Date(date);
  datePlusOneDay.setDate(datePlusOneDay.getDate() + 1);
  return format(datePlusOneDay, 'dd/MM/yyyy');
};

export const formatDateTime = (date: string): string => {
  return format(parseISO(date), 'dd/MM/yyyy HH:mm');
};

export const formatDateMX = (date: string): string => {
  return format(parseISO(date), 'dd/MM/yyyy');
};

export const formatDateUS = (date: string): string => {
  return format(parseISO(date), 'MM/dd/yyyy');
};

export const formatDateWithLocale = (dateString: string, country: string): string => {
  try {
    const date = new Date(dateString);
    if (country.toLowerCase() === Countries['United States'].toLowerCase()) {
      return format(date, 'MMM dd yyyy, HH:mm', { locale: enUS });
    }
    return format(date, 'dd MMM yyyy, HH:mm', { locale: es });
  } catch (e) {
    return dateString;
  }
};
