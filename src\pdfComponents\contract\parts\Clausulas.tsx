/* eslint-disable @typescript-eslint/no-use-before-define */
import React from 'react';
import { Text, View, StyleSheet } from '@react-pdf/renderer';
import moment from 'moment';
import 'moment/locale/es';
import { useMemo } from 'react';
import { NumerosALetras } from 'numero-a-letras';
import {
  ClauslasListFunction,
  clausulasList2Fun,
  clausulasList3Func,
  clausulasList4Func,
} from '../data/ClausulasList';

const styles = StyleSheet.create({
  clausulasContainer: {
    flexDirection: 'column',
    rowGap: 10,
  },

  item: {
    flexDirection: 'row',
    alignContent: 'center',
  },

  itemContent: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica',
    width: '100%',
  },

  clasulasBolds: {
    textAlign: 'justify',
    fontSize: 8,
    fontFamily: 'Helvetica-Bold',
  },
});

export const ListC = ({ children }: { children: React.ReactNode }) => <>{children}</>;

// export const ClauItem = () => (
export function ClauItem({ isElectric }: { isElectric: boolean }) {
  return (
    <View style={styles.clausulasContainer}>
      {ClauslasListFunction({ isElectric }).map(({ title, description }, i) => {
        if (!title && !description) {
          return null;
        }

        return (
          <View style={styles.item} key={i}>
            <Text style={styles.itemContent} break>
              {' '}
              {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
            </Text>
          </View>
        );
      })}
    </View>
  );
}

export const ClauItem2 = ({ isElectric }: { isElectric: boolean }) => (
  <View style={styles.clausulasContainer}>
    {clausulasList2Fun({ isElectric }).map(({ title, description }, i) => {
      if (!title && !description) return null;
      return (
        <View style={styles.item} key={i}>
          <Text style={styles.itemContent} break>
            {' '}
            {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
          </Text>
        </View>
      );
    })}
  </View>
);

interface ClauItem3Props {
  deliverDate: string;
  finalPrice: number;
  city: string;
  isElectric: boolean;
}

export const ClauItem3 = ({ deliverDate, finalPrice, city, isElectric }: ClauItem3Props) => {
  let date = moment(deliverDate, 'YYYY-MM-DD');
  // const monthName = date.format('MMMM'); <-- Esto es para obtener solo el mes si se requiere solo eso

  const clausla8 = useMemo(() => {
    return getClausula8Contract(city);
  }, [city]);

  const lastText = useMemo(() => {
    return getLastText(city, date);
  }, [city, date]);
  const priceParsed = finalPrice.toLocaleString('es-MX');

  const text = NumerosALetras(finalPrice).split(' ');

  const resolveOne =
    text[0] === 'Un'
      ? NumerosALetras(finalPrice).replace(' Pesos 00/100 M.N.', '').replace(' Peso 00/100 M.N.', '')
      : NumerosALetras(finalPrice)
          .replace('Un', 'Uno')
          .replace(' Pesos 00/100 M.N.', '')
          .replace(' Peso 00/100 M.N.', '');

  return (
    <View style={styles.clausulasContainer}>
      {clausulasList3Func({ isElectric }).map(({ title, description }, i) => {
        return (
          <View style={styles.item} key={i}>
            <Text style={styles.itemContent}>
              {' '}
              {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
            </Text>
          </View>
        );
      })}

      <View style={styles.item}>
        <Text style={styles.itemContent}>
          <Text style={styles.clasulasBolds}> DÉCIMA CUARTA.- VENTA DEL VEHÍCULO ARRENDADO.&apos; </Text>
          {`Las partes convienen que al vencimiento del plazo a que se refiere el "ANEXO A"y el “ANEXO C” del presente contrato, una vez que “EL ARRENDADOR” haya cumplido con todas sus obligaciones de pago,  “EL ARRENDADOR” le concede a “EL ARRENDATARIO” la opción de compra del vehículo arrendado en el presente contrato, mediante el pago de la cantidad de $ ${priceParsed} M.N. (${resolveOne} pesos 00/100 Moneda Nacional). “EL ARRENDATARIO” efectuará el pago de contado del precio de la compra por el vehículo arrendado en los siguientes 3 (tres) días en que se cubra la parte correspondiente de la última renta, según se establece en el "ANEXO A" y en el “ANEXO C” del presente contrato.`}
        </Text>
      </View>

      {clausulasList4Func({ isElectric }).map(({ title, description }, i) => {
        return (
          <View style={styles.item} key={i}>
            <Text style={styles.itemContent}>
              {' '}
              {title ? <Text style={styles.clasulasBolds}> {title} </Text> : ''} {description}{' '}
            </Text>
          </View>
        );
      })}

      <View style={styles.item}>
        {/* { city === 'cdmx' ? ( */}

        <Text style={styles.itemContent}>
          <Text style={styles.clasulasBolds}> VIGÉSIMA OCTAVA.- INTERPRETACIÓN Y CUMPLIMIENTO. </Text>
          {clausla8}
        </Text>
      </View>

      <View style={styles.item}>
        <Text style={styles.itemContent}>
          {' '}
          La Procuraduría Federal del Consumidor es competente en la vía administrativa para resolver
          cualquier controversia que se suscite sobre la interpretación o cumplimiento del presente contrato.{' '}
        </Text>
      </View>

      <View style={styles.item}>
        <Text style={styles.itemContent}>
          {' '}
          <Text style={styles.clasulasBolds}>VIGÉSIMA NOVENA.- ACUERDO TOTAL. </Text>El presente Contrato y
          sus anexos contienen la totalidad de los acuerdos entre “LAS PARTES” con relación al arrendamiento
          que constituye su objeto; ningún cambio, renuncia o modificación será exigible a menos que conste
          por escrito y firmado por “LAS PARTES”.{' '}
        </Text>
      </View>

      <Text style={styles.itemContent}>{lastText}</Text>
    </View>
  );
};

function getClausula8Contract(city: string = 'cdmx') {
  console.log('[getClausula8Contract] city', city);
  const cityLowerCase = city.toLowerCase();

  const cities: { [key: string]: string } = {
    cdmx: 'Ciudad de México',
    edomx: 'Ciudad de México',
    gdl: 'Ciudad de Guadalajara, Jalisco',
    ptv: 'Ciudad de Puerto Vallarta, Jalisco',
    mty: 'Ciudad Monterrey, Nuevo León',
    tij: 'Estado de Baja California',
    qro: 'Estado de Queretaro',
    pbe: 'Estado de Puebla ',
    mxli: 'Estado de Baja California',
    slp: 'Estado de San Luis Potosí',
    mer: 'Estado de Yucatán',
    sal: 'Estado de Coahuila',
    her: 'Estado de Sonora',
    ags: 'Estado de Aguascalientes',
    leo: 'Estado de Guanajuato',
    torr: 'Estado de Coahuila',
    chi: 'Estado de Chihuahua',
  };

  if (cityLowerCase in cities) {
    const text = cities[cityLowerCase].replace(/\s+/g, ' ').trim();
    return `Para la interpretación, cumplimiento y exigibilidad de este contrato, “LAS PARTES” se someten a la jurisdicción de los Tribunales competentes en ${text} sean locales y/o federales, renunciando expresamente a cualquier otra jurisdicción que pudiera corresponderles, por razón de sus domicilios presentes o futuros o por cualquier otra razón.`;
  } else {
    return 'Lo siento, no tenemos información sobre esa ciudad.';
  }
}

function getLastText(city: string, date: any) {
  console.log('[getLastText] city', city);
  const cityLowerCase = city.toLowerCase();

  const cities: { [key: string]: string } = {
    cdmx: 'la Ciudad de México,',
    edomx: 'la Ciudad de México,',
    gdl: 'el Estado de Jalisco,',
    ptv: 'el Estado de Jalisco,',
    mty: 'el Estado de Nuevo León,',
    tij: 'el Estado de Baja California,',
    qro: 'el Estado de Queretaro,',
    pbe: 'el Estado de Puebla,',
    mxli: 'el Estado de Baja California,',
    slp: 'el Estado de San Luis Potosí,',
    mer: 'el Estado de Yucatán,',
    sal: 'el Estado de Coahuila,',
    her: 'el Estado de Sonora,',
    ags: 'el Estado de Aguascalientes,',
    leo: 'el Estado de Guanajuato,',
    torr: 'el Estado de Coahuila,',
    chi: 'el Estado de Chihuahua,',
  };

  if (cityLowerCase in cities) {
    const text = cities[cityLowerCase].replace(/\s+/g, ' ').trim();
    return `Leído que fue el presente contrato por las partes y enteradas del alcance legal de todo el contenido de este, lo firman al calce y al margen para constancia, en ${text} México en el mes de ${date
      .locale('es')
      .format('LL')}.`;
  } else {
    return 'Lo siento, no tenemos información sobre esa ciudad.';
  }
}
