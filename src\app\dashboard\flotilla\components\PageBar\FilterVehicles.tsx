import SelectInput from '@/components/Inputs/SelectInput';
import CustomModal from '@/components/Modals/CustomModal';
import {
  CONTRACT_REGIONS,
  CONTRACT_REGIONS_IATA,
  platformOptions,
  statusSelected,
  stepsSelect,
  US_CITIES,
} from '@/constants';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
import { getCookie, setCookie } from 'cookies-next';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { useCountry } from '@/app/dashboard/providers/CountryProvider';

export type SubmitValues = {
  region: { value: string; label: string };
  step: { value: string; label: string };
  new: { value: string; label: string };
  status: { value: string; label: string };
  isElectric: { value: string; label: string };
  platform: { value: string; label: string };
  reason: { value: string; label: string };
};

export default function FilterVehicles() {
  const router = useRouter();
  const { isCountryUSA } = useCountry();
  const filterCookie = getCookie('filters');
  const filters = filterCookie ? JSON.parse(filterCookie) : {};

  const regionSelected = filters.city;
  const stepSelected = filters.stepNumber;
  const newSelected = filters.isNew;
  const statusParam = filters.status;
  const isElectric = filters.isElectric;
  const platformSelected = filters.platform;
  const reasonSelected = filters.reason;

  const rFind = isCountryUSA
    ? US_CITIES.find((city) => city.value.toLowerCase() === regionSelected)
    : CONTRACT_REGIONS.find((r) => r.value === regionSelected);

  const region = rFind
    ? { value: regionSelected || '', label: rFind.label }
    : { value: '', label: 'Selecciona' };

  const step = stepsSelect.find((s) => s.value === stepSelected) || { value: '', label: 'Selecciona' };

  const newVehicle = newSelected
    ? { value: newSelected, label: newSelected === 'true' ? 'Nuevos' : 'Seminuevos' }
    : { value: '', label: 'Selecciona' };

  const status = statusSelected.find((s) => s.value === statusParam) || { value: '', label: 'Selecciona' };

  const platform = platformOptions.find((p) => p.value === platformSelected) || {
    value: '',
    label: 'Selecciona',
  };

  const reasonFilter = reasonSelected
    ? { value: reasonSelected, label: reasonSelected }
    : { value: '', label: 'Selecciona' };

  const initialFilterValues = {
    region,
    step,
    new: newVehicle,
    status,
    isElectric: {
      value: isElectric || '',
      label: isElectric ? (isElectric === 'true' ? 'Sí' : 'No') : 'Selecciona',
    },
    platform,
    reason: reasonFilter,
  };

  const onClose = () => {
    // setIsLoading(false);
    // console.log('onClose');
  };

  return (
    <CustomModal
      confirmButtonText={isCountryUSA ? 'Filters' : 'Filtros'}
      cancelButtonText={isCountryUSA ? 'Cancel' : 'Cancelar'}
      header="Filtros de búsqueda"
      initialValues={initialFilterValues}
      onCloseModal={onClose}
      size="xl"
      onSubmit={async (values: SubmitValues) => {
        const params: Record<string, string> = {};
        if (values.region.value) {
          params.city = values.region.value;
        } else delete params.city;

        if (values.step.value) {
          params.stepNumber = values.step.value;
        } else delete params.stepNumber;

        if (values.new.value) {
          params.isNew = values.new.value;
        } else delete params.isNew;

        if (values.status.value) {
          params.status = values.status.value;
        } else delete params.status;

        if (values.platform.value) {
          params.platform = values.platform.value;
        } else delete params.platform;

        if (values.isElectric.value) {
          params.isElectric = values.isElectric.value;
        } else delete params.isElectric;

        if (values.reason.value) {
          params.reason = values.reason.value;
        } else delete params.reason;

        setCookie('filters', JSON.stringify(params));

        router.refresh();
      }}
      body={isCountryUSA ? <FiltersUS /> : <Filters />}
      // validatorSchema={createFilterSchema}
      openButtonText={isCountryUSA ? 'Filter' : 'Filtrar'}
    />
  );
}

function Filters() {
  const { user: currentUser, isSuperAdminOrAdmin } = useCurrentUser();

  const allowedRegions = CONTRACT_REGIONS_IATA.filter((r) => {
    const code = r.code as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'pbc';
    return currentUser.settings.allowedRegions.includes(code);
  });

  const path = usePathname();

  return (
    <div className="flex flex-col w-full gap-3">
      <SelectInput
        name="region"
        label="Region"
        options={isSuperAdminOrAdmin ? CONTRACT_REGIONS : allowedRegions}
      />
      <SelectInput name="step" label="Paso actual" options={stepsSelect} />
      <SelectInput
        name="new"
        label="Nuevo o seminuevo"
        options={[
          { label: 'Nuevos', value: 'true' },
          { label: 'Seminuevos', value: 'false' },
        ]}
      />
      {path.split('/')[3] === 'activos' && (
        <SelectInput
          name="status"
          label="Estatus"
          options={[
            { label: 'Activos', value: 'active' },
            { label: 'En Taller', value: 'in-service' },
            { label: 'En proceso legal', value: 'legal-process' },
            { label: 'Espera de seguro', value: 'awaiting-insurance' },
          ]}
        />
      )}
      {path.split('/')[3] === 'stock' && (
        <SelectInput
          name="status"
          label="Estatus"
          options={[
            { label: 'Stock', value: 'stock' },
            { label: 'Revisión', value: 'overhauling' },
          ]}
        />
      )}

      <SelectInput
        name="isElectric"
        label="¿Es eléctrico?"
        options={[
          { value: 'true', label: 'Sí' },
          { value: 'false', label: 'No' },
        ]}
      />

      <SelectInput name="platform" label="Plataforma" options={platformOptions} />

      {path.includes('withdrawn') && (
        <SelectInput
          name="reason"
          label="Motivo"
          options={[
            { label: 'Robo', value: 'Robo' },
            { label: 'Accidente', value: 'Accidente' },
          ]}
        />
      )}
    </div>
  );
}

function FiltersUS() {
  const path = usePathname();
  const usSupportedCitiesAsOfNow = US_CITIES.map((city) => {
    return {
      ...city,
      value: city.value.toLowerCase(),
    };
  });

  return (
    <div className="flex flex-col w-full gap-3">
      <SelectInput
        name="region"
        label="Region"
        options={usSupportedCitiesAsOfNow}
        placeholder="Select Region"
      />
      <SelectInput name="step" label="Current step" options={stepsSelect} />
      <SelectInput
        name="new"
        label="New or pre-owned"
        options={[
          { label: 'New', value: 'true' },
          { label: 'Semi New', value: 'false' },
        ]}
      />
      {path.split('/')[3] === 'activos' && (
        <SelectInput
          name="status"
          label="Status"
          options={[
            { label: 'Assets', value: 'active' },
            { label: 'In Workshop', value: 'in-service' },
            { label: 'In legal process', value: 'legal-process' },
            { label: 'Waiting for insurance', value: 'awaiting-insurance' },
          ]}
        />
      )}
      {path.split('/')[3] === 'stock' && (
        <SelectInput
          name="status"
          label="Status"
          options={[
            { label: 'Stock', value: 'stock' },
            { label: 'Revision', value: 'overhauling' },
          ]}
        />
      )}

      {path.includes('withdrawn') && (
        <SelectInput
          name="reason"
          label="Reason"
          options={[
            { label: 'Theft', value: 'Theft' },
            { label: 'Accident', value: 'Accident' },
          ]}
        />
      )}
    </div>
  );
}
