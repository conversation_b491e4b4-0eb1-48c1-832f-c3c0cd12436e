import { useState, useRef } from 'react';

interface UseCameraProps {
  vehicleId: string;
  accessToken: string;
  uploadUrl: string;
}

export function useCamera({ vehicleId, accessToken, uploadUrl }: UseCameraProps) {
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [isCameraAvailable, setIsCameraAvailable] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [photoPreviewUrl, setPhotoPreviewUrl] = useState<string | null>(null);
  const [photoPath, setPhotoPath] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');

  const toggleFacingMode = () => {
    setFacingMode((prev) => (prev === 'user' ? 'environment' : 'user'));
  };

  const openCamera = async () => {
    try {
      setIsCameraOpen(true);
      setTimeout(async () => {
        try {
          if (streamRef.current) {
            streamRef.current.getTracks().forEach((track) => track.stop());
            streamRef.current = null;
          }
          const stream = await navigator.mediaDevices.getUserMedia({
            video: { facingMode },
          });
          streamRef.current = stream;
          if (videoRef.current) {
            videoRef.current.srcObject = stream;
            videoRef.current.onloadedmetadata = () => {
              if (videoRef.current) {
                videoRef.current
                  .play()
                  .then(() => setIsCameraAvailable(true))
                  .catch((err) => {
                    console.log('[useCamera] - Error starting camera', err);
                    setError('Could not start camera preview. Please try again.');
                    setIsCameraOpen(false);
                  });
              }
            };
          } else {
            throw new Error('Video element reference not available');
          }
        } catch (err: any) {
          if (err instanceof DOMException && err.name === 'NotAllowedError') {
            setError('Camera access denied. Please check your browser permissions.');
          } else {
            setError(
              'Could not access camera. Please ensure your device has a working camera and try again.'
            );
          }
          if (streamRef.current) {
            streamRef.current.getTracks().forEach((track) => track.stop());
            streamRef.current = null;
          }
          setIsCameraOpen(false);
        }
      }, 100);
    } catch (err: any) {
      setError('Failed to initialize camera. Please try again.');
      setIsCameraOpen(false);
    }
  };

  const closeCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
    setIsCameraOpen(false);
    setIsCameraAvailable(false);
    setPhotoPreviewUrl(null);
    setPhotoPath(null);
  };

  const takePhoto = async () => {
    if (!videoRef.current) {
      setError('Camera not ready. Please try again.');
      return;
    }
    try {
      setIsUploading(true);
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth || 640;
      canvas.height = videoRef.current.videoHeight || 480;
      const context = canvas.getContext('2d');
      if (!context) throw new Error('Could not create canvas context');
      context.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
      setPhotoPreviewUrl(canvas.toDataURL('image/jpeg'));
      const blob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob(
          (b) => (b ? resolve(b) : reject(new Error('Could not create blob'))),
          'image/jpeg',
          0.8
        );
      });
      const formData = new FormData();
      const fileName = `qr_verification_${vehicleId}_${Date.now()}.jpg`;
      formData.append('file', blob, fileName);
      const response = await fetch(`${uploadUrl}/${vehicleId}`, {
        method: 'POST',
        headers: {
          Authorization: `bearer ${accessToken}`,
        },
        body: formData,
      });
      if (!response.ok) throw new Error('Failed to upload photo');
      const data = await response.json();
      setPhotoPath(data.filePath);
      if (streamRef.current) {
        streamRef.current.getTracks().forEach((track) => track.stop());
        streamRef.current = null;
      }
      setIsCameraOpen(false);
    } catch (err: any) {
      setError('Failed to capture photo. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return {
    isCameraOpen,
    isCameraAvailable,
    isUploading,
    photoPreviewUrl,
    photoPath,
    openCamera,
    closeCamera,
    takePhoto,
    setPhotoPreviewUrl,
    setPhotoPath,
    videoRef,
    error,
    setError,
    facingMode,
    toggleFacingMode,
  };
}
