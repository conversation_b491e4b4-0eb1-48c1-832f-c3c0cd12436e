import { FormControl, Image } from '@chakra-ui/react';
import { useFormikContext } from 'formik';

enum STEPS {
  COUNTRY = 0,
  REGISTRATION_TYPE = 1,
  NORMAL_FLOW = 2,
  BULK_UPLOAD = 3,
}

interface RegistrationMethodSelectorProps {
  onMethodSelect: (method: STEPS.NORMAL_FLOW | STEPS.BULK_UPLOAD) => void;
  translations: any;
}

const RegistrationMethodSelector = ({ onMethodSelect, translations }: RegistrationMethodSelectorProps) => {
  const { setFieldValue } = useFormikContext();

  const handleMethodSelect = (method: STEPS.NORMAL_FLOW | STEPS.BULK_UPLOAD) => {
    setFieldValue('registrationType', method === STEPS.NORMAL_FLOW ? 'normal' : 'bulk');
    onMethodSelect(method);
  };

  return (
    <FormControl>
      <div className="grid grid-cols-2 gap-4">
        <button
          type="button"
          className="border rounded-lg p-4 hover:border-blue-500 focus:outline-none focus:border-blue-500 transition-colors"
          onClick={() => handleMethodSelect(STEPS.NORMAL_FLOW)}
        >
          <div className="flex flex-col items-center gap-2">
            <div className="w-24 h-24 rounded-full bg-blue-100 flex items-center justify-center overflow-hidden">
              <Image
                src="/images/registration/car.gif"
                alt="Normal Registration"
                fallback={
                  <div className="w-16 h-16 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="12" y1="8" x2="12" y2="16"></line>
                      <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg>
                  </div>
                }
              />
            </div>
            <span className="font-inter font-[600] text-[16px]">{translations.normalRegister}</span>
          </div>
        </button>
        <button
          type="button"
          className="border rounded-lg p-4 hover:border-blue-500 focus:outline-none focus:border-blue-500 transition-colors"
          onClick={() => handleMethodSelect(STEPS.BULK_UPLOAD)}
        >
          <div className="flex flex-col items-center gap-2">
            <div className="w-24 h-24 rounded-full bg-green-100 flex items-center justify-center overflow-hidden">
              <Image
                src="/images/registration/upload.gif"
                alt="Bulk Upload"
                fallback={
                  <div className="w-16 h-16 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                  </div>
                }
              />
            </div>
            <span className="font-inter font-[600] text-[16px]">{translations.bulkXML}</span>
          </div>
        </button>
      </div>
    </FormControl>
  );
};

export default RegistrationMethodSelector;
