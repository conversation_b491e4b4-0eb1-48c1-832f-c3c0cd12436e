import React from 'react';
import { Badge } from '@/components/ui/badge';

interface CustomBadgeProps {
  label: string;
  className?: string;
}

export default function CustomBadge({ label, className }: CustomBadgeProps) {
  return (
    <Badge
      variant="outline"
      className={`bg-white text-[#5800F7] border-[#5800F7] hover:bg-white hover:text-[#5800F7] ${
        className || ''
      }`}
    >
      {label}
    </Badge>
  );
}
