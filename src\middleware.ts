import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  // ✅ Bypass auth for specific public redirect route
  const isVehicleRedirect = pathname.startsWith('/dashboard/flotilla/vehicle-redirect');

  if (isVehicleRedirect) {
    return NextResponse.next(); // Skip auth for this route
  }

  const isProtectedRoute = pathname.startsWith('/dashboard') || pathname.startsWith('/adminpanel');

  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET,
  });

  const isAuthenticated = !!token;

  // If user is not authenticated and trying to access a protected route
  if (!isAuthenticated && isProtectedRoute) {
    const fullPath = request.nextUrl.pathname + request.nextUrl.search;

    const signInUrl = new URL('/', request.url);
    signInUrl.searchParams.set('callbackUrl', fullPath);

    return NextResponse.redirect(signInUrl);
  }

  return NextResponse.next();
}

// Configure middleware to run on specific paths
export const config = {
  matcher: ['/dashboard/:path*', '/adminpanel/:path*'],
};
