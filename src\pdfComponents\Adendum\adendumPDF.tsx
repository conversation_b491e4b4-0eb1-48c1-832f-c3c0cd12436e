import { Document, PDFViewer, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import Signatures from '../contract/parts/Signatures';
import { format, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { useEffect, useState } from 'react';

const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
    width: '100%',
  },
  body: {
    marginHorizontal: '10%',
    marginVertical: '7%',
    // rowGap: '30px' as any,
  },
  body2: {
    marginVertical: '10%',
  },
  viewer: {
    width: '80%',
    height: '100vh',
  },
  text: {
    fontSize: 11,
    textAlign: 'justify',
    fontFamily: 'Helvetica',
  },
  textBold: {
    // fontSize: 12,
    // textAlign: 'justify',
    fontFamily: 'Helvetica-Bold',
  },
  tablesContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },

  table: {
    display: 'table' as unknown as 'flex',
    width: '25%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  tableHead: {
    borderStyle: 'solid',
    fontSize: 6,
    textAlign: 'center',
    flexDirection: 'row',
    backgroundColor: '#C3C6CB',
  },
  tableHeadCell: {
    textAlign: 'left',
    marginTop: 5,
    fontSize: 10,
  },

  tableHeadCellTitle: {
    textAlign: 'center',
    fontSize: 6,
    fontFamily: 'Helvetica',
    paddingVertical: 5,
  },
  tableRow: {
    flexDirection: 'row',
  },

  tableRowLast: {
    flexDirection: 'row',
  },

  tableCol: {
    width: '33.3333%',
    borderStyle: 'solid',
    borderWidth: 1,
    borderLeftWidth: 0,
    borderTopWidth: 0,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tableCol2: {
    width: '33.3333%',
    borderStyle: 'solid',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    borderRightWidth: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  tableCell: {
    margin: 'auto',
    fontSize: 6,
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    rowGap: 2,
    marginTop: '25px',
  },
});

const mockData = {
  fullName: 'Victor Manuel Jimenez Palacios',
  address: 'Calle 1',
  contract: '4008',
  city: 'Querétaro',
  weeklyRent: 4000,
  deliveredDate: '2024-01-10',
  dateFinished: '2024-11-01',
  allPDFPayments: [{ day: '10-01-2024', weeklyRent: 4000, fee: 0 }] as {
    day: string;
    weeklyRent: number;
    fee?: number;
    contractValue?: number;
    amountOutstanding?: number;
  }[],
  dateIn: '2024-01-10',
  differenceWeeks: 3,
  addAmount: 0,
};

interface AdendumProps {
  data: typeof mockData;
}

export default function AdendumPDF({
  data = mockData,
  stateChange,
}: AdendumProps & { stateChange: boolean }) {
  const [viewerKey, setViewerKey] = useState(0);
  useEffect(() => {
    setViewerKey((prevKey) => prevKey + 1);
  }, [stateChange]);

  return (
    <PDFViewer key={viewerKey} style={styles.viewer} showToolbar={false}>
      <AdendumDocument data={data} />
    </PDFViewer>
  );
}

function parsePriceMXN(price: number) {
  return price.toLocaleString('es-MX', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}
export function AdendumDocument({ data }: AdendumProps) {
  const firstFormatDate = format(parseISO(data?.deliveredDate || ''), "dd 'de' MMMM 'de' yyyy", {
    locale: es,
  });
  const formatDate = format(parseISO(data?.dateFinished || ''), "dd 'de' MMMM 'de' yyyy", { locale: es });

  if (data) {
    if (data.addAmount === 0) data.addAmount = undefined as unknown as number;
  }

  return (
    <Document>
      <Page style={styles.page} size="A4" wrap>
        <View style={styles.body}>
          <View style={{ rowGap: '30px' /* , marginHorizontal: '10%' */ }}>
            <Text style={styles.text}>
              ADENDUM QUE RESPECTO DEL CONTRATO DE ARRENDAMIENTO NÚMERO {data?.contract} DE FECHA{' '}
              {firstFormatDate.toUpperCase()}, CELEBRAN POR UNA PARTE EL ARRENDADOR{' '}
              <Text style={styles.textBold}>E-MKT GOODS DE MÉXICO S.A.P.I. DE C.V.</Text> REPRESENTADO EN ESTE
              ACTO POR SU APODERADO LEGAL MAIRON ESTEBAN SANDOVAL GÓMEZ Y POR LA OTRA EL ARRENDATARIO{' '}
              <Text style={styles.textBold}>{data?.fullName?.toUpperCase()},</Text> QUIENES SE SUJETAN AL
              SIGUIENTE ACUERDO.
            </Text>
            <Text style={styles.text}>
              <Text style={styles.textBold}>PRIMERO. </Text>
              Ambas partes reconocen de manera mutua su carácter de arrendador y arrendatario, así como su
              identificación plena en términos del instrumento contractual {data?.contract}, mismo que se
              modifica y se precisa al tenor siguiente.
            </Text>
            <Text style={styles.text}>
              <Text style={styles.textBold}>SEGUNDO. </Text>
              La vigencia o plazo de duración del contrato de arrendamiento, así como el monto a pagar del
              contrato con número {data?.contract} se estableció que fuera en base a la tabla de amortización
              que se insertó en el contrato que para garantizar el cumplimiento de tal contrato signo el C.{' '}
              {data?.fullName}, por lo que las partes acuerdan que se modificará el monto de la tabla de
              amortización de ese acto contractual para quedar en términos de la tabla de amortización que a
              continuación se indica:
            </Text>
          </View>
          <View style={styles.content}>
            {data?.addAmount == undefined ? (
              <View style={{ ...styles.tablesContainer }}>
                <View style={styles.table}>
                  <View style={styles.tableHead}>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableHeadCellTitle}>N. de pago</Text>
                    </View>

                    <View style={styles.tableCol}>
                      <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                    </View>

                    <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                      <Text style={styles.tableHeadCellTitle}>Pago Total</Text>
                    </View>
                  </View>

                  {data?.allPDFPayments.slice(0, 50).map(({ day, weeklyRent }, i) => {
                    return (
                      <View style={styles.tableRow} key={i}>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}> {i + 1} </Text>
                        </View>
                        <View style={styles.tableCol}>
                          <Text style={styles.tableCell}> {day} </Text>
                        </View>
                        <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                          <Text style={styles.tableCell}> ${parsePriceMXN(weeklyRent)} </Text>
                        </View>
                      </View>
                    );
                  })}
                </View>

                {data?.allPDFPayments.length > 50 && (
                  <View style={styles.table}>
                    <View style={styles.tableHead}>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                      </View>

                      <View style={data?.allPDFPayments.length > 100 ? styles.tableCol2 : styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Pago Total</Text>
                      </View>
                    </View>
                    {data?.allPDFPayments.slice(50, 100).map(({ day, weeklyRent }, i) => {
                      return (
                        <View style={styles.tableRow} key={i}>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {50 + i + 1} </Text>
                          </View>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {day} </Text>
                          </View>
                          <View
                            style={data?.allPDFPayments.length > 100 ? styles.tableCol2 : styles.tableCol}
                          >
                            <Text style={styles.tableCell}> ${parsePriceMXN(weeklyRent)} </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                )}

                {data?.allPDFPayments.length > 100 && (
                  <View style={styles.table}>
                    <View style={styles.tableHead}>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                      </View>

                      <View style={data?.allPDFPayments.length > 150 ? styles.tableCol2 : styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Pago Total</Text>
                      </View>
                    </View>

                    {data?.allPDFPayments.slice(100, 150).map(({ day, weeklyRent }, i) => {
                      return (
                        <View style={styles.tableRow} key={i}>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {100 + i + 1} </Text>
                          </View>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {day} </Text>
                          </View>
                          <View
                            style={data?.allPDFPayments.length > 150 ? styles.tableCol2 : styles.tableCol}
                          >
                            <Text style={styles.tableCell}> ${parsePriceMXN(weeklyRent)} </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                )}

                {data?.allPDFPayments?.length > 150 && (
                  <View style={styles.table}>
                    <View style={styles.tableHead}>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableHeadCellTitle}>Pago Total</Text>
                      </View>
                    </View>

                    {data?.allPDFPayments.slice(150).map(({ day, weeklyRent }, i) => {
                      return (
                        <View style={styles.tableRowLast} key={i}>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {150 + i + 1} </Text>
                          </View>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> {day} </Text>
                          </View>
                          <View style={styles.tableCol}>
                            <Text style={styles.tableCell}> ${parsePriceMXN(weeklyRent)} </Text>
                          </View>
                        </View>
                      );
                    })}
                  </View>
                )}
              </View>
            ) : (
              <TableWithFee data={data} />
            )}
          </View>

          <View style={{ rowGap: '30px' }}>
            <View style={styles.body2} break>
              <Text style={styles.text}>
                <Text style={styles.textBold}>TERCERA. </Text>
                Las partes acuerdan que a partir de la firma de este Adendum, tanto la vigencia del contrato{' '}
                {data.contract} como la tabla de amortización y compromiso de pago del mismo, es en términos
                de la tabla de amortización señalada en la cláusula anterior de este Adendum. El cual como ya
                se indicó, tal adendum forma parte integral de las obligaciones que asume el arrendatario
                respecto del contrato de arrendamiento firmado en su oportunidad.
              </Text>
              <Text style={styles.text}>
                Leído que fue el presente contrato, comprendiendo las partes el alcance legal de todo el
                contenido de este, lo suscriben en dos tantos, en {data?.city}, México, el {formatDate}.
              </Text>
              <Signatures firstName={data?.fullName || ''} lastName="" />
            </View>
          </View>
        </View>
      </Page>
    </Document>
  );
}

// const stylesForTableWithFee = StyleSheet.create({

function TableWithFee({ data }: AdendumProps) {
  // divide the table like the other one but the last division should be below not in the same level
  // for example: 50, 100, 150, 200 to 50, 100, 150,
  //                                 200

  return (
    <>
      {/* PAYMENTS TABLE FROM 0 TO 100 */}
      <View style={{ width: '100%' }}>
        <View style={styles.tablesContainer}>
          <View style={{ ...styles.table, width: '50%' }}>
            <View style={styles.tableHead}>
              <View style={styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>N. pago</Text>
              </View>

              <View style={styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
              </View>

              <View style={styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>Pago semanal</Text>
              </View>

              <View style={/* data?.allPDFPayments.length > 50 ? styles.tableCol2 :  */ styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>Tarifa</Text>
              </View>

              <View style={/* data?.allPDFPayments.length > 50 ? styles.tableCol2 :  */ styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>Valor contrato</Text>
              </View>

              <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                <Text style={styles.tableHeadCellTitle}>Cantidad pendiente</Text>
              </View>
            </View>

            {data?.allPDFPayments.slice(0, 50).map(({ day, weeklyRent, fee, ...rest }, i) => {
              return (
                <View style={styles.tableRow} key={i}>
                  <View style={styles.tableCol}>
                    <Text style={styles.tableCell}>{i + 1}</Text>
                  </View>
                  <View style={styles.tableCol}>
                    <Text style={styles.tableCell}>{day}</Text>
                  </View>
                  <View style={styles.tableCol}>
                    <Text style={styles.tableCell}>
                      ${parsePriceMXN(fee ? weeklyRent - fee : weeklyRent)}
                    </Text>
                  </View>

                  <View style={/* data?.allPDFPayments.length > 50 ? styles.tableCol2 : */ styles.tableCol}>
                    <Text style={styles.tableCell}>${parsePriceMXN(fee || 0)}</Text>
                  </View>
                  <View style={/* data?.allPDFPayments.length > 50 ? styles.tableCol2 : */ styles.tableCol}>
                    <Text style={styles.tableCell}>${parsePriceMXN(rest.contractValue || 0)}</Text>
                  </View>
                  <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                    <Text style={styles.tableCell}>${parsePriceMXN(rest.amountOutstanding || 0)}</Text>
                  </View>
                </View>
              );
            })}
          </View>

          {data?.allPDFPayments.length > 50 && (
            <View style={{ ...styles.table, width: '50%' }}>
              <View style={styles.tableHead}>
                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Pago semanal</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Tarifa</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Valor contrato</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Cantidad pendiente</Text>
                </View>
              </View>
              {data?.allPDFPayments.slice(50, 100).map(({ day, weeklyRent, fee, ...rest }, i) => {
                return (
                  <View style={styles.tableRow} key={i}>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}> {50 + i + 1} </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}> {day} </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        ${parsePriceMXN(fee ? weeklyRent - fee : weeklyRent)}{' '}
                      </Text>
                    </View>

                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(fee || 0)}</Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(rest.contractValue || 0)}</Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(rest.amountOutstanding || 0)}</Text>
                    </View>
                  </View>
                );
              })}
            </View>
          )}
        </View>
      </View>
      {/* PAYMENTS TABLE FROM 100 TO 156 OR MORE */}
      {data.allPDFPayments.length > 100 && (
        <View break style={{ width: '100%', marginTop: '40px' }}>
          <View style={styles.tablesContainer}>
            <View style={{ ...styles.table, width: '50%' }}>
              <View style={styles.tableHead}>
                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Pago semanal</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Tarifa</Text>
                </View>

                <View style={styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Valor contrato</Text>
                </View>

                <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                  <Text style={styles.tableHeadCellTitle}>Cantidad pendiente</Text>
                </View>
              </View>

              {data?.allPDFPayments.slice(100, 150).map(({ day, weeklyRent, fee, ...rest }, i) => {
                return (
                  <View style={styles.tableRow} key={i}>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}> {100 + i + 1} </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}> {day} </Text>
                    </View>
                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>
                        ${parsePriceMXN(fee ? weeklyRent - fee : weeklyRent)}{' '}
                      </Text>
                    </View>

                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(fee || 0)}</Text>
                    </View>

                    <View style={styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(rest.contractValue || 0)}</Text>
                    </View>

                    <View style={data?.allPDFPayments.length > 50 ? styles.tableCol2 : styles.tableCol}>
                      <Text style={styles.tableCell}>${parsePriceMXN(rest.amountOutstanding || 0)}</Text>
                    </View>
                  </View>
                );
              })}
            </View>

            {data?.allPDFPayments.length > 150 && (
              <View style={{ ...styles.table, width: '50%' }}>
                <View style={styles.tableHead}>
                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>N. pago</Text>
                  </View>

                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>Fecha de vencimiento</Text>
                  </View>

                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>Pago semanal</Text>
                  </View>

                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>Tarifa</Text>
                  </View>

                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>Valor contrato</Text>
                  </View>

                  <View style={styles.tableCol}>
                    <Text style={styles.tableHeadCellTitle}>Cantidad pendiente</Text>
                  </View>
                </View>
                {data?.allPDFPayments.slice(150).map(({ day, weeklyRent, fee, ...rest }, i) => {
                  return (
                    <View style={styles.tableRow} key={i}>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}> {150 + i + 1} </Text>
                      </View>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}> {day} </Text>
                      </View>
                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}>
                          ${parsePriceMXN(fee ? weeklyRent - fee : weeklyRent)}{' '}
                        </Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}>${parsePriceMXN(fee || 0)}</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}>${parsePriceMXN(rest.contractValue || 0)}</Text>
                      </View>

                      <View style={styles.tableCol}>
                        <Text style={styles.tableCell}>${parsePriceMXN(rest.amountOutstanding || 0)}</Text>
                      </View>
                    </View>
                  );
                })}
              </View>
            )}
          </View>
        </View>
      )}
    </>
  );
}
