import { VehicleResponse } from '@/actions/getVehicleData';
import { create } from 'zustand';

interface ContractDetailStateProps {
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  contractData: VehicleResponse['drivers'][number]['contractData'];
  setContractData: (contractData: VehicleResponse['drivers'][number]['contractData']) => void;
}

export const useContractDetailState = create<ContractDetailStateProps>((set) => ({
  isOpen: false,
  onOpen: () => set(() => ({ isOpen: true })),
  onClose: () => set(() => ({ isOpen: false })),
  contractData: null as unknown as VehicleResponse['drivers'][number]['contractData'],
  setContractData: (contractData) => set(() => ({ contractData })),
}));
