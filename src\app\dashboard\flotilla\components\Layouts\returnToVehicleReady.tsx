/* eslint-disable consistent-return */
'use client';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import SelectInput from '@/components/Inputs/SelectInput';
import CustomModal from '@/components/Modals/CustomModal';
import { URL_API } from '@/constants';
import { useToast } from '@chakra-ui/react';
import axios from 'axios';
import { useParams, useRouter } from 'next/navigation';
import Swal from 'sweetalert2';

export default function ReturnToVehicleReady() {
  const { user } = useCurrentUser();
  const { id } = useParams();
  const router = useRouter();
  const toast = useToast();
  const onSubmit = async (values: any) => {
    try {
      const response = await axios.put(
        `${URL_API}/stock/returnVehicleReady/${id}`,
        {
          deleteAll: values.deleteAll.value !== 'no',
        },
        {
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
        }
      );
      toast({
        title: response.data.message || 'Vehiculo regresado a listo',
        status: 'info',
        position: 'top',
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => {
        return router.refresh();
      }, 3000);
    } catch (error: any) {
      return Swal.fire({
        icon: 'error',
        title: 'Hubo un error',
        text: error.message || '',
      });
    }
  };

  const confirSubmit = async (values: any) => {
    Swal.fire({
      title: '¿Desea regresar a vehiculo listo?',
      text: 'Esta accion no se puede deshacer',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Si, regresar a vehiculo listo',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  return (
    <>
      <CustomModal
        header="Regresar a vehiculo listo"
        isPrimaryButton
        plusButton={false}
        confirmButtonText="Regresar a vehiculo listo"
        initialValues={{
          deleteAll: { label: 'No', value: 'no' },
        }}
        onCloseModal={() => {}}
        openButtonText="Regresar a vehiculo listo"
        onSubmit={async (values) => {
          await confirSubmit(values);
        }}
        body={
          <div className="flex flex-col gap-3">
            <p>Por default elimina solo el conductor actual cuando el selector esta en no</p>
            <SelectInput
              name="deleteAll"
              label="¿Desea eliminar a todos los usuarios?"
              options={[
                { label: 'No', value: 'no' },
                { label: 'Si', value: 'si' },
              ]}
            />
          </div>
        }
      />
    </>
  );
}
