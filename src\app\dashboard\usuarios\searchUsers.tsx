'use client';
import { AiOutlineSearch } from 'react-icons/ai';
import { StatusFilterType, useUsers } from './UsersProvider';

export default function SearcherUsers() {
  const { handleSearch, handleStatusFilter } = useUsers();

  return (
    <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center">
      {/* Search Input */}
      <div className="relative w-full sm:max-w-[300px]">
        <input
          className="px-3 h-[40px] border border-[#9CA3AF] rounded w-full pr-10 !outline-none"
          type="text"
          onChange={handleSearch}
          placeholder="Buscar usuario..."
          aria-label="Buscar usuario"
        />
        <div className="absolute top-0 right-0 text-[#9CA3AF] flex items-center h-full pr-2 pointer-events-none">
          <AiOutlineSearch size={20} />
        </div>
      </div>

      {/* Status Dropdown */}
      <select
        className="h-[40px] border border-[#9CA3AF] rounded px-2 text-sm bg-white text-gray-700"
        onChange={(e) => handleStatusFilter(e.target.value as StatusFilterType)}
        aria-label="Filtrar por estado"
        defaultValue="all"
      >
        <option value="all">Todos</option>
        <option value="active">Activos</option>
        <option value="inactive">Inactivos</option>
        <option value="pending">Pendiente</option>
      </select>
    </div>
  );
}
