'use client';
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ody,
  Modal<PERSON>loseButton,
  ModalHeader,
  ModalOverlay,
  ModalContent,
  Flex,
  VStack,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import WeeklyEarningsChart from './WeeklyEarningsChart';
import PlatformMetricsTable from './PlatformMetricsTable';
import { useCountry } from './detail';
import { Poppins } from 'next/font/google';
import { cn } from '@/lib/utils';
import { translations } from './home-visit/_components/translations';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-poppins',
});

export function PlatformDetailDialog({
  requestId,
  isOpen,
  onClose,
  platform,
  isPlatformMetricsTable = true,
}: {
  requestId: string;
  isOpen: boolean;
  onClose: () => void;
  platform: string;
  isPlatformMetricsTable?: boolean;
}) {
  const { isCountryUSA } = useCountry();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const [earnings, setEarnings] = useState([]);
  const [metrics, setMetrics] = useState(null);

  useEffect(() => {
    async function fetchMetrics() {
      if (!platform) return;
      if (!user) return;
      const res = await fetch(`${URL_API}/admission/requests/${requestId}/metrics/${platform}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      const data = await res.json();
      setMetrics(data.data);
    }

    async function fetchEarnings() {
      if (!platform) return;
      if (!user) return;
      const res = await fetch(`${URL_API}/admission/requests/${requestId}/earnings/${platform}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      const data = await res.json();
      setEarnings(data.data);
    }

    fetchEarnings();
    fetchMetrics();
  }, [isOpen, requestId, platform, user]);

  const headerMessage = isCountryUSA ? `Information ${platform}` : `Información ${platform}`;

  return (
    <>
      {
        <Modal isOpen={isOpen} onClose={onClose}>
          <ModalOverlay />
          <ModalContent minWidth="fit-content">
            <ModalHeader>{headerMessage}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4} align="stretch">
                <Flex>
                  {metrics &&
                    (isPlatformMetricsTable ? (
                      <PlatformMetricsTable metric={metrics} requestId={requestId} onClose={onClose} />
                    ) : (
                      <PlatformMetrics metrics={metrics} />
                    ))}
                </Flex>
                <Flex>
                  {earnings && earnings.length > 0 && <WeeklyEarningsChart weeklyEarnings={earnings} />}
                </Flex>
              </VStack>
            </ModalBody>
          </ModalContent>
        </Modal>
      }
    </>
  );
}

const PlatformMetrics = (props: { metrics: Record<string, any> }) => {
  const { metrics } = props;

  const platformMetadata = [
    {
      name: translations.es.TotalTrips,
      value: metrics.lifetimeTrips,
      unit: '',
    },
    {
      name: translations.es.AcceptanceRate,
      value: metrics.acceptanceRate,
      unit: '%',
    },
    {
      name: translations.es.CancellationRate,
      value: metrics.cancellationRate,
      unit: '%',
    },
    {
      name: translations.es.Rating,
      value: metrics.rating,
      unit: '',
    },
    {
      name: translations.es.TimeSinceFirstTrip,
      value: metrics.timeSinceFirstTrip,
      unit: translations.es.Year,
    },
  ];

  return (
    <div className="flex gap-2">
      {platformMetadata.map((platform) => {
        return (
          <div
            key={platform.name}
            className={
              (cn(poppins.variable),
              'px-4 py-4 border border-primaryBorderGray rounded-md bg-primaryBackground')
            }
          >
            <p className="text-sm text-primaryTypography font-inter font-medium ">{platform.name}</p>
            <span className="pt-3 text-3xl text-primaryGray900 font-poppins font-semibold block">
              {`${platform.value}${platform.unit}`}
            </span>
          </div>
        );
      })}
    </div>
  );
};
