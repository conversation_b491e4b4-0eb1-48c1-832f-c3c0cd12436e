import { NextResponse } from 'next/server';
import axios from 'axios';
import { URL_API } from '@/constants';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';

enum VehicleStatus {
  ACTIVE = 'active',
  ACTIVO = 'activo',
  INACTIVE = 'inactive',
  INACTIVO = 'inactivo',
  STOCK = 'stock',
  SOLD = 'sold',
  VENDIDO = 'vendido',
  INVOICED = 'invoiced',
  FACTURADO = 'facturado',
  BAJAS = 'bajas',
  DISCHARGED = 'discharged',
  REINGRESO = 'reingreso',
  READMISSION = 'readmission',
}

enum StatusPath {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  STOCK = 'stock',
  SOLD = 'sold',
  INVOICED = 'invoiced',
  BAJAS = 'bajas',
  REINGRESOS = 'reingresos',
}

export async function GET(req: Request, { params }: { params: { vehicleId: string } }) {
  try {
    // Get auth token from the user's session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { accessToken } = session.user as any;
    const { vehicleId } = params;

    // Fetch the vehicle details from the backend to determine its status
    const response = await axios.get(`${URL_API}/vehicles/${vehicleId}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.data) {
      return NextResponse.json({ error: 'Vehicle not found' }, { status: 404 });
    }

    const vehicle = response.data;
    const { status, category = '', country = '' } = vehicle;

    // Map the vehicle status to the corresponding route in the admin panel
    let statusPath = '';
    let categoryPath = category.toLowerCase();

    // Determine the correct redirect path based on vehicle status
    switch (status.toLowerCase()) {
      case VehicleStatus.ACTIVE:
      case VehicleStatus.ACTIVO:
        statusPath = StatusPath.ACTIVE;
        break;
      case VehicleStatus.INACTIVE:
      case VehicleStatus.INACTIVO:
        statusPath = StatusPath.INACTIVE;
        break;
      case VehicleStatus.STOCK:
        statusPath = StatusPath.STOCK;
        break;
      case VehicleStatus.SOLD:
      case VehicleStatus.VENDIDO:
        statusPath = StatusPath.SOLD;
        break;
      case VehicleStatus.INVOICED:
      case VehicleStatus.FACTURADO:
        statusPath = StatusPath.INVOICED;
        break;
      case VehicleStatus.BAJAS:
      case VehicleStatus.DISCHARGED:
        statusPath = StatusPath.BAJAS;
        break;
      case VehicleStatus.REINGRESO:
      case VehicleStatus.READMISSION:
        statusPath = StatusPath.REINGRESOS;
        break;
      default:
        // Default fallback to search if status doesn't match known categories
        return NextResponse.json(
          {
            redirectUrl: '/dashboard/flotilla/search',
            vehicleStatus: status.toLowerCase(),
            vehicleId,
          },
          { status: 200 }
        );
    }

    // Construct the redirect URL, including category if available
    let redirectUrl = `/dashboard/flotilla/${statusPath}`;

    // Add category path if available
    if (categoryPath) {
      redirectUrl += `/${categoryPath}`;
    }

    // Add vehicle ID
    redirectUrl += `/${vehicleId}`;

    // Add country as a query parameter if available
    if (country) {
      redirectUrl += `?country=${country}`;
    }

    return NextResponse.json(
      {
        redirectUrl,
        vehicleStatus: status.toLowerCase(),
        category: categoryPath,
        vehicleId,
        country,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error determining redirect URL:', error);
    return NextResponse.json(
      {
        error: 'Failed to determine redirect URL',
        vehicleId: params.vehicleId,
      },
      { status: 500 }
    );
  }
}
