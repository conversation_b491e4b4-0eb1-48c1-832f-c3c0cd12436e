import dayjs from 'dayjs';

/**
 * Returns true or false if the given ISOString date is one week of difference
 *
 * @param {string} date - The ISOString date to compare.
 */

export default function isNearOneWeek(date?: string) {
  // if (!date) return true;
  const policyValidity = dayjs(date);
  const policyValidityISO = policyValidity.toISOString();
  const today = dayjs();

  // Calcula la diferencia en días entre la fecha dada y la fecha de hoy
  const diffDays = policyValidity.diff(today, 'day');

  // Antes verifica si la fecha es antes que hoy y regresa true porque ya expiró
  if (policyValidityISO < today.toISOString()) return true;
  // Verifica si la diferencia es menor o igual a 7 (una semana)
  return Math.abs(diffDays) <= 15;
}
