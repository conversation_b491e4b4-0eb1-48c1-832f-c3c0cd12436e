'use client';

import React, { useState } from 'react';
import PermissionMatrix from '../PermissionMatrix';
import { areaOptions } from '@/constants';
import CustomInput from '@/components/Inputs/CustomInput';
import SelectInput from '@/components/Inputs/SelectInput';
import axios from 'axios';
import { Formik, Form, FormikHelpers } from 'formik';
import Swal from 'sweetalert2';
import { createPermissionSet } from '@/validatorSchemas/createPermissionSet';
import { useSession } from 'next-auth/react';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';
import { MyUser } from '@/actions/getCurrentUser';
import { useRouter } from 'next/navigation';
import { getRolesByArea } from '@/utils/roleHelpers';

interface Props {
  permissionMatrix: any[];
}

export interface Option {
  value: string;
  label: string;
}

const CreatePermissionForm: React.FC<Props> = ({ permissionMatrix }) => {
  const [selectedPermissions, setSelectedPermissions] = useState<Record<string, boolean>>({});
  const [, setSelectedArea] = useState('');
  const [availableRoles, setAvailableRoles] = useState<Option[]>([]);
  const { data: session } = useSession();
  const url = useCurrentUrl();
  const user = session?.user as unknown as MyUser;
  const router = useRouter();

  if (!user) return null;

  const handleAreaChange = (option: Option, formik: any) => {
    const selected = option.value;
    setSelectedArea(selected);

    setAvailableRoles(getRolesByArea(selected, user.role));

    // Reset the role field in Formik
    formik.setFieldValue('role', '');
  };

  const createPermissionSetAPI = async (body: any) => {
    const response = await axios.post(`${url}/permissionSet/create`, body, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response;
  };

  const handleSubmit = async (
    values: { name: string; role: any; area: any },
    actions: FormikHelpers<{ name: string; role: any; area: any }>
  ) => {
    const permissionData = {
      name: values.name,
      role: values.role.value,
      area: values.area.value,
      permissions: [] as Array<{ section: string; subSection: string; capability: string }>,
    };

    Object.keys(selectedPermissions).forEach((key) => {
      if (selectedPermissions[key]) {
        const [section, subSection, capability] = key.split('.');
        permissionData.permissions.push({ section, subSection, capability });
      }
    });

    try {
      const response = await createPermissionSetAPI(permissionData);

      if (response.status === 201) {
        Swal.fire({
          title: 'Creación exitosa',
          text: response?.data?.message || 'Permission set created successfully',
          icon: 'success',
          confirmButtonText: 'Cerrar',
        }).then(() => {
          router.push('/dashboard/permisos');
        });
      } else {
        Swal.fire({
          title: 'Algo salió mal',
          text: 'Respuesta inesperada del servidor',
          icon: 'warning',
          confirmButtonText: 'Cerrar',
        });
      }
    } catch (error: any) {
      Swal.fire({
        title: 'Algo salió mal',
        text: error?.response?.data?.message || 'Ocurrió un error inesperado',
        icon: 'error',
        confirmButtonText: 'Cerrar',
      });
    } finally {
      actions.setSubmitting(false);
    }
  };

  const defaultValues = {
    name: '',
    role: '',
    area: '',
  };

  return (
    <Formik
      initialValues={defaultValues}
      validationSchema={createPermissionSet}
      onSubmit={handleSubmit}
      validateOnMount
    >
      {({ isSubmitting }) => (
        <Form className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <CustomInput name="name" label="Nombre" type="text" />
            <SelectInput name="area" label="Área" options={areaOptions} onChange={handleAreaChange} />
            <SelectInput name="role" label="Rol" options={availableRoles} />
          </div>

          <div className="mt-6">
            <PermissionMatrix
              permissionSets={permissionMatrix}
              selected={selectedPermissions}
              setSelected={setSelectedPermissions}
              disabledPermissions={{}}
            />
          </div>

          <div className="flex justify-end pt-6">
            <button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded"
              disabled={isSubmitting}
            >
              Guardar
            </button>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default CreatePermissionForm;
