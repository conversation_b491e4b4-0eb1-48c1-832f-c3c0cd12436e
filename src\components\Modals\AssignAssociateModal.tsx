/* eslint-disable prettier/prettier */
/* eslint-disable consistent-return */
'use client';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>dalHeader,
  ModalOverlay,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  useDisclosure,
  useToast,
} from '@chakra-ui/react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Form as FormikForm, Formik, FormikValues, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import axios from 'axios';
import { Countries, CountriesShortNames, URL_API } from '@/constants';
import { MyUser } from '@/actions/getCurrentUser';
import Spinner from '../Loading/Spinner';
import { CountrySelector } from './USAssociateFields';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
import { useRouter, useSearchParams } from 'next/navigation';
import { Virtuoso } from 'react-virtuoso';
import useDebouncedCallback from '@/app/hooks/useDebouncedCallback';
import DriverInfoCard from '../DriverDetails/DriverInfoCard';
import DriverDocumentsCard, { RequestDocument } from '../DriverDetails/DriverDocumentsCard';
import HomeVisitDriverCard from '../DriverDetails/HomeVisitDriverCard';
import PlatformsDriverCard from '../DriverDetails/PlatformsDriverCard';
import Swal from 'sweetalert2';
import SolidarityObligatorDetailsCard from '@/app/dashboard/clientes/solicitudes/[id]/SolidarityObligatorDetailsCard';
import { associateTranslationsMX, associateTranslationsUS } from '@/constants/translations';
import CustomRadio from '../ui/radioButton';
import { AdmissionRequestStatus } from '@/app/dashboard/clientes/solicitudes/enums';

enum STEPS {
  COUNTRY = 0,
  DRIVER = 1,
  CONFIRM_DRIVER = 2,
}

const driverSelectionSchema = Yup.object().shape({
  selectedValue: Yup.string().required('Please select a driver.'),
});

// Update the interface and handleSubmit function
interface IDriverFormValues extends FormikValues {
  selectedValue: string;
}

type ReferenceObject = {
  [key: string]: string | undefined;
};

type Contact = {
  name: string;
  kinship: string;
  phone: string;
  address: string;
};

const convertReferences = (references: ReferenceObject): { contacts: Contact[] } => {
  const contacts: Contact[] = [];

  // Extract unique reference numbers from the keys
  const referenceNumbers = new Set(
    Object.keys(references)
      .map((key) => key.match(/reference(\d+)/))
      .filter((match): match is RegExpMatchArray => Boolean(match))
      .map((match) => match[1])
  );

  // Build the contacts array
  referenceNumbers?.forEach((num) => {
    const name = references[`reference${num}Name`];
    const kinship = references[`reference${num}Relationship`];
    const phone = references[`reference${num}Phone`];
    const address = references[`reference${num}Address`];

    if (name || kinship || phone || address) {
      contacts.push({ name: name || '', kinship: kinship || '', phone: phone || '', address: address || '' });
    }
  });

  return { contacts };
};

type ContactUS = {
  emergencyContactName: string;
  emergencyContactRelation: string;
  emergencyContactPhone: string;
};

const convertReferencesUS = (references: ReferenceObject): { contactsUS: ContactUS[] } => {
  const contactsUS: ContactUS[] = [];

  // Extract unique reference numbers from the keys
  const referenceNumbers = new Set(
    Object.keys(references)
      .map((key) => key.match(/reference(\d+)/))
      .filter((match): match is RegExpMatchArray => Boolean(match))
      .map((match) => match[1])
  );

  // Build the contacts array
  referenceNumbers.forEach((num) => {
    const emergencyContactName = references[`reference${num}Name`];
    const emergencyContactRelation = references[`reference${num}Relationship`];
    const emergencyContactPhone = references[`reference${num}Phone`];

    if (emergencyContactName || emergencyContactRelation || emergencyContactPhone) {
      contactsUS.push({
        emergencyContactName: emergencyContactName || '',
        emergencyContactRelation: emergencyContactRelation || '',
        emergencyContactPhone: emergencyContactPhone || '',
      });
    }
  });

  return { contactsUS };
};

export const AssociateModal = ({
  user,
  vehicleId,
  disabled,
  vehicleCountry = 'Mexico',
}: {
  user: MyUser;
  vehicleId: string;
  disabled?: boolean;
  vehicleCountry?: string;
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(STEPS.COUNTRY);
  const updateSideData = useUpdateSideData();
  const router = useRouter();
  const search = useSearchParams();
  const country = search ? search.get('country') : 'Mexico';

  const isInitialRender = useRef(true);

  const countryCondition = vehicleCountry === Countries['United States'];

  const translations = countryCondition ? associateTranslationsUS : associateTranslationsMX;

  const [admissionRequests, setAdmissionRequests] = useState<any[]>([]);
  const [pagination, setPagination] = useState<any>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [isFetching, setIsFetching] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [selectedValue, setSelectedValue] = useState('');
  const [homevisitFlag, setHomevisitFlag] = useState(false);
  const [changedRequest, setChangedRequests] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDriverData, setSelectedDriverData] = useState<any>(null);
  const [documentsAnalysis, setDocumentsAnalysis] = useState<any>(null);
  const [isEmailChecking, setIsEmailChecking] = useState(false);
  const [screenShots, setScreenShots] = useState<any>(null);
  const [stepLoading, setStepLoading] = useState(false);

  const getAdmissionRequest = async (driverId: string) => {
    try {
      const response = await axios.get(`${URL_API}/admission/requests/${driverId}`, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });

      return response?.data?.data;
    } catch (error) {
      console.error('Error fetching admission request:', error);
    }
  };

  const fetchDocumentAnalysis = async (driverId: string) => {
    setStepLoading(true);
    try {
      const response = await axios.get(
        `${URL_API}/admission/requests/${driverId}/documents-analysis/all_documents`,
        {
          headers: {
            Authorization: `Bearer ${user?.accessToken}`,
          },
        }
      );
      setDocumentsAnalysis(response?.data?.data);
      setStepLoading(false);
    } catch (error) {
      console.error('Error fetching document analysis:', error);
      setStepLoading(false);
    }
  };

  const fetchScreenshots = async (driverId: string) => {
    try {
      setStepLoading(true);
      const response = await axios.get(`${URL_API}/admission/requests/screenshots/${driverId}`, {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
        },
      });
      setScreenShots(response?.data);
      setStepLoading(false);
    } catch (error) {
      console.error('Error fetching screenshots:', error);
      setStepLoading(false);
    }
  };

  const fetchAdmissionRequests = async (page: number, searchParam?: string) => {
    setIsFetching(true);
    try {
      const countryShortName = CountriesShortNames[vehicleCountry as keyof typeof CountriesShortNames];
      const status = 'approved';

      const query = {
        country: countryShortName,
        status: status,
        q: searchParam,
        options: {
          page,
          limit: 10,
        },
      };

      const response = await axios.post(`${URL_API}/admission/requests/search`, query, {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      const newRequests = response?.data?.data;
      setPagination(response?.data?.pagination);
      if (newRequests?.length > 0) {
        if (page === 1) {
          setAdmissionRequests(newRequests);
        } else {
          setAdmissionRequests((prev) => [...prev, ...newRequests]);
        }
        setHasMore(response?.data?.pagination?.hasMore);
      }
    } catch (error) {
      console.error('Error fetching admission requests:', error);
      setHasMore(false);
    } finally {
      setIsFetching(false);
    }
  };

  useEffect(() => {
    if (isInitialRender.current) {
      isInitialRender.current = false;
      return;
    }
    if (step === STEPS.DRIVER && admissionRequests.length === 0) {
      fetchAdmissionRequests(currentPage, searchQuery);
    }
  }, [step]);

  const debounceSearch = useDebouncedCallback((val) => {
    setCurrentPage(1);
    setAdmissionRequests([]);
    setPagination({});
    setSelectedDriverData(null);
    setHomevisitFlag(false);
    setChangedRequests([]);
    setSelectedValue('');
    setHasMore(true);
    fetchAdmissionRequests(1, val);
  }, 500);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target?.value;
    setSearchQuery(value);
    debounceSearch(value);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
    setAdmissionRequests([]);
    setSelectedValue('');
    setSelectedDriverData(null);
    setHomevisitFlag(false);
    setChangedRequests([]);
    setDocumentsAnalysis(null);
    setScreenShots(null);
    setPagination({});
    setHasMore(true);
    fetchAdmissionRequests(1, '');
  };

  const handleClose = () => {
    onClose();
    setStep(STEPS.COUNTRY);
    setSelectedValue('');
    setSelectedDriverData(null);
    setHomevisitFlag(false);
    setChangedRequests([]);
    setSearchQuery('');
    setHasMore(true);
    setAdmissionRequests([]);
    setPagination({});
    setDocumentsAnalysis(null);
    setScreenShots(null);
    setCurrentPage(1);
  };

  const loadMore = () => {
    if (!isFetching && hasMore) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchAdmissionRequests(nextPage, searchQuery);
    }
  };

  const onBack = () => {
    setStep((value) => (value > 0 ? value - 1 : value));
  };

  const onNext = () => {
    setStep((value) => value + 1);
  };

  const onSubmit = useCallback(async () => {
    if (homevisitFlag || changedRequest?.includes(selectedDriverData?.id)) {
      const checkRequest = await getAdmissionRequest(selectedDriverData?.id);
      if (checkRequest.status && checkRequest.status != AdmissionRequestStatus.approved) {
        setChangedRequests((prev) =>
          prev.includes(selectedDriverData?.id) ? prev : [...prev, selectedDriverData?.id]
        );
        throw new Error(translations.statusError);
      }
    }
    const data = {
      vehicleId: vehicleId,
      userId: user?.id,
      personalData: {
        ...selectedDriverData?.personalData,
        ...convertReferences(selectedDriverData?.personalData?.references),
        ...(countryCondition ? { ...convertReferencesUS(selectedDriverData?.personalData?.references) } : {}),
      },
      documentsAnalysis: documentsAnalysis,
      avalData: selectedDriverData?.avalData,
      requestId: selectedDriverData?._id || selectedDriverData?.id,
    };
    const res = await axios.post(`${URL_API}/associate/assignAssociate`, data, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return res;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    user?.id,
    user.accessToken,
    vehicleId,
    selectedDriverData,
    documentsAnalysis,
    countryCondition,
    homevisitFlag,
    changedRequest,
  ]);

  const confirmSuperAdmin = async () => {
    Swal.fire({
      title: translations.submitConfirmMessage,
      text: translations.submitConfirmText,
      showDenyButton: true,
      confirmButtonText: translations.confirmButtonText,
      denyButtonText: `No`,
    }).then(async (result) => {
      if (result.isConfirmed) {
        setIsLoading(true);
        try {
          await onSubmit();
          toast({
            title: translations.successTitle,
            description: translations.successDescription,
            status: 'success',
            duration: 3000,
            position: 'top',
          });
          await updateSideData(user);
          handleClose();
          if (window.location.pathname.includes('inactive')) {
            router.push(
              `/dashboard/flotilla/inactive/assigned/${vehicleId}${country ? `?country=${encodeURI(country)}` : ''
              }`
            );
          }
          router.refresh();
        } catch (error: any) {
          toast({
            title: error?.response?.data?.message || error.message || translations.apiError,
            status: 'error',
            duration: 3000,
            position: 'top',
          });
          onOpen();
        } finally {
          setIsLoading(false);
        }
      } else if (result.isDenied) {
        Swal.fire(translations.driverNotAssigned, '', 'info');
        handleClose();
      }
    });
  };

  const handleSubmit = async (
    data: IDriverFormValues,
    setFieldError: (field: string, message: string) => void
  ) => {
    if (step === STEPS.COUNTRY) {
      return onNext();
    }
    if (step === STEPS.DRIVER) {
      if (!selectedValue) {
        setFieldError('selectedValue', translations.selectDriver);
        return;
      }
      const selectedDriver = admissionRequests?.find((driver) => driver?.id === selectedValue);
      if (selectedDriver?.personalData?.email) {
        setIsEmailChecking(true);
        const checkEmail = selectedDriver?.personalData?.email?.toLowerCase();
        try {
          const result = await axios.get(`${URL_API}/associate/email/${checkEmail}`, {
            headers: {
              Authorization: `Bearer ${user?.accessToken}`,
            },
          });
          if (result?.data?.message?.toLowerCase()?.includes('email encontrado')) {
            setFieldError('selectedValue', translations.emailError);
            return;
          }
        } catch (error) {
          console.error('Error verifying email:', error);
        } finally {
          setIsEmailChecking(false);
        }
      }
      setSelectedDriverData((prev: any) => {
        if (prev?.id === selectedValue) {
          return prev;
        }
        return selectedDriver;
      });
      await fetchDocumentAnalysis(selectedValue);
      await fetchScreenshots(selectedValue);
      onNext();
      return;
    }

    if (step === STEPS.CONFIRM_DRIVER) {
      try {
        setIsLoading(true);
        const requiredDocs = [
          'proof_of_address',
          'drivers_license_front',
          'drivers_license_back',
          'bank_statement_month_1',
          'bank_statement_month_2',
          'bank_statement_month_3',
          'selfie_photo',
          'garage_photo',
        ];

        const additionalDocs = countryCondition
          ? ['bank_statement_month_4', 'bank_statement_month_5', 'bank_statement_month_6']
          : ['identity_card_front', 'identity_card_back', 'proof_of_tax_situation'];

        const allRequiredDocs = [...requiredDocs, ...additionalDocs];

        const uploadedDocs = new Set(documentsAnalysis?.documents?.map((doc: any) => doc.type));

        const hasAllDocs = allRequiredDocs?.every((doc) => uploadedDocs?.has(doc));

        if (user.role === 'superadmin' && !hasAllDocs) {
          return await confirmSuperAdmin();
        } else {
          await onSubmit();
          toast({
            title: translations.successTitle,
            description: translations.successDescription,
            status: 'success',
            duration: 3000,
            position: 'top',
          });
          await updateSideData(user);
          handleClose();
          router.refresh();
          if (window.location.pathname.includes('inactive')) {
            router.push(
              `/dashboard/flotilla/inactive/assigned/${vehicleId}${country ? `?country=${encodeURI(country)}` : ''
              }`
            );
          }
        }
      } catch (error: any) {
        console.error(error);
        const response = error?.response?.data;
        const errors = response?.errors || response?.message;

        toast({
          title:
            error?.message == translations.statusError ? translations.statusError : translations.apiError,
          description: Array.isArray(errors) ? errors[0]?.msg : errors,
          status: 'error',
          duration: 3000,
          position: 'top',
        });
        onOpen();
      } finally {
        setIsLoading(false);
      }
    }
    return null;
  };

  const actionLabel = useMemo(() => {
    if (step === STEPS.DRIVER) {
      return translations.assignText;
    }
    if (step === STEPS.CONFIRM_DRIVER) {
      return translations.confirmText;
    }
    return translations.continueText;
  }, [step]);

  let bodyContent = (
    <>
      <CountrySelector defaultCountry={vehicleCountry ? vehicleCountry : Countries.Mexico} />
    </>
  );

  if (step === STEPS.DRIVER) {
    bodyContent = (
      <FormControl>
        <FormLabel>Seleccionar conductora</FormLabel>

        <Box mb={2} position="relative">
          <input
            type="text"
            placeholder={translations.searchText}
            value={searchQuery}
            onChange={handleInputChange}
            className="w-full p-2 border rounded-md"
          />
          {searchQuery && (
            <button onClick={clearSearch} className="absolute right-2 top-2 text-gray-500 hover:text-black">
              &#10005;
            </button>
          )}
        </Box>

        {admissionRequests.length === 0 && !isFetching ? (
          <Box p="4" textAlign="center" color="gray.500">
            {translations.noDriver}
          </Box>
        ) : (
          <Box position="relative" borderWidth="1px" borderRadius="md" overflow="hidden" maxH="500px">
            <div style={{ height: '500px' }}>
              <Table>
                <Thead bg="#FAFAFA" position="sticky" top={0} zIndex={1}>
                  <Tr borderBottom="1px solid" borderColor="#CFD8E1">
                    <Th p={4} width="50px"></Th>
                    <Th p={4} width="300px" textAlign="left" color="#5A7190">
                      {translations.driverTranslation}
                    </Th>
                    <Th p={4} textAlign="left" color="#5A7190">
                      {translations.curpTranslation}
                    </Th>
                  </Tr>
                </Thead>
              </Table>
              <Virtuoso
                style={{ height: 'calc(500px - 43px)' }}
                overscan={10}
                totalCount={admissionRequests.length}
                endReached={loadMore}
                itemContent={(index) => {
                  const driver = admissionRequests[index];
                  const isChecked = selectedValue === driver.id;
                  const key = driver?.id || `driver-${index}`;

                  return (
                    <Table style={{ tableLayout: 'fixed', width: '100%' }}>
                      <Tbody>
                        <Tr
                          key={key}
                          borderBottom="1px solid"
                          borderColor="gray.100"
                          _hover={{ bg: 'gray.50' }}
                          cursor="pointer"
                          onClick={() => {
                            const newValue = selectedValue === driver.id ? '' : driver.id;
                            setSelectedValue(newValue);
                            setHomevisitFlag(false);
                          }}
                        >
                          <Td p={4} width="50px">
                            <CustomRadio
                              checked={isChecked}
                              onChange={() => {
                                const newValue = selectedValue === driver.id ? '' : driver.id;
                                setSelectedValue(newValue);
                                setHomevisitFlag(false);
                              }}
                            />
                          </Td>
                          <Td p={4} width="300px" textAlign="left" color="#0A293B">
                            {driver?.personalData?.firstName || ''} {driver?.personalData?.lastName || ''}
                          </Td>
                          <Td p={4} textAlign="left" color="#5A7190" fontWeight={400}>
                            {driver?.personalData?.nationalId || 'N/A'}
                          </Td>
                        </Tr>
                      </Tbody>
                    </Table>
                  );
                }}
                components={{
                  Footer: () => <Footer loading={isFetching} />,
                }}
                computeItemKey={(index) => admissionRequests[index]?._id}
              />
            </div>
          </Box>
        )}
        <Box p={4} color="gray.600">
          {isFetching ? 'Loading...' : translations.getRecordsText(admissionRequests, pagination)}
        </Box>
      </FormControl>
    );
  }

  if (step === STEPS.CONFIRM_DRIVER && selectedDriverData) {
    bodyContent = (
      <div className="flex flex-col gap-6">
        <DriverInfoCard data={selectedDriverData?.personalData} countryCondition={countryCondition} />
        <PlatformsDriverCard
          palenca={selectedDriverData?.palenca}
          earningsAnalysis={selectedDriverData?.earningsAnalysis}
          screenshots={screenShots || []}
          countryCondition={countryCondition}
        />
        <HomeVisitDriverCard
          homeVisit={selectedDriverData?.homeVisit}
          countryCondition={countryCondition}
          requestId={selectedDriverData?.id}
          setHomevisitFlag={setHomevisitFlag}
        />
        <DriverDocumentsCard
          documentsAnalysis={documentsAnalysis || selectedDriverData?.documentsAnalysis}
          countryCondition={countryCondition}
        />
        <SolidarityObligatorDetailsCard
          requestId={selectedDriverData?.id}
          user={user}
          solidarityObligatorDetails={selectedDriverData?.avalData}
          setDocumentsAnalysis={setDocumentsAnalysis}
          avalDocs={[
            documentsAnalysis?.documents?.filter(
              (doc: RequestDocument) => doc.type.startsWith('solidarity_obligor') && doc.mediaId
            ),
          ]}
          setSelectedDriverData={setSelectedDriverData}
        />
      </div>
    );
  }

  if (user.role === 'auditor') return null;

  return (
    <>
      <div className="relative">
        <button
          disabled={disabled}
          className="bg-[#5800F7] text-white text-start px-3 h-[40px] rounded w-auto disabled:bg-gray-300 cursor-pointer"
          onClick={onOpen}
          data-cy="driverAssign"
        >
          {translations.assignText}
        </button>
      </div>
      {isLoading && <Spinner />}

      <Modal
        closeOnOverlayClick={false}
        size={step === STEPS.DRIVER ? '2xl' : step !== STEPS.CONFIRM_DRIVER ? '2xl' : '4xl'}
        isOpen={isOpen}
        onClose={handleClose}
      >
        <ModalOverlay zIndex={30} />
        <ModalContent>
          <ModalHeader>{translations.assignText}</ModalHeader>
          <ModalCloseButton
            onClick={() => {
              handleClose();
            }}
          />
          <Formik<IDriverFormValues>
            initialValues={{ selectedValue: selectedValue }}
            onSubmit={(values, { setFieldError }) => {
              if (step === STEPS.CONFIRM_DRIVER) onClose();
              handleSubmit(values, setFieldError);
            }}
            validationSchema={step === STEPS.DRIVER ? driverSelectionSchema : undefined}
            enableReinitialize
          >
            {({ }) => (
              <FormikForm>
                <ModalBody pb={6}>
                  <div className="flex flex-col gap-[20px]">
                    {bodyContent}
                    {step === STEPS.DRIVER && (
                      <ErrorMessage name="selectedValue" component="div" className="text-red-500" />
                    )}
                  </div>
                </ModalBody>
                <ModalFooter gap={3}>
                  <Button
                    sx={{
                      color: '#5800F7',
                      borderColor: '#5800F7 !important',
                      border: '2px',
                      h: '40px',
                    }}
                    onClick={onBack}
                  >
                    {translations.previousText}
                  </Button>
                  <Button
                    sx={{
                      color: 'white',
                      h: '40px',
                    }}
                    className={`
                    ${(!selectedValue && step === STEPS.DRIVER) || isEmailChecking || stepLoading
                        ? 'bg-[#9CA3AF]'
                        : 'bg-[#5800F7]'
                      }
                    text-white rounded-md h-[40px] cursor-pointer`}
                    type="submit"
                    disabled={(!selectedValue && step === STEPS.DRIVER) || isEmailChecking || stepLoading}
                    data-cy="next"
                  >
                    {isEmailChecking ? translations.verifyingText : stepLoading ? 'Loading...' : actionLabel}
                  </Button>
                </ModalFooter>
              </FormikForm>
            )}
          </Formik>
        </ModalContent>
      </Modal>
    </>
  );
};

interface IAssignAssociateModal {
  user: MyUser;
  vehicleId: string;
  vehicleCountry: string;
}

export const AssignAssociateModal = (props: IAssignAssociateModal) => {
  const { user, vehicleId, vehicleCountry } = props;

  return <AssociateModal user={user} vehicleId={vehicleId} vehicleCountry={vehicleCountry} />;
};

function Footer({ loading }: { loading: boolean }) {
  return (
    <>
      {loading && (
        <div
          style={{
            padding: '2rem',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          Loading...
        </div>
      )}
    </>
  );
}
