'use client';
import EmptyState from '@/components/EmptyState';
import RequestsTable from './RequestsTable';
import { Box } from '@chakra-ui/react';
import { EmptyStateTitleMX, EmptyStateTitleUS } from './translations';
import { useCountry } from '../../providers/CountryProvider';

export default function Requests({ requests }: { requests: any }) {
  const { data, pagination } = requests;

  const { isCountryUSA } = useCountry();

  return (
    <Box bg="white" boxShadow="sm" border={1} borderColor="gray.200" borderRadius={8} p={4}>
      {!data || data.length === 0 ? (
        <EmptyState title={isCountryUSA ? `${EmptyStateTitleUS}` : `${EmptyStateTitleMX}`} minH={48} />
      ) : (
        <RequestsTable requests={data} pagination={pagination} isCountryUSA={isCountryUSA} />
      )}
    </Box>
  );
}
