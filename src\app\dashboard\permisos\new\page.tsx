import getPermissionMatrix from '@/actions/getPermissionMatrix';
import CreatePermissionForm from './CreatePermissionForm';
import { Suspense } from 'react';

export default async function CrearPermisoPage() {
  const permissionMatrix = await getPermissionMatrix();
  if (!permissionMatrix) return null;

  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <h1 className="text-[28px] font-bold text-[#262D33]">Agregar Permiso</h1>
      <div className="py-2 bg-gray-50">
        <CreatePermissionForm permissionMatrix={permissionMatrix} />
      </div>
    </Suspense>
  );
}
