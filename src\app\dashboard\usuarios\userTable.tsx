'use client';
// import { UserResponse } from '@/actions/getUsers';
import {
  Avatar,
  Checkbox,
  IconButton,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  Table,
  TableContainer,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  useToast,
} from '@chakra-ui/react';
import { useUsers } from './UsersProvider';
import CustomModal from '@/components/Modals/CustomModal';
import { useEffect, useState } from 'react';
import SelectInput from '@/components/Inputs/SelectInput';
// import { UserResponse } from '@/actions/getUsers';
import { useCurrentUser } from '../providers/CurrentUserProvider';
import axios from 'axios';
import {
  CONTRACT_REGIONS,
  URL_API,
  cities,
  roleOptions,
  areaOptions,
  Capabilities,
  Sections,
  Subsections,
  Areas,
  Roles,
} from '@/constants';
import { AiOutlineEye } from 'react-icons/ai';
import { useRouter } from 'next/navigation';
import { Option } from './ModalCreateUser';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { canPerform } from '@/casl/canPerform';
import { usePermissions } from '@/casl/PermissionsContext';
import { MyUser } from '@/actions/getCurrentUser';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { getRolesByArea } from '@/utils/roleHelpers';
import { editUserValidationSchema } from '@/validatorSchemas/editUserValidationSchema';
import Swal from 'sweetalert2';
import useCurrentUrl from '@/app/hooks/useCurrentUrl';

/* interface UserTableProps {
  users?: UserResponse[] | null;
} */

export default function UserTable() {
  const { users } = useUsers();
  const { user: currentUser } = useCurrentUser();
  // const [allowedRegions, setAllowedRegions] = useState<any[]>(currentUser.settings.allowedRegions);
  const [allowedRegionsUsers, setAllowedRegionsUsers] = useState<any[]>(users);

  const onChange = (id: string, option: Option) => {
    const newRegion = option.value;
    const find = allowedRegionsUsers.find((u) => u._id === id);
    const selectAllowedRegions = find.settings.allowedRegions;
    const filterNewUsers = allowedRegionsUsers.filter((u) => u._id !== id);

    setAllowedRegionsUsers([
      ...filterNewUsers,
      {
        ...find,
        settings: {
          allowedRegions: [...selectAllowedRegions, newRegion],
        },
      },
    ]);
  };

  const removeRegion = (id: string, regionToDelete: string) => {
    const find = allowedRegionsUsers.find((u) => u._id === id);

    if (find.settings.allowedRegions.length === 1) return;

    const newSettingsToUser = find.settings.allowedRegions.filter((region: any) => region !== regionToDelete);

    find.settings.allowedRegions = newSettingsToUser;

    const othersUsers = allowedRegionsUsers.filter((u) => u._id !== id);
    setAllowedRegionsUsers([...othersUsers, find]);
  };

  const router = useRouter();

  const ability = usePermissions();

  const canEdit = canPerform(ability, Capabilities.Edit, Sections.UserManagement, Subsections.Users);
  const canMakeHomeVisitor = canPerform(
    ability,
    Capabilities.MakeHomeVisitor,
    Sections.UserManagement,
    Subsections.Users
  );
  const canDelete = canPerform(ability, Capabilities.Delete, Sections.UserManagement, Subsections.Users);
  const canAssignPermissions = canPerform(
    ability,
    Capabilities.AssignPermissions,
    Sections.UserManagement,
    Subsections.Users
  );

  const url = useCurrentUrl();

  const deleteUserAPI = async (_id: string) => {
    const response = await axios.delete(`${url}/user/${_id}`, {
      headers: {
        Authorization: `Bearer ${currentUser.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    return response;
  };

  const deleteUser = async (_id: string) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This will permanently delete this user.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it',
      cancelButtonText: 'Cancel',
    });

    if (result.isConfirmed) {
      try {
        await deleteUserAPI(_id);
        Swal.fire('Deleted!', 'The user has been deleted.', 'success').then(() => {
          window.location.reload();
        });
      } catch (error: any) {
        Swal.fire('Error', 'Failed to delete the user.', 'error');
      }
    }
  };

  return (
    <>
      {/* User Count */}
      <p className="text-sm text-gray-600 mb-2">
        Mostrando <strong>{users.length}</strong> usuario{users.length === 1 ? '' : 's'}
      </p>
      <TableContainer bgColor="#FFFFFF" px={6} pt={3} pb={6} borderRadius="5px">
        <Table variant="striped" colorScheme="gray">
          <Thead>
            <Tr>
              <Th px="4px" pl="68px">
                Nombre
              </Th>
              <Th px="4px" py="6px">
                Correo
              </Th>
              <Th px="4px" py="6px">
                Área
              </Th>
              <Th px="4px" py="6px">
                Rol
              </Th>
              <Th px="4px" py="6px">
                Ciudades
              </Th>
              <Th px="4px" py="6px">
                Estatus
              </Th>
              <Th px="4px" py="6px">
                Acciones
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {users &&
              users.map((user) => (
                <Tr key={user._id} h="30px" color="#262D33">
                  <Td py="6px" pl="24px">
                    <div className="flex gap-2 items-center">
                      <Avatar
                        src={
                          user.image.url ||
                          'https://w7.pngwing.com/pngs/81/570/png-transparent-profile-logo-computer-icons-user-user-blue-heroes-logo-thumbnail.png'
                        }
                        w="36px"
                        h="36px"
                      />
                      <p
                        className={`
                      ml-2 ${user.role === 'auditor' ? 'cursor-pointer' : ''}
                      `}
                        onClick={() => {
                          if (user.role === 'auditor') {
                            router.push(`/dashboard/usuarios/${user._id}`);
                          }
                        }}
                      >
                        {user.name}
                      </p>
                    </div>
                  </Td>
                  <Td px="4px" py="6px">
                    {user.email}
                  </Td>
                  <Td px="4px" py="6px">
                    {areaOptions.find((option) => option.value === user.area)?.label}
                  </Td>
                  <Td px="4px" py="6px">
                    {roleOptions.find((option) => option.value === user.role)?.label}
                  </Td>
                  <Td px="4px" py="6px">
                    {/* {cities[user.city]} */}
                    <Popover placement="top">
                      <PopoverTrigger>
                        <IconButton icon={<AiOutlineEye />} aria-label="see-allowed-regions" />
                      </PopoverTrigger>
                      <PopoverContent w="max-content" minW="300px">
                        <PopoverHeader fontWeight="semibold">Ciudades permitidas o a cargo</PopoverHeader>
                        <PopoverArrow />
                        <PopoverCloseButton />
                        <PopoverBody w="max-content">
                          {user.settings.allowedRegions.map((region: string, i: number) => {
                            return (
                              <div className="flex gap-2" key={i}>
                                <p>{cities[region]?.label}</p>
                              </div>
                            );
                          })}
                        </PopoverBody>
                      </PopoverContent>
                    </Popover>
                  </Td>
                  <Td
                    px="4px"
                    py="6px"
                    color={user.isActive ? (user.isVerified ? '#29CC97' : '#9CA3AF') : '#9CA3AF'}
                  >
                    {user.isActive ? (user.isVerified ? 'Activo' : 'Pendiente') : 'Inactivo'}
                  </Td>
                  <Td px="4px" py="6px">
                    <div className="flex gap-2 items-center self-end">
                      {canEdit && (
                        <CustomModal
                          header="Editar usuario"
                          openButtonText="Editar"
                          body={
                            <Body
                              userId={user._id}
                              allowedRegions={
                                allowedRegionsUsers.find((u) => u._id === user._id).settings.allowedRegions
                              }
                              onChange={onChange}
                              removeRegion={removeRegion}
                              area={user.area}
                            />
                          }
                          onSubmit={async (values) => {
                            const obj = {
                              allowedRegions: allowedRegionsUsers.find((u) => u._id === user._id).settings
                                .allowedRegions,
                              role: values.role.value,
                              area: values.area.value,
                            };
                            const response = await axios.patch(`${URL_API}/user/update/${user._id}`, obj, {
                              headers: {
                                Authorization: `Bearer ${currentUser.accessToken}`,
                              },
                            });
                            return response;
                          }}
                          onCloseModal={async () => {}}
                          isPrimaryButton
                          confirmButtonText="Editar"
                          initialValues={{
                            allowedRegions: currentUser.settings.allowedRegions,
                            role: {
                              label: 'Selecciona',
                              value: '',
                            },
                            area: {
                              label: 'Selecciona',
                              value: '',
                            },
                          }}
                          plusButton={false}
                          isEditUser
                          validatorSchema={editUserValidationSchema(user.area)}
                        />
                      )}
                      {/* <button className="border-[2px] border-[#5800F7] text-[#5800F7] rounded py-[7px] px-[5px] font-bold ">
                      Editar
                    </button> */}
                      {canMakeHomeVisitor && <HomeVisitorBody user={user} />}
                      {canAssignPermissions && (
                        <Link
                          href={`/dashboard/usuarios/assign/${user._id}?t=${Date.now()}`}
                          className="inline-block border-[2px] border-[#5800F7] text-[#5800F7] rounded py-[7px] px-[5px] font-bold"
                        >
                          Permisos Adicionales
                        </Link>
                      )}
                      {canDelete && user.isActive && currentUser._id !== user._id && (
                        <button
                          className="border-[2px] border-[#E14942] text-[#E14942] rounded py-[7px] px-[5px] font-bold"
                          onClick={() => deleteUser(user._id)}
                        >
                          Eliminar
                        </button>
                      )}
                    </div>
                  </Td>
                </Tr>
              ))}
          </Tbody>
        </Table>
      </TableContainer>
    </>
  );
}

interface BodyProps {
  // userSelected: UserResponse;
  allowedRegions: any[];
  onChange: (id: string, option: Option) => void;
  removeRegion: (id: string, region: string) => void;
  userId: string;
  area: string;
}

function Body({ userId, allowedRegions, onChange, removeRegion, area }: BodyProps) {
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const isSuperAdmin = user?.role === Roles.Superadmin && user?.area === Areas.Superadmin;
  const filteredOptions = CONTRACT_REGIONS.filter((op) => !allowedRegions.includes(op.value));
  const availableAreas = isSuperAdmin ? areaOptions : areaOptions.filter((opt) => opt.value === area);
  const [availableRoles, setAvailableRoles] = useState<Option[]>(getRolesByArea(area, user?.role));

  const handleAreaChange = (option: Option, formik: any) => {
    const selected = option.value;

    setAvailableRoles(getRolesByArea(selected, user?.role));

    // Reset the role field in Formik
    formik.setFieldValue('role', '');
  };

  return (
    <div className="grid gap-3">
      <SelectInput label="Cambiar Área" name="area" options={availableAreas} onChange={handleAreaChange} />
      <SelectInput label="Cambiar Rol" name="role" options={availableRoles} />
      <SelectInput
        label="Agregar region"
        name="addRegion"
        onChange={(option) => onChange(userId, option)}
        options={filteredOptions}
      />

      <div className="flex flex-col gap-1 mt-2">
        <p>Regiones permitidas:</p>
        <div className="flex flex-wrap gap-2 ">
          {allowedRegions.map((region, i) => {
            return (
              <div className="flex gap-2 border-2 border-gray-400 rounded px-3 py-1 " key={i}>
                <p>{cities[region].label}</p>
                <div onClick={() => removeRegion(userId, region)} className="cursor-pointer ">
                  X
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

function HomeVisitorBody(props: any) {
  const { user } = props;
  const [open, setOpen] = useState(false);
  return <HomeVisitorModal open={open} setOpen={setOpen} user={user} />;
}

export function HomeVisitorModal({ open, setOpen, user }: any) {
  const [homeVisitor, setHomeVisitor] = useState(user.homeVisitor);
  const { user: currentUser } = useCurrentUser();
  const [loading, setLoading] = useState(false);

  const router = useRouter();
  const toast = useToast();

  useEffect(() => {
    setHomeVisitor(user.homeVisitor);
  }, [open, user.homeVisitor]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const obj = {
        homeVisitor: homeVisitor,
      };
      await axios.patch(`${URL_API}/user/update-home-visit-status/${user._id}`, obj, {
        headers: {
          Authorization: `Bearer ${currentUser.accessToken}`,
        },
      });
      toast({
        title: 'success',
        description: 'Estado de visita domiciliaria del usuario actualizado correctamente',
        status: 'success',
        duration: 6000,
        isClosable: true,
      });
      setOpen(false);
      router.refresh();
    } catch (error: any) {
      const translationsCode = {
        en: {
          futureHomeVisitAppointments: `You cannot revoke this user's home visitor status as they have future scheduled appointments.`,
        },
        es: {
          futureHomeVisitAppointments: `No puedes revocar el estado de visitante domiciliario de este usuario ya que tiene citas programadas en el futuro.`,
        },
      };
      const errorMessage = error.response?.data?.errorKey || 'Ocurrió un error al actualizar el usuario';
      toast({
        title: 'Error',
        description: translationsCode.es[errorMessage as keyof typeof translationsCode.es],
        status: 'error',
        duration: 6000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          onClick={() => {
            setOpen(true);
          }}
          className=" bg-primaryVibrantPurpleGradient  hover:bg-primaryVibrantPurpleGradient "
        >
          Home Visitor
        </Button>
      </DialogTrigger>
      <DialogContent className="w-4/5 max-h-screen overflow-y-auto">
        <DialogTitle>{'Make Home Visitor'}</DialogTitle>
        <p className=" font-bold">User Email: {user.email}</p>

        <Checkbox
          defaultChecked={homeVisitor}
          onChange={(e: any) => {
            const value = e.target.checked;
            setHomeVisitor(value);
          }}
        >
          Home Visitor
        </Checkbox>

        <DialogFooter>
          <Button
            type="button"
            onClick={() => {
              setOpen(false);
            }}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            Crear
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
