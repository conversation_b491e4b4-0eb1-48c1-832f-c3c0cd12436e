import { cache } from 'react';
import getCurrentUser from './getCurrentUser';
import axios from 'axios';
import { URL_API } from '@/constants';

export interface Section {
  section: string;
  subSection: string;
  capability: string;
}

export interface PermissionSetResponse {
  _id: string;
  name: string;
  role: string;
  area: string;
  permissions: Section[];
}

const getPermissionSetById = cache(async (permissionSetId: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const id = permissionSetId;

    const response = await axios.get(`${URL_API}/permissionSet/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response?.data?.permissionSet as PermissionSetResponse;
  } catch (error: any) {
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getPermissionSetById;
