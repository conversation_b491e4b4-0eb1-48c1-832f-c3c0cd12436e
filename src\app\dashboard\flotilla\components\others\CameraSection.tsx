import React from 'react';
import { Box, Button, Flex, Spinner, Text, VStack, Image, IconButton } from '@chakra-ui/react';
import { CameraIcon, XCircleIcon } from 'lucide-react';

interface CameraSectionProps {
  isCameraOpen: boolean;
  isCameraAvailable: boolean;
  isUploading: boolean;
  photoPreviewUrl: string | null;
  photoTexts: {
    takePhoto: string;
    retakePhoto: string;
    photoRequired: string;
    photoInstruction: string;
    photoDescription: string;
    uploading: string;
  };
  openCamera: () => void;
  takePhoto: () => void;
  setPhotoPreviewUrl: (url: string | null) => void;
  setPhotoPath: (path: string | null) => void;
  videoRef: React.RefObject<HTMLVideoElement>;
  borderColor?: string;
  textColor?: string;
  facingMode: 'user' | 'environment';
  toggleFacingMode: () => void;
}

const CameraSection: React.FC<CameraSectionProps> = ({
  isCameraOpen,
  isCameraAvailable,
  isUploading,
  photoPreviewUrl,
  photoTexts,
  openCamera,
  takePhoto,
  setPhotoPreviewUrl,
  setPhotoPath,
  videoRef,
  borderColor = 'gray.200',
  textColor = 'gray.600',
  facingMode,
  toggleFacingMode,
}) => (
  <Box borderWidth="1px" borderRadius="md" p={4} borderColor={borderColor}>
    <Text fontWeight="medium" mb={3}>
      {photoTexts.photoRequired}
    </Text>

    {isCameraOpen ? (
      <VStack spacing={3} align="center" w="100%">
        <Box position="relative" width="100%" height="300px" bg="black" borderRadius="md" overflow="hidden">
          <video
            ref={videoRef}
            style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            autoPlay
            playsInline
            muted
          />
          {!isCameraAvailable && (
            <Flex
              position="absolute"
              top="0"
              left="0"
              right="0"
              bottom="0"
              justifyContent="center"
              alignItems="center"
              bg="rgba(0,0,0,0.7)"
            >
              <Spinner color="white" size="lg" />
            </Flex>
          )}
        </Box>
        <Flex w="100%" justify="space-between" align="center" gap={2}>
          <Button
            colorScheme="blue"
            leftIcon={<CameraIcon size={18} />}
            onClick={takePhoto}
            size="md"
            w="full"
            isLoading={isUploading}
            loadingText={photoTexts.uploading}
            isDisabled={!isCameraAvailable}
            className="text-white rounded-md h-[40px] px-4 bg-[#5800F7] hover:bg-[#4A00D1]"
          >
            {photoTexts.takePhoto}
          </Button>
          <Button
            onClick={toggleFacingMode}
            size="md"
            variant="outline"
            colorScheme="gray"
            minW="120px"
            ml={2}
          >
            {facingMode === 'user' ? 'Front' : 'Back'} Camera
          </Button>
        </Flex>
      </VStack>
    ) : photoPreviewUrl ? (
      <VStack spacing={3} align="center">
        <Box position="relative" width="100%" borderRadius="md" overflow="hidden">
          <Image src={photoPreviewUrl} alt="Vehicle Photo" w="100%" borderRadius="md" />
          <IconButton
            aria-label="Remove photo"
            icon={<XCircleIcon />}
            position="absolute"
            top={2}
            right={2}
            colorScheme="red"
            size="sm"
            borderRadius="full"
            onClick={() => {
              setPhotoPreviewUrl(null);
              setPhotoPath(null);
            }}
          />
        </Box>
        <Button
          leftIcon={<CameraIcon size={18} />}
          onClick={openCamera}
          size="sm"
          sx={{
            background: '#5800F7',
            color: 'white',
            borderRadius: 'md',
            height: '40px',
            px: 4,
            _hover: { background: '#4A00D1' },
          }}
          className="text-white rounded-md h-[40px] px-4 bg-[#5800F7] hover:bg-[#4A00D1]"
        >
          {photoTexts.retakePhoto}
        </Button>
      </VStack>
    ) : (
      <Flex
        direction="column"
        align="center"
        justify="center"
        p={4}
        bg="gray.50"
        _dark={{ bg: 'gray.700' }}
        borderRadius="md"
        cursor="pointer"
        onClick={openCamera}
      >
        <CameraIcon size={48} />
        <Text mt={2} fontWeight="medium">
          {photoTexts.photoInstruction}
        </Text>
        <Text fontSize="sm" color={textColor} mt={1}>
          {photoTexts.photoDescription}
        </Text>
        <Button
          mt={4}
          leftIcon={<CameraIcon size={18} />}
          onClick={openCamera}
          sx={{
            background: '#5800F7',
            color: 'white',
            borderRadius: 'md',
            height: '40px',
            px: 4,
            _hover: { background: '#4A00D1' },
          }}
          className="text-white rounded-md h-[40px] px-4 bg-[#5800F7] hover:bg-[#4A00D1]"
        >
          {photoTexts.takePhoto}
        </Button>
      </Flex>
    )}
  </Box>
);

export default CameraSection;
