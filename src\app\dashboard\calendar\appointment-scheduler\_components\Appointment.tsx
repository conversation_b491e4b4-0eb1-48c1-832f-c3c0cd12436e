'use client';
import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { createCalendarSchedule } from '@/actions/calendar';
import { AlertStatus, useToast } from '@chakra-ui/react';
import { Separator } from '../../../clientes/solicitudes/[id]/home-visit/_components/FormHeaderSection';
import {
  HookFormRadixUIField,
  HookFormRadixUISelect,
} from '../../../clientes/solicitudes/[id]/home-visit/_components/HookFormField';
import { useForm, UseFormReturn } from 'react-hook-form';
import { Form, FormLabel } from '@/components/ui/form';
import { FiClock } from 'react-icons/fi';
import { cn } from '@/lib/utils';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { StepperButton } from '../../../clientes/solicitudes/[id]/home-visit/_components/StepperButtons';
import { CiCalendarDate } from 'react-icons/ci';
import { translations } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/translations';
import { FiPlusCircle } from 'react-icons/fi';
import { IoMdCloseCircle } from 'react-icons/io';

import { format } from 'date-fns';
import { DatePickerForm } from './HookFormShadcnDatePicker';
import { DateTime } from 'luxon';
import { generateMessagesForAddSlotsErrors, generateMessagesForBlockSlotsErrors } from './utils';

const DAYS_FULL_NAME_MAPPINGS = {
  sunday: 'Sunday',
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
};

const DEFAULT_AVAILABLE_HOURS = {
  sunday: { start: '00:00am', end: '00:00am' },
  monday: { start: '09:00am', end: '06:00pm' },
  tuesday: { start: '09:00am', end: '06:00pm' },
  wednesday: { start: '09:00am', end: '06:00pm' },
  thursday: { start: '09:00am', end: '06:00pm' },
  friday: { start: '09:00am', end: '06:00pm' },
  saturday: { start: '00:00am', end: '00:00am' },
};
const DEFAULT_BREAK_TIME = { start: '01:00pm', end: '02:00pm' };
const DEFAULT_MAX_TIME_IN_ADVANCE = 5;
const DEFAULT_MIN_TIME_BEFORE_APPOINTMENT_SCHEDULE = 30;

interface IAppointmentSchedulerProps {
  calendarSchedule: any;
}

const AppointmentSchema = z.object({
  appointmentDuration: z
    .number({ invalid_type_error: translations.es.ShouldBeAValidNumber })
    .min(1, translations.es.ShouldBeAValidNumber),
  repeat: z.string(),
  breakTimeStart: z.string(),
  breakTimeEnd: z.string(),
  maxTimeInAdvanceSlotsAvailable: z
    .number({ invalid_type_error: translations.es.ShouldBeAValidNumber })
    .min(1, translations.es.ShouldBeAValidNumber),
  minTimeBeforeAppointmentStart: z
    .number({ invalid_type_error: translations.es.ShouldBeAValidNumber })
    .min(1, translations.es.ShouldBeAValidNumber),
  timezone: z.string(),

  blockSlotStartDate: z.date().optional(),
  blockSlotEndDate: z.date().optional(),
  blockSlotStartTime: z.string().optional(),
  blockSlotEndTime: z.string().optional(),
  blockSlots: z.array(
    z.object({ startDate: z.string(), endDate: z.string(), startTime: z.string(), endTime: z.string() })
  ),

  addSlotStartDate: z.date().optional(),
  addSlotEndDate: z.date().optional(),
  addSlotStartTime: z.string().optional(),
  addSlotEndTime: z.string().optional(),
  addedSlots: z.array(
    z.object({ startDate: z.string(), endDate: z.string(), startTime: z.string(), endTime: z.string() })
  ),
});
export default function AppointmentScheduler(props: IAppointmentSchedulerProps) {
  const { calendarSchedule } = props;

  const [loading, setLoading] = useState(false);
  const [weeklySchedule, setWeeklySchedule] = useState(
    calendarSchedule?.weeklySchedule || DEFAULT_AVAILABLE_HOURS
  );
  const breakTime =
    Object.keys(calendarSchedule).length > 0 ? calendarSchedule?.breakTimes[0] : DEFAULT_BREAK_TIME;

  const toast = useToast();
  const form = useForm({
    resolver: zodResolver(AppointmentSchema),
    defaultValues: {
      appointmentDuration: calendarSchedule?.duration || 30,
      repeat: 'Weekly',
      breakTimeStart: breakTime.start,
      breakTimeEnd: breakTime.end,
      timezone: calendarSchedule?.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
      maxTimeInAdvanceSlotsAvailable: calendarSchedule?.maxAdvanceBookingDays || DEFAULT_MAX_TIME_IN_ADVANCE,
      /**
       * this field is DEPRECATED
       * now Min Time Before Appointment Book is in minutes and hard coded to 30 minutes
       * both in front-end and backend
       */
      minTimeBeforeAppointmentStart: DEFAULT_MIN_TIME_BEFORE_APPOINTMENT_SCHEDULE,

      blockSlotStartDate: new Date(),
      blockSlotEndDate: new Date(),
      blockSlotStartTime: '',
      blockSlotEndTime: '',
      blockSlots: [] as Slot[],

      addSlotStartDate: new Date(),
      addSlotEndDate: new Date(),
      addSlotStartTime: '',
      addSlotEndTime: '',
      addedSlots: [] as Slot[],
    },
    mode: 'onChange',
  });

  const handleSaveSchedule = async (data: any) => {
    try {
      setLoading(true);
      const schedulePayload = {
        name: 'Weekly Schedule',
        weeklySchedule: weeklySchedule,
        timezone: data.timezone,
        breakTimes: [
          {
            start: data.breakTimeStart,
            end: data.breakTimeEnd,
          },
        ],
        bufferTime: 0,
        duration: data.appointmentDuration,
        repeat: data.repeat,
        maxAdvanceBookingDays: data.maxTimeInAdvanceSlotsAvailable,
        /**
         * this field is DEPRECATED
         * now Min Time Before Appointment Book is in minutes and hard coded to 30 minutes
         * both in front-end and backend
         */
        minBookingNoticeHours: data.minTimeBeforeAppointmentStart,

        blockSlots: data.blockSlots,
        addedSlots: data.addedSlots,
      };
      const response = await createCalendarSchedule(schedulePayload);
      if (response && response.success) {
        const responseData = response.data;
        let toastMessage = translations.es.AppointmentSchedulingSuccessMsg;
        let status: AlertStatus = 'success';
        let title = translations.es.success;
        if (
          responseData &&
          Array.isArray(responseData?.blockSlotsErrors) &&
          responseData?.blockSlotsErrors.length > 0
        ) {
          toastMessage = '';
          status = 'error';
          title = translations.es.error;
          const blockSlotsErrorMessages = generateMessagesForBlockSlotsErrors(responseData?.blockSlotsErrors);
          toastMessage = blockSlotsErrorMessages;
        }
        if (
          responseData &&
          Array.isArray(responseData?.addedSlotsErrors) &&
          responseData?.addedSlotsErrors.length > 0
        ) {
          if (responseData?.blockSlotsErrors.length === 0) {
            toastMessage = '';
          }
          status = 'error';
          title = translations.es.error;
          const addedSlotsErrorMessages = generateMessagesForAddSlotsErrors(responseData?.addedSlotsErrors);
          toastMessage += addedSlotsErrorMessages;
        }
        toast({
          title: title,
          status: status,
          duration: 9000,
          description: toastMessage,
          isClosable: true,
        });
      }
      if (response && response.error) {
        toast({
          title: translations.es.error,
          status: 'error',
          duration: 3000,
          description: translations.es.AppointmentSchedulingErrorMsg,
        });
      }
    } catch (err) {
      toast({
        title: translations.es.error,
        status: 'error',
        duration: 3000,
        description: translations.es.AppointmentSchedulingErrorMsg,
      });
    } finally {
      setLoading(false);
      form.reset();
    }
  };

  const renderTimeSlots = () => {
    return (
      <div className="py-2">
        <div>
          {Object.keys(weeklySchedule).map((day) => {
            return (
              <div key={day}>
                <div className="flex  gap-8 px- 2 py-2">
                  <div className="flex items-center pt-4 flex-[0_0_20%]">
                    <h5>{DAYS_FULL_NAME_MAPPINGS[day as keyof typeof DAYS_FULL_NAME_MAPPINGS]}</h5>
                  </div>
                  <div className="flex-1 flex gap-2 items-center">
                    <RadixUiCustomInput
                      label={translations.es.StartTime}
                      id={`${day}-start`}
                      value={weeklySchedule[day as keyof typeof weeklySchedule].start}
                      onChange={(e: any) => {
                        setWeeklySchedule((prev: any) => ({
                          ...prev,
                          [day as keyof typeof DEFAULT_AVAILABLE_HOURS]: {
                            ...prev[day as keyof typeof DEFAULT_AVAILABLE_HOURS],
                            start: e.target.value,
                          },
                        }));
                      }}
                      Icon={FiClock}
                    />
                    <RadixUiCustomInput
                      label={translations.es.EndTime}
                      id={`${day}-end`}
                      value={weeklySchedule[day as keyof typeof weeklySchedule].end}
                      onChange={(e: any) => {
                        setWeeklySchedule((prev: any) => ({
                          ...prev,
                          [day as keyof typeof DEFAULT_AVAILABLE_HOURS]: {
                            ...prev[day as keyof typeof DEFAULT_AVAILABLE_HOURS],
                            end: e.target.value,
                          },
                        }));
                      }}
                      Icon={FiClock}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full  bg-white py-6 px-4">
      <div className="flex justify-between items-center pt-2 pb-2">
        <h3 className=" text-2xl font-bold pb-4">{translations.es.AppointmentScheduler}</h3>
        <StepperButton
          text={translations.es.Save}
          className={'border border-primaryPurple text-white bg-primaryBtn'}
          size={'lg'}
          isLoading={loading}
          isDisabled={loading}
          onClick={form.handleSubmit(async (data) => {
            await handleSaveSchedule(data);
          })}
        />
      </div>
      <Separator />
      <div>
        <section>
          <h3 className="text-xl font-bold">{translations.es.AppointmentDuration}</h3>
          <Form {...form}>
            <form>
              <div className="w-3/6 py-4 flex items-end gap-2">
                <HookFormRadixUIField
                  form={form}
                  fieldName={'appointmentDuration'}
                  formLabel={translations.es.HowLongShouldEachAppointmentLast}
                  type="number"
                />
                <p className="pb-2">{translations.es.minutes}</p>
              </div>
              <Separator />
              <h3 className="text-xl font-bold  py-2 ">{translations.es.GeneralAvailability}</h3>
              <div>
                <HookFormRadixUISelect
                  control={form.control}
                  fieldName="repeat"
                  selectOptions={[{ label: 'Weekly', value: 'Weekly' }]}
                  formLabel={translations.es.Repeat}
                  className="py-2"
                />
              </div>
              <div className="flex gap-2">
                <div className="flex-1">{renderTimeSlots()}</div>
                <Separator />
              </div>
              <div className="py-2">
                <h3 className="text-xl font-bold  py-2 ">{translations.es.BreakTime}</h3>
                <div className="flex-1 flex gap-8 w-3/6 ">
                  <HookFormRadixUIField
                    form={form}
                    fieldName={'breakTimeStart'}
                    formLabel={translations.es.StartTime}
                    Icon={FiClock}
                    placeholder="HH:MM"
                  />
                  <HookFormRadixUIField
                    form={form}
                    fieldName={'breakTimeEnd'}
                    formLabel={translations.es.EndTime}
                    Icon={FiClock}
                    placeholder="HH:MM"
                  />
                </div>
              </div>
              <Separator />
              <BlockSlots form={form} />
              <Separator />
              <AddSlots form={form} />
              <Separator />
              <div className="py-2">
                <h3 className="text-xl font-bold py-2">{translations.es.SchedulingWindow}</h3>
                <div className="w-3/6 flex gap-2 items-end py-2">
                  <HookFormRadixUIField
                    form={form}
                    fieldName={'maxTimeInAdvanceSlotsAvailable'}
                    formLabel={translations.es.MaximumTimeInAdvanceThatAnAppointmentCanBeBooked}
                    type="number"
                    placeholder="7"
                    Icon={CiCalendarDate}
                    isDisabled={true}
                  />
                  <p className="pb-2">{translations.es.days}</p>
                </div>
                <div className="w-3/6 flex gap-1 items-end py-2">
                  <HookFormRadixUIField
                    form={form}
                    fieldName={'minTimeBeforeAppointmentStart'}
                    formLabel={translations.es.MininumTimeBeforeTheAppointmentStartThatItCanBeBooked}
                    type="number"
                    placeholder="4"
                    Icon={CiCalendarDate}
                    isDisabled={true}
                  />
                  <p className="pb-2">{translations.es.minutes}</p>
                </div>
              </div>
              <div className="py-2">
                <h3 className="text-xl font-bold  py-2 ">{translations.es.Timezone}</h3>
                <div className="w-3/6">
                  <HookFormRadixUIField
                    form={form}
                    fieldName={'timezone'}
                    formLabel={'Select'}
                    isDisabled={true}
                  />
                </div>
              </div>
            </form>
          </Form>
        </section>
      </div>
    </div>
  );
}

type Slot = {
  startDate: string | null;
  endDate: string | null;
  startTime: string;
  endTime: string;
};

interface IBlockSlotsProps {
  form: UseFormReturn<
    {
      appointmentDuration: any;
      repeat: string;
      breakTimeStart: any;
      breakTimeEnd: any;
      timezone: any;
      maxTimeInAdvanceSlotsAvailable: any;
      minTimeBeforeAppointmentStart: any;
      blockSlotStartDate: Date;
      blockSlotEndDate: Date;
      blockSlotStartTime: string;
      blockSlotEndTime: string;
      blockSlots: Slot[];
      addSlotStartDate: Date;
      addSlotEndDate: Date;
      addSlotStartTime: string;
      addSlotEndTime: string;
      addedSlots: Slot[];
    },
    any,
    undefined
  >;
}

const BlockSlots = (props: IBlockSlotsProps) => {
  const { form } = props;

  const blockSlotsList = form.getValues('blockSlots');
  form.watch('blockSlots');

  const handleAddBlockSlot = async () => {
    if (!form.getValues('blockSlotStartDate')) {
      form.setError('blockSlotStartDate', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!form.getValues('blockSlotEndDate')) {
      form.setError('blockSlotEndDate', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!form.getValues('blockSlotStartTime')) {
      form.setError('blockSlotStartTime', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    const timeRegex = /^(0[1-9]|1[0-2]):([0-5][0-9])([ap]m)$/i;
    if (!timeRegex.test(form.getValues('blockSlotStartTime'))) {
      form.setError('blockSlotStartTime', {
        type: 'manual',
        message: translations.es.InvalidTimeFormat,
      });
      return;
    }

    if (!form.getValues('blockSlotEndTime')) {
      form.setError('blockSlotEndTime', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!timeRegex.test(form.getValues('blockSlotEndTime'))) {
      form.setError('blockSlotEndTime', {
        type: 'manual',
        message: translations.es.InvalidTimeFormat,
      });
      return;
    }

    const blockSlotStartTime = form.getValues('blockSlotStartTime');
    const blockSlotEndTime = form.getValues('blockSlotEndTime');

    const dateTime1 = DateTime.fromFormat(blockSlotStartTime, 'hh:mma');
    const dateTime2 = DateTime.fromFormat(blockSlotEndTime, 'hh:mma');

    if (dateTime1 > dateTime2) {
      form.setError('blockSlotEndTime', {
        type: 'manual',
        message: translations.es.EndTimeGreaterThanStartTime,
      });
      return;
    } else if (dateTime1.equals(dateTime2)) {
      form.setError('blockSlotEndTime', {
        type: 'manual',
        message: translations.es.EndTimeGreaterThanStartTime,
      });
      return;
    }

    const blockSlotStartDate = form.getValues('blockSlotStartDate');
    const blockSlotEndDate = form.getValues('blockSlotEndDate');
    const blockSlot = {
      startDate: DateTime.fromJSDate(blockSlotStartDate).setZone('local').toISODate(),
      endDate: DateTime.fromJSDate(blockSlotEndDate).setZone('local').toISODate(),
      startTime: form.getValues('blockSlotStartTime'),
      endTime: form.getValues('blockSlotEndTime'),
    };

    /**
     * need to check if the block slot is not overlapping with the existing slots
     */
    const blockSlots = form.getValues('blockSlots');
    blockSlots.push(blockSlot);
    form.setValue('blockSlots', blockSlots);
    form.resetField('blockSlotStartDate');
    form.resetField('blockSlotEndDate');
    form.resetField('blockSlotStartTime');
    form.resetField('blockSlotEndTime');
  };

  const handleDeleteBlockSlot = (index: number) => {
    const blockSlotsListArr = form.getValues('blockSlots');
    form.setValue(
      'blockSlots',
      blockSlotsListArr.filter((_: any, i: number) => i !== index)
    );
  };

  return (
    <div className="py-2">
      <h3 className="text-xl font-bold  py-2 ">{translations.es.BlockSlots}</h3>
      <div className="flex-1 flex gap-2 py-2 ">
        <div className="flex gap-2 w-3/6">
          <DatePickerForm form={form} label={translations.es.StartDate} name={'blockSlotStartDate'} />
          <DatePickerForm
            form={form}
            label={translations.es.EndDate}
            name={'blockSlotEndDate'}
            isDisabled={true}
          />
        </div>
        <div className="flex-1 h-full flex items-end ">
          <FiPlusCircle size={25} onClick={handleAddBlockSlot} />
        </div>
      </div>

      <div className=" flex gap-4 items-center py-2 w-3/6">
        <div className="w-3/6">
          <HookFormRadixUIField
            form={form}
            fieldName={'blockSlotStartTime'}
            formLabel={translations.es.StartTime}
            Icon={CiCalendarDate}
            placeholder={'HH:MM'}
            className="py-2"
            defaultErrorBoxStyles={true}
          />
        </div>
        <div className="w-3/6">
          <HookFormRadixUIField
            form={form}
            fieldName={'blockSlotEndTime'}
            formLabel={translations.es.EndTime}
            Icon={CiCalendarDate}
            placeholder={'HH:MM'}
            className="py-2"
            defaultErrorBoxStyles={true}
          />
        </div>
      </div>

      <div className="flex-1 flex flex-wrap gap-2 items-center py-2">
        {blockSlotsList.map((slot: Slot, index) => {
          return (
            <div key={index} className="flex gap-2 bg-primaryGrayBadge rounded-md p-2  items-center">
              <div>
                {format(new Date(slot.startDate!), 'PPP')} {slot.startTime} - {slot.endTime}
              </div>
              <div>
                <IoMdCloseCircle size={20} fill={'#586D79'} onClick={() => handleDeleteBlockSlot(index)} />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface IAddSlotsProps extends IBlockSlotsProps {}

const AddSlots = (props: IAddSlotsProps) => {
  const { form } = props;

  const addedSlotsList = form.getValues('addedSlots');
  form.watch('addedSlots');

  const handleAddSlot = async () => {
    if (!form.getValues('addSlotStartDate')) {
      form.setError('addSlotStartDate', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!form.getValues('addSlotEndDate')) {
      form.setError('addSlotEndDate', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!form.getValues('addSlotStartTime')) {
      form.setError('addSlotStartTime', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    const timeRegex = /^(0[1-9]|1[0-2]):([0-5][0-9])([ap]m)$/i;
    if (!timeRegex.test(form.getValues('addSlotStartTime'))) {
      form.setError('addSlotStartTime', {
        type: 'manual',
        message: translations.es.InvalidTimeFormat,
      });
      return;
    }

    if (!form.getValues('addSlotEndTime')) {
      form.setError('addSlotEndTime', {
        type: 'manual',
        message: translations.es.FieldIsRequired,
      });
      return;
    }

    if (!timeRegex.test(form.getValues('addSlotEndTime'))) {
      form.setError('addSlotEndTime', {
        type: 'manual',
        message: translations.es.InvalidTimeFormat,
      });
      return;
    }

    const addSlotStartTime = form.getValues('addSlotStartTime');
    const addSlotEndTime = form.getValues('addSlotEndTime');

    const dateTime1 = DateTime.fromFormat(addSlotStartTime, 'hh:mma');
    const dateTime2 = DateTime.fromFormat(addSlotEndTime, 'hh:mma');

    if (dateTime1 > dateTime2) {
      form.setError('addSlotEndTime', {
        type: 'manual',
        message: translations.es.EndTimeGreaterThanStartTime,
      });
      return;
    } else if (dateTime1.equals(dateTime2)) {
      form.setError('addSlotEndTime', {
        type: 'manual',
        message: translations.es.EndTimeGreaterThanStartTime,
      });
      return;
    }

    const addSlotStartDate = form.getValues('addSlotStartDate');
    const addSlotEndDate = form.getValues('addSlotEndDate');

    const addSlot = {
      startDate: DateTime.fromJSDate(addSlotStartDate).setZone('local').toISODate(),
      endDate: DateTime.fromJSDate(addSlotEndDate).setZone('local').toISODate(),
      startTime: form.getValues('addSlotStartTime'),
      endTime: form.getValues('addSlotEndTime'),
    };

    const addedSlots = form.getValues('addedSlots');
    addedSlots.push(addSlot);
    form.setValue('addedSlots', addedSlots);
    form.resetField('addSlotStartDate');
    form.resetField('addSlotEndDate');
    form.resetField('addSlotStartTime');
    form.resetField('addSlotEndTime');
  };

  const handleDeleteAddSlot = (index: number) => {
    const addedSlotsListArr = form.getValues('addedSlots');
    form.setValue(
      'addedSlots',
      addedSlotsListArr.filter((_: any, i: number) => i !== index)
    );
  };

  return (
    <div className="py-2">
      <h3 className="text-xl font-bold  py-2 ">{translations.es.AddSlots}</h3>
      <div className="flex-1 flex gap-2 py-2 ">
        <div className="flex gap-2 w-3/6">
          <DatePickerForm form={form} label={translations.es.StartDate} name={'addSlotStartDate'} />
          <DatePickerForm
            form={form}
            label={translations.es.EndDate}
            name={'addSlotEndDate'}
            isDisabled={true}
          />
        </div>
        <div className="flex-1 h-full flex items-end">
          <FiPlusCircle size={25} onClick={handleAddSlot} />
        </div>
      </div>

      <div className="flex gap-4 items-center py-2 w-3/6">
        <div className="w-3/6">
          <HookFormRadixUIField
            form={form}
            fieldName={'addSlotStartTime'}
            formLabel={translations.es.StartTime}
            Icon={CiCalendarDate}
            placeholder={'HH:MM'}
            className="pt-2"
            defaultErrorBoxStyles={true}
          />
        </div>
        <div className="w-3/6">
          <HookFormRadixUIField
            form={form}
            fieldName={'addSlotEndTime'}
            formLabel={translations.es.EndTime}
            Icon={CiCalendarDate}
            placeholder={'HH:MM'}
            className="pt-2"
            defaultErrorBoxStyles={true}
          />
        </div>
      </div>
      <div className="flex-1 flex flex-wrap gap-2 items-center py-2">
        {addedSlotsList.map((slot: Slot, index) => {
          return (
            <div key={index} className="flex gap-2 bg-primaryGrayBadge rounded-md p-2 items-center">
              <div>
                {format(new Date(slot.startDate!), 'PPP')} {slot.startTime} - {slot.endTime}
              </div>
              <div>
                <IoMdCloseCircle size={20} fill={'#586D79'} onClick={() => handleDeleteAddSlot(index)} />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface IRadixUiCustomInputProps {
  label: string;
  onChange?: any;
  id?: string;
  value?: string;
  Icon: any;
  placeholder?: string;
  type?: string;
}

const RadixUiCustomInput = (props: IRadixUiCustomInputProps) => {
  const { label, onChange, id, value, Icon, placeholder, type } = props;

  return (
    <div>
      <FormLabel className="basis-1/3 text-primaryBlueGray text-sm">{label}</FormLabel>
      <div
        className={cn(
          'flex gap-2 items-center w-full px-2 rounded-md border border-input text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
        )}
      >
        {<Icon size={25} />}
        <Input
          className={cn('w-full h-full py-2 border-0 focus-visible:outline-none')}
          placeholder={placeholder}
          type={type ? type : 'text'}
          defaultStyles={false}
          id={id}
          value={value}
          onChange={onChange}
        />
      </div>
    </div>
  );
};
