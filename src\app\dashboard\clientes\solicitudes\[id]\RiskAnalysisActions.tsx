'use client';
import { URL_API } from '@/constants';
import { Flex, Button, useToast } from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { MyUser } from '@/actions/getCurrentUser';
import Swal from 'sweetalert2';
import { useCountry } from './detail';

export default function RiskAnalysisActions({ requestId }: { requestId: string }) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTransitioning, startTransition] = useTransition();
  const toast = useToast();
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const { isCountryUSA } = useCountry();

  function handleApprove() {
    setIsSubmitting(true);
    fetch(`${URL_API}/admission/requests/${requestId}/risk-analysis/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
    })
      .then((res) => {
        if (res.ok) {
          startTransition(() => {
            router.replace(`${pathname}`, { scroll: false });
            router.refresh();
            toast({
              title: 'Análisis de riesgo Preaprobado',
              status: 'success',
            });
          });
        } else {
          throw new Error('Error approving risk analysis');
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  }

  function handleReject() {
    Swal.fire({
      animation: false,
      title: '¿Estás seguro de rechazar en análisis de riesgo?',
      text: 'Esta acción rechazará por completo la solicitud',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, rechazar',
      cancelButtonText: 'Cancelar',
      preConfirm: async () => {
        try {
          await fetch(`${URL_API}/admission/requests/${requestId}/risk-analysis/reject`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.accessToken}`,
            },
          });
        } catch (error) {
          Swal.showValidationMessage(`Request failed: ${error}`);
        }
      },
    }).then((result) => {
      if (result.isConfirmed) {
        startTransition(() => {
          router.refresh();
          router.push(`${pathname}`, { scroll: false });
          toast({
            title: 'Análisis de riesgo rechazado',
            status: 'success',
          });
        });
      }
    });
  }

  const DeclineText = isCountryUSA ? 'Decline' : 'Rechazar';
  const PreApproveAnalysis = isCountryUSA ? 'Pre-approve analysis' : 'Preaprobar análisis';

  return (
    <>
      <Flex gap={3}>
        <Button
          sx={{
            // bg: '#5800F7 !important',
            color: '#E14942',
            h: '40px',
          }}
          className="bg-white cursor-pointer hover:bg-gray-50 border border-[#E14942]"
          type="submit"
          isLoading={isSubmitting || isTransitioning}
          onClick={handleReject}
        >
          {DeclineText}
        </Button>

        <Button
          sx={{
            // bg: '#5800F7 !important',
            color: 'white',
            h: '40px',
          }}
          className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
          type="submit"
          isLoading={isSubmitting || isTransitioning}
          onClick={handleApprove}
        >
          {PreApproveAnalysis}
        </Button>
      </Flex>
    </>
  );
}
