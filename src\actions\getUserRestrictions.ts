import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';

export interface DocumentData {
  url: string;
  docId: string;
  originalName: string;
}

export interface UserVehicleRestriction {
  _id: string;
  carNumber: string;
  status: string;
}

const getUserRestrictions = cache(async (userId: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/user/restrictions/${userId}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const data = response.data.restrictions.stockRestrictions;
    return data as UserVehicleRestriction[];
  } catch (error: any) {
    // console.log(error.response);
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getUserRestrictions;
