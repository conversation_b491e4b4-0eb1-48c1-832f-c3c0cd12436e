import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';
import { Section } from './getPermissionSetById';

export interface DocumentData {
  url: string;
  docId: string;
  originalName: string;
}

export interface UserResponse {
  name: string;
  role: string;
  area: string;
  image: DocumentData;
  email: string;
  _id: string;
  city: string;
  isVerified: boolean;
  isActive: boolean;
  settings: {
    allowedRegions: ('cdmx' | 'gdl' | 'mty' | 'tij' | 'pbc' | 'qro')[];
  };
  accessToken: string;
  permissions: Section[];
}

const getUserById = cache(async (userId?: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const id = userId || user.id;

    const response = await axios.get(`${URL_API}/user/${id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    const currentUserLoggedIn = {
      ...user,
      ...response.data.user,
    };

    const completeUser = userId ? response.data.user : currentUserLoggedIn;
    return completeUser as UserResponse;
  } catch (error: any) {
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getUserById;
