import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/authOptions';
import { redirect } from 'next/navigation';

export default async function AdminPanelLayout({ children }: { children: React.ReactNode }) {
  // Check if user is authenticated
  const session = await getServerSession(authOptions);

  // If not authenticated, redirect to home (the middleware will handle adding callbackUrl)
  if (!session?.user) {
    redirect('/');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-blue-600 text-white p-4">
        <div className="container mx-auto">
          <h1 className="text-xl font-bold">Admin Panel</h1>
        </div>
      </header>
      <main className="container mx-auto py-6">{children}</main>
    </div>
  );
}
