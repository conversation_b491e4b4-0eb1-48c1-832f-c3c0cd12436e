import { create } from 'zustand';

interface SidebarCounts {
  vehicleStatusCounts: {
    active: number;
    inactive: number;
  };
  categoryCounts: Record<
    | 'withdrawn'
    | 'sold'
    | 'insurance'
    | 'collection'
    | 'legal'
    | 'workshop'
    | 'revision'
    | 'adendum'
    | 'in-preparation'
    | 'stock'
    | 'assigned'
    | 'delivered'
    | 'utilitary',
    number
  >;
}

interface SidebarStore extends SidebarCounts {
  setVehicleStatusCounts: (counts: SidebarCounts['vehicleStatusCounts']) => void;
  setCategoryCounts: (counts: SidebarCounts['categoryCounts']) => void;
}

const useSidebarStore = create<SidebarStore>((set) => ({
  // Initial state
  vehicleStatusCounts: { active: 0, inactive: 0 },
  categoryCounts: {
    withdrawn: 0,
    sold: 0,
    insurance: 0,
    collection: 0,
    legal: 0,
    workshop: 0,
    revision: 0,
    adendum: 0,
    'in-preparation': 0,
    stock: 0,
    assigned: 0,
    delivered: 0,
    utilitary: 0,
  },
  // State setters
  setVehicleStatusCounts: (counts) =>
    set(() => ({
      vehicleStatusCounts: counts, // Replace with new data
    })),
  setCategoryCounts: (counts) =>
    set(() => ({
      categoryCounts: counts, // Replace with new data
    })),
}));

export default useSidebarStore;
