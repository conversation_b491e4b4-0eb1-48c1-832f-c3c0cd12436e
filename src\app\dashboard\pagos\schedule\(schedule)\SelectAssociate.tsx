'use client';

import React, { useEffect, useState } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ChevronsUpDown } from 'lucide-react';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { PAYMENT_API_SECRET, PAYMENTS_API_URL } from '@/constants';
import axios from 'axios';
import { User } from '../../types';

async function getClients(search?: string): Promise<User[]> {
  let res = null;
  try {
    let url = `${PAYMENTS_API_URL}/clients`;

    if (search) {
      url += '/q?search=' + search;
    }

    res = await axios(url.toString(), {
      headers: {
        Authorization: `Bearer ${PAYMENT_API_SECRET}`,
      },
    });
  } catch (error) {
    console.log(error);
  }
  return res ? res.data.data : res;
}

type Props = {
  associateData: User | undefined;
  setAssociateData: (val: User) => void;
};

function SelectAssociate({ associateData, setAssociateData }: Props) {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [clientData, setClientData] = useState<User[]>([]);

  useEffect(() => {
    if (searchText) {
      setLoading(true);
      getClients(searchText).then((res) => {
        setLoading(false);
        setClientData(res);
      });
    }
  }, [searchText]);

  return (
    <div className="pr-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            className={cn('w-[380px] justify-between', !associateData && 'text-muted-foreground')}
          >
            {associateData
              ? associateData?.contractNumber + ' : ' + associateData.legal_name
              : 'Select Associate'}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="basis-4/5 p-0">
          <Command>
            <CommandInput
              onValueChange={(e) => {
                setSearchText(e);
              }}
              placeholder="Search Client..."
            />
            <CommandEmpty>
              {searchText ? 'No client found' : 'Please enter client contract number or name'}
            </CommandEmpty>
            {loading ? (
              <div role="status" className="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                <svg
                  aria-hidden="true"
                  className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
                  viewBox="0 0 100 101"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                    fill="currentColor"
                  />
                  <path
                    d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                    fill="currentFill"
                  />
                </svg>
                <span className="sr-only">Loading...</span>
              </div>
            ) : (
              ''
            )}
            <CommandList>
              <CommandGroup>
                {clientData.map((client: User) => (
                  <CommandItem
                    value={client.contractNumber + ' : ' + client.legal_name}
                    key={client.id}
                    onSelect={() => {
                      setAssociateData(client);
                    }}
                  >
                    {client.contractNumber + ' : ' + client.legal_name}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default SelectAssociate;
