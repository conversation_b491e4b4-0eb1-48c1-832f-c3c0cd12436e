import { FormikProps } from 'formik';
import { useEffect } from 'react';
import DatePicker from 'react-date-picker';
import { format } from 'date-fns';
import { Field, useField, ErrorMessage, FieldProps } from 'formik';
import 'react-date-picker/dist/DatePicker.css';
import 'react-calendar/dist/Calendar.css';

type ValuePiece = Date | null;

type Value = ValuePiece | [ValuePiece, ValuePiece];

export const CalenderFormikIntegration = ({
  className,
  onChange,
  name,
  label,
  defaultValue,
}: {
  form?: FormikProps<any>;
  fieldName: string;
  className?: string;
  onChange?: (date: Value) => void;
  name?: string;
  label?: string;
  defaultValue?: string;
}) => {
  useEffect(() => {
    const element = document.querySelector('.react-date-picker__wrapper');
    if (element) {
      element.setAttribute('style', 'border: 0');
    }
  }, []);

  const [field, meta] = useField(name as string);
  const hasError = meta.touched && meta.error;

  const localDate = field.value ? new Date(field.value) : new Date(defaultValue as string);
  const defaultClasses = `border border-gray rounded px-3 h-[40px] w-full ` + (className ? className : '');

  return (
    <Field
      name={name}
      component={({ form }: FieldProps) => {
        return (
          <div>
            <label className="block text-gray-700 text-[16px] mb-2">{label} </label>
            <DatePicker
              onChange={(date) => {
                if (onChange) {
                  onChange(date);
                  return;
                }
                const formattedDate = format(new Date(date as Date), 'yyyy-MM-dd');
                form.setFieldTouched(name as string, true);
                form.setFieldValue(name as string, formattedDate);
              }}
              value={format(localDate, 'MM-dd-yyyy')}
              className={defaultClasses}
              closeCalendar={true}
              clearAriaLabel="clear"
              calendarAriaLabel="calendar"
            />
            <p className="ml-[8px] text-[14px] ">{'*Use the calendar button'} </p>
            {hasError && (
              <ErrorMessage name={name as string} component="div" className="text-red-500 text-sm mt-1" />
            )}
          </div>
        );
      }}
    />
  );
};

export const CalenderComp = ({
  form,
  fieldName,
  className,
  onChange,
}: {
  form?: FormikProps<any>;
  fieldName: string;
  className?: string;
  onChange?: (date: Value) => void;
}) => {
  useEffect(() => {
    const element = document.querySelector('.react-date-picker__wrapper');
    if (element) {
      element.setAttribute('style', 'border: 0');
    }
  }, []);
  const handleChange = (date: Value) => {
    if (onChange) {
      onChange(date);
      return;
    }
    form?.setFieldTouched(fieldName, true);
    form?.setFieldValue(fieldName, date);
  };

  const formFieldDate = form?.values[fieldName];
  const localDate = formFieldDate ? new Date(formFieldDate) : new Date();
  const defaultClasses = `border border-gray rounded px-3 h-[40px] w-full ` + (className ? className : '');

  return (
    <div>
      <DatePicker
        onChange={handleChange}
        value={format(localDate, 'MM-dd-yyyy')}
        className={defaultClasses}
        closeCalendar={true}
        clearAriaLabel="clear"
        calendarAriaLabel="calendar"
      />
    </div>
  );
};
