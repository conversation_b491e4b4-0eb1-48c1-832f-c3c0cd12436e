import { AdmissionRequestStatus } from '../enums';
import CopyLinkButton from './CopyLinkButton';
import RiskAnalysisActions from './RiskAnalysisActions';
import DocumentsAnalysisActions from './DocumentsAnalysisActions';
import useAllRequiredDocumentsApproved from '@/app/hooks/useAllRequiredDocumentsApproved';
import { useCountry } from './detail';
import SocialAnalysisActions from './SocialAnalysisActions';
import { ModelScores } from '../types';

export default function MainActions({
  status,
  requestId,
  documentsAnalysis,
  modelScores,
}: {
  status: AdmissionRequestStatus;
  requestId: string;
  documentsAnalysis: any;
  modelScores: ModelScores;
}) {
  const { isCountryUSA } = useCountry();
  const displayRiskAnalysisActions = status === AdmissionRequestStatus.risk_analysis;
  const displaySocialAnalysisActions = status === AdmissionRequestStatus.social_analysis;

  const documentsAnalysisActions = useAllRequiredDocumentsApproved(isCountryUSA, status, documentsAnalysis);

  return (
    <>
      {<CopyLinkButton requestId={requestId} />}
      {displayRiskAnalysisActions ? <RiskAnalysisActions requestId={requestId} /> : null}
      {displaySocialAnalysisActions ? (
        <SocialAnalysisActions requestId={requestId} modelScores={modelScores} />
      ) : null}
      {documentsAnalysisActions ? <DocumentsAnalysisActions requestId={requestId} /> : null}
    </>
  );
}
