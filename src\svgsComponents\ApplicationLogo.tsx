export const ApplicationLogo = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_64_1490)">
        <path
          d="M13.3333 0H6.66667C5.562 0.00132321 4.50296 0.440735 3.72185 1.22185C2.94073 2.00296 2.50132 3.062 2.5 4.16667V15.8333C2.50132 16.938 2.94073 17.997 3.72185 18.7782C4.50296 19.5593 5.562 19.9987 6.66667 20H13.3333C14.438 19.9987 15.497 19.5593 16.2782 18.7782C17.0593 17.997 17.4987 16.938 17.5 15.8333V4.16667C17.4987 3.062 17.0593 2.00296 16.2782 1.22185C15.497 0.440735 14.438 0.00132321 13.3333 0V0ZM15.8333 15.8333C15.8333 16.4964 15.5699 17.1323 15.1011 17.6011C14.6323 18.0699 13.9964 18.3333 13.3333 18.3333H10.8333V17.5C10.8333 17.279 10.7455 17.067 10.5893 16.9107C10.433 16.7545 10.221 16.6667 10 16.6667C9.77899 16.6667 9.56702 16.7545 9.41074 16.9107C9.25446 17.067 9.16667 17.279 9.16667 17.5V18.3333H6.66667C6.00363 18.3333 5.36774 18.0699 4.8989 17.6011C4.43006 17.1323 4.16667 16.4964 4.16667 15.8333V4.16667C4.16667 3.50363 4.43006 2.86774 4.8989 2.3989C5.36774 1.93006 6.00363 1.66667 6.66667 1.66667H13.3333C13.9964 1.66667 14.6323 1.93006 15.1011 2.3989C15.5699 2.86774 15.8333 3.50363 15.8333 4.16667V15.8333ZM13.3333 13.3333C13.3333 13.5543 13.2455 13.7663 13.0893 13.9226C12.933 14.0789 12.721 14.1667 12.5 14.1667H7.5C7.27899 14.1667 7.06702 14.0789 6.91074 13.9226C6.75446 13.7663 6.66667 13.5543 6.66667 13.3333C6.66667 13.1123 6.75446 12.9004 6.91074 12.7441C7.06702 12.5878 7.27899 12.5 7.5 12.5H12.5C12.721 12.5 12.933 12.5878 13.0893 12.7441C13.2455 12.9004 13.3333 13.1123 13.3333 13.3333ZM8.33333 10.8333C8.55435 10.8333 8.76631 10.7455 8.92259 10.5893C9.07887 10.433 9.16667 10.221 9.16667 10V9.16667H10.8333V10C10.8333 10.221 10.9211 10.433 11.0774 10.5893C11.2337 10.7455 11.4457 10.8333 11.6667 10.8333C11.8877 10.8333 12.0996 10.7455 12.2559 10.5893C12.4122 10.433 12.5 10.221 12.5 10V6.66667C12.5 6.00363 12.2366 5.36774 11.7678 4.8989C11.2989 4.43006 10.663 4.16667 10 4.16667C9.33696 4.16667 8.70107 4.43006 8.23223 4.8989C7.76339 5.36774 7.5 6.00363 7.5 6.66667V10C7.5 10.221 7.5878 10.433 7.74408 10.5893C7.90036 10.7455 8.11232 10.8333 8.33333 10.8333ZM10 5.83333C10.221 5.83333 10.433 5.92113 10.5893 6.07741C10.7455 6.23369 10.8333 6.44565 10.8333 6.66667V7.5H9.16667V6.66667C9.16667 6.44565 9.25446 6.23369 9.41074 6.07741C9.56702 5.92113 9.77899 5.83333 10 5.83333Z"
          fill="#374957"
        />
      </g>
      <defs>
        <clipPath id="clip0_64_1490">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
