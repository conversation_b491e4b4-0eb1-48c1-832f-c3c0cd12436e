'use client';
import { Table, Thead, Tbody, Tr, Th, Td, TableContainer, Text, useToast, Badge } from '@chakra-ui/react';
import { formatDateMX, formatDateUS } from '@/utils/dates';

import { Pagination } from '@/components/Pagination';
import { useRouter } from 'next/navigation';

import {
  AssignedStatusMX,
  AssignedStatusUS,
  NotAssignedStatusMX,
  NotAssignedStatusUS,
  RequestsTableColumnsMX,
  RequestsTableColumnsUS,
} from './translations';
import { AdmissionRequestStatus } from '../solicitudes/enums';
import { RequestStatusTranslations, RequestStatusTranslationsUS } from '../solicitudes/data';
import { approvalOptionsMap } from '@/constants';
import { cn } from '@/lib/utils';

interface Request {
  id: number;
  personalData: {
    firstName: string | null;
    lastName: string | null;
    country: string;
  };
  status: string;
  createdAt: string;
}

interface PaginationProps {
  page: number;
  hasMore: boolean;
  totalPages: number;
}

const formatFullName = (firstName?: string, lastName?: string) => {
  return `${firstName || ''} ${lastName || ''}`;
};

export default function RequestsTable({
  requests,
  pagination,
  isCountryUSA,
}: {
  requests: Request[];
  pagination: PaginationProps;
  isCountryUSA: boolean;
}) {
  const router = useRouter();

  const navivateToRequest = (id: string) => {
    router.push(`solicitudes/${id}`);
  };

  function formatStatus(status: AdmissionRequestStatus) {
    const colorScheme = {
      [AdmissionRequestStatus.approved]: 'green',
      [AdmissionRequestStatus.rejected]: 'red',
      [AdmissionRequestStatus.created]: 'blue',
      [AdmissionRequestStatus.documents_analysis]: 'blue',
      [AdmissionRequestStatus.earnings_analysis]: 'blue',
      [AdmissionRequestStatus.home_visit]: 'blue',
      [AdmissionRequestStatus.judicial_analysis]: 'blue',
      [AdmissionRequestStatus.risk_analysis]: 'blue',
      [AdmissionRequestStatus.social_analysis]: 'blue',
    }[status];

    return (
      <Text color={`${colorScheme}.500`}>
        {isCountryUSA
          ? RequestStatusTranslationsUS[status] || status
          : RequestStatusTranslations[status] || status}
      </Text>
    );
  }

  const TableHeads = isCountryUSA ? RequestsTableColumnsUS : RequestsTableColumnsMX;
  const AssignedStatus = isCountryUSA ? AssignedStatusUS : AssignedStatusMX;
  const NotAssignedStatus = isCountryUSA ? NotAssignedStatusUS : NotAssignedStatusMX;

  const toast = useToast();
  return (
    <>
      <TableContainer>
        <Table>
          <Thead>
            <Tr>
              {TableHeads.map((head: string) => {
                return <Th key={head}>{head}</Th>;
              })}
            </Tr>
          </Thead>
          <Tbody>
            {requests.map((request: any) => (
              <Tr
                key={request.id}
                onClick={(event) => {
                  const target = event.target as HTMLElement;
                  if (target.id !== 'id') {
                    navivateToRequest(request.id);
                  }
                }}
                sx={{
                  cursor: 'pointer',
                  '&&:hover': {
                    bg: 'gray.100',
                  },
                }}
              >
                <Td
                  id="id"
                  onClick={() => {
                    // copy to clipboard
                    navigator.clipboard.writeText(request.id.trim().toString());
                    toast({
                      title: 'ID copiado',
                      description: 'El ID de la solicitud ha sido copiado al portapapeles',
                      status: 'success',
                      position: 'top',
                      duration: 2000,
                      isClosable: true,
                    });
                  }}
                >
                  {request.id}
                </Td>
                <Td>{formatFullName(request.personalData.firstName, request.personalData.lastName)}</Td>
                <Td>
                  {request?.convertedToAssociate ? (
                    <Badge colorScheme="green" variant="subtle" px={3} py={1} borderRadius="full">
                      {AssignedStatus}
                    </Badge>
                  ) : (
                    <Badge colorScheme="red" variant="subtle" px={3} py={1} borderRadius="full">
                      {NotAssignedStatus}
                    </Badge>
                  )}
                </Td>

                <Td>{formatStatus(request.status)}</Td>
                <Td className={cn(approvalOptionsMap[request.typeOfPreapproval as string]?.textColor)}>
                  {isCountryUSA
                    ? request.typeOfPreapproval
                    : approvalOptionsMap[request.typeOfPreapproval as string]?.shortLabel}
                </Td>
                <Td>{isCountryUSA ? formatDateUS(request.createdAt) : formatDateMX(request.createdAt)}</Td>
                <Td>{request.personalData.country.toUpperCase()}</Td>
                <Td>{request.personalData.state?.toUpperCase()}</Td>
                <Td>{request.personalData.city?.toUpperCase()}</Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </TableContainer>
      <div className="mt-4">
        <Pagination
          currentPage={pagination.page}
          hasMorePages={pagination.hasMore}
          pagesCount={pagination.totalPages}
        />
      </div>
    </>
  );
}
