import { Form } from '@/components/ui/form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { HookFormRadixUIField } from '../HookFormField';
import { FormSectionHeader } from '../FormHeaderSection';
import { FormSection } from '../FormSection';
import { IStepperButtonsProps } from '../StepperButtons';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import { updateAdmissionRequestHomeVisit } from '@/actions/postAdmissionRequest';
import { Steps, toastConfigs } from '.';
import { HomeVisitStepsStatus } from '@/constants';
import { useStepperNavigation } from './useStepperNavigation';
import { translations } from '../translations';

const AddressFormSchema = z.object({
  exterior: z.string({
    required_error: translations.es.ExteriorNoRequired,
  }),
  interior: z.string({
    required_error: translations.es.InteriorNoRequired,
  }),
  street: z
    .string({
      required_error: translations.es.StreetRequired,
    })
    .min(3, {
      message: translations.es.StreetMinLengthErrorMsg,
    }),
  postalCode: z
    .string({
      required_error: translations.es.PostalCodeRequired,
    })
    .min(5, {
      message: translations.es.PostalCodeLengthErrorMsg,
    })
    .max(5, {
      message: translations.es.PostalCodeLengthErrorMsg,
    }),
  timeInResidency: z.string({
    required_error: translations.es.TimeInResidenceRequired,
  }),
  colony: z.string({
    required_error: translations.es.ColonyRequired,
  }),
  municipality: z.string({
    required_error: translations.es.MunicipalityRequired,
  }),
  state: z.string({
    required_error: translations.es.StateRequired,
  }),
});

interface IAddressInformation extends IStepperButtonsProps {
  admissionRequest: Record<string, any>;
}

export default function AddressInformation(props: IAddressInformation) {
  const { goToNext, goToPrevious, admissionRequest, activeStep } = props;
  const { id: requestId, personalData, homeVisit } = admissionRequest;
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();
  const toast = useToast();
  const { naviteToClientDetailsPage } = useStepperNavigation();

  const form = useForm<z.infer<typeof AddressFormSchema>>({
    resolver: zodResolver(AddressFormSchema),
    defaultValues: {
      exterior: personalData.streetNumber || '',
      interior: personalData.department || '',
      street: personalData.street || '',
      postalCode: personalData.postalCode || 0,
      timeInResidency: personalData.timeInResidency || '',
      colony: personalData.neighborhood || '',
      municipality: personalData.municipality || '',
      state: personalData.state || '',
    },
  });

  async function onSubmit(data: z.infer<typeof AddressFormSchema>, navigate: () => void) {
    try {
      setIsSubmitting(true);
      const isDataCompleted = Object.values(data).every((value) => value !== '');
      const payload = {
        personalData: {
          streetNumber: data.exterior,
          department: data.interior,
          street: data.street,
          postalCode: data.postalCode,
          timeInResidency: data.timeInResidency,
          neighborhood: data.colony,
          municipality: data.municipality,
          state: data.state,
        },
        homeVisitData: {
          homeVisitStepsStatus: {
            ...homeVisit?.homeVisitStepsStatus,
            address: isDataCompleted ? HomeVisitStepsStatus.complete : HomeVisitStepsStatus.incomplete,
          },
        },
        isPersonalData: true,
        isHomeVisitData: true,
      };
      const response = await updateAdmissionRequestHomeVisit({
        requestId: requestId,
        requestPayload: payload,
      });
      if (response && response.success) {
        toast(toastConfigs(Steps.Address, 'success'));
        router.refresh();
        navigate();
      }
    } catch (error) {
      toast(toastConfigs(Steps.Address, 'error'));
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <FormSection
      goToNext={form.handleSubmit((data) => onSubmit(data, goToNext))}
      goToPrevious={goToPrevious}
      isLoading={isSubmitting}
      activeStep={activeStep}
      finishWithoutCompleting={form.handleSubmit((data) => onSubmit(data, naviteToClientDetailsPage))}
    >
      <FormSectionHeader title={translations.es.Address} />
      <Form {...form}>
        <form>
          <div className="flex py-4 gap-4">
            <HookFormRadixUIField form={form} fieldName="exterior" formLabel={translations.es.ExteriorNo} />
            <HookFormRadixUIField form={form} fieldName="interior" formLabel={translations.es.InteriorNo} />
          </div>

          <div className="flex py-2 gap-4">
            <HookFormRadixUIField form={form} fieldName="street" formLabel={translations.es.Street} />
            <HookFormRadixUIField form={form} fieldName="postalCode" formLabel={translations.es.PostalCode} />
          </div>

          <div className="flex py-2 gap-4">
            <HookFormRadixUIField
              form={form}
              fieldName="timeInResidency"
              formLabel={translations.es.TimeInResidence}
            />
            <HookFormRadixUIField form={form} fieldName="colony" formLabel={translations.es.Colony} />
          </div>

          <div className="flex py-2 gap-4">
            <HookFormRadixUIField
              form={form}
              fieldName="municipality"
              formLabel={translations.es.Municipality}
            />
            <HookFormRadixUIField form={form} fieldName={'state'} formLabel={translations.es.State} />
          </div>
        </form>
      </Form>
    </FormSection>
  );
}
