import { pdf } from '@react-pdf/renderer';
import RecessionTerminationDocumentPDF from '../RecessionTermination-DocumentPDF';

type RecessionTerminationDocumentPDFProps = React.ComponentProps<typeof RecessionTerminationDocumentPDF>;

/**
 * Function to get the Agreement termination file
 * @param data - The props of the component RecessionTerminationDocumentPDF
 * @returns The file instance
 */

export const getRecessionTerminationFile = async (data: RecessionTerminationDocumentPDFProps) => {
  const blob = await pdf(<RecessionTerminationDocumentPDF {...data} />).toBlob();

  const buffer = await blob.arrayBuffer();
  const upperCaseFirstName = data.firstName.split('')[0].toUpperCase() + data.firstName.slice(1);
  const upperCaseLastName = data.lastName.split('')[0].toUpperCase() + data.lastName.slice(1);

  // create random id with 6 characters including numbers and letters with upperCase and lowerCase
  const randomId = Math.random().toString(36).slice(2, 8);

  const fileInstance = new File(
    [buffer],
    `Recesión-${upperCaseFirstName}${upperCaseLastName}-${randomId}.pdf`,
    {
      type: 'application/pdf',
    }
  );

  return fileInstance;
};
