'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import CustomInput from '@/components/Inputs/CustomInput';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import InputDate from '@/components/Inputs/InputDate';
import { useOpenChangeStatusModal } from '@/zustand/modalStates';
import {
  sendAwaitingInsurance,
  sendLegalProcess,
  sendServiceSchema,
} from '@/validatorSchemas/changeStatusSchema';
import { useState } from 'react';
import SelectInput from '@/components/Inputs/SelectInput';
import * as Yup from 'yup';
import { FormikHelpers, FormikValues } from 'formik';
import RadioOptions from '@/components/Inputs/RadioOptions';
import {
  categoriesOptions,
  findSubCategory,
  categoryToStatusMapping,
  getTranslationMap,
} from '@/constants/status';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';

function ServiceBody() {
  return (
    <>
      <InputDate name="dateIn" label="Fecha de ingreso" />
      <InputDate name="dateOut" label="Fecha tentativa de salida" />
      <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
    </>
  );
}

function AwaitingInsurance() {
  return (
    <>
      <InputDate name="date" label="Fecha de siniestro" />
      <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
    </>
  );
}

function LegalProcess() {
  const [selected, setSelected] = useState('Si');
  return (
    <>
      <div className="flex flex-col gap-2">
        <p>¿Pausar pagos?</p>
        <RadioOptions
          name="stopPayments"
          selectedAddress={selected}
          options={[
            { value: 'Si', label: 'Si', name: 'stopPayments' },
            { value: 'No', label: 'No', name: 'stopPayments' },
          ]}
          onChange={(value) => {
            setSelected(value);
          }}
        />
      </div>
      <InputDate name="date" label="Fecha de siniestro" />
    </>
  );
}

type OptionLabels =
  | 'Taller'
  | 'Espera de seguro'
  | 'Proceso legal'
  | 'Vendido'
  | 'Utilitario'
  | 'Entregado'
  | 'Adendum';

const bodies: {
  [key: string]: { body: JSX.Element | null; validator: Yup.ObjectSchema<any>; option: OptionLabels };
} = {
  workshop: {
    body: <ServiceBody />,
    validator: sendServiceSchema,
    option: 'Taller',
  },
  legal: {
    body: <LegalProcess />,
    validator: sendLegalProcess,
    option: 'Proceso legal',
  },
  insurance: {
    body: <AwaitingInsurance />,
    validator: sendAwaitingInsurance,
    option: 'Espera de seguro',
  },
  sold: {
    body: null,
    validator: Yup.object({}),
    option: 'Vendido',
  },
  utilitary: {
    body: null,
    validator: Yup.object({}),
    option: 'Utilitario',
  },
  delivered: {
    body: null,
    validator: Yup.object({}),
    option: 'Entregado',
  },
  adendum: {
    body: null,
    validator: Yup.object({}),
    option: 'Adendum',
  },
};

export const statusServiceOptions = Object.entries(bodies).map(([key, body]) => ({
  value: key,
  label: body.option,
}));

const initialValues = {
  dateIn: '',
  dateOut: '',
  date: '',
  comments: '',
  category: {
    value: '',
    label: 'Selecciona',
  },

  subCategory: {
    value: '',
    label: 'Selecciona',
  },
  stopPayments: 'Si',
};

const valuesDependOnStatus: { [key: string]: FormikValues } = {
  'in-service': {
    dateIn: '',
    dateOut: '',
    comments: '',
  },
  'awaiting-insurance': {
    date: '',
    comments: '',
  },
  'legal-process': {
    date: '',
    stopPayments: 'Si',
  },
};

export default function ChangeStatus() {
  const changeStatus = useOpenChangeStatusModal();
  const updateSideData = useUpdateSideData();
  const { id } = useParams();
  const router = useRouter();
  const { user } = useCurrentUser();
  const toast = useToast();
  const search = useSearchParams();
  const country = search ? search.get('country') : '';

  const onSubmit = async (values: typeof initialValues) => {
    const category = values.category.value;
    const status = categoryToStatusMapping[category as keyof typeof categoryToStatusMapping];
    const data: any = {
      status,
      category,
      subCategory: values.subCategory.value,
    };

    // Only extract additional properties if they exist for this status
    if (valuesDependOnStatus[status]) {
      const propsToExtract = Object.keys(valuesDependOnStatus[status]);
      propsToExtract.forEach((prop: any) => {
        const valuesTransformed = values as any;
        data[prop] = valuesTransformed[prop];
        if (prop === 'stopPayments') {
          data[prop] = valuesTransformed[prop] === 'Si'; // send boolean to backend
        }
      });
    }

    console.log('data', data);
    try {
      console.log('url:', `${URL_API}/stock/changeStatus-processes/${id}`);
      await axios.patch(`${URL_API}/stock/changeStatus-processes/${id}`, data, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      toast({
        title: 'Estatus cambiado',
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      await updateSideData(user);
      router.refresh();
      if (window.location.pathname.includes('active')) {
        router.push(
          `/dashboard/flotilla/inactive/${category}/${id}${country ? `?country=${encodeURI(country)}` : ''}`
        );
      }
    } catch (error: any) {
      toast({
        title: error.response.data.message,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    } finally {
      changeStatus.onClose();
    }
  };

  const confirmSubmit = async (
    values: typeof initialValues,
    formikHelpers: FormikHelpers<typeof initialValues>
  ) => {
    const category = values?.category?.value as string;
    if (!category) return formikHelpers.setFieldError('category', 'Selecciona un estatus');
    return Swal.fire({
      title: '¿Estás seguro?',
      text: `Se enviará a ${bodies[category]?.option}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Si, enviar',
      cancelButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  const [categorySelected, setCategorySelected] = useState({ label: '', value: '' });
  const [subCategoryOptions, setSubCategoryOptions] = useState<{ label: string; value: string }[]>([]);
  return (
    <ModalContainer title="Cambiar estatus" onClose={changeStatus.onClose} classAnimation="animate__fadeIn">
      <FormikContainer
        onSubmit={confirmSubmit}
        onClose={changeStatus.onClose}
        initialValues={initialValues}
        confirmBtnText="Cambiar estatus"
        footerClassName="flex gap-3 pt-[20px] justify-center"
        validatorSchema={categorySelected && bodies[categorySelected.value]?.validator}
      >
        <div className="flex flex-col gap-[20px]">
          {/* <SelectInput
            label="Selecciona el estatus"
            name="status"
            options={statusServiceOptions}
            onChange={(option) => {
              setSelectedOption(option);
            }}
          /> */}

          <SelectInput
            label="Selecciona el estatus"
            name="category"
            options={categoriesOptions.map((option) => ({
              label: getTranslationMap(country)?.categories[option.value] || option.label,
              value: option.value,
            }))}
            onChange={(option, form) => {
              setCategorySelected(option);
              setSubCategoryOptions([]);
              form.setFieldValue('subCategory', { value: '', label: 'Selecciona' });
              const subCategories = findSubCategory(option.value as any, country);
              if (subCategories && subCategories.length > 0) {
                console.log('subCategories', subCategories);
                setSubCategoryOptions(subCategories);
              }
            }}
          />

          {subCategoryOptions.length > 0 && (
            <SelectInput
              label="Motivo del cambio de estatus"
              name="subCategory"
              options={subCategoryOptions}
            />
          )}

          {categorySelected.value && bodies[categorySelected.value].body}
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
