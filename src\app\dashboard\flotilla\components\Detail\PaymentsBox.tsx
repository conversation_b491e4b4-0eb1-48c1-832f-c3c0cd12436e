'use client';
import { ColumnDef } from '@tanstack/react-table';
import TableInDetail from '../TableInDetail';
import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  PAGOS_PAYMENT_URL,
  PAYMENTS_API_URL,
  PAYMENT_API_SECRET,
  canPerformPaymentActions,
} from '@/constants';
import { Check, X } from 'lucide-react';
import CellAction from '../CellAction';
import { useToast } from '@chakra-ui/react';
import PrimaryButton from '@/components/PrimaryButton';
import Swal from 'sweetalert2';
import Spinner from '@/components/Loading/Spinner';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import { Payment } from '@/app/dashboard/pagos/types';
import { usePaymentTransactionState } from '@/hooks/usePaymentTransaction';
import PaymentTransactionModal from '@/app/dashboard/pagos/payments/_components/PaymentTransactionModal';

interface PaymentProps {
  carNumber?: string;
  associateId?: string;
  clientId?: string;
}

export default function PaymentsBox({ carNumber, associateId, clientId }: PaymentProps) {
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreatingPayment, setIsCreatingPayment] = useState(false);
  const { user } = useCurrentUser();
  const { isOpen, onOpen, setPaymentId, setPaymentAmount, updatePayments } = usePaymentTransactionState();
  const toast = useToast();

  const confirmMarkAsPaid = async (paymentId: string, total: number) => {
    console.log('confirmMarkAsPaid');
    setPaymentId(paymentId);
    setPaymentAmount(total);
    onOpen();
  };

  const columns: ColumnDef<Payment>[] = [
    {
      header: 'Estatus',
      accessorKey: 'status',
    },
    {
      header: 'Total ($)',
      accessorKey: 'total',
    },
    {
      header: 'Fecha de creación',
      accessorKey: 'createdAt',
    },
    {
      header: 'Está pagado',
      accessorKey: 'isPaid',
      cell: ({ row }) => {
        const isPaid = row.getValue('isPaid');
        if (isPaid) {
          return <Check />;
        } else {
          return <X />;
        }
      },
    },
    {
      accessorKey: 'paidAt',
      header: 'Paid At',
      cell: ({ row }) => {
        const paidAtDateStr = row.getValue('paidAt');
        let formatted = '';
        if (paidAtDateStr) {
          const date = new Date(row.getValue('paidAt'));
          formatted = date.toLocaleDateString();
        }
        return <div className="font-medium">{formatted}</div>;
      },
    },
    {
      header: 'Acciones',
      id: 'actions',
      cell: ({ row }) => {
        const actions = [
          {
            label: () => 'Copiar link de pago',
            onClick: (data: any) => {
              navigator.clipboard.writeText(`${PAGOS_PAYMENT_URL}` + data?.id);
              toast({
                title: 'Copiado',
                status: 'success',
                duration: 3000,
                position: 'top',
              });
            },
          },
        ];
        const shouldDisplayMarkAsPaidOnly = canPerformPaymentActions(user.email);
        if (!row.original.isPaid && shouldDisplayMarkAsPaidOnly) {
          actions.push({
            label: () => 'Marcar como pagado',
            onClick: async (data: any) => {
              await confirmMarkAsPaid(data.id, data.total);
            },
          });
        }
        return <CellAction data={row.original} actions={actions} />;
      },
    },
  ];

  useEffect(() => {
    const getPayments = async () => {
      try {
        const response = await axios.get(`${PAYMENTS_API_URL}/payments/associateId/${associateId}`, {
          headers: {
            Authorization: `Bearer ${PAYMENT_API_SECRET}`,
          },
        });
        setPayments(response.data?.data);
      } catch (error) {
        console.log('error', error);
      } finally {
        setIsLoading(false);
      }
    };
    getPayments();
  }, [carNumber, associateId, updatePayments]);

  const confirmBlock = () => {
    return Swal.fire({
      text: `Crear pago de reactivación`,
      html: `
      <p class="mb-6">El pago de reactivación requiere una doble confirmación</p>
      <label for="admin-password" style="display:block; margin-bottom:10px;">
        Ingresa la cantidad a multiplicar por 100, ejemplo si requieren ser 500 pesos, ingresar 5
      </label>
      <input id="amount-input" type="number" class="swal2-input" placeholder="Ingresa la cantidad" />
    `,
      icon: 'info',
      inputAttributes: {
        autocapitalize: 'off',
        autocorrect: 'off',
      },
      customClass: {
        icon: ' !text-primaryPurple !border-primaryPurple  ',
        confirmButton: '!bg-primaryPurple',
      },
      reverseButtons: true,
      confirmButtonText: 'Confirmar',
      cancelButtonText: 'Cancelar',
      showCancelButton: true,
      showCloseButton: true,
      showLoaderOnConfirm: true,
      preConfirm: async () => {
        const inputValue = (document.getElementById('amount-input') as HTMLInputElement).value; // Obtener el valor del input
        if (!inputValue) {
          Swal.showValidationMessage('Por favor, ingresa una cantidad válida.');
          return false; // Impide el cierre del diálogo si no hay valor
        } else {
          return inputValue; // Devuelve el valor ingresado para usarlo en el then
        }
      },
      allowOutsideClick: () => !Swal.isLoading(),
    }).then((result) => {
      if (result.isConfirmed) {
        const inputValue = result.value;

        const total = inputValue * 100;
        console.log('total', total);
        Swal.fire({
          title: `Crear pago de reactivación`,
          text: `¿Estás seguro de crear el pago de reactivación por $${total}?`,
          confirmButtonText: 'Confirmar',
          cancelButtonText: 'Cancelar',
          customClass: {
            confirmButton: '!bg-primaryPurple',
          },
          reverseButtons: true,
          showCancelButton: true,
          showCloseButton: true,
        }).then(async (res) => {
          if (res.isConfirmed) {
            console.log('some action here', total, clientId);

            try {
              setIsCreatingPayment(true);
              await axios.post(
                `${PAYMENTS_API_URL}/payments/unlock-payment`,
                {
                  clientId,
                  quantity: inputValue,
                },
                {
                  headers: {
                    Authorization: `Bearer ${PAYMENT_API_SECRET}`,
                  },
                }
              );

              toast({
                title: 'Pago de reactivación creado',
                status: 'success',
                duration: 3000,
                position: 'top',
              });

              setTimeout(() => {
                window.location.reload();
              }, 3000);
            } catch (error) {
              toast({
                title: 'Error al crear el pago de reactivación',
                status: 'error',
                duration: 3000,
                position: 'top',
              });
            } finally {
              setIsCreatingPayment(false);
            }
          }
        });

        // Swal.fire({
        //   // title: `Bloquear auto`,
        //   // text: `¿Estás seguro de $} el auto?`,
        //   title: `Confirmar pago de reactivación`,
        //   text: "¿Estás seguro de crear el pago de reactivación?",
        //   confirmButtonText: 'Confirmar',
        //   cancelButtonText: 'Cancelar',
        //   customClass: {
        //     confirmButton: '!bg-primaryPurple',
        //   },
        //   reverseButtons: true,
        //   showCancelButton: true,
        //   showCloseButton: true,
        // }).then(async (res) => {
        //   if (res.isConfirmed) {
        //     console.log('isConfirmed', res);
        //   }

        // });
      }
    });
  };

  return (
    <>
      {isCreatingPayment && <Spinner />}
      <div
        className="
      w-full
      bg-white
      border-[#EAECEE]
      font-bold
      rounded
      min-h-[300px]
      py-[25px]
      pl-[20px]
        pr-[15px]
        border-[1px]
        flex
        flex-col
        overflow-y-auto
      "
      >
        <div className="flex justify-between">
          <p className="text-[24px] pt-[5px]">Pagos</p>

          {clientId && <PrimaryButton onClick={confirmBlock}>Crear pago de reactivación</PrimaryButton>}
        </div>
        {isOpen && <PaymentTransactionModal />}
        {isLoading ? <p>Cargando...</p> : <TableInDetail columns={columns} data={payments} />}
      </div>
    </>
  );
}
