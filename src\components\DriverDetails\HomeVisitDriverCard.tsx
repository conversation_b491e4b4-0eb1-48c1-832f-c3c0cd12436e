import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>,
  <PERSON><PERSON>ontainer,
  Tbody,
  Text,
  Thead,
  Th,
  Tr,
  Table,
  Td,
  Box,
  Button,
} from '@chakra-ui/react';
import { formatDateTime } from '@/utils/dates';
import { associateTranslationsMX, associateTranslationsUS } from '@/constants/translations';
import { firstIncompleteStepIndex } from '@/app/dashboard/clientes/solicitudes/[id]/home-visit/_components/steps';
import { ApiPath } from '@/constants/api.endpoints';

enum HomeVisitStatus {
  pending = 'pending',
  approved = 'approved',
  rejected = 'rejected',
}

interface HomeVisit {
  residentOwnershipStatus: string;
  hasGarage: boolean;
  comments: string;
  images: string[];
  responsible: string;
  visitDate: string;
  status: HomeVisitStatus;
  homeVisitStepsStatus: Record<string, any>;
}

const statusColorScheme = {
  [HomeVisitStatus.approved]: { text: '#067F20', bg: '#E8FAEB' },
  [HomeVisitStatus.rejected]: { text: '#D32F2F', bg: '#FDECEA' },
  [HomeVisitStatus.pending]: { text: '#946200', bg: '#FFF5CC' },
};

const statusLabelMap = {
  [HomeVisitStatus.approved]: 'Aprobada',
  [HomeVisitStatus.rejected]: 'Rechazada',
  [HomeVisitStatus.pending]: 'Pendiente',
};

export default function HomeVisitDriverCard({
  homeVisit,
  countryCondition = false,
  requestId,
  setHomevisitFlag,
}: {
  homeVisit: HomeVisit | null;
  countryCondition: boolean;
  requestId: string;
  setHomevisitFlag: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const translations = countryCondition ? associateTranslationsUS : associateTranslationsMX;

  const handleClickDetails = (event: React.MouseEvent) => {
    setHomevisitFlag(true);
    event.stopPropagation();
    const stepIndex = firstIncompleteStepIndex(homeVisit?.homeVisitStepsStatus!);
    let url = `${window.location.origin}/dashboard/clientes/solicitudes/${requestId}/${ApiPath.HOME_VISIT}`;

    if (stepIndex !== null && stepIndex >= 0) {
      url += `?stepIndex=${stepIndex}`;
    }

    window.open(url, '_blank');
  };

  return (
    <Card variant="outline">
      <CardHeader pb={0}>
        <Flex justifyContent="space-between" alignItems="center">
          <Heading size="md" color="gray.600">
            {translations.homeVisit}
          </Heading>
          {homeVisit && (
            <Text fontSize="sm" color={statusColorScheme[homeVisit.status]?.text || '#333'}>
              {statusLabelMap[homeVisit.status]}
            </Text>
          )}
        </Flex>
      </CardHeader>
      <CardBody>
        <TableContainer fontSize="sm" border="1px solid #e2e8f0" borderRadius="md" overflow="hidden">
          <Table>
            <Thead bg="#FAFAFA">
              <Tr>
                <Th width="30%" textAlign="start" pl={4}>
                  {translations.date}
                </Th>
                <Th width="40%" textAlign="start">
                  {translations.homeVisitor}
                </Th>
                <Th width="30%" textAlign="start">
                  {translations.status}
                </Th>
                <Th textAlign="right">Acciones</Th>
              </Tr>
            </Thead>
            <Tbody>
              {homeVisit ? (
                <Tr _hover={{ bg: 'gray.50' }}>
                  <Td pl={4} textAlign="start">
                    {homeVisit.visitDate ? formatDateTime(homeVisit.visitDate) : 'N/A'}
                  </Td>
                  <Td textAlign="start">{homeVisit.responsible || 'N/A'}</Td>
                  <Td textAlign="start">
                    <Box
                      display="inline-block"
                      px={2}
                      py={1}
                      borderWidth="1px"
                      borderRadius="md"
                      fontSize="xs"
                      fontWeight={600}
                      bg={statusColorScheme[homeVisit.status]?.bg || '#EEE'}
                      color={statusColorScheme[homeVisit.status]?.text || '#333'}
                      borderColor={statusColorScheme[homeVisit.status]?.bg || '#EEE'}
                    >
                      {statusLabelMap[homeVisit.status] || 'N/A'}
                    </Box>
                  </Td>
                  <Td textAlign="right">
                    <Button
                      onClick={handleClickDetails}
                      className="font-semibold  bg-white text-[#5800F7] px-2.5 py-1 text-xs rounded border-[#5800F7] border hover:bg-gray-100"
                    >
                      Ver detalle
                    </Button>
                  </Td>
                </Tr>
              ) : (
                <Tr>
                  <Td colSpan={3} textAlign="center">
                    {translations.noHomeVisit}
                  </Td>
                </Tr>
              )}
            </Tbody>
          </Table>
        </TableContainer>
      </CardBody>
    </Card>
  );
}
