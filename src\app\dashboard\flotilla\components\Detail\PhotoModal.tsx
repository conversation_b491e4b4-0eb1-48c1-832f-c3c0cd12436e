import React from 'react';
import {
  <PERSON>dal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Image,
  Box,
  Text,
} from '@chakra-ui/react';
import { AlertCircle } from 'lucide-react';

interface PhotoModalProps {
  isOpen: boolean;
  onClose: () => void;
  photoUrl: string | null;
  translatedText: any;
  primaryColor: string;
}

const PhotoModal: React.FC<PhotoModalProps> = ({
  isOpen,
  onClose,
  photoUrl,
  translatedText,
  primaryColor,
}) => (
  <Modal isOpen={isOpen} onClose={onClose} isCentered size="xl">
    <ModalOverlay />
    <ModalContent>
      <ModalHeader>{translatedText.vehiclePhoto}</ModalHeader>
      <ModalCloseButton />
      <ModalBody pb={6}>
        {photoUrl ? (
          <Image
            src={photoUrl}
            alt="Vehicle Photo"
            width="100%"
            borderRadius="md"
            fallback={
              <Box textAlign="center" p={4}>
                <AlertCircle size={48} color={primaryColor} />
                <Text mt={2}>{translatedText.loadingImage}</Text>
              </Box>
            }
          />
        ) : (
          <Box textAlign="center" p={4}>
            <AlertCircle size={48} color={primaryColor} />
            <Text mt={2}>{translatedText.noImageAvailable}</Text>
          </Box>
        )}
      </ModalBody>
    </ModalContent>
  </Modal>
);

export default PhotoModal;
