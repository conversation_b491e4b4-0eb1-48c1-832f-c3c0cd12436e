'use client';
import React from 'react';
import { Bar } from 'react-chartjs-2';
import 'chart.js/auto';

interface BarChartProps {
  data: any;
  responsive?: boolean;
  stepYSize?: number;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
  barThickness?: number;
}

export default function BarChart({
  data,
  responsive = true,
  backgroundColor,
  borderColor,
  borderRadius,
  borderWidth,
  barThickness,
  stepYSize = 100,
}: BarChartProps) {
  const chartData = {
    labels: data.labels,
    datasets: [
      {
        label: data.label,
        data: data.indicators,
        backgroundColor: backgroundColor || 'rgba(88, 0, 247, 0.2)',
        borderColor: borderColor || 'rgba(88, 0, 247, 1)',
        borderWidth: borderWidth || 1,
        borderRadius: borderRadius || 5,
        barThickness: barThickness || 90,
      },
    ],
  };

  const maxValue = Math.max(...data.indicators);

  const totalYLabels = Math.ceil((maxValue + stepYSize) / stepYSize);
  const stepSize =
    totalYLabels > 10 ? Math.ceil((maxValue + stepYSize) / 10 / stepYSize) * stepYSize : stepYSize;

  const options: any = {
    // plugins: {
    //   legend: {
    //     display: false,
    //   },
    // },
    response: true,
    maintainAspectRatio: !responsive, // false for responsive witdh chart
    scales: {
      y: {
        stacked: true,
        beginAtZero: true,
        suggestedMin: 0,
        suggestedMax: maxValue + 100,
        ticks: {
          stepSize: stepSize,
          // callback: (value: any) => `$${value / 1000}k`,
        },
      },
    },
  };

  return <Bar data={chartData} options={options} />;
}
/* INFORMACIÓN UTIL PARA CONFIGURACIONES */
// const options: any = {
//   scales: {
//     x: {
//       stacked: true,
//       grid: {
//         display: false, // Oculta las líneas verticales de referencia
//       },
//     },
//     y: {
//       stacked: true,
//       beginAtZero: true,
//       ticks: {
//         stepSize: 5000,
//         callback: (value: any) => `$${value / 1000}k`,
//       },
//     },
//   },
//   indexAxis: 'x', // Cambia el eje de índice para el ancho de la barra
//   barPercentage: 0.6, // Controla el ancho de la barra
//   categoryPercentage: 0.8, // Controla el espacio entre las barras
//   plugins: {
//     tooltip: {
//       callbacks: {
//         label: (context: any) => {
//           const datasetLabel = context.dataset.label || '';
//           const value = context.parsed.y;

//           // Calcula el monto total sumando los valores de ambos indicadores
//           const total =
//             chartData.datasets[0].data[context.dataIndex] + chartData.datasets[1].data[context.dataIndex];

//           return `${datasetLabel}: ${value}k\nTotal: ${total}k`;
//         },
//       },
//     },
//   },
// };
