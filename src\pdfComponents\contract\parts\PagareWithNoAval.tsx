/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable react/jsx-no-undef */
import { Text, View, StyleSheet, Image } from '@react-pdf/renderer';
// import { Text, View, StyleSheet, Image, Document, Page } from '@react-pdf/renderer';
import PagareGris from '../assets/images/logoGris.png';
import moment from 'moment';

// { nombre, estado, domicilio, zip, fecha, totalPrice }: any

interface PagereWithNoAvalProps {
  fullName: string;
  state: string;
  address: string;
  deliveryDate: string;
  totalPays: number;
  weeklyRent: number;
}

export default function PagareWithNoAval({
  fullName,
  state,
  address,
  deliveryDate,
  // totalPrice,
  totalPays,
  weeklyRent,
}: PagereWithNoAvalProps) {
  const totalPrice = weeklyRent * (totalPays ? totalPays : 156);
  fullName = fullName.toUpperCase();
  const priceParsed =
    '$' +
    totalPrice.toLocaleString('es-MX', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

  // const text = NumerosALetras(totalPrice).split(" ");
  const dateParsed = moment(deliveryDate).format('DD/MM/YYYY');

  return (
    <>
      {/* <Image style={styles.header} src={HeaderImg} fixed /> */}
      <View break>
        <Image src={PagareGris.src} style={styles.viewBackground} />
        <Text style={styles.anexoTitle}>
          PAGARÉ QUE SE SUSCRIBE EN TÉRMINOS DE LO DISPUESTO POR EL ARTÍCULO 170 Y DEMÁS RELATIVOS Y
          APLICABLES DE LA LEY GENERAL DE TÍTULOS Y OPERACIONES DE CRÉDITO.
        </Text>
        <View style={styles.viewMain}>
          <Text style={{ ...styles.anexoText, paddingTop: 10 }}>
            Total: {priceParsed || '$538,200.00'} M.N.
          </Text>
          <Text style={styles.anexoText}>
            Lugar y fecha de suscripción: {state}, México, a {deliveryDate}. Por valor pactado, el suscrito{' '}
            {fullName} incondicionalmente promete pagar a la orden de E-MKT GOODS DE MÉXICO, S.A.P.I. de C.V.,
            sus cesionarios o sucesores, la cantidad principal de {priceParsed} al momento en el que dicho
            título de crédito le sea puesto a la vista para su pago único; señalando como domicilio para
            efectuar el pago el ubicado en Prolongación Paseo de la Reforma 1015 PISO 5 INT 140, Santa Fe
            Cuajimalpa, Cuajimalpa de Morelos, Ciudad de México, C.P. 05348, México Intereses Moratorios.
          </Text>

          <Text style={styles.anexoText}>
            En el evento en que el suscriptor incumpla con el pago de la totalidad de la cantidad principal
            señalada en este pagaré, se causarán intereses moratorios a una tasa de 3% ( tres por ciento)
            mensual sobre la cantidad total señalada al inicio de este documento, durante todo el tiempo en
            que dure la mora y hasta el pago total de la misma. El pago de intereses moratorios se deberá
            efectuar adicionado con el IVA correspondiente. El suscriptor señala como domicilio donde puede
            ser requerido de pago, el ubicado en {address}
            {/* AV ZITLALCUA MZ2 LT18 SAN PABLO TECALCO Estado de
              México TECAMAC */}{' '}
            {/* {zip} */}Este Pagaré que consta en 1 (una) página se suscribe y entrega en el estado de{' '}
            {state}, México, a {dateParsed}.
          </Text>
        </View>
        <View style={styles.containerS}>
          <View style={styles.content}>
            <Text
              style={{
                ...styles.dateAndWho,
                fontFamily: 'Helvetica-BoldOblique',
                marginTop: '30px',
              }}
            >
              Suscriptor de este pagaré
            </Text>
            {/* <Text style={styles.dateAndWho}>{nombre}</Text> */}
            <Text style={styles.dateAndWho}>Fecha: {deliveryDate}</Text>
            <Text style={styles.dateAndWho}>Por: {fullName}</Text>
          </View>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  page: {
    paddingBottom: 30,
    flexDirection: 'column',
    alignContent: 'center',
  },
  viewer: {
    width: '100%',
    height: '100vh',
  },
  header: {
    width: '100vw',
  },
  body: {
    flexDirection: 'column',
    rowGap: 1,
    marginBottom: '20px',
    marginLeft: '10%',
    marginRight: '10%',
  },
  viewBackground: {
    position: 'absolute',
    zIndex: '0',
    marginTop: '70px',
    opacity: 0.08,
    height: '500px',
    width: '100%',
  },
  anexoTitle: {
    textAlign: 'center',
    color: 'black',
    fontFamily: 'Helvetica-Bold',
    fontSize: 14,
    lineHeight: '0px',
    zIndex: 1,
    textTransform: 'uppercase',
    marginBottom: '1.5vh',
  },
  anexoSubTitle: {
    fontWeight: 800,
    fontSize: 10,
    marginBottom: 10,
    zIndex: 1,
  },

  anexoText: {
    color: 'black',
    // fontFamily: 'Helvetica-Bold',
    fontSize: 12,
    textAlign: 'justify',
    letterSpacing: '0.2',
    lineHeight: '1.2',
    // lineHeight: '2px',
    // zIndex: 1,
  },

  viewMain: {
    rowGap: 10,
    zIndex: 1,
  },

  interesMora: {
    fontFamily: 'Helvetica-BoldOblique',
    fontSize: '10px',
    zIndex: 1,
    // fontWeight: 'bold',
  },
  containerS: {
    marginTop: '5vh',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  content: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'center',
    rowGap: 8,
  },
  names: {
    fontSize: 10,
    textAlign: 'left',
    fontFamily: 'Helvetica-Bold',
    marginLeft: 20,
    marginBottom: 12,
  },
  dateAndWho: {
    fontSize: 12,
  },
});
