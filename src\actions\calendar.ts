'use server';
import axios from 'axios';
import { URL_API } from '@/constants';
import { ApiPath } from '@/constants/api.endpoints';
import getCurrentUser from './getCurrentUser';

export interface IWeeklySchedule {
  sunday: {
    start: string;
    end: string;
  };
  monday: {
    start: string;
    end: string;
  };
  tuesday: {
    start: string;
    end: string;
  };
  wednesday: {
    start: string;
    end: string;
  };
  thursday: {
    start: string;
    end: string;
  };
  friday: {
    start: string;
    end: string;
  };
  saturday: {
    start: string;
    end: string;
  };
}

interface ICreateCalendarSchedule {
  name: string;
  weeklySchedule: IWeeklySchedule;
  timezone: string;
  breakTimes: Array<{ start: string; end: string }>;
  bufferTime: number;
  duration: number;
}

export async function createCalendarSchedule(requestPayload: ICreateCalendarSchedule) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const { data } = await axios.put(`${URL_API}${ApiPath.CALENDAR_SCHEDULE}/${user.id}`, requestPayload, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return data;
  } catch (err: any) {
    const { response } = err;
    if (response?.status >= 400 && response?.status < 500) {
      return response.data;
    }
    throw new Error('Error occured while creating calendar schedule.');
  }
}

export async function statusChangeAppointment(requestPayload: any) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const { data } = await axios.patch(`${URL_API}${ApiPath.CALENDAR_EVENT_STATUS}`, requestPayload, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    return data;
  } catch (err) {
    throw new Error('Error occured while changing appointment status');
  }
}

export async function appointmentHomeVisitorChange(requestPayload: any) {
  try {
    const user = await getCurrentUser();
    if (!user) return null;
    const { data } = await axios.patch(
      `${URL_API}${ApiPath.CALENDAR_EVENT_HOME_VISITOR_CHANGE}`,
      requestPayload,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      }
    );
    return data;
  } catch (err) {
    throw new Error('Se produjo un error al cambiar la cita del visitador domiciliario');
  }
}
