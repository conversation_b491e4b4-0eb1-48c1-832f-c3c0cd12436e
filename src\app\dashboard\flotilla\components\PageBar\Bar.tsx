import { Capabilities, CONTRACT_REGIONS_IATA, Sections, Subsections } from '@/constants';
import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import SearchInput from './SearchInput';
import FilterVehicles from './FilterVehicles';
import CountrySelector from '@/app/dashboard/clientes/_components/CountrySelector';
import { useCurrentUser } from '@/app/dashboard/providers/CurrentUserProvider';
import VehicleRegistrationFlow from './VehicleRegistrationFlow';
import { usePermissions } from '@/casl/PermissionsContext';
import { canPerform } from '@/casl/canPerform';
import BulkUploadVehicleDocuments from './BulkUploadVehicleDocuments';

export default function Bar({ page }: { page: string }) {
  const ability = usePermissions();

  const canAdd = canPerform(ability, Capabilities.Add, Sections.Fleet, Subsections.General);

  return (
    <div>
      <div className="flex items-center justify-between md:gap-0 flex-wrap pr-12 pb-4 ">
        <h1 className="font-bold text-interBold32 font-inter">{page}</h1>
        <CountrySelector />
      </div>
      <div className="flex flex-row justify-end pr-8 xl:pr-12 gap-2 lg:gap-0 lg:gap-x-2 flex-wrap">
        <SearchInput
          page={page && page?.toLowerCase() === 'active' ? page?.toLowerCase() : null}
          vehicleStatus={page && page?.toLowerCase() === 'active' ? page?.toLowerCase() : null}
        />
        <FilterVehicles />
        {canAdd && <CreateVehicle />}
        {canAdd && <BulkUploadVehicleDocuments />}
      </div>
    </div>
  );
}

function CreateVehicle() {
  const [nameFiles, setNameFiles] = useState({
    vehiclePhoto: '',
    bill: '',
  });
  const search = useSearchParams();
  const paramCountry = search ? search.get('country') : '';

  const handleSetName = (name: string, file: File) => {
    setNameFiles({
      ...nameFiles,
      [name]: file.name,
    });
  };

  const { user: currentUser } = useCurrentUser();
  const allowedRegions = CONTRACT_REGIONS_IATA.filter((r) => {
    const code = r.code as 'cdmx' | 'gdl' | 'mty' | 'qro' | 'tij' | 'pbc';
    return currentUser.settings.allowedRegions.includes(code);
  });

  return (
    <VehicleRegistrationFlow
      paramCountry={paramCountry}
      allowedRegions={allowedRegions}
      handleSetName={handleSetName}
      nameFiles={nameFiles}
    />
  );
}
