import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from './getCurrentUser';

export interface DashData {
  vehicleStatusCounts: any;
  categoryCounts: any;
}

export interface ChartData {
  label: number[];
  labels: string[];
  indicators: number[];
}

export interface DashStats {
  chartData: any;
  categoryData: any;
}

export const getDashboardStatus = async (country?: string) => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(
      `${URL_API}/stock/dashboardStatus/${country ? `?country=${encodeURI(country)}` : ''}`,
      {
        headers: {
          Authorization: `Bearer ${user?.accessToken}`,
        },
      }
    );
    return response?.data?.dashboardData as DashData;
  } catch (error) {
    console.log(error);
    return null;
  }
};

export const getDashboardData = async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/stock/dashboardData/`, {
      headers: {
        Authorization: `Bearer ${user?.accessToken}`,
      },
    });
    return response?.data?.data as DashStats;
  } catch (error) {
    console.log(error);
    return null;
  }
};
