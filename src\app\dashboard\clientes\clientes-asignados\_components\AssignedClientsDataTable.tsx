'use client';

import { DataTable } from '@/components/data-table/data-table';
import { DataTableColumnHeader } from '@/components/data-table/data-table-column-header';
import { DataTableToolbar } from '@/components/data-table/data-table-toolbar';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useDataTable } from '@/hooks/use-data-table';
import { ColumnDef, Column } from '@tanstack/react-table';
import { Text } from 'lucide-react';
import { parseAsString, useQueryState } from 'nuqs';
import React, { useEffect, useState } from 'react';
import { URL_API } from '@/constants';
import { useSession } from 'next-auth/react';
import { MyUser } from '@/actions/getCurrentUser';
import { useRouter } from 'next/navigation';

interface AdmissionRequest {
  _id: string;
  status: string;
  personalData: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    city: string;
    state: string;
  };
  createdAt: string;
  typeOfPreapproval?: string;
  documentsAnalysis: {
    status: string;
  };
  agentId: string;
  agentName: string;
}

const ApprovalTypes = [
  { value: 'preapproved', label: 'Preaprobado' },
  { value: 'pre-owned', label: 'Preaprobado Seminuevo' },
  { value: 'down payment', label: 'Preaprobado con Enganche' },
  { value: 'deposit', label: 'Preaprobado con Deposito' },
];

const DocumentAnalysisStatus = [
  { value: 'approved', label: 'Aprobada' },
  { value: 'rejected', label: 'Rechazada' },
  { value: 'pending', label: 'Pendiente' },
];

const AdmissionRequestStatus = [
  { value: 'created', label: 'Creada' },
  { value: 'earnings_analysis', label: 'Análisis de ingresos' },
  { value: 'documents_analysis', label: 'Análisis de documentos' },
  { value: 'risk_analysis', label: 'Análisis de riesgo' },
  { value: 'social_analysis', label: 'Análisis social' },
  { value: 'home_visit', label: 'Visita domiciliaria' },
  { value: 'approved', label: 'Aprobada' },
  { value: 'rejected', label: 'Rechazada' },
];

function isExactlyThreeCalendarDaysOld(inputDate: Date): boolean {
  const today = new Date();
  // Normalize to midnight today
  const normalizedToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  // Three calendar days ago at midnight
  const threeDaysAgo = new Date(normalizedToday);
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

  // Normalize input date to midnight
  const normalizedInput = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());

  return normalizedInput.getTime() === threeDaysAgo.getTime();
}

function isGreaterThanThreeDaysOld(inputDate: Date): boolean {
  const today = new Date();
  // Normalize to midnight of today
  const normalizedToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  // Subtract 3 days to get "3 days ago" at midnight
  const threeDaysAgo = new Date(normalizedToday);
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

  // Normalize input date to midnight
  const normalizedInput = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());

  return normalizedInput < threeDaysAgo;
}

export function AssignedClientsDataTable() {
  const router = useRouter();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;

  // URL state management
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const [_id] = useQueryState('_id', parseAsString.withDefault(''));
  const [firstName] = useQueryState('personalData.firstName', parseAsString.withDefault(''));
  const [lastName] = useQueryState('personalData.lastName', parseAsString.withDefault(''));
  const [status] = useQueryState('status', parseAsString.withDefault(''));
  const [phone] = useQueryState('phone', parseAsString.withDefault(''));
  const [email] = useQueryState('email', parseAsString.withDefault(''));
  const [state] = useQueryState('state', parseAsString.withDefault(''));
  const [city] = useQueryState('personalData.city', parseAsString.withDefault(''));
  const [agentId] = useQueryState('agentId', parseAsString.withDefault(''));
  const [agentName] = useQueryState('agentName', parseAsString.withDefault(''));
  const [typeOfPreapproval] = useQueryState('typeOfPreapproval', parseAsString.withDefault(''));
  const [documentsAnalysisStatus] = useQueryState('documentsAnalysis.status', parseAsString.withDefault(''));

  // Local state
  const [data, setData] = useState<AdmissionRequest[]>([]);
  const [pageCount, setPageCount] = useState(1);
  const [totalResult, setTotalResult] = useState(0);

  // Define columns based on your Mongoose schema
  const columns = React.useMemo<ColumnDef<AdmissionRequest>[]>(
    () => [
      {
        id: 'select',
        header: ({ table }) => (
          <Checkbox
            checked={
              table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
            }
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        size: 32,
        enableSorting: false,
        enableHiding: false,
      },
      {
        id: 'agentName',
        accessorKey: 'agentName',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Nombre Agente" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Nombre Agente',
          placeholder: 'Buscar nombre agente',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'agentId',
        accessorKey: 'agentId',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Agente ID" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Agente ID',
          placeholder: 'Buscar Agente ID',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: '_id',
        accessorKey: '_id',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Solicitudes ID" />
        ),
        cell: ({ cell }) => {
          return (
            <div
              className="cursor-pointer"
              onClick={() => router.push(`/dashboard/clientes/solicitudes/${cell.getValue<string>()}`)}
            >
              {cell.getValue<string>()}
            </div>
          );
        },
        meta: {
          label: 'Solicitudes ID',
          placeholder: 'Buscar Solicitudes ID...',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'personalData.firstName',
        accessorKey: 'personalData.firstName',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Nombre" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Nombre',
          placeholder: 'Buscar nombre...',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'personalData.lastName',
        accessorKey: 'personalData.lastName',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Apellido" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        meta: {
          label: 'Apellido',
          placeholder: 'Buscar apellido...',
          variant: 'text',
          icon: Text,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'status',
        accessorKey: 'status',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Estado" />
        ),
        cell: ({ cell }) => {
          const statusVal = cell.getValue<string>();
          return (
            <Badge variant="outline" className="capitalize">
              {statusVal}
            </Badge>
          );
        },
        meta: {
          label: 'Estado',
          variant: 'multiSelect',
          options: AdmissionRequestStatus,
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'createdAt',
        accessorKey: 'createdAt',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Creado En" />
        ),
        cell: ({ cell }) => {
          const date = cell.getValue<Date>();
          const localDate = new Date(date);
          const isThreeDayOld = isExactlyThreeCalendarDaysOld(localDate);
          const isGreaterThanThreeDayOld = isGreaterThanThreeDaysOld(localDate);
          if (isThreeDayOld) {
            return (
              <Badge variant="outline" className="capitalize bg-yellow-300">
                {localDate.toLocaleString()}
              </Badge>
            );
          }
          if (isGreaterThanThreeDayOld) {
            return (
              <Badge variant="outline" className="capitalize bg-orange-300">
                {localDate.toLocaleString()}
              </Badge>
            );
          }
          return (
            <Badge variant="outline" className="capitalize">
              {localDate.toLocaleString()}
            </Badge>
          );
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'personalData.phone',
        accessorKey: 'personalData.phone',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Teléfono" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        enableSorting: false,
      },
      {
        id: 'personalData.email',
        accessorKey: 'personalData.email',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Email" />
        ),
        cell: ({ cell }) => cell.getValue<string>(),
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'personalData.city',
        accessorKey: 'personalData.city',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Ubicación" />
        ),
        cell: ({ row }) => {
          const cityVal = row.original.personalData.city;
          const stateVal = row.original.personalData.state;
          return `${cityVal}, ${stateVal}`;
        },
        meta: {
          label: 'Ciudad',
          variant: 'multiSelect',
          options: [
            { value: 'CDMX/EDOMEX', label: 'Ciudad de México / Estado México' },
            { value: 'Guadalajara', label: 'Guadalajara' },
            { value: 'Monterrey', label: 'Monterrey' },
            { value: 'Puebla', label: 'Puebla' },
            { value: 'Tijuana', label: 'Tijuana' },
            { value: 'Queretaro', label: 'Queretaro' },
            { value: 'Cuernavaca', label: 'Cuernavaca' },
            { value: 'Otro', label: 'Otro' },
          ],
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'typeOfPreapproval',
        accessorKey: 'typeOfPreapproval',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Tipo de Preaprobación" />
        ),
        cell: ({ cell }) => cell.getValue<string>() || '-',
        meta: {
          label: 'Tipo de Preaprobación',
          variant: 'multiSelect',
          options: ApprovalTypes,
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
      {
        id: 'documentsAnalysis.status',
        accessorKey: 'documentsAnalysis.status',
        header: ({ column }: { column: Column<AdmissionRequest, unknown> }) => (
          <DataTableColumnHeader column={column} title="Análisis de Documentos" />
        ),
        cell: ({ cell }) => cell.getValue<string>() || '-',
        meta: {
          label: 'Análisis de Documentos',
          variant: 'multiSelect',
          options: DocumentAnalysisStatus,
          icon: undefined,
        },
        enableColumnFilter: true,
        enableSorting: false,
      },
    ],
    []
  );

  // Fetch data when filters or pagination changes
  const { table, page, perPage } = useDataTable({
    data,
    columns,
    pageCount,
    initialState: {
      sorting: [{ id: 'createdAt', desc: true }],
      columnPinning: { right: ['actions'] },
    },
    getRowId: (row) => row._id?.toString() || '',
    shallow: false,
    clearOnDefault: true,
  });

  useEffect(() => {
    // Flag to track mounted status to avoid state updates after unmount
    let isMounted = true;

    // AbortController to cancel ongoing fetch request if dependencies change
    const controller = new AbortController();

    const fetchData = async (pageIndex: number, pageSize: number) => {
      try {
        const params = new URLSearchParams();

        // Add filters conditionally
        if (_id) params.append('_id', _id.trim());
        if (agentId) params.append('agentId', agentId.trim());
        if (agentName) params.append('agentName', agentName.trim());
        if (firstName) params.append('personalData.firstName', firstName.trim());
        if (lastName) params.append('personalData.lastName', lastName.trim());
        if (status) params.append('status', status);
        if (phone) params.append('personalData.phone', phone.trim());
        if (email) params.append('personalData.email', email.trim());
        if (state) params.append('personalData.state', state);
        if (city) params.append('personalData.city', city);
        if (typeOfPreapproval) params.append('typeOfPreapproval', typeOfPreapproval);
        if (documentsAnalysisStatus) params.append('documentsAnalysis.status', documentsAnalysisStatus);

        // Pagination
        params.append('page', pageIndex.toString());
        params.append('limit', pageSize.toString());
        params.append('includeApprovedOrRejected', 'true');

        const url = `${URL_API}/leadAssignment/search?${params}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${user.accessToken}`,
          },
          signal: controller.signal,
        });

        // Handle non-2xx responses
        if (!response.ok) {
          throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();

        // Only update state if component is still mounted
        if (isMounted) {
          setData(result.data);
          setPageCount(result.pagination.totalPages || 1);
          setTotalResult(result.pagination.total || 0);
        }
      } catch (error: any) {
        // Ignore aborted fetches
        if (error.name === 'AbortError') {
          console.log('Request aborted');
          return;
        }

        console.error('Error fetching data:', error);

        // Optionally show a toast or error message to the user
        // showErrorNotification('Failed to load data. Please try again.');
      }
    };

    if (user && isMounted) {
      fetchData(page, perPage);
    }

    // Cleanup function
    return () => {
      isMounted = false;
      controller.abort(); // Cancel any ongoing fetch
    };
  }, [
    user,
    agentId,
    agentName,
    firstName,
    lastName,
    _id,
    city,
    status,
    typeOfPreapproval,
    documentsAnalysisStatus,
    page,
    perPage,
  ]);

  return (
    <div className="space-y-4">
      {/* Table */}
      <p> Resultados: {totalResult} </p>
      <div className="rounded-md border">
        <DataTable table={table}>
          <DataTableToolbar table={table} />
        </DataTable>
      </div>
    </div>
  );
}
