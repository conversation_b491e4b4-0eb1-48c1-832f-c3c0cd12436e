/* eslint-disable @typescript-eslint/no-explicit-any */
// src/services/apiVendorPlatform.ts
import { URL_API } from '@/constants';
import axios from 'axios';

// create api again but with a name that is more descriptive

export const apiVendorPlatform = axios.create({
  baseURL: URL_API + '/vendor-platform',
  headers: {
    'Content-Type': 'application/json',
    adpt: 'true',
  },
});

export const appointmentService = {
  getServiceTypes: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/service-types`);
    return data;
  },

  getAvailableSlots: async (workshopId: string, date: string, serviceTypeId: string) => {
    // console.log('workshopId', workshopId, 'date', date);
    const { data } = await apiVendorPlatform.get(
      `/workshops/${workshopId}/available-slots/${date}/${serviceTypeId}`
    );
    console.log('data', data);
    return data;
  },

  createAppointment: async (workshopId: string, appointmentData: any) => {
    const postData = {
      startTime: appointmentData.startTime,
      serviceTypeId: appointmentData.serviceTypeId,
      ...appointmentData,
    };

    const { data } = await apiVendorPlatform.post(`/workshops/${workshopId}/appointments`, postData);
    return data;
  },

  getAllAppointments: async (workshopId: string, startDate?: string, endDate?: string) => {
    const url = new URL(`${apiVendorPlatform.defaults.baseURL}/workshops/${workshopId}/appointments`);
    if (startDate) {
      url.searchParams.append('startDate', startDate);
    } else {
      const current01Date = new Date();
      current01Date.setDate(1);
      startDate = current01Date.toISOString().split('T')[0];
    }

    if (endDate) {
      url.searchParams.append('endDate', endDate);
    } else {
      const currentLastDate = new Date();
      currentLastDate.setMonth(currentLastDate.getMonth() + 1);
      currentLastDate.setDate(0);
      endDate = currentLastDate.toISOString().split('T')[0];
    }

    console.log('url', url.toString());
    const { data } = await apiVendorPlatform.get(url.toString());

    return data;
  },

  getAllWorkshops: async () => {
    console.log('headers', apiVendorPlatform.defaults.headers);

    const { data } = await apiVendorPlatform.get(`/workshops`);
    return data;
  },

  getServiceTypesByOrganization: async (organizationId: string) => {
    const { data } = await apiVendorPlatform.get(`/organizations/${organizationId}/service-types`);
    return data;
  },

  getLastScheduledppointmentByAssociateId: async (associateId: string) => {
    const { data } = await apiVendorPlatform.get(`/appointments/last/associate/${associateId}`);
    return data;
  },
  cancelAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.delete(`/appointments/${appointmentId}/cancel`);
    return data;
  },

  notAttendedAppointment: async (appointmentId: string) => {
    const { data } = await apiVendorPlatform.patch(`/appointments/${appointmentId}/not-attended`);
    return data;
  },
  reescheduleAppointment: async (appointmentId: string, startTime: string, serviceTypeId: string) => {
    const { data } = await apiVendorPlatform.put(`/appointments/${appointmentId}/reschedule`, {
      startTime,
      serviceTypeId,
    });
    return data;
  },
};
