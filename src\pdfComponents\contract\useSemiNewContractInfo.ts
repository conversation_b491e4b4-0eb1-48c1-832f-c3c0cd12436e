import { create } from 'zustand';

interface UseSemiNewContractInfoProps {
  isNew: boolean;
  totalWeeks: number;
  setIsNew: (isNew: boolean) => void;
  setTotalWeeks: (restWeeks: number) => void;
}

export const useSemiNewContractInfo = create<UseSemiNewContractInfoProps>((set) => ({
  isNew: true,
  totalWeeks: 0,
  setIsNew: (isNew) => set(() => ({ isNew })),
  setTotalWeeks: (totalWeeks) => set(() => ({ totalWeeks })),
}));
