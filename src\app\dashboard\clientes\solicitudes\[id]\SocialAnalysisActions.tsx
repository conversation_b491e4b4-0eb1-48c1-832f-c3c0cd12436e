'use client';
import { URL_API } from '@/constants';
import { Flex, Button, useToast } from '@chakra-ui/react';
import { useSession } from 'next-auth/react';
import { usePathname, useRouter } from 'next/navigation';
import { useState, useTransition } from 'react';
import { MyUser } from '@/actions/getCurrentUser';
import Swal from 'sweetalert2';
import { useCountry } from './detail';
import { MLModels } from '../enums';
import { ModelScores } from '../types';

export default function SocialAnalysisActions({
  requestId,
  modelScores,
}: {
  requestId: string;
  modelScores: ModelScores;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isTransitioning, startTransition] = useTransition();
  const toast = useToast();
  const router = useRouter();
  const pathname = usePathname();
  const { data: session } = useSession();
  const user = session?.user as unknown as MyUser;
  const { isCountryUSA } = useCountry();

  function handleApprove() {
    setIsSubmitting(true);
    fetch(`${URL_API}/admission/requests/${requestId}/social-analysis/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${user.accessToken}`,
      },
    })
      .then(async (res) => {
        if (res.ok) {
          startTransition(() => {
            toast({
              title: 'Análisis social aprobado',
              status: 'success',
            });
            router.replace(`${pathname}`, { scroll: false });
            router.refresh();
          });
        } else {
          const data = await res.json();
          if (data.error?.code === 'required_documents_not_approved') {
            toast({
              title: 'Documentación del cliente pendiente',
              status: 'error',
              duration: 4000,
              isClosable: true,
            });
          } else {
            throw new Error('Error approving social analysis');
          }
        }
      })
      .catch((err) => {
        console.error(err);
      })
      .finally(() => {
        setIsSubmitting(false);
      });
  }

  function handleReject() {
    Swal.fire({
      animation: false,
      title: '¿Estás seguro de rechazar en análisis social?',
      text: 'Esta acción rechazará por completo la solicitud',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Sí, rechazar',
      cancelButtonText: 'Cancelar',
      preConfirm: async () => {
        try {
          const res = await fetch(`${URL_API}/admission/requests/${requestId}/social-analysis/reject`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${user.accessToken}`,
            },
          });

          const data = await res.json();

          if (!res.ok) {
            throw new Error(data.message || 'Error rejecting social analysis');
          }
        } catch (error) {
          Swal.showValidationMessage(`Request failed: ${error}`);
        }
      },
    }).then((result) => {
      if (result.isConfirmed) {
        startTransition(() => {
          toast({
            title: 'Análisis social rechazado',
            status: 'success',
          });
          router.replace(`${pathname}`, { scroll: false });
          router.refresh();
        });
      }
    });
  }

  const DeclineText = isCountryUSA ? 'Decline' : 'Rechazar';
  const runAnalysis = isCountryUSA ? 'Run analysis' : 'Ejecutar análisis';
  const approveAnalysis = isCountryUSA ? 'Approve' : 'Aprobar';

  const showRunAnalysis =
    !modelScores ||
    Object.keys(modelScores).some((key) => {
      return (
        (key === MLModels.PERSONAL_INFORMATION || key === MLModels.HOMEVISIT_INFORMATION) &&
        (modelScores[key as keyof ModelScores]?.status === 'pending' ||
          !modelScores[key as keyof ModelScores]?.status)
      );
    });

  async function handleRunAnalysis() {
    setIsSubmitting(true);
    try {
      const res = await fetch(`${URL_API}/admission/requests/${requestId}/social-analysis/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      const data = await res.json();

      if (res.ok) {
        startTransition(() => {
          toast({
            title: isCountryUSA
              ? 'Social analysis run successfully'
              : 'Análisis social ejecutado correctamente',
            status: 'success',
          });
          router.replace(`${pathname}`, { scroll: false });
          router.refresh();
        });
      } else {
        throw new Error(data.message || 'Error executing social analysis');
      }
    } catch (err: any) {
      console.error(err);
      toast({
        title: isCountryUSA ? 'Error executing analysis' : 'Error al ejecutar el análisis',
        description: err.message,
        status: 'error',
        duration: 4000,
        isClosable: true,
      });
      router.replace(`${pathname}`, { scroll: false });
      router.refresh();
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="pl-3">
      {showRunAnalysis ? (
        <Button
          sx={{
            color: 'white',
            h: '40px',
          }}
          className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
          type="submit"
          isLoading={isSubmitting || isTransitioning}
          onClick={handleRunAnalysis}
        >
          {runAnalysis}
        </Button>
      ) : (
        <Flex gap={3}>
          <Button
            sx={{
              color: '#E14942',
              h: '40px',
            }}
            className="bg-white cursor-pointer hover:bg-gray-50 border border-[#E14942]"
            type="submit"
            isLoading={isSubmitting || isTransitioning}
            onClick={handleReject}
          >
            {DeclineText}
          </Button>
          <Button
            sx={{
              color: 'white',
              h: '40px',
            }}
            className="bg-[#5800F7] cursor-pointer hover:bg-[#5800F7]"
            type="submit"
            isLoading={isSubmitting || isTransitioning}
            onClick={handleApprove}
          >
            {approveAnalysis}
          </Button>
        </Flex>
      )}
    </div>
  );
}
