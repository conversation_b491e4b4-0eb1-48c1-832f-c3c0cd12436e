import axios from 'axios';
import getCurrentUser from './getCurrentUser';
import { URL_API } from '@/constants';
import { cache } from 'react';
import { UserResponse } from './getUserById';

export interface DocumentData {
  url: string;
  docId: string;
  originalName: string;
}

const getUsers = cache(async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/user/getAllUsers?adminId=${user.id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response?.data?.users as UserResponse[];
  } catch (error: any) {
    return null;
    /* if (error.response) return error.response.data.message;
    else return error.message; */
  }
});

export default getUsers;

export const getHomeVisitors = async () => {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    const response = await axios.get(`${URL_API}/user/homeVisitors?adminId=${user.id}`, {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
      },
    });

    return response?.data?.users as UserResponse[];
  } catch (error: any) {
    console.log('error: ', error);
    return null;
  }
};
