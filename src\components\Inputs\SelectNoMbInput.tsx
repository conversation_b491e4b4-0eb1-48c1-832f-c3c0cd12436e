import { ErrorMessage, Field, useField } from 'formik';
import Select, { StylesConfig } from 'react-select';

interface SelectInputProps {
  options: {
    value: string;
    label: string;
  }[];
  label: string;
  name: string;
}

const customStyles: StylesConfig = {
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected || state.isFocused ? '#5800F7' : 'white',
    color: state.isSelected || state.isFocused ? 'white' : 'black',
  }),
};

export default function SelectNoMbInput({ label, options, name }: SelectInputProps) {
  const [field, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  return (
    <>
      <div className="w-full">
        <label className="block text-gray-700 mb-2" htmlFor="selectOption">
          {label}
        </label>
        <Field
          id={name}
          name={name}
          component={({ form, ...props }: any) => (
            <Select
              styles={customStyles}
              {...field}
              {...props}
              value={field.value}
              options={options}
              placeholder="Selecciona"
              onChange={(option: SelectInputProps['options'][number]) => {
                // console.log('soy el on change', option);
                form.setFieldValue(field.name, option);
              }}
              onBlur={() => form.setFieldTouched(field.name, true)}
              inputId="431"
              className={`
                border-[1px]
                outline-none
                ${
                  form.touched[field.name] && form.errors[field.name] ? 'border-red-500' : 'border-[#9CA3AF]'
                } bg-white rounded-md shadow-sm w-full`}
            />
          )}
        />
        {hasError && <ErrorMessage name={name} component="div" className="text-red-500 text-sm mt-1" />}
      </div>
    </>
  );
}
