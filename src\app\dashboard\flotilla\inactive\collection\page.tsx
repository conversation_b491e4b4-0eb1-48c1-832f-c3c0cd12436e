import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { allCategory, allSubCategory, combineSearchParamsAndFilters } from '@/constants';
import VehicleTable from '../../components/VehicleTable';

export const metadata = {
  title: 'Flotilla',
  description: 'Esto es la flotilla',
};

interface StockPageProps {
  searchParams: Record<string, string>;
}

export default async function CollectionPage({ searchParams }: StockPageProps) {
  const user = await getCurrentUser();

  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });
  //console.log(`definitiveFilters: ${JSON.stringify(definitiveFilters, null, 2)}`);

  if (!user) return null;

  const result = await getStockVehicles({
    limit: 10,
    searchParams: {
      ...definitiveFilters,
      vehicleStatus: 'inactive',
      category: definitiveFilters?.category || allCategory.collection,
      subCategory: definitiveFilters?.subCategory || allSubCategory['non-payment'],
      country: definitiveFilters?.country,
    },
  });

  const subOptions = [
    { label: 'Non Payment', name: allSubCategory['non-payment'] },
    { label: 'Incomplete Payment', name: allSubCategory['incomplete-payment'] },
    { label: 'Payment Commitment', name: allSubCategory['payment-commitment'] },
    { label: 'Payment Extension', name: allSubCategory['payment-extension'] },
    { label: 'In Recovery', name: allSubCategory['in-recovery'] },
  ];

  const page = {
    api: 'inactive',
    name: 'Inactive',
    count: 12,
  };

  const subPage = {
    name: 'Collection',
    api: 'collection',
    count: 12,
  };

  if (!result) return null;

  return (
    <VehicleTable
      route="collection"
      page={page}
      subPage={subPage}
      data={result.stock}
      totalCount={result.totalCount}
      subOptions={subOptions}
    />
  );
}
