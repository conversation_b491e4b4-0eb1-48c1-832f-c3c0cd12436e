'use client'; // Enable client-side rendering

import React from 'react';
import { useCountry } from './detail';

interface FileData {
  name: string;
  platform: string;
  path: string;
}

interface Props {
  fileList: FileData[] | null;
}

const FileDownloadList = ({ fileList }: Props) => {
  const handleDownload = (url: string) => {
    window.open(url, '_blank');
  };
  const { isCountryUSA } = useCountry();
  const noFilesUploadedText = isCountryUSA ? 'No files uploaded' : 'No se subieron archivos';

  return (
    <div className="p-4">
      <ul className="bg-white rounded-lg shadow-lg">
        {fileList ? (
          fileList.map((file, index) => (
            <li
              key={index}
              className="border-b last:border-none p-4 cursor-pointer hover:bg-gray-100 flex items-center"
              onClick={() => handleDownload(file.path)}
            >
              <span className="text-purple-600 hover:underline">{file.name}</span>
            </li>
          ))
        ) : (
          <li className="border-b last:border-none p-4 cursor-pointer hover:bg-gray-100 flex items-center">
            <span>{noFilesUploadedText}</span>
          </li>
        )}
      </ul>
    </div>
  );
};

export default FileDownloadList;
