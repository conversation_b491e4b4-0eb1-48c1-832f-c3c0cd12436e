/* 'use client';
import FormikContainer from '@/components/Formik/FormikContainer';
import ModalContainer from './ModalContainer';
import CustomInput from '@/components/Inputs/CustomInput';
import axios from 'axios';
import { URL_API } from '@/constants';
import { useParams, useRouter } from 'next/navigation';
import { useToast } from '@chakra-ui/react';
import Swal from 'sweetalert2';
import { useCurrentUser } from '@/app/dashboard/CurrentUserProvider';
import InputDate from '@/components/Inputs/InputDate';
import { sendServiceSchema } from '@/validatorSchemas/changeStatusSchema';
// import { FormikHelpers } from 'formik';

// type Helpers = FormikHelpers<{
//   reason: {
//     label: string;
//     value: string;
//   };
//   comments: string;
//   platesDischargedDoc: string;
//   dictamenDoc: string;
//   reportDoc: string;
// }>;

const initialValues = {
  dateIn: '',
  dateOut: '',
  comments: '',
};

export default function SendService() {
  const sendServiceModal = useOpenSendServiceModal();

  const { id } = useParams();
  const router = useRouter();
  const { user } = useCurrentUser();
  const toast = useToast();

  const onSubmit = async (values: typeof initialValues) => {
    console.log('Enviado: ', values);
    try {
      const response = await axios.patch(`${URL_API}/stock/sendService/${id}`, values, {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
        },
      });
      toast({
        title: response.data.message,
        description: 'Actualizando pagina...',
        duration: 3000,
        status: 'success',
        position: 'top',
      });
      router.refresh();
      router.push(`/dashboard/flotilla/bajas/${id}`);
    } catch (error: any) {
      toast({
        title: error.response.data.message,
        duration: 6000,
        status: 'error',
        position: 'top',
      });
    } finally {
      // helpers.setSubmitting(false);
      sendServiceModal.onClose();
    }
  };

  const confirmSubmit = async (values: typeof initialValues) => {
    return Swal.fire({
      title: '¿Estás seguro?',
      text: 'Se enviará a taller',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Si, enviar',
      cancelButtonText: 'Cancelar',
    }).then(async (result) => {
      if (result.isConfirmed) {
        await onSubmit(values);
      }
    });
  };

  return (
    <ModalContainer
      title="Enviar al taller"
      onClose={sendServiceModal.onClose}
      classAnimation="animate__fadeIn"
    >
      <FormikContainer
        onSubmit={confirmSubmit}
        onClose={sendServiceModal.onClose}
        initialValues={initialValues}
        confirmBtnText="Enviar al taller"
        footerClassName="flex gap-3 pt-[20px] justify-center"
        validatorSchema={sendServiceSchema}
      >
        <div className="flex flex-col gap-[20px]">
          <InputDate name="dateIn" label="Fecha de ingreso" />
          <InputDate name="dateOut" label="Fecha tentativa de salida" />
          <CustomInput name="comments" label="Comentarios (opcional)" type="text" />
        </div>
      </FormikContainer>
    </ModalContainer>
  );
}
 */
