export const PhoneLogo = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_60_1446)">
        <path
          d="M10.8335 0.833443C10.8335 0.61243 10.9213 0.400468 11.0775 0.244188C11.2338 0.0879075 11.4458 0.000110103 11.6668 0.000110103C13.8762 0.00253643 15.9944 0.881289 17.5567 2.44357C19.1189 4.00584 19.9977 6.12405 20.0001 8.33344C20.0001 8.55446 19.9123 8.76642 19.756 8.9227C19.5998 9.07898 19.3878 9.16678 19.1668 9.16678C18.9458 9.16678 18.7338 9.07898 18.5775 8.9227C18.4213 8.76642 18.3335 8.55446 18.3335 8.33344C18.3315 6.56594 17.6285 4.8714 16.3786 3.62159C15.1288 2.37178 13.4343 1.66876 11.6668 1.66678C11.4458 1.66678 11.2338 1.57898 11.0775 1.4227C10.9213 1.26642 10.8335 1.05446 10.8335 0.833443ZM11.6668 5.00011C12.5509 5.00011 13.3987 5.3513 14.0238 5.97642C14.6489 6.60154 15.0001 7.44939 15.0001 8.33344C15.0001 8.55446 15.0879 8.76642 15.2442 8.9227C15.4005 9.07898 15.6124 9.16678 15.8335 9.16678C16.0545 9.16678 16.2664 9.07898 16.4227 8.9227C16.579 8.76642 16.6668 8.55446 16.6668 8.33344C16.6655 7.00777 16.1383 5.73677 15.2009 4.79937C14.2635 3.86198 12.9925 3.33477 11.6668 3.33344C11.4458 3.33344 11.2338 3.42124 11.0775 3.57752C10.9213 3.7338 10.8335 3.94576 10.8335 4.16678C10.8335 4.38779 10.9213 4.59975 11.0775 4.75603C11.2338 4.91231 11.4458 5.00011 11.6668 5.00011ZM19.2443 13.9493C19.7272 14.4335 19.9984 15.0895 19.9984 15.7734C19.9984 16.4573 19.7272 17.1133 19.2443 17.5976L18.486 18.4718C11.661 25.0059 -4.94734 8.40178 1.48598 1.55511L2.44431 0.721777C2.92912 0.252341 3.57926 -0.007349 4.25407 -0.00111086C4.92887 0.00512727 5.5741 0.276792 6.05014 0.75511C6.07598 0.780944 7.62014 2.78678 7.62014 2.78678C8.07833 3.26813 8.33339 3.90755 8.33231 4.57211C8.33122 5.23667 8.07407 5.87525 7.61431 6.35511L6.64931 7.56844C7.18335 8.86604 7.96853 10.0453 8.95973 11.0385C9.95093 12.0317 11.1286 12.8193 12.4251 13.3559L13.646 12.3851C14.1259 11.9257 14.7643 11.6689 15.4287 11.6679C16.0931 11.667 16.7323 11.9221 17.2135 12.3801C17.2135 12.3801 19.2185 13.9234 19.2443 13.9493ZM18.0976 15.1609C18.0976 15.1609 16.1035 13.6268 16.0776 13.6009C15.9059 13.4307 15.674 13.3352 15.4322 13.3352C15.1904 13.3352 14.9585 13.4307 14.7868 13.6009C14.7643 13.6243 13.0835 14.9634 13.0835 14.9634C12.9702 15.0536 12.8354 15.1127 12.6923 15.1349C12.5493 15.1571 12.4029 15.1417 12.2676 15.0901C10.588 14.4648 9.06242 13.4857 7.79417 12.2193C6.52592 10.953 5.54464 9.4288 4.91681 7.75011C4.86114 7.61299 4.84299 7.46352 4.86423 7.31706C4.88547 7.1706 4.94532 7.03244 5.03764 6.91678C5.03764 6.91678 6.37681 5.23511 6.39931 5.21344C6.56953 5.04177 6.66504 4.80979 6.66504 4.56803C6.66504 4.32626 6.56953 4.09429 6.39931 3.92261C6.37348 3.89761 4.83931 1.90178 4.83931 1.90178C4.66506 1.74553 4.43765 1.66186 4.20369 1.6679C3.96972 1.67394 3.74693 1.76925 3.58098 1.93428L2.62265 2.76761C-2.07901 8.42094 12.3135 22.0151 17.2676 17.3334L18.0268 16.4584C18.2047 16.2937 18.3115 16.0662 18.3248 15.824C18.338 15.5819 18.2565 15.3441 18.0976 15.1609Z"
          fill="#374957"
        />
      </g>
      <defs>
        <clipPath id="clip0_60_1446">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
