import getCurrentUser from '@/actions/getCurrentUser';
import { getStockVehicles } from '@/actions/getAllVehicles';
import { listStatus } from './[id]/lib';
import VehicleInfiniteList from '../components/VehicleInfiniteList';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import { combineSearchParamsAndFilters } from '@/constants';
import { redirect } from 'next/navigation';

export const metadata = {
  title: 'Flotilla  | Activos',
  description: 'Esto es la flotilla',
};

interface ActivosProps {
  searchParams: Record<string, string>;
}

export default async function Activos({ searchParams }: ActivosProps) {
  const user = await getCurrentUser();
  if (!user) return redirect('/');
  const filtersCookie = getCookie('filters', { cookies });

  const filters = filtersCookie ? JSON.parse(filtersCookie) : {};

  const definitiveFilters = combineSearchParamsAndFilters({ searchParams, filters });

  const result = await getStockVehicles({ limit: 50, listStatus, searchParams: definitiveFilters });
  if (!result) return null;

  return (
    <VehicleInfiniteList route="activos" page="Activos" data={result.stock} totalCount={result.totalCount} />
  );
}
