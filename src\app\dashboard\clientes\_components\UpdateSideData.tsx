import { useSearchParams } from 'next/navigation';
import useSidebarStore from '@/zustand/sidebarStore';
import axios from 'axios';
import { URL_API } from '@/constants';

export const useUpdateSideData = () => {
  const search = useSearchParams();
  const { setVehicleStatusCounts, setCategoryCounts } = useSidebarStore((state) => ({
    setVehicleStatusCounts: state.setVehicleStatusCounts,
    setCategoryCounts: state.setCategoryCounts,
  }));

  const updateSideData = async (user: any) => {
    const country = search ? search.get('country') : '';
    try {
      const response = await axios.get(
        `${URL_API}/user/sideData/${country ? `?country=${encodeURI(country)}` : ''}`,
        {
          headers: {
            Authorization: `Bearer ${user?.accessToken}`,
          },
        }
      );

      if (response.data?.sideBarData) {
        setVehicleStatusCounts(response?.data?.sideBarData?.vehicleStatusCounts);
        setCategoryCounts(response?.data?.sideBarData?.categoryCounts);
      }
    } catch (error) {
      console.error('Error fetching sidebar data:', error);
    }
  };

  return updateSideData;
};
