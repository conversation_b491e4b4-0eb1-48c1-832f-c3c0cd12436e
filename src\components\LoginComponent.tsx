/* eslint-disable prettier/prettier */
/* eslint-disable consistent-return */
'use client';
import { signIn, useSession } from 'next-auth/react';
import { IconContext } from 'react-icons/lib';
import { useEffect, useState } from 'react';
import Spinner from './Loading/Spinner';
import { useToast } from '@chakra-ui/react';
import { GoogleLoginButton } from './Auth/GoogleLoginButton';
import { AuthErrors, authErrors } from '@/lib/utils';
import { signOut } from 'next-auth/react';
import { CredentialsSignIn } from './CredentialsSignIn';
import { isLocal, MAXIMUM_GOOGLE_SINGIN_FAILURES_ALLOWED } from '@/constants';
import { useSearchParams } from 'next/navigation';

export default function LoginComponent() {
  const { data: session, status } = useSession();
  const toast = useToast();
  const [fallbackToCredsSignIn, setFallbackToCredsSignIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

  useEffect(() => {
    if (session && session.user.googleSignInfailureCount >= MAXIMUM_GOOGLE_SINGIN_FAILURES_ALLOWED) {
      setFallbackToCredsSignIn(true);
      signOut({
        redirect: false,
      });
      return;
    }
    if (session && session.user?.error) {
      const { error } = session.user;
      let title = 'Error';
      if (error === AuthErrors.Google_Registered_User_Not_Found) {
        title = authErrors[AuthErrors.Google_Registered_User_Not_Found];
      } else if (error === AuthErrors.Id_Token_Invalid) {
        title = authErrors[AuthErrors.Id_Token_Invalid];
      } else if (error === AuthErrors.Server_Is_Down) {
        title = authErrors[AuthErrors.Server_Is_Down];
      } else if (error === AuthErrors.Internal_Server_Error) {
        title = authErrors[AuthErrors.Internal_Server_Error];
      }
      toast({
        status: 'error',
        title: title,
        description: 'Please try again',
        isClosable: true,
        position: 'top',
        duration: 3000,
      });
      signOut({
        redirect: false,
      });
    }
  }, [toast, session]);

  const handleGoogleLogin = async () => {
    try {
      await signIn('google', { 
        redirect: false,
        callbackUrl,
      });
    } catch (err) {
      toast({
        status: 'error',
        title: 'Error occurred while signing in',
        description: 'Please try again',
        isClosable: true,
        position: 'top',
        duration: 3000,
      });
    }
  };

  const handleSetLoading = (lodingState: boolean) => {
    setIsLoading(lodingState);
  };

  if (status === 'loading' || isLoading) {
    return <Spinner />;
  }

  // Check if the app is running in local environment to be able to use credentials to sign in
  // Due to google auth restrictions, we can't use google auth in local environment

  return (
    <>
      {isLocal ? (
        <CredentialsSignIn handleSetLoading={handleSetLoading} callbackUrl={callbackUrl} />
      ) : (
          <IconContext.Provider value={{ style: { strokeWidth: '1px' } }}>
            {fallbackToCredsSignIn ? (
              <CredentialsSignIn 
                handleSetLoading={handleSetLoading} 
                callbackUrl={callbackUrl} 
              />
            ) : null}
            <div className="flex w-full items-center justify-center py-8">
              <GoogleLoginButton onClick={handleGoogleLogin} />
            </div>
          </IconContext.Provider>
      )}
    </>
  );
}
