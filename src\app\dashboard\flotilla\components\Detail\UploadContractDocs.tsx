/* eslint-disable @typescript-eslint/no-use-before-define */
'use client';
import { MyUser } from '@/actions/getCurrentUser';
import AddMoreFiles from '@/components/Inputs/AddMoreFiles';
import InputFile from '@/components/Inputs/InputFile';
import Spinner from '@/components/Loading/Spinner';
import CustomModal, { OnSubmitCustomModal } from '@/components/Modals/CustomModal';
import { URL_API } from '@/constants';
// import {
//   uploadSignedDocsSchema,
//   uploadSignedDocsSchemaAdmin,
// } from '@/validatorSchemas/uploadSignedDocsSchema';
import axios from 'axios';
import { useFormikContext } from 'formik';
import Image from 'next/image';
import { useState } from 'react';
import { RiCloseLine } from 'react-icons/ri';
import { Driver, VehicleResponse } from '@/actions/getVehicleData';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUpdateSideData } from '@/app/dashboard/clientes/_components/UpdateSideData';
//contract, promissoryNote, deliveryReceipt, warranty, invoice y privacy
interface UploadContractDocsProps {
  user: MyUser;
  associateId: string;
  driver: Driver;
  vehicleDetail: VehicleResponse;
}

const initialValues = {
  contract: '',
  promissoryNote: '',
  deliveryReceipt: '',
  warranty: '',
  invoice: '',
  privacy: '',
  deliveredImages: '',
};

export default function UploadContractDocs({
  user,
  associateId,
  driver,
  vehicleDetail,
}: UploadContractDocsProps) {
  // const signed = driver.digitalSignature.signed;
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const search = useSearchParams();
  const country = search ? search.get('country') : '';
  const updateSideData = useUpdateSideData();

  const onSubmit: OnSubmitCustomModal<typeof initialValues> = async (data, _, onClose) => {
    onClose();
    setLoading(true);
    const result = await axios.patch(`${URL_API}/associate/update/contract/${associateId}`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${user.accessToken}`,
      },
    });
    setLoading(false);
    await updateSideData(user);
    if (window.location.pathname.includes('inactive')) {
      router.push(
        `/dashboard/flotilla/active/${vehicleDetail?._id}${country ? `?country=${encodeURI(country)}` : ''}`
      );
    }
    return result;
  };

  const notManuelEmail = driver.digitalSignature?.participants?.filter(
    (participant) => participant.email !== '<EMAIL>'
  );

  const allAreSigned = notManuelEmail?.every((participant) => participant.signed);

  return (
    <>
      {loading && <Spinner />}

      {allAreSigned && (
        <CustomModal
          openButtonText="Entregar"
          testId="docsContract"
          size="3xl"
          header="Agregar Documentos de Contrato"
          initialValues={initialValues}
          onSubmit={onSubmit}
          body={<Body />}
          isPrimaryButton
          reloadWindow={false}
          onCloseModal={() => {
            console.log();
          }}
          confirmButtonText="Enviar"
        />
      )}
    </>
  );
}

interface ImagesType {
  url: string;
  name: string;
}

function Body() {
  const { setFieldValue } = useFormikContext();

  const [files, setFiles] = useState<FileList>({} as FileList);
  const [imgs, setImgs] = useState<ImagesType[]>([]);

  const onChange = (fileList: FileList | null) => {
    if (fileList) {
      setFiles((prevFileList) => {
        if (prevFileList) {
          // Convierte los FileList a arrays
          const newFilesArray = Array.from(fileList);

          // Combina los dos arrays
          const combinedFilesArray = [...newFilesArray];

          // Crea un nuevo FileList a partir del array combinado
          const combinedFileList = new DataTransfer();
          combinedFilesArray.forEach((file) => {
            combinedFileList.items.add(file);
          });
          return combinedFileList.files;
        } else {
          // Si prevFileList es null, simplemente devuelve newFileList
          return fileList;
        }
      });

      // Convierte las imagenes a url blob para preview
      const fls2 = Object.values(fileList);
      const filterNews = fls2.filter((item1) => !imgs.some((item2) => item1.name === item2.name));
      const imgs2 = filterNews?.map((file) => {
        return {
          url: URL.createObjectURL(file),
          name: file.name,
        };
      });
      setImgs([]);
      setImgs([...imgs, ...imgs2]);
    }
  };

  const removeImg = (url: string) => {
    const imgIndex = imgs.findIndex((img) => img.url === url);

    if (imgIndex !== -1) {
      const newImgs = [...imgs];
      newImgs.splice(imgIndex, 1);
      setImgs(newImgs);

      // Elimina el archivo correspondiente del FileList
      setFiles((prevFileList) => {
        if (prevFileList) {
          const newFileList = new DataTransfer();
          newImgs.forEach((img) => {
            // Agrega los archivos al nuevo FileList
            newFileList.items.add(new File([new Blob([img.name])], img.name));
          });
          setFieldValue('deliveredImages', newFileList.files);
          return newFileList.files;
        } else {
          setFieldValue('deliveredImages', prevFileList);
          return prevFileList;
        }
      });
    }
  };

  return (
    <div className="flex flex-col gap-3 ">
      {imgs.length < 1 ? (
        <InputFile
          name="deliveredImages"
          nameFile={''}
          label="Fotos de entrega (5 imagenes minimo)"
          multiple
          accept="all-images"
          placeholder="Archivo no mayor a 2mb"
          buttonText="Subir"
          onChange={(fls) => {
            onChange(fls);
          }}
        />
      ) : (
        <div className={` flex flex-col`}>
          <label htmlFor="deliveredImages">Fotos de entrega (5 imagenes minimo)</label>
          <div
            className={`
              flex gap-3 items-center pt-[10px]
              ${imgs.length > 6 && 'pb-[10px] overflow-y-hidden overflow-x-scroll custom-scroll'}
            `}
            style={{ width: '100%' }}
          >
            {imgs.map((img, index) => {
              return (
                <div className="min-w-[80px] relative  " key={index}>
                  <div
                    className="absolute z-[5 bg-gray-300 rounded-full top-[-10px] right-[-10px] cursor-pointer "
                    onClick={() => removeImg(img.url)}
                  >
                    <RiCloseLine size={22} width="2px" className="text-red-600" />
                  </div>
                  <Image
                    width="1000"
                    height="1000"
                    src={img.url}
                    alt={img.name}
                    className="w-[80px] h-[80px] object-cover "
                  />
                </div>
              );
            })}
            <AddMoreFiles
              name="deliveredImages"
              accept="all-images"
              multiple
              currentImages={files}
              onChange={(fls) => onChange(fls)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
