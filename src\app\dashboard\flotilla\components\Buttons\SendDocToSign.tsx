import { VehicleResponse } from '@/actions/getVehicleData';
import FormikContainer from '@/components/Formik/FormikContainer';
import Spinner from '@/components/Loading/Spinner';
import PrimaryButton from '@/components/PrimaryButton';
import { sendDocumentToSign } from '@/server-actions/weetrust';
import {
  IconButton,
  Popover,
  PopoverArrow,
  PopoverBody,
  PopoverCloseButton,
  PopoverContent,
  PopoverHeader,
  PopoverTrigger,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';
import { AiOutlineEye } from 'react-icons/ai';
import ModalContainer from '../Modals/ModalContainer';
import SelectInput from '@/components/Inputs/SelectInput';
import { Countries } from '@/constants';
import { useVehicleDetailData } from '../Providers/VehicleDetailDataProvider';

interface SendDocToSignProps {
  driver: VehicleResponse['drivers'][number];
  associateId: string;
  vehicleId: string;
}

export default function SendDocToSign({ driver, associateId, vehicleId }: SendDocToSignProps) {
  const [isSending, setIsSending] = useState(false);
  const toast = useToast();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const isCountryUSA = driver.country === Countries['United States'];
  const sendContractSignTest = isCountryUSA ? 'Send Contract to Sign' : 'Enviar Contrato a Firmar';
  const cancelBtnText = isCountryUSA ? 'Cancel' : 'Cancelar';
  const confirmBtnText = isCountryUSA ? 'Confirm' : 'Confirmar';

  const { vehicleData } = useVehicleDetailData();

  console.log('iselectric', vehicleData.isElectric);

  return (
    <>
      {isSending && <Spinner />}
      {driver.digitalSignature?.isSent && (
        <div>
          <ViewParticipantSignatureStatus driver={driver} />
        </div>
      )}
      {!driver.digitalSignature?.isSent && (
        <>
          <PrimaryButton
            disabled={isSending}
            onClick={() => {
              setIsModalOpen(true);
            }}
          >
            {sendContractSignTest}
          </PrimaryButton>

          {isModalOpen && (
            <ModalContainer
              title={sendContractSignTest}
              onClose={() => {
                setIsModalOpen(false);
              }}
            >
              <FormikContainer
                initialValues={{
                  // withAval: { label: 'Sin Aval', value: '0' },
                  withAval: { label: 'Con Aval', value: '1' },
                }}
                onSubmit={async (data) => {
                  const urlFile = driver.unSignedContractDoc?.url!;
                  const filename = driver.unSignedContractDoc?.originalName!;

                  const associate = {
                    email: driver.email,
                    name: `${driver.firstName} ${driver.lastName}`,
                    phone: driver.phone.toString(),
                  };

                  const aval = {
                    email: driver.avalData?.email!,
                    name: driver.avalData?.name!,
                    phone: driver.avalData?.phone!,
                  };

                  const withAval = data.withAval.value === '1';

                  try {
                    setIsSending(true);
                    const { success, error } = await sendDocumentToSign({
                      urlFile,
                      filename,
                      associateId,
                      associate,
                      aval,
                      stockId: vehicleId,
                      withAval,
                      isElectric: vehicleData.isElectric,
                      country: isCountryUSA ? Countries['United States'] : Countries.Mexico,
                    });
                    // console.log('RESULT', success);
                    if (success) {
                      toast({
                        title: 'Exito',
                        description: 'Documento enviado a firmar correctamente',
                        status: 'success',
                        position: 'top',
                        duration: 5000,
                        isClosable: true,
                      });

                      setTimeout(() => {
                        window.location.reload();
                      }, 2500);
                    } else {
                      console.log('error', error);
                      console.error('error', error);

                      if (error?.isExpired || error?.status === 403) {
                        toast({
                          title: 'La url del contrato generado ha expirado',
                          description:
                            'Recargando la página para generar una nueva url, por favor intenta de nuevo al recargar',
                          status: 'info',
                          position: 'top',
                          duration: 5000,
                          isClosable: true,
                        });
                        setTimeout(() => {
                          window.location.reload();
                        }, 5000);
                      }
                      toast({
                        title: error?.message || 'Something went wrong',
                        status: 'error',
                        position: 'top',
                        duration: 5000,
                        isClosable: true,
                      });

                      setIsSending(false);
                    }
                  } catch (error: any) {
                    console.error(error.message || error.response.data.message);
                    // createToast('Error', error.message || error.response.data.message, 'error');
                  } finally {
                    setIsSending(false);
                    setIsModalOpen(false);
                  }
                }}
                onClose={() => {
                  setIsModalOpen(false);
                }}
                addFooterBtns={<>{/* <FooterPreviewBtn /> */}</>}
                validateOnMount={false}
                validateChanges={true}
                confirmBtnText={confirmBtnText}
                cancelBtnText={cancelBtnText}
              >
                {isCountryUSA ? (
                  <ContractConfirmationUS />
                ) : (
                  <div className="flex flex-col gap-4">
                    <p>
                      Si se regeneró el contrato &quot;Sin Aval&quot; debes seleccionar &quot;Sin Aval&quot;
                      en el siguiente campo
                    </p>
                    <SelectInput
                      // label="Elige si el contrato es con o sin aval"
                      label="Selecciona si se le debe enviar el contrato al aval o no"
                      name="withAval"
                      options={[
                        { label: 'Con Aval', value: '1' },
                        { label: 'Sin Aval', value: '0' },
                      ]}
                    />
                  </div>
                )}
              </FormikContainer>
            </ModalContainer>
          )}
        </>
      )}
    </>
  );
}

const ContractConfirmationUS = () => {
  return (
    <div>
      <p>{'Are you sure you want to send Contract?'}</p>
    </div>
  );
};

function ViewParticipantSignatureStatus({ driver }: { driver: VehicleResponse['drivers'][number] }) {
  const toast = useToast();
  const digitalSignature = driver.digitalSignature;
  const participants = digitalSignature.participants || [];

  const signed = digitalSignature.signed;
  const isCountryUSA = driver.country === Countries['United States'];

  const signedText = isCountryUSA ? 'Signatures Completed' : 'Firmas completadas';
  const notSignedText = isCountryUSA
    ? 'The contract has already been sent to sign, wait for it to be signed by the driver, guarantor and administrator'
    : 'El contrato ya fue enviado a firmar, espera a que sea firmado por el conductor, aval y administrador';

  return (
    <>
      <Popover>
        <PopoverTrigger>
          <IconButton icon={<AiOutlineEye />} aria-label="see-allowed-regions" />
        </PopoverTrigger>
        <PopoverContent minW="300px">
          <PopoverHeader fontWeight="semibold">{signed ? signedText : notSignedText}</PopoverHeader>
          <PopoverArrow />
          <PopoverCloseButton />
          <PopoverBody w="100%" zIndex={200} className="flex flex-col gap-2">
            {/* <div>hola</div> */}
            {participants.map((participant, i) => (
              <div key={i} className="w-full flex justify-between  ">
                <div className="flex gap-2 max-w-[190px] items-center">
                  <p>{participant.name}</p>
                  <p className="w-[75px]">{participant.signed ? '✅' : '❌'}</p>
                </div>
                <button
                  className="text-[#5800F7] text-[14px]"
                  onClick={() => {
                    navigator.clipboard.writeText(participant.urlSign);
                    toast({
                      title: 'URL Copiada',
                      description: 'URL copiada al portapapeles',
                      status: 'success',
                      position: 'top',
                      duration: 5000,
                      isClosable: true,
                    });
                  }}
                >
                  Copiar URL
                </button>
              </div>
            ))}
          </PopoverBody>
        </PopoverContent>
      </Popover>
    </>
  );
}
